<script lang="tsx">
import { requestClient } from '#/api/request';
import DynamicTable from '#/components/dynamic-table/index.vue';
import Ketcher from '#/components/ketcher/index.vue';
import LuckySheet from '#/components/luckysheet/index.vue';
import UEditor from '#/components/ueditor/index.vue';
import { jsonToUrlParams } from '#/utils';
import { EventBus, offEvent, onEvent } from '#/utils/eventBus.ts';
import { useAccessStore } from '@vben/stores';
import { ButtonIcon, DeleteIcon } from 'tdesign-icons-vue-next';
import {
  Checkbox,
  CheckboxGroup,
  Collapse,
  CollapsePanel,
  Comment,
  DatePicker,
  FormItem,
  Input,
  InputNumber, MessagePlugin,
  Option,
  Radio,
  RadioGroup,
  Select,
  Textarea,
  TimePicker,
  Upload,
} from 'tdesign-vue-next';
import { defineAsyncComponent, defineComponent, ref } from 'vue';
import Draggable from 'vuedraggable';

export default defineComponent({
  name: 'FormItemRender',
  components: {
    Upload,
    Draggable,
    FormItem,
    Input,
    Textarea,
    RadioGroup,
    Radio,
    Checkbox,
    CheckboxGroup,
    Select,
    Option,
    DatePicker,
    TimePicker,
    InputNumber,
    Collapse,
    CollapsePanel,
    ButtonIcon,
    DeleteIcon,
    DynamicTable,
    UEditor,
    LuckySheet,
    Ketcher,
    Comment,
    Drager: defineAsyncComponent(
      () => import('#/views/modules/formEditer/components/editer/Drager.vue'),
    ),
    FormItemRender: defineAsyncComponent(
      () =>
        import(
          '#/views/modules/formEditer/components/render/FormItemRender.vue'
        ),
    ),
    // DragContent: defineAsyncComponent(
    //   () =>
    //     import('#/views/modules/formEditer/components/editer/DragContent.vue'),
    // ),
  },
  props: {
    eventPrefix: {
      type: String,
      default: '',
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const accessStore = useAccessStore();
    const selectedPanel = ref({});
    const asyncOptions = ref([]);
    const formData = ref(props.modelValue);
    const doHttp = async (data: {
      label: any;
      method: string;
      payload: any;
      url: any;
      value: any;
    }) => {
      if (data?.url) {
        let url = data.url;
        if (data.method === 'GET') {
          url = `${data.url}?${jsonToUrlParams(JSON.parse(data?.payload || '{}'))}`;
        }
        const res = await requestClient[data?.method?.toLowerCase() || 'get']?.<
          any[]
        >(url, data.payload);
        asyncOptions.value = res.map((item: { [x: string]: any }) => {
          return {
            value: `${item[data?.value || 'value']}`,
            label: `${item[data?.label || 'label']}`,
          };
        });
      }
    };
    const collapsePanelClick = (pitem: unknown) => {
      selectedPanel.value = pitem;
      // EventBus.emit(`collapse_${pitem.id}childrenPanel_select`, pitem);
      EventBus.emit(`formEditer_itemClick`, pitem);
      EventBus.emit(`childrenPanel_select`, pitem);
      EventBus.emit(`reSetChildrenPanel_select`, pitem);
    };
    const defHandler = {};
    const eventHandler = {
      reSetChildrenPanel_select: (data: {}) => {
        selectedPanel.value = data;
      },
    } as any;
    const makeEventHandler = () => {
      Object.keys(defHandler).forEach((item) => {
        eventHandler[`${props.eventPrefix}${item}`] = defHandler[item];
      });
    };

    const buildRules = () => {
      // 构建规则
    };
    const uploadSuccess = (context: { fileList: any[] }) => {
      formData.value[props.config.schema.name] = context.fileList.map(
        (item: {
          name: any;
          response: { result: { url: any } };
          status: any;
        }) => {
          return {
            name: item?.name,
            status: item?.status,
            path: item?.response?.result?.url || '',
            url: item?.response?.result?.temporaryUrl || '',
          };
        },
      );
    };
    const suffix = ref('.png,.svg,.jpeg,.jpg,.md,.txt,.pdf,.doc,.docx,.xls,.xlsx,.sdf')
    const beforeUpload = async (file) => {
      const index = file.name.lastIndexOf('.')
      if (index !== -1) {
        const suffix2 = file.name.substring(index + 1);
        if(!suffix.value.includes(suffix2)){
          MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
          return false;
        }
      }else{
        MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
        return false;
      }
      return true;
    };

    return {
      makeEventHandler,
      defHandler,
      formData,
      eventHandler,
      asyncOptions,
      selectedPanel,
      doHttp,
      collapsePanelClick,
      accessStore,
      uploadSuccess,
      beforeUpload,
    };
  },
  watch: {
    config: {
      handler(val) {
        if (val?.schema?.syncOptions?.url) {
          this.doHttp(val.schema.syncOptions);
        }
      },
      deep: true,
    },
    modelValue: {
      handler(val) {
        try {
          this.formData =
            this.config.component === 'TimePicker' && this.isEdit ? val : val;
        } catch (error) {
          console.error(error);
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.makeEventHandler();
    onEvent(this.eventHandler);
    if (this.config?.isGroup) {
      this.selectedPanel = this.config?.schema?.children?.[0];
      EventBus.emit(`childrenPanel_select`, this.config?.schema?.children?.[0]);
    }
    if (this.config?.schema?.syncOptions?.url) {
      this.doHttp(this.config.schema.syncOptions);
    }
    console.log('isEdit:', this.isEdit);
  },
  unmounted() {
    this.makeEventHandler();
    offEvent(this.eventHandler);
  },
});
</script>

<template>
  <!--    {{ formData }}-->
  <!--  {{ config.schema }}-->
  <!--  name:{{ config.schema.name }}&#45;&#45;&#45;&#45;-->
  <!--  {{ formData[config.schema.name] }}-->
  <!--  {{ config.props }}-->
  <FormItem
    :rules="config.schema.rules"
    v-if="!config.unFormItem"
    :label="config.schema?.title"
    :name="config.schema.name"
    class="custom-form-item"
  >
    <Input
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      v-bind="config.props"
      v-if="config.component === 'Input'"
      :placeholder="config.schema?.placeholder"
      style="width: 100%"
    />
    <InputNumber
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      v-bind="config.props"
      v-if="config.component === 'InputNumber'"
      :placeholder="config.schema?.placeholder"
      style="width: 100%"
    />

    <Textarea
      v-if="config.component === 'Textarea'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      :autosize = true
    />
    <RadioGroup
      v-if="config.component === 'Radio'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    >
      <div
        class="bg-background flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border p-1 pl-2 pr-2"
      >
        <Radio
          v-for="item in [...config.schema.options, ...asyncOptions]"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </Radio>
      </div>
    </RadioGroup>
    <CheckboxGroup
      v-if="config.component === 'Checkbox'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    >
      <div
        class="bg-background flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border p-1 pl-2 pr-2"
      >
        <Checkbox
          v-for="item in [...config.schema.options, ...asyncOptions]"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </Checkbox>
      </div>
    </CheckboxGroup>
    <Select
      v-if="config.component === 'Select'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      :multiple="config.schema?.multiple"
    >
      <Option
        v-for="item in [...config.schema.options, ...asyncOptions]"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </Select>

    <DatePicker
      v-if="config.component === 'DatePicker'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    />
    <DatePicker
      v-if="config.component === 'DateAndTimePicker'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    />
    <TimePicker
      v-if="config.component === 'TimePicker'"
      v-model="formData[config.schema.name]"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
    />
    <Upload
      :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
      v-if="config.component === 'Upload' && (formData[config.schema.name].length != 0 || !config.schema?.isReadonly)"
      v-model="formData[config.schema.name]"
      v-bind="{ ...config.props, ...config.schema, name: 'file' }"
      :accept="config.schema.accept?.join(',')"
      :upload-button="{ content: '上传' }"
      :cancel-upload-button="{ theme: 'default', content: '取消' }"
      style="width: 100%"
      :disabled="isEdit || config.schema?.isReadonly"
      @success="uploadSuccess" :beforeUpload="beforeUpload"
    />

    <DynamicTable
      v-if="config.component === 'DynamicTable'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      style="width: 100%"
    />

    <UEditor
      v-bind="config.props"
      v-if="config.component === 'UEditor'"
      v-model="formData[config.schema.name]"
      style="width: 100%; height: 800px"
    />

    <Ketcher
      v-bind="config.props"
      v-if="config.component === 'Ketcher'"
      v-model="formData[config.schema.name]"
      style="width: 100%; height: 800px"
    />
    <Comment
      v-bind="config.props"
      v-if="config.component === 'Comment'"
      :avatar="config.schema.avatar"
      :author="config.schema.author"
      :datetime="config.schema.datetime"
      :content="config.schema.content"
      style="width: 100%"
    />
  </FormItem>
  <div v-else class="w-full">

    <FormItem
      v-if="config.component === 'LuckySheet'"
      :rules="config.schema.rules"
      :label="config.schema?.title"
      :name="config.schema.name"
      class="custom-form-item"
    >
      <LuckySheet
        v-bind="config.props"
        v-if="config.component === 'LuckySheet'"
        v-model="formData[config.schema.name]"
        style="width: 100%; height: 800px"
      />
    </FormItem>

    <Collapse
      v-if="config.component === 'Collapse'"
      default-expand-all
      style="width: 100%; margin-bottom: 24px"
    >
      <CollapsePanel
        v-for="pitem in config.schema.children"
        :key="pitem.id"
        :header="pitem.schema.title"
        style="width: 100%"
      >
        <div class="w-full">
          <div
            v-if="isEdit"
            :class="`flex p-1 ${selectedPanel.id == pitem.id ? 'border border-dashed border-blue-400' : ''}`"
          >
            <div
              class="mr-2 h-[100%] w-[20px] justify-center"
              @click.stop="collapsePanelClick(pitem)"
            >
              <ButtonIcon size="20px" />
            </div>
            <!-- v-if="pitem?.schema?.children?.length > 0" -->
            <Drager
              :columns="pitem?.schema?.columns || 1"
              :event-prefix="`collapse_${pitem.id}`"
              :model-value="pitem.schema.children"
              drag-group="vbox"
              style="width: 100%; min-height: 24px"
            />
          </div>
          <div
            v-else
            :class="`grid w-full grid-cols-${pitem?.schema?.columns || 1} w-full gap-1`"
          >
            <FormItemRender
              v-for="item in pitem.schema.children"
              :key="item.id"
              v-model="formData"
              :config="item"
            />
          </div>
        </div>
      </CollapsePanel>
    </Collapse>
  </div>
</template>

<style scoped lang="less">
:deep(.custom-form-item) {
  .t-form__label {
    min-width: 300px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: -70px;
  }
}

:deep(.t-form__controls) {
  flex: 1;
}
:deep(.t-collapse-panel__content) {
  background: transparent !important;
  padding: 25px;
}

:deep(.t-collapse-panel__body) {
  background: transparent !important;
}

:deep(.t-form__item) {
  //margin-bottom: 10px;
  margin-right: 0px;
}

:deep(.t-collapse-panel__header) {
  padding: 6px;
}

:deep(.t-upload__flow) {
  width: 100%;
  min-width: 0px !important;
  max-width: none !important;
}
</style>
