<script setup lang="tsx">
import Mo<PERSON>culeViewer from '#/components/molecule-viewer/index.vue';
import { useSearchStore } from '#/store/search';
import { computed, onMounted, reactive, ref, watch } from 'vue';

import { getStructureInfoData } from '../api';

// 定义 props
const props = defineProps({
  structureData: {
    type: Object,
    default: {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const state: any = reactive({
  detailItem: {},
  loading: false,
});

// Store
const searchStore = useSearchStore();
state.detailItem = computed(() => {
  const detail = searchStore.currentDetailItem;
  console.log('detail', detail);

  if (detail) {
    return detail;
  } else {
    const local = localStorage.getItem('currentDetailItem');
    return JSON.parse(local ?? '{}');
  }
});
const smiles = ref('');

// 监听 props 变化，更新数据
watch(() => props.structureData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    // 从父组件传递的数据中获取结构信息
    smiles.value = newData.structure2d || newData.smiles || newData.structure || '';
  }
}, { immediate: true, deep: true });

// 监听 loading 状态变化
watch(() => props.loading, (newLoading) => {
  console.log("loading", newLoading, 2222);
});

const handleError = (message: string) => {
  console.error(message);
};

const handleLoaded = (success: boolean) => {
  console.log('Molecule loaded:', success);
};
const initSmiles = async () => {
  const res = await getStructureInfoData({
    inchikey: state.detailItem.baseCode,
  });

  const structure2d = res.structure2d;
  smiles.value = structure2d;
};
onMounted(() => {
  // 如果父组件没有传递数据，则使用原有的逻辑
  // if (!props.structureData || Object.keys(props.structureData).length === 0) {
  //   initSmiles();
  // }
});
</script>
<template>
  <div class="structure">
    <MoleculeViewer
      v-model="smiles"
      @error="handleError"
      @loaded="handleLoaded"
    />
  </div>
</template>

<style scoped lang="scss">
.structure {
  padding: 20px;

  .info-content {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    text-align: center;

    .structure-diagram {
      width: 300px;
      height: 200px;
      background: #fff;
      margin: 0 auto 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-text {
        color: #999;
        font-size: 14px;
      }
    }

    .molecular-formula {
      margin: 0;
      text-align: center;
    }
  }
}
</style>
