<script setup lang="tsx">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import EditForm from './components/EditForm.vue';
import IndexTable from './components/IndexTable.vue';

const editFormRef = ref();
const tableRef = ref();
</script>

<template>
  <Page
    description="数据工具列表"
    title="数据工具列表"
  >
    <EditForm ref="editFormRef" :out-ref="tableRef" />
    <IndexTable ref="tableRef" :edit-form-ref="editFormRef" />
  </Page>
</template>

<style scoped></style>
