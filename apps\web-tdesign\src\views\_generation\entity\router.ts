// import type { RouteRecordRaw } from 'vue-router';

// import { BasicLayout } from '#/layouts';
// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     component: BasicLayout,
//     meta: {
//       icon: 'lucide:wrench',
//       keepAlive: true,
//       order: 1000,
//       title: "代码生成器",
//     },
//     name: 'Generation',
//     path: '/generation',
//     children: [
//       {
//         meta: {
//           title: "实体模型",
//         },
//         name: 'GenerationEntity',
//         path: '/generation/entity',
//         component: () => import('#/views/_generation/entity/UserPopSelect.vue'),
//       },
//     ],
//   },
// ];

// export default routes;
