// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/tSysBannerManage/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/tSysBannerManage/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/tSysBannerManage/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/tSysBannerManage/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/tSysBannerManage/getByIds/${data}`);
}
