<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Form,
  FormItem,
  type FormProps,
  Input,
  Select,
  Textarea,
} from 'tdesign-vue-next';

import { getDictItems } from '#/api';

import { permissionSave } from '../../api';

const formData: any = ref({});
const form = ref();

const types = ref([]);
const powers = ref([]);

const FORM_RULES: FormProps['rules'] = {
  code: [
    {
      required: true,
      message: '必填',
    },
  ],
  name: [
    {
      required: true,
      message: '必填',
    },
  ],
  tag: [
    {
      required: true,
      message: '必填',
    },
  ],
  type: [
    {
      required: true,
      message: '必填',
    },
  ],
  power: [
    {
      required: true,
      message: '必填',
    },
  ],
  remark: [
    {
      required: false,
    },
  ],
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    const vali = await form.value.validate();
    if (vali === true) {
      // 验证通过提交请求 并且关闭窗口
      await permissionSave(formData.value);
      modalApi.getData().refresh();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    formData.value = isOpen
      ? { ...modalApi.getData<Record<string, any>>()?.record }
      : {};
  },
  title: '新增',
});

onMounted(async () => {
  types.value = await getDictItems('PERMISSION_TYPE');
  powers.value = await getDictItems('PERMISSION_POWER');
});
</script>
<template>
  <Modal>
    <Form
      ref="form"
      :data="formData"
      :label-width="120"
      :rules="FORM_RULES"
      colon
      label-align="top"
    >
      <FormItem label="权限标识" name="code">
        <Input v-model="formData.code" placeholder="请输入权限标识" />
      </FormItem>

      <FormItem label="权限名称" name="name">
        <Input
          v-model="formData.name"
          :style="{ minWidth: '134px' }"
          placeholder="请输入权限名称"
        />
      </FormItem>
      <FormItem label="标签" name="tag">
        <Input
          v-model="formData.tag"
          :style="{ minWidth: '134px' }"
          placeholder="请输入标签"
        />
      </FormItem>

      <FormItem label="授权类型" name="type">
        <Select
          v-model="formData.type"
          :options="types"
          clearable
          placeholder="选择授权类型"
        />
      </FormItem>

      <FormItem v-if="formData.type === 0" label="系统权限开关" name="power">
        <Select
          v-model="formData.power"
          :options="powers"
          clearable
          placeholder="选择系统权限开关"
        />
      </FormItem>

      <FormItem label="备注" name="remark">
        <Textarea
          v-model="formData.remark"
          :style="{ minWidth: '134px' }"
          class="form-item-content"
          placeholder="请输入备注"
        />
      </FormItem>
    </Form>
  </Modal>
</template>
