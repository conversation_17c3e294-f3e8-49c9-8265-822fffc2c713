import { requestClient } from '#/api/request';

// 获取搜索数据
export async function getSearchData(params: any) {
  return requestClient.post('/rgdc-search/tSearch/search', params);
}

// 获取搜索条件接口(关键词和数量)
export async function getLabelManage(params: any) {
  return requestClient.post('/rgdc-submit/tDataBase/getLabelManage', params);
}

// 获取物质/化学反应数据查询接口
export async function getSubstanceInfo(params: any) {
  return requestClient.post(
    '/rgdc-api/dSubstanceBase/getSubstanceInfo',
    params,
  );
}

// 获取左侧树结构
export async function getCollectByClassCode(params: any) {
  return requestClient.post(
    '/rgdc-submit/tDataClassify/collectByClassCode',
    params,
  );
}
// 获取资源类型和数量
export async function getResourcesTypeWithCount(data: any) {
  return requestClient.post<any>(
    `/rgdc-search/tSearch/getResourcesTypeWithCount`,
    data,
  );
}
