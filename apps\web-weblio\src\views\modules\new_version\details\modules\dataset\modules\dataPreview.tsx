import type { PropType, Ref } from 'vue';

import { Card, Select } from 'tdesign-vue-next';
import { computed, defineComponent, ref, watch } from 'vue';

import Preview from './preview';

export default defineComponent({
  name: 'DataPreview',
  props: {
    data: {
      type: Object as PropType<Ref<any>>,
      default: () => [],
    },
  },
  setup(props) {
    // 格式化 options
    const select1Options = computed(() => {
      return Array.isArray(props.data.value)
        ? props.data.value.map((item: any) => ({
            label: item.subName,
            value: item.id,
            child: (item.purposeList || []).map((p: any) => ({
              label: p.purpose,
              value: p.datasetFileCode,
              fileStr: p.fileStr,
            })),
          }))
        : [];
    });

    // 选中第一个下拉的值
    const select1Value = ref<any>(null);
    // 选中第二个下拉的值
    const select2Value = ref<any>(null);

    // 监听 select1Options，自动设置默认值
    watch(
      select1Options,
      (optsArr) => {
        if (Array.isArray(optsArr) && optsArr.length > 0) {
          const first = optsArr[0];
          select1Value.value =
            first && first.value !== undefined ? first.value : null;
          const child = first && Array.isArray(first.child) ? first.child : [];
          select2Value.value =
            child.length > 0 && child[0].value !== undefined
              ? child[0].value
              : null;
        } else {
          select1Value.value = null;
          select2Value.value = null;
        }
      },
      { immediate: true },
    );

    // select2Options依赖select1Value
    const select2Options = computed(() => {
      const arr = Array.isArray(select1Options.value)
        ? select1Options.value
        : [];
      const found = arr.find((opt) => opt.value === select1Value.value);
      return found ? found.child : [];
    });

    // select1Value变化时，重置select2Value
    function handleSelect1Change(val: any) {
      const arr = Array.isArray(select1Options.value)
        ? select1Options.value
        : [];
      const found = arr.find((opt) => opt.value === val);
      const child = found && Array.isArray(found.child) ? found.child : [];
      select2Value.value = child.length > 0 ? child[0].value : null;
    }
    const showFileStr = computed(() => {
      const arr = Array.isArray(select2Options.value)
        ? select2Options.value
        : [];
      const found = arr.find((opt) => opt.value === select2Value.value);
      if (found && found.fileStr) {
        try {
          return JSON.parse(found.fileStr);
        } catch {
          return [];
        }
      }
      return [];
    });
    return () => (
      <Card
        style={{
          maxWidth: '1200px',
          margin: '32px auto',
          boxShadow: '0 2px 8px #f0f1f2',
        }}
      >
        <div
          style={{
            display: 'flex',
            gap: '24px',
            alignItems: 'center',
            marginBottom: '20px',
            flexWrap: 'wrap',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <span
              style={{
                minWidth: '56px',
                fontSize: '14px',
                color: '#555',
                textAlign: 'right',
              }}
            >
              子数据集
            </span>

            <Select
              onChange={handleSelect1Change}
              options={select1Options.value}
              placeholder="请选择子数据集"
              style={{ width: '180px' }}
              v-model={select1Value.value}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <span
              style={{
                minWidth: '40px',
                fontSize: '14px',
                color: '#555',
                textAlign: 'right',
              }}
            >
              用途
            </span>
            <Select
              disabled={!select1Value.value}
              options={select2Options.value}
              placeholder="请选择用途"
              style={{ width: '180px' }}
              v-model={select2Value.value}
            />
          </div>
        </div>
        <Preview jsonData={showFileStr.value} />
      </Card>
    );
  },
});
