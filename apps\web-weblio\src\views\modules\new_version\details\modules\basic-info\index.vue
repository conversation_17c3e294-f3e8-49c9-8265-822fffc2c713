<script setup lang="tsx">
import Collection from '#/components/collection/index.vue';
import Share from '#/components/share/index.vue';
import { useSearchStore } from '#/store/search';
import { computed, h, onMounted, reactive, ref } from 'vue';

import DetailHeader from '../common/detail-header/index.tsx';
import DetailLayout from '../common/detail-layout-tabbed/index.tsx';

// 导入子组件
import {
  CodmDiatomicInfo,
  CombinedInfo,
  CondensedPhaseInfo,
  DSubstanceBusinessInfo,
  DSubstanceToc,
  GasPhaseInfo,
  // DSubstanceClassification,
  HazardInfo,
  HlConstantInfo,
  PhaseChangeInfo,
  ReactionInfo,
  References,
  Notes,
  DSubstanceUpDownInfo,
} from './components';

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
   * 加载状态
   */
  loading: false,
});

// Store
const searchStore = useSearchStore();

// 获取当前详情数据 - 修复响应式数据获取
const detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log('当前详情数据Index', detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log('当前详情数据Index', local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

// 更新state中的detailItem
state.detailItem = detailItem;

// 模拟阿莫西林数据
const mockData = ref({
  // 顶部卡片数据
  // topCardData: {
  //   id: '551-16-6',
  //   infomation: {
  //     author: '化学数据库',
  //     browsingCount: 122,
  //     // '阿莫西林 | amoxicillin',
  //     data_name: state.detailItem.infomation.data_name,
  //     dataAuth_text: '公开',
  //     dataType_text: '化合物数据',
  //     describe: '阿莫西林是一种青霉素类抗生素，广泛用于治疗细菌感染。',
  //     img: '/api/placeholder/200/150',
  //   },
  //   tags: ['抗生素', '青霉素类', '药物'],
  //   createTime: '2024-01-15',
  //   isFavorite: false,
  //   doi: '10.1000/182',
  //   dataType_text: '化合物数据',
  // },
  topCardData: { ...state.detailItem },

  // 左侧菜单数据 - 完全匹配图片中的菜单项
  menuData: [
    {
      title: '物质基础信息',
      children: [
        {
          title: '基本信息',
          content: () => h(CombinedInfo),
        },
        {
          title: '图谱',
          content: () => h(DSubstanceToc),
        },
        {
          title: '危险识别',
          content: () => h(HazardInfo),
        },
        {
          title: '上下游信息',
          content: () => h(DSubstanceUpDownInfo),
        },
      ],
    },
    {
      title: '商家信息',
      content: () => h(DSubstanceBusinessInfo),
    },
    {
      title: '反应数据',
      content: () => h(ReactionInfo),
    },
    {
      title: '热力学数据',
      children: [
        {
          title: '气相',
          content: () => h(GasPhaseInfo),
        },
        {
          title: '凝聚相',
          content: () => h(CondensedPhaseInfo),
        },
        {
          title: '相变',
          content: () => h(PhaseChangeInfo),
        },
        {
          title: '双原子分子常数',
          content: () => h(CodmDiatomicInfo),
        },
        {
          title: '亨利定律',
          content: () => h(HlConstantInfo),
        },
        {
          title: '引用',
          content: () => h(References),
        },
        {
          title: '说明',
          content: () => h(Notes),
        },
      ],
    },
    // {
    //   title: '分类',
    //   content: () => h(DSubstanceClassification),
    // },
    // {
    //   title: '物化性质',
    //   content: () => h(PhysicalInfo),
    // },
    // {
    //   title: '计算性质',
    //   content: () => h(CalculateInfo),
    // },
    // {
    //   title: '用途信息',
    //   content: () => h(PurposeInfo),
    // },

    // {
    //   title: '毒性',
    //   content: () => h(Toxicity),
    // },
    // {
    //   title: '结构',
    //   content: () => h(Structure),
    // },
  ],

  // 右侧内容数据 - 根据图片，这是两栏布局，所以移除右侧栏
  rightData: [],
});

// 计算属性：拆分标签名称
const splitLabels = computed(() => {
  const labelName = detailItem.value?.labelName;
  return labelName ? labelName.split(',').filter((label) => label.trim()) : [];
});

// 计算属性：根据共享状态获取自定义颜色
const getDataAuthColor = (dataAuth: number | string | undefined) => {
  if (dataAuth === undefined || dataAuth === null) return '#ef4444'; // 默认红色

  switch (+dataAuth) {
    case 0: {
      // 完全共享
      return '#10b981'; // 绿色
    }
    case 1: {
      // 审批共享
      return '#f59e0b'; // 橙色
    }
    case 2: {
      // 暂不共享
      return '#ef4444'; // 红色
    }
    default: {
      return '#6366f1'; // 紫色
    }
  }
};

// 构建layout数据 - 由于头部已显示基本信息，这里不再显示顶部卡片
const layoutData = ref({
  // topCard: () => <TopCard data={mockData.value.topCardData} />, // 注释掉避免重复
  menu: mockData.value.menuData,
  right: mockData.value.rightData,
});

// 拖拽滚动功能
const tagsContainer = ref<HTMLElement | null>(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);

// 检查滚动位置，控制箭头显示
const checkScrollPosition = () => {
  const container = tagsContainer.value;
  if (!container) return;

  const scrollLeft = container.scrollLeft;
  const scrollWidth = container.scrollWidth;
  const clientWidth = container.clientWidth;

  // 内容是否超出容器宽度
  const hasOverflow = scrollWidth > clientWidth + 10;

  // 左箭头：有向左滚动空间时显示
  showLeftArrow.value = hasOverflow && scrollLeft > 5;

  // 右箭头：有向右滚动空间时显示
  showRightArrow.value =
    hasOverflow && scrollLeft < scrollWidth - clientWidth - 5;
};

// 左滚动
const scrollToLeft = () => {
  const container = tagsContainer.value;
  if (!container) return;
  container.scrollBy({ left: -200, behavior: 'smooth' });
  setTimeout(checkScrollPosition, 300);
};

// 右滚动
const scrollToRight = () => {
  const container = tagsContainer.value;
  if (!container) return;
  container.scrollBy({ left: 200, behavior: 'smooth' });
  setTimeout(checkScrollPosition, 300);
};

function onCollectionChange(
  row: any,
  event: { isFavorite: boolean; row: any },
) {
  row.isFavorite = event.isFavorite;
}

onMounted(() => {
  const container = tagsContainer.value;
  if (!container) return;

  let isDown = false;
  let startX = 0;
  let scrollLeftPos = 0;

  // 初始检查 - 延迟更长时间确保DOM完全渲染
  setTimeout(() => {
    checkScrollPosition();
    // 再次检查确保准确
    setTimeout(checkScrollPosition, 100);
  }, 300);

  // 鼠标按下
  const handleMouseDown = (e: MouseEvent) => {
    isDown = true;
    container.classList.add('dragging');
    startX = e.pageX - container.offsetLeft;
    scrollLeftPos = container.scrollLeft;
    e.preventDefault();
  };

  // 鼠标抬起
  const handleMouseUp = () => {
    isDown = false;
    container.classList.remove('dragging');
    checkScrollPosition();
  };

  // 鼠标离开
  const handleMouseLeave = () => {
    isDown = false;
    container.classList.remove('dragging');
    checkScrollPosition();
  };

  // 鼠标移动
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDown) return;
    e.preventDefault();
    const x = e.pageX - container.offsetLeft;
    const walk = (x - startX) * 2; // 调节滚动速度
    container.scrollLeft = scrollLeftPos - walk;
  };

  // 滚动事件监听
  const handleScroll = () => {
    checkScrollPosition();
  };

  // 窗口大小变化监听
  const handleResize = () => {
    setTimeout(checkScrollPosition, 100);
    // 多次检查确保准确
    setTimeout(checkScrollPosition, 300);
  };

  // 添加事件监听
  container.addEventListener('mousedown', handleMouseDown);
  container.addEventListener('mouseup', handleMouseUp);
  container.addEventListener('mouseleave', handleMouseLeave);
  container.addEventListener('mousemove', handleMouseMove);
  container.addEventListener('scroll', handleScroll);
  window.addEventListener('resize', handleResize);

  // 组件卸载时移除事件监听
  return () => {
    container.removeEventListener('mousedown', handleMouseDown);
    container.removeEventListener('mouseup', handleMouseUp);
    container.removeEventListener('mouseleave', handleMouseLeave);
    container.removeEventListener('mousemove', handleMouseMove);
    container.removeEventListener('scroll', handleScroll);
    window.removeEventListener('resize', handleResize);
  };
});
</script>

<template>
  <div>
    <!-- 详情页头部 -->
    <DetailHeader mode="slot" :height="90">
      <!-- 左插槽：化合物信息 -->
      <template #left>
        <div class="header-left-container">
          <div class="compound-info">
            <!-- <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              class="compound-icon"
            >
              <path
                d="M22.464 18.56l-2.976-1.952c0.384-1.088 0.288-2.304-0.256-3.36l2.432-2.176c1.376 0.832 3.168 0.672 4.32-0.512 1.376-1.344 1.376-3.584 0-4.928s-3.552-1.376-4.928 0c-1.088 1.056-1.312 2.656-0.704 3.968l-2.432 2.208c-0.669-0.471-1.501-0.752-2.399-0.752-0.566 0-1.105 0.112-1.597 0.314l0.028-0.010-2.848-3.904c0.438-0.564 0.702-1.281 0.702-2.060 0-0.933-0.378-1.777-0.99-2.388l-0-0c-1.344-1.344-3.488-1.344-4.832 0s-1.344 3.488 0 4.8c0.96 0.96 2.336 1.248 3.52 0.832l2.848 3.904c-1.408 1.632-1.312 4.096 0.224 5.664 0.032 0.032 0.064 0.032 0.096 0.064l-2.752 4.832c-0.25-0.066-0.537-0.104-0.833-0.104-0.954 0-1.816 0.395-2.431 1.031l-0.001 0.001c-1.376 1.376-1.376 3.584 0 4.96s3.552 1.344 4.928 0c1.344-1.376 1.376-3.552 0.032-4.928l2.752-4.832c1.376 0.416 2.944 0.096 4.032-0.96l3.008 1.952c-0.192 0.864 0.064 1.824 0.736 2.528 1.088 1.088 2.848 1.088 3.936 0s1.088-2.848 0-3.904c-0.504-0.509-1.203-0.823-1.975-0.823-0.617 0-1.187 0.201-1.648 0.541l0.008-0.005zM7.808 25.728c-0.064 0.064-0.128 0.128-0.16 0.192-0.096 0.096-0.192 0.192-0.32 0.224-0.16 0.064-0.32 0-0.448-0.128-0.352-0.32-0.224-0.96 0.288-1.472 0.512-0.48 1.12-0.64 1.472-0.288 0.128 0.128 0.16 0.288 0.128 0.448-0.064 0.128-0.16 0.256-0.224 0.32s-0.128 0.128-0.224 0.192c-0.064 0.064-0.16 0.16-0.256 0.224-0.096 0.096-0.16 0.192-0.256 0.288zM21.568 6.144c0.512-0.512 1.152-0.64 1.472-0.288 0.128 0.128 0.16 0.288 0.128 0.448-0.032 0.128-0.128 0.224-0.224 0.32-0.064 0.032-0.128 0.096-0.192 0.16-0.096 0.096-0.192 0.16-0.288 0.256-0.064 0.096-0.16 0.192-0.224 0.256-0.064 0.096-0.128 0.16-0.192 0.224-0.096 0.064-0.192 0.16-0.32 0.192-0.046 0.020-0.1 0.032-0.156 0.032-0.115 0-0.219-0.049-0.291-0.128l-0-0c-0.352-0.352-0.224-0.96 0.288-1.472zM7.136 4.64c-0.064 0.096-0.128 0.16-0.192 0.224s-0.16 0.16-0.288 0.192c-0.16 0.032-0.32 0-0.448-0.128-0.32-0.32-0.192-0.928 0.288-1.408s1.12-0.64 1.44-0.288c0.096 0.096 0.16 0.256 0.096 0.416-0.032 0.128-0.128 0.256-0.192 0.32l-0.192 0.192c-0.101 0.059-0.186 0.134-0.255 0.222l-0.002 0.002-0.256 0.256zM13.216 12.896c0.576-0.608 1.344-0.736 1.728-0.352 0.16 0.16 0.224 0.352 0.16 0.544-0.062 0.15-0.148 0.277-0.256 0.384l-0 0c-0.064 0.064-0.16 0.128-0.256 0.224s-0.192 0.192-0.32 0.288c-0.096 0.096-0.192 0.224-0.288 0.32-0.064 0.096-0.16 0.16-0.224 0.256-0.107 0.108-0.234 0.194-0.376 0.253l-0.008 0.003c-0.038 0.009-0.081 0.014-0.126 0.014-0.163 0-0.311-0.066-0.418-0.174l-0-0c-0.384-0.384-0.224-1.152 0.384-1.76zM22.944 20.352c-0.071 0.069-0.156 0.124-0.251 0.158l-0.005 0.002c-0.028 0.008-0.061 0.012-0.094 0.012-0.101 0-0.192-0.041-0.258-0.108l-0-0c-0.288-0.256-0.16-0.768 0.224-1.152 0.416-0.416 0.896-0.512 1.152-0.256 0.128 0.096 0.16 0.256 0.096 0.384-0.032 0.096-0.096 0.192-0.16 0.224-0.037 0.068-0.092 0.123-0.158 0.159l-0.002 0.001c-0.064 0.064-0.128 0.128-0.224 0.192s-0.128 0.16-0.192 0.224c-0.032 0.064-0.096 0.096-0.128 0.16z"
                fill="currentColor"
              />
            </svg> -->
            <img
              width="32"
              height="32"
              :src="detailItem?.infomation.img"
              class="compound-icon"
            />
            <h1 class="compound-title">
               {{ detailItem?.dataName }}
            </h1>
          </div>
          <div class="tags-wrapper">
            <!-- 左箭头 -->
            <button
              v-show="showLeftArrow"
              @click="scrollToLeft"
              class="scroll-arrow scroll-arrow-left"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- 标签容器 -->
            <div class="tags-container" ref="tagsContainer">
              <div v-for="label in splitLabels" :key="label">
                <a href="#" class="tag-link"> {{ label }} </a>
              </div>
            </div>

            <!-- 右箭头 -->
            <button
              v-show="showRightArrow"
              @click="scrollToRight"
              class="scroll-arrow scroll-arrow-right"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </template>

      <!-- 右插槽：操作按钮 -->
      <template #right>
        <div class="header-right-container">
          <!-- 共享状态tag -->
          <Tag
            v-if="state.detailItem.dataAuth_text"
            class="card-auth-tag"
            size="small"
            :style="{
              backgroundColor: `${getDataAuthColor(state.detailItem.dataAuth)}E6`,
              color: 'white',
              border: `1px solid ${getDataAuthColor(state.detailItem.dataAuth)}`,
            }"
          >
            {{ state.detailItem.dataAuth_text }}
          </Tag>

          <!-- 浏览量 -->
          <div class="card-actions">
            <Collection
              :is-favorite="state.detailItem.isFavorite"
              :row="state.detailItem"
              @collection-change="onCollectionChange(state.detailItem, $event)"
            />
            <Share :row="state.detailItem" />
          </div>

          <!-- 收藏按钮
          <button class="icon-btn">
            <svg
              class="btn-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
          </button> -->

          <!-- 分享按钮 -->
          <!-- <button class="icon-btn">
            <svg
              class="btn-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
              />
            </svg>
          </button> -->
        </div>
      </template>
    </DetailHeader>

    <!-- 三栏布局主体 -->
    <DetailLayout :data="layoutData" />

    <!-- 右下角用户反馈按钮 -->
    <!-- <div class="feedback-container">
      <button class="feedback-btn">用户反馈</button>
    </div> -->
  </div>
</template>

<style scoped lang="scss">
// 页面整体样式
:deep(.three-col-layout) {
  margin: 0 auto;
  max-width: 1400px;
}

// Header - 白色背景
:deep(.header-container) {
  height: 90px !important;
  margin-bottom: 0 !important;
  background: #ffffff !important;
  border-bottom: 1px solid #e8e8e8 !important;

  @media (max-width: 1000px) {
    height: auto !important;
    min-height: 120px !important;
  }
}

:deep(.header-content) {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;

  @media (max-width: 1000px) {
    flex-direction: column;
    align-items: stretch;
    padding: 15px 10px;
    gap: 15px;
  }
}

:deep(.header-back-btn) {
  margin-right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px !important;
  font-size: 14px !important;
  color: #666 !important;
}

:deep(.header-left) {
  flex: 1;
  overflow: hidden;
  width: 1px;

  @media (max-width: 1000px) {
    flex: none;
    width: 100%;
  }
}

:deep(.header-right) {
  flex: 0 0 auto;
  text-align: right;

  @media (max-width: 1000px) {
    flex: none;
    width: 100%;
    text-align: left;
  }
}

:deep(.three-col-content-block) {
  margin-bottom: 20px;

  h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1890ff;
  }

  h4 {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
  }
}

// 右侧内容样式
:deep(.three-col-right-block) {
  margin-bottom: 16px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;

  .three-col-right-title {
    background: #f5f5f5;
    padding: 12px 15px;
    font-weight: 500;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 8px 8px 0 0;
  }
}

:deep(.three-col-menu) {
  width: 150px;
  background: #fff;
  border: 1px solid #e8e8e8;
}

:deep(.three-col-menu-item) {
  padding: 12px 16px;
  font-size: 14px;
  border-bottom: 1px solid #e8e8e8;
  cursor: pointer;
  background: #fff;
  color: #333;

  &:hover {
    background: #f8f9fa;
  }

  &.active {
    background: #1890ff;
    color: white;
  }

  &:last-child {
    border-bottom: none;
  }
}

// Header左侧样式
.header-left-container {
  display: flex;
  align-items: center;
  margin-left: 10px;
  gap: 16px;
  flex: 1;
  min-width: 0;
  overflow: hidden;

  @media (max-width: 1100px) {
    flex-direction: column;
    align-items: flex-start;
    margin-left: 0;
    gap: 12px;
    overflow: visible;
  }
}

.compound-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  max-width: 400px;
}

.compound-icon {
  color: #000a7b;
}

.compound-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #000a7b;
  font-size: 28px;
  margin: 0 0 7px 7px;
  font-weight: normal;
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
  max-width: 350px;

  @media (max-width: 1000px) {
    font-size: 24px;
    margin: 0;
    max-width: 280px;
  }
}

.tags-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  margin-right: 12px;
  position: relative;
  min-width: 0;
  max-width: 100%;

  // 纵向布局时限制宽度，确保标签容器能触发滚动
  @media (max-width: 1100px) and (min-width: 769px) {
    max-width: 100%;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
    max-width: none;
  }
}

.tags-container {
  display: flex;
  align-items: center;
  gap: 16px;
  overflow-x: auto;
  flex: 1;
  cursor: grab;
  user-select: none;
  min-width: 0;

  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;

  &.dragging {
    cursor: grabbing;

    .tag-link {
      pointer-events: none;
    }
  }

  @media (max-width: 768px) {
    flex-wrap: wrap;
    gap: 8px;
    overflow-x: visible;
    cursor: default;
  }
}

.scroll-arrow {
  width: 28px;
  height: 28px;
  border: 0px solid rgba(24, 144, 255, 0.3);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;

  svg {
    width: 14px;
    height: 14px;
    stroke-width: 2;
  }

  &:hover {
    background: rgba(24, 144, 255, 0.08);
    border-color: rgba(24, 144, 255, 0.5);
    color: #0066cc;
  }

  &:active {
    transform: scale(0.96);
    background: rgba(24, 144, 255, 0.12);
  }

  @media (max-width: 768px) {
    display: none;
  }
}

.scroll-arrow-left {
  margin-right: 4px;
}

.scroll-arrow-right {
  margin-left: 4px;
}

.tag-link {
  color: #000a7b;
  font-size: 14px;
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(193, 230, 249, 0.5);
  font-weight: normal;
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
  white-space: nowrap;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(193, 230, 249, 0.8);
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    font-size: 12px;
    padding: 3px 6px;
  }
}

.header-right-container {
  display: flex;
  align-items: center;
  gap: 28px;
  padding-right: 20px;

  @media (max-width: 1000px) {
    justify-content: flex-start;
    padding-right: 0;
    gap: 20px;
    flex-wrap: wrap;
  }
}

.upgrade-btn {
  background: #16bc87;
  color: #ffffff;
  border: none;
  padding: 4px 10px;
  border-radius: 5px;
  font-size: 15px;
  cursor: pointer;
  height: 24px;
  display: flex;
  align-items: center;
  gap: 4px;

  @media (max-width: 1000px) {
    font-size: 14px;
    padding: 3px 8px;
    height: 22px;
  }
}

.unlock-icon {
  width: 15px;
  height: 15px;

  @media (max-width: 1000px) {
    width: 13px;
    height: 13px;
  }
}

.view-count {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  text-align: left;
  font-family:
    'PingFang SC',
    PingFangSC-regular,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
  gap: 8px;

  @media (max-width: 1000px) {
    font-size: 16px;
  }
}

.view-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;

  @media (max-width: 1000px) {
    width: 18px;
    height: 18px;
  }
}

.icon-btn {
  background: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &:hover {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  &:active {
    transform: scale(0.95);
    transition: all 0.1s ease;
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(24, 144, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
  }

  &:hover::before {
    width: 40px;
    height: 40px;
  }
}

.btn-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
  color: rgb(0, 0, 0);
  transition: all 0.3s ease;
  z-index: 1;
  position: relative;

  @media (max-width: 1000px) {
    width: 18px;
    height: 18px;
  }
}

.icon-btn:first-of-type {
  &:hover .btn-icon {
    animation: heartbeat 0.6s ease-in-out;
    color: #ff4757;
  }
}

.icon-btn:last-of-type {
  &:hover .btn-icon {
    transform: rotate(15deg);
    color: #1890ff;
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.15);
  }
  50% {
    transform: scale(1.3);
  }
  75% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1);
  }
}

.feedback-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.feedback-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  writing-mode: vertical-rl;
  text-orientation: upright;
}
.card-auth-tag,
.card-type-tag {
  backdrop-filter: blur(6px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  font-weight: 500;
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 6px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: auto;
  border: 1px solid rgba(255, 255, 255, 0.3);
}
</style>
