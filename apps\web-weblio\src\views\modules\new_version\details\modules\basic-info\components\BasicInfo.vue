<script setup lang="ts">
import {
  Loading,
  DialogPlugin
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import MoleculeViewer from '#/components/molecule-viewer/index.vue';
// import {
//   getBasicInfoData
// } from '../api';
import { useSearchStore } from '#/store/search';

const props = defineProps({
  basicData: {
    type: Object,
    default: {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

interface BasicInfoData {
  img: string;
  inchikey: string;
  nameEn: string;
  synonymEn: string;
  nameCn: string;
  synonymCn: string;
  chemicalFormula: string;
  description: string;
  cas: string;
  inchi: string;
  smiles: string;
  brn: string;
  merck: string;
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: false,

});

// 定义加载状态，用于控制页面加载动画显示
const loading = ref(true);

// 初始化危险识别数据对象，字段命名与接口定义保持一致，确保类型安全
const data = ref<BasicInfoData>({
  img: '',
  inchikey: '',
  nameEn: '',
  synonymEn: '',
  nameCn: '',
  synonymCn: '',
  chemicalFormula: '',
  description: '',
  cas: '',
  inchi: '',
  smiles: '',
  brn: '',
  merck: ''
});

// 监听 props 变化，更新数据
watch(() => props.basicData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    data.value = {
      img: newData.smiles || newData.img || '',
      inchikey: newData.inchikey || '',
      nameEn: newData.nameEn || newData.iupacName || '',
      synonymEn: newData.synonymEn || '',
      nameCn: newData.nameCn || '',
      synonymCn: newData.synonymCn || '',
      chemicalFormula: newData.chemicalFormula || newData.molecularFormula || '',
      description: newData.description || '',
      cas: newData.cas || newData.casNumber || '',
      inchi: newData.inchi || '',
      smiles: newData.smiles || '',
      brn: newData.brn || '',
      merck: newData.merck || ''
    };
  }
}, { immediate: true, deep: true });

// 监听 loading 状态变化
watch(() => props.loading, (newLoading) => {
  loading.value = newLoading;
  console.log("loading", loading.value, 2222);
});

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

onMounted(async () => {
  // try {
  //   // 调用基本信息查询API请求方法，传入 inchikey 参数
  //   const response = await getBasicInfoData({
  //     inchikey: state.detailItem.baseCode
  //   });
  //   data.value = {
  //     ...response,
  //     img: response.smiles
  //   };
  // } catch (error) {
  //   console.error('获取基础信息失败:', error);
  // } finally {
  //   // 关闭加载状态
  //   loading.value = false;
  // }
});

const fieldLabels = {
  img: '结构式：',
  inchikey: 'InChIKey：',
  nameEn: 'IUPAC名称：',
  synonymEn: '英文别名：',
  nameCn: '中文名称：',
  synonymCn: '中文别名：',
  chemicalFormula: '化学式：',
  description: '描述信息：',
  cas: 'CAS号：',
  inchi: 'InChI：',
  smiles: 'SMILES：',
  brn: 'BRN：',
  merck: 'Merck：'
};

// 查看编码弹窗
const showFieldValue = (value: string) => {
  DialogPlugin({
    header: 'SMILES',
    body: value,
    closeOnOverlayClick: true,
    confirmBtn: null,
    cancelBtn: null
  });
};

const handleError = (message: string) => {
  console.error(message);
};

const handleLoaded = (success: boolean) => {
  console.log('Molecule loaded:', success);
};
</script>

<template>
  <div class="basic-info">
    <!-- 加载状态提示 -->
    <div v-if="loading" class="loading-container">
      <Loading />
    </div>
    <!-- 主体内容展示 -->
    <div v-else class="content">
      <!-- 信息项展示 -->
      <div class="info-items">
        <div v-for="(label, key) in fieldLabels" :key="key">
          <!-- 如果 data[key] 存在，则显示对应信息 -->
          <div v-if="data[key]" class="info-item">
            <!-- 字段标签 -->
            <span class="label">{{ label }}</span>
            <!-- 字段值 -->
            <span class="value">
              <!-- 结构式 -->
              <template v-if="key === 'img'">
                <div class="structure">
                  <MoleculeViewer v-model="data.img" @error="handleError" @loaded="handleLoaded" />
                </div>
              </template>
              <!-- SMILES -->
              <template v-else-if="key === 'smiles'">
                <a href="#" class="smiles-link" @click.prevent="showFieldValue(data[key])">
                  查看编码
                </a>
              </template>
              <template v-else>
                {{ data[key] }}
              </template>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.basic-info {
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  font-size: 24px;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.structure {
  .info-content {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    text-align: center;

    .structure-diagram {
      width: 300px;
      height: 200px;
      background: #fff;
      margin: 0 auto 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-text {
        color: #999;
        font-size: 14px;
      }
    }

    .molecular-formula {
      margin: 0;
      text-align: center;
    }
  }
}

.structure-image img {
  width: 100%;
  height: auto;
}

.info-items {
  width: 80%;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 1px dashed #ddd;
}

.label {
  font-weight: bold;
  min-width: 150px;
  color: #555;
}

.value {
  flex-grow: 1;
  color: #333;
  word-break: break-all;
}

.smiles-link {
  color: #3498db;
  text-decoration: underline;
  cursor: pointer;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-data-container {
  text-align: center;
  padding: 40px;
  color: #999;
}
</style>
