<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>化工知识协同加工与管理平台</title>
    <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
    <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
    <link rel="stylesheet" href="../static/vue/theme/index.css">
    <script src="../static/vue/min/vue.min.js"></script>
    <script src="../static/vue/element-ui2.15.13_index.js"></script>
    <script src="../static/vue/axios0.26.0_axios.min.js"></script>
    <style>
        html,
        body {
            min-width: 100%;
        }

        .mon_warp {
            margin: 0px;
            width: 100%;
            background-size: cover;
        }

        .mon_body {
            display: none;
            width: 100%;
        }

        .el-menu-vertical-demo {
            height: 100%;
        }

        .el-card {
            margin-top: 20px;
        }

        .el-upload__tip {
            margin-top: 10px;
        }

        .clearfix:before,
        .clearfix:after {
            display: table;
            content: "";
        }

        .clearfix:after {
            clear: both;
        }

        .center {
            border: 1px solid #ccc;
            width: 60%;
            margin: 20px auto 20px;
            border-radius: 20px;
            padding: 30px;
            min-width: 1200px;
        }

        .upload-demo {
            width: 100%;
        }

        .el-upload-dragger {
            width: 660px;
            height: 250px;
            padding: 40px;
        }

        .el-upload__text {
            font-size: 16px;
            margin: 20px 0;
        }

        .el-icon-upload {
            font-size: 67px;
            margin: 20px 0;
        }

        .el-menu-item.is-active {
            background-color: #ecf5ff;
            color: #409EFF;
        }

        .el-menu-item {
            font-size: 14px;
            height: 56px;
            line-height: 56px;
        }

        .el-menu-item:hover {
            background-color: #ecf5ff;
        }

        .download-notice {
            font-size: 14px;
            color: #666;
            display: inline-block;
            margin-top: 10px;
        }

        .notice-icon {
            color: #ff9800;
            margin-right: 5px;
        }

        .download-all-container {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .left {
            width: 70%;
        }
    </style>
</head>

<body>
    <div class="header_app" id="header_app"></div>

    <div class="mon_warp clearfix" id="app">
        <div class="mon_body clearfix">
            <el-row :gutter="20">
                <!-- 左侧菜单 -->
                <el-col :span="3">
                    <div style="padding-top:20px">
                        <el-menu :default-active="menuActive"  class="el-menu-vertical-demo" 
             @select="handleMenuSelect"
            style="padding-top:20px;"
              active-text-color="#409EFF">
                <el-menu-item index="cleanTool">
                   <el-tooltip content="数据汇聚与清洗工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据汇聚与清洗工具
                  </span>
                </el-tooltip>
                  </el-menu-item>
              <el-menu-item index="classiFication">

                 <el-tooltip content="数据整编与分类工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据整编与分类工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="home">
                <el-tooltip content="全文多模态解析重组工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    全文多模态解析重组工具
                  </span>
                </el-tooltip>
              </el-menu-item><el-menu-item index="relationship">
                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    知识对象及关系挖掘工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>

            </el-menu>
                    </div>
                </el-col>

                <!-- 右侧内容 -->
                <el-col :span="21">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <h2>数据汇聚与清洗工具</h2>
                        </div>
                        <div>
                            <p style="font-size: 16px;">
                                多来源获取科技文献原始语料数据，能够对获取到的数据进行机器清洗和人工校正补全等操作，并邀请化工领域专家深入参与数据遴选流程，结合领域知识对原始数据进行筛选与评估，剔除无关或低质量数据，确保收集到的语料具有高质量和高相关性。

                            </p>
                            <div style="text-align: right;width: 100%;padding-top: 10px;">

                            </div>
                            <div class="center">
                                <div style="display: flex;width: 100%;justify-content: space-around;">
                                    <div class="left" style="padding-top: 34px;">
                                        <el-form label-width="100px" label-position="left">




                                            <el-form-item label="任务名称：" required>
                                                <div style="display: flex;">
                                                    <el-input v-model="taskName" placeholder="请输入任务名称"
                                                        style="max-width: 500px;"></el-input>
                                                    <el-button type="success" style="margin-left: 60px;"
                                                        @click="startopen">任务列表</el-button>
                                                </div>

                                            </el-form-item>

                                            <el-form-item label="DOI">
                                                <el-input v-model="doi" placeholder="请输入doi"
                                                    style="max-width: 500px;"></el-input>
                                            </el-form-item>

                                            <el-form-item label="DOI列表">
                                                <el-upload class="upload-demo" drag :show-file-list="false"
                                                    action="/query_file_json"
                                                    :before-upload="($event) =>{beforeAvatarUploadFile($event, 'need_publish_trailer')}"
                                                    multiple>
                                                    <i class="el-icon-upload"></i>
                                                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                                    <div class="el-upload__tip" slot="tip">仅支持Excel文件上传</div>
                                                </el-upload>
                                            </el-form-item>

                                            <el-form-item label="" required>
                                                <div style="text-align: right;">
                                                    <el-button type="primary" style="margin-right: 30px;"
                                                        @click="getInfoList" v-text="btnStart"></el-button>
                                                </div>
                                            </el-form-item>
                                        </el-form>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div style="text-align: right;width: 100%;">

                        </div>
                        <div class="download-all-container">
                            <!-- <button class="download-all">全部下载</button> -->
                            <div class="download-notice">
                                <span class="notice-icon">⚠</span>解构过程中请勿刷新页面
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 上传记录对话框 -->
            <el-dialog title="上传记录" :visible.sync="dialogVisible" width="60%">
                <el-table :data="uploadLists" style="width: 100%;" :border="true" stripe>
                    <el-table-column prop="timestamp" label="上传时间" width="180"></el-table-column>
                    <el-table-column prop="task_name" label="任务名称"></el-table-column>
                    <el-table-column label="操作" width="180">
                        <template slot-scope="scope">
                            <el-button size="mini" @click="handlePreview(scope.row)">查看</el-button>

                        </template>
                    </el-table-column>
                </el-table>
            </el-dialog>
        </div>
    </div>

    <div id="dataid" data="{{session.user}}" style="display:none"></div>
    <div class="mon_footer"></div>

    <script src="../static/js/jquery.min.js"></script>
    <script src="../static/js/monitor.js"></script>

    <script>
        let _this = this;

        const vm = new Vue({
            el: '#app',
            data: {
                activeIndex: '3',
                taskName: '',
                menuActive: 'cleanTool',
                doi: '',
                fileList: [],
                btnStart: "开始汇聚",
                dialogVisible: false,
                uploadLists: [],
                files: null,
            },
            mounted() {
                $('.mon_body').css({ 'display': 'revert' });


            },
            methods: {
    handleMenuSelect(index) {
    if (index === 'cleanTool') {
      window.location.href = '/cleanTool'; // 跳转到对应的页面
    } else if (index === 'classiFication') {
      window.location.href = '/classiFication';
    } else if (index === 'relationship') {
      window.location.href = '/relationship';
    } else if (index === 'home') {
      window.location.href = '/'; // 跳转到首页
    } else if (index === 'qualitycontrol') {
      window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
    } 
    this.menuActive = index; // 更新高亮状态
  },
                startopen() {
                    this.dialogVisible = true;
                    this.getTableData(); // 打开对话框时获取最新的上传记录
                },
                handleRemove(file, fileList) {
                    console.log('File removed:', file);
                },
                handlePreview(row) {
                    // 预览功能可以根据需要实现
                    location.href = `./cleanTask?table_name=${row.table_name}`;
                },
               beforeAvatarUploadFile(file, type = '') {
        let that = this;
        const isLt100M = file.size / 1024 / 1024 < 500;
        if (!isLt100M) {
            this.$message.error('上传文件大小不能超过 500MB!');
            return false;
        }
        if (!Array.isArray(that.files)) {
            that.files = [];
        }
        that.files.push(file);
        that.files.forEach((item)=>{
            item.size = item.size / (1024 * 1024)  + 'MB'
        })
        that.$message.success('上传成功！');
        return true;
    },
                // 新增：阻止自动上传
                customUpload(request) {
                    // 不做处理，这只是为了拦截默认上传
                },

                // 你的后端响应处理方法
                response_handle(data, success) {
                    console.log("上传结果：", data);
                    // TODO: 你可以在这里添加逻辑，比如跳转或展示解析结果
                },

                getTableData() {
                    let that = this
                    let param = {
                        "page": 1,
                        "page_size": 10
                    }
                    axios.post(server_url + '/gather_api/get_gather_tasks',
                        JSON.stringify(param), {
                        headers: { 'Content-Type': "application/json;charset=utf-8", },
                        timeout: 60000
                    }).then(function (res) {
                        const data = res.data
                        if (data.code == 0) {
                            that.uploadLists = data.data || [];
                            // that.$message.success('汇聚成功！');
                            // location.href = `./cleanTask?table_name=${data.data.table_name}&task_name=${data.data.task_name}`;
                        }
                    }).catch((error) => {

                    })
                },
              getInfoList() {
        let that = this;
        console.log(that.files);
        if (!that.doi && (!that.files || that.files.length === 0)) {
            that.$message.error('需要输入一个DOI或者上传至少一个含有DOI数据的EXCEL文件！');
            return;
        }
        let formData = new FormData();
        if (that.files && that.files.length > 0) {
            that.files.forEach(file => {
                formData.append('excel_file', file); // 支持多文件上传
            });
        }
        formData.append('doi', that.doi);
        formData.append('task_name', that.taskName);
        axios.post(`${server_url}/gather_api/create_task_all`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
        }).then(function (res) {
            const data = res.data;
            if (data.code == 0) {
                that.$message.success('汇聚成功！');
                location.href = `./cleanTask?table_name=${data.data.table_name}&task_name=${data.data.task_name}`;
            }
        });
    }
            }
        });
    </script>
</body>

</html>