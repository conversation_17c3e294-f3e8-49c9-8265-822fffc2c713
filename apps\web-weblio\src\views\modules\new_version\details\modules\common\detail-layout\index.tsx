import { defineComponent, nextTick, onMounted, ref } from 'vue';

import styles from './index.module.less';

interface MenuItem {
  title: string;
  content: () => any;
}

interface RightItem {
  title: string;
  content: () => any;
}

interface ThreeColumnData {
  topCard?: () => any;
  menu: MenuItem[];
  right: RightItem[];
}

export default defineComponent({
  name: 'Detailayout',
  props: {
    data: {
      type: Object as () => ThreeColumnData,
      required: true,
    },
  },
  setup(props) {
    const activeIndex = ref(0);
    const contentRefs = ref<(HTMLElement | null)[]>([]);

    const scrollToAnchor = (idx: number) => {
      activeIndex.value = idx;
      nextTick(() => {
        const el = contentRefs.value[idx];
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    };

    const onScroll = () => {
      const main = document.querySelector(
        '#three-col-main',
      ) as HTMLElement | null;
      if (!main) return;
      const scrollTop = main.scrollTop;
      let current = 0;
      for (let i = 0; i < contentRefs.value.length; i++) {
        const el = contentRefs.value[i];
        if (el) {
          // 计算内容块相对于容器顶部的距离
          const offset = (el as HTMLElement).offsetTop - main.offsetTop;
          if (scrollTop >= offset) {
            current = i;
          }
        }
      }

      activeIndex.value = current;
    };

    onMounted(() => {
      const main = document.querySelector('#three-col-main');
      if (main) {
        main.addEventListener('scroll', onScroll);
      }
    });

    return () => (
      <div class={styles['three-col-layout']}>
        {/* 顶部卡片区 */}
        {props.data.topCard && (
          <div class={styles['three-col-top-card']}>
            {props?.data?.topCard?.() || ''}
          </div>
        )}
        {/* 主体三栏区 */}
        <div class={styles['three-col-main-area']}>
          {/* 左侧菜单 */}
          <div class={styles['three-col-menu']}>
            {props.data.menu.map((item, idx) => (
              <div
                class={[
                  styles['three-col-menu-item'],
                  activeIndex.value === idx ? styles.active : '',
                ].join(' ')}
                key={idx}
                onClick={() => scrollToAnchor(idx)}
              >
                {item.title}
              </div>
            ))}
          </div>
          {/* 中间内容区 */}
          <div
            class={styles['three-col-content']}
            id="three-col-main"
            onScroll={onScroll}
          >
            {props.data.menu.map((item, idx) => (
              <div
                class={styles['three-col-content-block']}
                key={idx}
                ref={(el) => (contentRefs.value[idx] = el as HTMLElement)}
              >
                <div class={styles['three-col-content-title']}>
                  {item.title}
                </div>
                <div>{item.content()}</div>
              </div>
            ))}
          </div>
          {/* 右侧内容区 */}
          {props.data.right.length > 0 ? (
            <div class={styles['three-col-right']}>
              {props.data.right.map((item, idx) => (
                <div class={styles['three-col-right-block']} key={idx}>
                  <div class={styles['three-col-right-title']}>
                    {item.title}
                  </div>
                  <div>{item.content()}</div>
                </div>
              ))}
            </div>
          ) : null}
        </div>
      </div>
    );
  },
});
