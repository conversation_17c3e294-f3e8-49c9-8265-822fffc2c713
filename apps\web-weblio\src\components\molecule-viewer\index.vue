<script setup lang="ts">
import { ref } from 'vue';

import MoleculeModal from './modal.vue';
import MoleculeThumbnail from './thumbnail.vue';

interface Props {
  modelValue?: string;
  mode?: 'inline' | 'modal' | 'thumbnail';
  width?: number | string;
  height?: number | string;
  defaultViewMode?: '2d' | '3d';
  clickable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  mode: 'thumbnail',
  width: '200px',
  height: '200px',
  defaultViewMode: '2d',
  clickable: true,
});

const emit = defineEmits<{
  error: [error: string];
  structureLoaded: [data: { smiles: string; viewMode: '2d' | '3d' }];
  thumbnailClick: [smiles: string];
  'update:modelValue': [value: string];
}>();

// 弹窗状态
const showModal = ref(false);

// 处理缩略图点击
const handleThumbnailClick = (smiles: string) => {
  emit('thumbnailClick', smiles);
  if (props.mode === 'thumbnail') {
    showModal.value = true;
  }
};

// 处理错误
const handleError = (error: string) => {
  emit('error', error);
};

// 处理结构加载
const handleStructureLoaded = (data: {
  smiles: string;
  viewMode: '2d' | '3d';
}) => {
  emit('structureLoaded', data);
};
</script>

<template>
  <div class="molecule-viewer-container">
    <!-- 缩略图模式 -->
    <template v-if="mode === 'thumbnail'">
      <MoleculeThumbnail
        :model-value="modelValue"
        :width="width"
        :height="height"
        :clickable="clickable"
        @click="handleThumbnailClick"
        @error="handleError"
      />

      <!-- 弹窗 -->
      <MoleculeModal
        v-model="showModal"
        :smiles="modelValue"
        :default-view-mode="defaultViewMode"
        width="700px"
        height="500px"
        @error="handleError"
        @structure-loaded="handleStructureLoaded"
      />
    </template>

    <!-- 内联模式 - 直接显示弹窗内容但不作为弹窗 -->
    <template v-else-if="mode === 'inline'">
      <div class="inline-viewer">
        <!-- 这里可以放置内联版本的查看器 -->
        <div class="coming-soon">内联模式开发中...</div>
      </div>
    </template>

    <!-- 弹窗模式 - 只显示弹窗，由外部控制 -->
    <template v-else-if="mode === 'modal'">
      <MoleculeModal
        :model-value="true"
        :smiles="modelValue"
        :default-view-mode="defaultViewMode"
        :width="width"
        :height="height"
        @error="handleError"
        @structure-loaded="handleStructureLoaded"
        @update:model-value="$emit('update:modelValue', $event)"
      />
    </template>
  </div>
</template>

<style scoped lang="scss">
.molecule-viewer-container {
  display: inline-block;
}

.inline-viewer {
  width: v-bind(width);
  height: v-bind(height);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.coming-soon {
  color: #666;
  font-size: 14px;
}
</style>
