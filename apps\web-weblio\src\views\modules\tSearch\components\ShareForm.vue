<script setup lang="tsx">
import {Form, FormItem, MessagePlugin, Textarea} from 'tdesign-vue-next';
import {useVbenModal} from '@vben/common-ui';
import {defineProps, onMounted, ref} from 'vue';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
const shareUrl = ref();
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: () => {
    navigator.clipboard.writeText(shareUrl.value)
    MessagePlugin.success('链接复制成功');
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = async (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  console.log("data", data)
  if (data) {
    shareUrl.value = data;
    console.log("shareUrl", shareUrl)
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});

onMounted(async () => {
});

const copyableClick = () => {
  navigator.clipboard.writeText(shareUrl.value)
  MessagePlugin.success('链接复制成功');
};
</script>

<template>
  <Modal title="数据分享链接" class="w-[40%]">
    <Form
      ref="form"
      class="w-full"
      label-align="right"
    >
      <div class="grid w-full grid-cols-1 gap-3">
        <FormItem class="w-full" label="分享链接" name="shareUrl">
          <Textarea @click="copyableClick" :readonly="true" :autosize="true" :default-value="shareUrl" />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
