<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" src="../internal.js?04dbe7f0"></script>
    <link rel="stylesheet" type="text/css" href="background.css?982ee6c2">
</head>
<body>
<div id="bg_container" class="wrapper">
    <div id="tabHeads" class="tabhead">
        <span class="focus" data-content-id="normal"><var id="lang_background_normal"></var></span>
    </div>
    <div id="tabBodys" class="tabbody">
        <div id="normal" class="panel focus">
            <fieldset class="bgarea">
                <legend><var id="lang_background_set"></var></legend>
                <div class="content">
                    <div>
                        <label><input id="nocolorRadio" class="iptradio" type="radio" name="t" value="none"
                                      checked="checked"><var id="lang_background_none"></var></label>
                        <label><input id="coloredRadio" class="iptradio" type="radio" name="t" value="color"><var
                            id="lang_background_colored"></var></label>
                    </div>
                    <div class="wrapcolor pl">
                        <div class="color">
                            <var id="lang_background_color"></var>:
                        </div>
                        <div id="colorPicker"></div>
                        <div class="clear"></div>
                    </div>
                    <div class="wrapcolor pl">
                        <label><var id="lang_background_netimg"></var>:</label><input class="txt" type="text" id="url">
                    </div>
                    <div id="alignment" class="alignment">
                        <var id="lang_background_align"></var>:<select id="repeatType">
                        <option value="center"></option>
                        <option value="repeat-x"></option>
                        <option value="repeat-y"></option>
                        <option value="repeat"></option>
                        <option value="self"></option>
                    </select>
                    </div>
                    <div id="custom">
                        <var id="lang_background_position"></var>:x:<input type="text" size="1" id="x" maxlength="4"
                                                                           value="0">px&nbsp;&nbsp;y:<input type="text"
                                                                                                            size="1"
                                                                                                            id="y"
                                                                                                            maxlength="4"
                                                                                                            value="0">px
                    </div>
                </div>
            </fieldset>

        </div>
    </div>
</div>
<script type="text/javascript" src="background.js?e67eb657"></script>
</body>
</html>
