import { requestClient } from '#/api/request';

export async function roleListByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-user/role/listByPage', data);
}

export async function allRoleList(data: any) {
  return requestClient.get<any>('/rgdc-user/role/allList', data);
}

export async function roleSave(data: any) {
  return requestClient.post<any>('/rgdc-user/role/save', data);
}

// 批量删除
export async function roleDeleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-user/role/deleteBatch/${data}`);
}

export async function roleBindAccountListByPageApi(data: any, roleCode: any) {
  return requestClient.post<any>(`/rgdc-user/account/bind/role/${roleCode}`, data);
}

export async function bindRoleMenuListApi(roleCode: any) {
  return requestClient.get<any>(`/rgdc-user/role/bind/menu/${roleCode}`);
}

export async function roleBindAccountSave(data: any, roleCode: any) {
  return requestClient.put<any>(`/rgdc-user/account/bind/role/${roleCode}`, data);
}

export async function roleBindAccountDeleteBatch(roleCode: any, data: any) {
  return requestClient.delete<any>(
    `/rgdc-user/account/bind/role/deleteBatch/${roleCode}/${data}`,
  );
}

export async function roleBindAccountDelete(data: any) {
  return requestClient.delete<any>(`/rgdc-user/account/bind/role/deleteRole/${data}`);
}

export async function roleBindPermissionListByPageApi(
  data: any,
  roleCode: any,
) {
  return requestClient.post<any>(`/rgdc-user/account/bind/permission/${roleCode}`, data);
}

export async function roleBindPermissionSave(data: any, roleCode: any) {
  return requestClient.put<any>(`/rgdc-user/account/bind/permission/${roleCode}`, data);
}

export async function saveBindRoleMenuList(data: any, roleCode: any) {
  return requestClient.put<any>(`/rgdc-user/role/bind/menu/${roleCode}`, data);
}

export async function roleBindPermissionDeleteBatch(roleCode: any, data: any) {
  return requestClient.delete<any>(
    `/rgdc-user/account/bind/permission/deleteBatch/${roleCode}/${data}`,
  );
}

export async function roleBindMenuDeleteBatch(roleCode: any, data: any) {
  return requestClient.post<any>(
    `/rgdc-user/role/bind/menu/deleteBatch/${roleCode}`,
    data,
  );
}

export async function deleteRoleMenu(roleCode: any) {
  return requestClient.delete<any>(`/rgdc-user/role/bind/menu/deleteRole/${roleCode}`);
}

export async function roleBindPermissionDelete(data: any) {
  return requestClient.delete<any>(
    `/rgdc-user/account/bind/permission/deleteRole/${data}`,
  );
}
