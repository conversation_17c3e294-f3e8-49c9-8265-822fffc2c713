import { requestClient } from '#/api/request';

export async function allMenu(data: any) {
  return requestClient.get<any>('/rgdc-sys/menu/allMenu', { params: data });
}

export async function saveMenu(data: any) {
  return requestClient.post<any>('/rgdc-sys/menu/saveMenu', data);
}

export async function deleteMenu(data: any) {
  return requestClient.delete<any>(`/rgdc-sys/menu/deleteMenu/${data}`);
}
