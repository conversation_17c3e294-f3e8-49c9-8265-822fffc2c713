<script setup lang="ts">
import { Card } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 响应式数据
const jsonData = ref<any[]>([]);

// 组件挂载时获取数据
onMounted(() => {
  const storedData = localStorage.getItem('jsonData');
  if (storedData) {
    jsonData.value = JSON.parse(storedData);
  } else {
    // 如果没有数据，返回上一页
    router.back();
  }
});
</script>

<template>
  <div class="relationship-result-container">
    <Card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">知识对象及关系挖掘工具 - 对齐结果</h2>
        </div>
      </template>

      <div class="description">
        <p class="description-text">
          化工领域知识对象抽取是针对化工科技文献中的文本、图像、表格等多源信息，精准抽取实体及实体之间关系的关键技术。
          其功能设计需深度适配化工领域的专业性、复杂性与多模态特性，知识对象抽取功能可实现从非结构化数据到可复用知识资产的转化，
          为研发、生产、安全等环节提供智能化支持，推动化工行业向数据驱动新型研发范式转型。
        </p>
      </div>

      <div class="content-area">
        <div v-if="jsonData.length === 0" class="empty-state">
          <p>暂无对齐结果数据</p>
        </div>

        <div v-else class="result-list">
          <div
            v-for="(item, index) in jsonData"
            :key="index"
            class="journal-entry"
          >
            <div class="image-section">
              <img
                :src="`data:image/png;base64,${item.image_url}`"
                :alt="`图像 ${index + 1}`"
                class="journal-image"
              />
            </div>

            <div class="content-section">
              <div class="journal-content">
                <div
                  v-for="(textItem, textIndex) in item.text"
                  :key="textIndex"
                  class="text-item"
                  v-html="textItem.value"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<style scoped lang="less">
.relationship-result-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 48px);
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.description {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #0052d9;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.content-area {
  min-height: 400px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 16px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.journal-entry {
  display: flex;
  align-items: flex-start;
  padding: 24px;
  border-radius: 12px;
  background: #f0f7ff;
  border: 1px solid #e6f4ff;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 82, 217, 0.1);
    transform: translateY(-2px);
  }
}

.image-section {
  flex-shrink: 0;
  margin-right: 24px;
}

.journal-image {
  width: 300px;
  height: 300px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  background: #fff;
}

.content-section {
  flex: 1;
  min-width: 0;
}

.journal-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-item {
  padding: 12px 16px;
  background: #fff;
  border-radius: 6px;
  border-left: 3px solid #0052d9;
  font-size: 16px;
  line-height: 1.6;
  color: #02678d;
  font-weight: 500;

  :deep(strong) {
    color: #000;
    font-weight: 600;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .journal-entry {
    flex-direction: column;
    gap: 20px;
  }

  .image-section {
    margin-right: 0;
    align-self: center;
  }

  .journal-image {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .relationship-result-container {
    padding: 16px;
  }

  .journal-entry {
    padding: 16px;
  }

  .journal-image {
    width: 200px;
    height: 200px;
  }

  .text-item {
    font-size: 14px;
  }
}
</style>
