// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataTemplate/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataTemplate/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tDataTemplate/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataTemplate/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataTemplate/getByIds/${data}`);
}

export async function saveMetaData(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataTemplate/saveMetaData', data);
}

export async function checkMetaData(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataTemplate/checkMetaData', data);
}
