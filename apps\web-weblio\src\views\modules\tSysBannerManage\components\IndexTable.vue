<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';

import { baseDownloadFile, baseUploadFile } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import PutAway from '#/components/put-away/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { useAppConfig } from '@vben/hooks';
import {
  AddCircleIcon,
  DeleteIcon,
  RefreshIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Space,
  Table,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { deleteBatch, listByPage } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
const initStatus = () => {
  /**
   * 默认选中全部
   */
  state.selectedRowKeys = [];
  /**
   * 默认不显示删除按钮
   */
  state.delDailogShow = false;
  /**
   * 默认不显示查询
   */
  state.hideQuery = false;
  /**
   * 默认不显示加载
   */
  state.loading = false;
};
/**
 * table 排序字段
 */
const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',

    width: 64,
  },

  { colKey: 'imgCode', ellipsis: true, sorter: true, title: '图片编码' },
  { colKey: 'imgDescribe', ellipsis: true, sorter: true, title: '图片描述' },
  { colKey: 'imgPath', ellipsis: true, sorter: true, title: '图片预览' },
  { colKey: 'imgSeq', ellipsis: true, sorter: true, title: '循环顺序' },
  { colKey: 'createdBy', ellipsis: true, sorter: true, title: '创建人' },
  { colKey: 'createTime', ellipsis: true, sorter: true, title: '创建时间' },
  { colKey: 'op', width: 100, title: '操作', align: 'center' },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      state.selectedRowKeys = [];
      reload();
      initStatus();
    },
  }),
  excelExport: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  excelImport: useRequest(baseUploadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      // 导入完成后刷新列表
      reload();
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run({
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};
/**
 * 新建按钮相应
 */
const edit = (record?: any) => {
  /**
   * 通知Form组件打开编辑窗口
   */
  props.editFormRef?.open(record ? { ...record } : {});
};
/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};
/**
 * 单条删除
 */
const remove = (record: any) => {
  reqRunner.deleteBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  searchForm.value.reset();
  setTimeout(() => {
    reload();
  }, 0);
}; /**
 * table 行点击响应
 * @param record
 */
const handleRowClick = (record: any) => {};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowKeys = value;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};

const excelExport = async () => {
  // 下载Excel
  reqRunner.excelExport.run(
    `/tSysBannerManage/excelExport/${state.selectedRowKeys}`,
  );
};

const excelImport = async (...args: any) => {
  reqRunner.excelImport.run('/tSysBannerManage/excelImport', {
    file: args[0].raw,
  });
};

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

onMounted(() => {
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});

const remake_url = (...args: any) => {
  const url = `${apiURL}/file/download?path=${encodeURI(args[0] || '')}`;
  return url;
};
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Space :size="8" class="tiny-tdesign-style-patch" direction="vertical">
    <!--    查询表单定义区域-->
    <Card v-if="isSearchForm">
      <Form
        ref="searchForm"
        :data="formData"
        class="w-full"
        @reset="resetSearch"
        @submit="searchFormSubmit"
      >
        <!--一列表单布局-->
        <div class="grid w-full grid-cols-3 gap-1">
          <FormItem label="图片编码" name="imgCode">
            <Input
              v-model="formData.imgCode"
              clearable
              placeholder="请输入内容"
            />
          </FormItem>
          <FormItem v-if="state.hideQuery" label="图片描述" name="imgDescribe">
            <Input
              v-model="formData.imgDescribe"
              clearable
              placeholder="请输入内容"
            />
          </FormItem>
          <FormItem label="创建人" name="createdBy">
            <Input
              v-model="formData.createdBy"
              clearable
              placeholder="请输入内容"
            />
          </FormItem>
          <!--            <FormItem  label="创建时间" name="createTime">-->
          <!--              <Input v-model="formData.createTime" clearable placeholder="请输入内容" />-->
          <!--            </FormItem>-->
        </div>

        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置
          </Button>

          <PutAway v-model="state.hideQuery" variant="text" />
        </div>
      </Form>
    </Card>
    <Card>
      <!-- 表格定义区域 -->
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :bordered="true"
        :columns="columns"
        :data="state.dataSource"
        :hover="true"
        :loading="state.loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="state.selectedRowKeys"
        :sort="state.sort"
        :stripe="true"
        cell-empty-content="-"
        lazy-load
        resizable
        row-key="id"
        table-layout="fixed"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
        v-bind="tableConfig"
      >
        <!--        表格顶部按钮区域-->
        <template #topContent>
          <div class="mb-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">Banner管理</div>
              <div
                v-if="state.selectedRowKeys?.length > 0"
                class="text-blue-600/80"
              >
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <!--              <Upload-->
              <!--                  :trigger-button-props="{ theme: 'primary', variant: 'base' }"-->
              <!--                  :request-method="excelImport"-->
              <!--                >-->
              <!--                  <Button theme="primary">-->
              <!--                    <template #icon>-->
              <!--                      <UploadIcon />-->
              <!--                    </template>-->
              <!--                    导入Excel-->
              <!--                  </Button>-->
              <!--              </Upload>-->
              <!--              <Button theme="primary" @click="excelExport">-->
              <!--                <template #icon>-->
              <!--                  <DownloadIcon />-->
              <!--                </template>-->
              <!--                导出Excel-->
              <!--              </Button>-->
              <Button
                v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                theme="danger"
                @click="del"
              >
                <template #icon>
                  <DeleteIcon />
                </template>
                删除
              </Button>
              <Button theme="primary" @click="edit">
                <template #icon>
                  <AddCircleIcon />
                </template>
                新增
              </Button>
              <Button variant="text" @click="reload">
                <RefreshIcon />
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
            </div>
          </div>
        </template>
        <!--        空数据显示定义-->
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
            <Button
              class="w-[100%]"
              theme="primary"
              variant="text"
              @click="edit"
            >
              <template #icon>
                <AddCircleIcon />
              </template>
              点击创建新数据
            </Button>
          </div>
        </template>
        <template #imgPath="slotProps">
          <Space size="large">
            <img
              :src="remake_url(slotProps.row.imgPath)"
              alt="Uploaded Image"
            />
          </Space>
        </template>
        <!--        编辑按钮-->
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)">编辑</Link>
            <Popconfirm
              content="确定删除？"
              theme="warning"
              @confirm="remove(slotProps.row)"
            >
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
