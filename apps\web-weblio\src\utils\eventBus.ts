import mitt from 'mitt';

const EventBus = mitt();
export { EventBus };

export function onEvent(eventHandle: any) {
  Object.keys(eventHandle).forEach((item: string) => {
    // @data-ignore 关注event
    EventBus.on(item, eventHandle[item]);
  });
}

export function offEvent(eventHandle: any) {
  Object.keys(eventHandle).forEach((item: string) => {
    // @data-ignore 关注event
    EventBus.off(item, eventHandle[item]);
  });
}
