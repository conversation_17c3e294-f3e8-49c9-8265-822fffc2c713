import JSEncrypt from 'jsencrypt'
import {getPublicKey} from "#/views/_sys/rsa/api.ts";

// 类型定义
type RSAEncryptor = {
  setPublicKey: (publicKey: string) => void;
  encrypt: (data: string) => string | false;
}

// 创建加密实例
const encryptor: RSAEncryptor = new JSEncrypt() as unknown as RSAEncryptor

// 设置公钥方法
export function setPublicKey(publicKey: string): void {
  encryptor.setPublicKey(publicKey)
}

// RSA加密方法
export function rsaEncrypt(data: string): string {
  const encrypted = encryptor.encrypt(data)
  if (!encrypted) {
    throw new Error('RSA加密失败')
  }
  return encrypted
}

// 获取公钥接口
export async function fetchPublicKey(): Promise<string> {
  // try {
  //   const response = await fetch('/rgdc-boot/rsa/publicKey')
  //   if (!response.ok) {
  //     throw new Error('获取公钥失败')
  //   }
  //   return await response.text()
  // } catch (error) {
  //   throw new Error(`公钥请求错误: ${(error as Error).message}`)
  // }
  try {
    const response = await getPublicKey();
    if (!response) {
      throw new Error('获取公钥失败')
    }
    return response
  } catch (error) {
    throw new Error(`公钥请求错误: ${(error as Error).message}`)
  }
}
