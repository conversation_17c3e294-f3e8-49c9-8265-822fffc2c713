# 端口号
VITE_PORT=5888

# 是否开启 Nitro Mock服务，true 为开启，false 为关闭
VITE_NITRO_MOCK=true

# 是否打开 devtools，true 为打开，false 为关闭
VITE_DEVTOOLS=true

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# 是否为认证主服务
VITE_AUTH_SERVER=true

# 钉钉相关配置 Start
# 服务域名，需要与钉钉应用配置的域名一致
VITE_APP_API_BASE_URL = 'http://localhost:5888/'

# 钉钉开发者后台的应用ID（CorpId或SuiteId）,Client ID (原 AppKey 和 SuiteKey)
VITE_APP_ID = 'ding7joesot9ogtehn1n'

# 钉钉corpId
VITE_APP_CORP_ID = 'ding5ccf8c7171ff38e835c2f4657eb6378f'
# 钉钉相关配置 End
