import { requestClient } from '#/api/request';

// 数据整编与分类相关API接口

/**
 * 创建分类任务
 * @param data 任务数据
 */
export async function createClassificationTask(data: FormData) {
  return requestClient.post(
    '/rgdc-sys/dataTools/classification/create_task',
    data,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
      timeout: 60_000,
    },
  );
}

/**
 * 获取任务列表
 * @param params 查询参数
 */
export async function getTaskList(params: { page: number; page_size: number }) {
  return requestClient.post(
    '/rgdc-sys/dataTools/classification/get_task_list',
    params,
  );
}

/**
 * 下载模板文件
 */
export async function downloadTemplate() {
  return requestClient.get(
    '/rgdc-sys/dataTools/classification/download_template',
    {
      responseType: 'blob',
    },
  );
}

/**
 * 获取任务详情
 * @param taskId 任务ID
 */
export async function getTaskDetail(taskId: string) {
  return requestClient.get(
    `/rgdc-sys/dataTools/classification/task_detail/${taskId}`,
  );
}

/**
 * 删除任务
 * @param taskId 任务ID
 */
export async function deleteTask(taskId: string) {
  return requestClient.delete(
    `/rgdc-sys/dataTools/classification/delete_task/${taskId}`,
  );
}

/**
 * 获取分类结果
 * @param taskId 任务ID
 */
export async function getClassificationResult(taskId: string) {
  return requestClient.get(
    `/rgdc-sys/dataTools/classification/get_result/${taskId}`,
  );
}

/**
 * 导出分类结果
 * @param taskId 任务ID
 * @param format 导出格式 (excel/txt)
 */
export async function exportClassificationResult(
  taskId: string,
  format: 'excel' | 'txt',
) {
  return requestClient.get(
    `/rgdc-sys/dataTools/classification/export_result/${taskId}`,
    {
      params: { format },
      responseType: 'blob',
    },
  );
}

/**
 * 批量导出
 * @param taskIds 任务ID列表
 * @param format 导出格式
 */
export async function batchExport(taskIds: string[], format: 'excel' | 'txt') {
  return requestClient.post(
    '/rgdc-sys/dataTools/classification/batch_export',
    {
      task_ids: taskIds,
      format,
    },
    {
      responseType: 'blob',
    },
  );
}

/**
 * 获取元数据字段配置
 */
export async function getMetadataFields() {
  return requestClient.get(
    '/rgdc-sys/dataTools/classification/get_metadata_fields',
  );
}

/**
 * 更新任务状态
 * @param taskId 任务ID
 * @param status 状态
 */
export async function updateTaskStatus(taskId: string, status: string) {
  return requestClient.put(
    `/rgdc-sys/dataTools/classification/update_task_status/${taskId}`,
    {
      status,
    },
  );
}

/**
 * 重新运行分类任务
 * @param taskId 任务ID
 */
export async function rerunClassificationTask(taskId: string) {
  return requestClient.post(
    `/rgdc-sys/dataTools/classification/rerun_task/${taskId}`,
  );
}

/**
 * 获取分类统计信息
 * @param taskId 任务ID
 */
export async function getClassificationStats(taskId: string) {
  return requestClient.get(
    `/rgdc-sys/dataTools/classification/get_stats/${taskId}`,
  );
}
