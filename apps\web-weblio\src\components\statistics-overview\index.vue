<script setup lang="ts">
import { computed, h } from 'vue';

// 定义统计数据类型
interface TrendData {
  type: 'down' | 'stable' | 'up';
  text: string;
}

interface StatisticItem {
  key: string;
  title: string;
  value: number;
  displayValue?: number;
  unit?: string;
  icon: string;
  trend?: TrendData;
  color?: string;
}

interface StatisticsOverviewProps {
  data?: StatisticItem[];
  animationEnabled?: boolean;
}

interface StatisticsOverviewEmits {
  cardClick: [item: StatisticItem];
}

// Props
const props = withDefaults(defineProps<StatisticsOverviewProps>(), {
  data: () => [],
  animationEnabled: true,
});

// Emits
const emit = defineEmits<StatisticsOverviewEmits>();

// 图标组件
const DatabaseIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('ellipse', { cx: '12', cy: '5', rx: '9', ry: '3' }),
      h('path', { d: 'M3 5v14c0 3 4 6 9 6s9-3 9-6V5' }),
      h('path', { d: 'M3 12c0 3 4 6 9 6s9-3 9-6' }),
    ],
  );

const FolderIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('path', {
        d: 'M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-6l-2-2H5a2 2 0 0 0-2 2z',
      }),
    ],
  );

const HardDriveIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('line', { x1: '22', y1: '12', x2: '2', y2: '12' }),
      h('path', {
        d: 'M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z',
      }),
      h('circle', { cx: '6', cy: '17', r: '1' }),
      h('circle', { cx: '10', cy: '17', r: '1' }),
    ],
  );

const FileTextIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('path', {
        d: 'M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z',
      }),
      h('polyline', { points: '14,2 14,8 20,8' }),
      h('line', { x1: '16', y1: '13', x2: '8', y2: '13' }),
      h('line', { x1: '16', y1: '17', x2: '8', y2: '17' }),
      h('polyline', { points: '10,9 9,9 8,9' }),
    ],
  );

const ToolIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('path', {
        d: 'M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z',
      }),
    ],
  );

const UsersIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('path', { d: 'M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2' }),
      h('circle', { cx: '9', cy: '7', r: '4' }),
      h('path', { d: 'M23 21v-2a4 4 0 0 0-3-3.87' }),
      h('path', { d: 'M16 3.13a4 4 0 0 1 0 7.75' }),
    ],
  );

const TrendUpIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('polyline', { points: '23 6 13.5 15.5 8.5 10.5 1 18' }),
      h('polyline', { points: '17 6 23 6 23 12' }),
    ],
  );

const TrendDownIcon = () =>
  h(
    'svg',
    {
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      'stroke-width': '2',
    },
    [
      h('polyline', { points: '23 18 13.5 8.5 8.5 13.5 1 6' }),
      h('polyline', { points: '17 18 23 18 23 12' }),
    ],
  );

// 默认统计数据
const defaultStatistics: StatisticItem[] = [];

// 计算属性
const statisticsData = computed(() => props.data || defaultStatistics);

// 图标映射
const iconMap: Record<string, any> = {
  DatabaseIcon,
  FolderIcon,
  HardDriveIcon,
  FileTextIcon,
  ToolIcon,
  UsersIcon,
};

// 获取图标
const getIcon = (iconName: string) => {
  return iconMap[iconName] || FolderIcon;
};

// 格式化数字
const formatNumber = (num: number): string => {
  if (num >= 10_000) {
    return num.toLocaleString();
  }
  return num.toString();
};

// 获取趋势图标
const getTrendIcon = (type: string) => {
  switch (type) {
    case 'down': {
      return TrendDownIcon;
    }
    case 'up': {
      return TrendUpIcon;
    }
    default: {
      return null;
    }
  }
};

// 处理卡片点击
const handleCardClick = (item: StatisticItem) => {
  emit('cardClick', item);
};

// 导出类型
export type { StatisticItem, TrendData };
</script>

<template>
  <div class="statistics-overview">
    <div class="statistics-grid">
      <div
        v-for="item in statisticsData"
        :key="item.key"
        class="statistic-card"
        :class="{ animate: animationEnabled }"
        @click="handleCardClick(item)"
      >
        <div class="card-icon" :style="{ background: item.color }">
          <component :is="getIcon(item.icon)" />
        </div>
        <div class="card-content">
          <div class="card-title">{{ item.title }}</div>
          <div class="card-value">
            <span class="number">
              {{ formatNumber(item.displayValue || item.value) }}
            </span>
            <span v-if="item.unit" class="unit">{{ item.unit }}</span>
          </div>
          <div v-if="item.trend" class="card-trend" :class="item.trend.type">
            <component :is="getTrendIcon(item.trend.type)" />
            <span>{{ item.trend.text }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.statistics-overview {
  width: 100%;
  padding-left: clamp(16px, 4vw, 80px);
  padding-right: clamp(16px, 4vw, 80px);
  padding-top: 24px;
  padding-bottom: 24px;
  background: transparent;
  box-sizing: border-box;
}

.statistics-grid {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: clamp(16px, 2vw, 32px);
  max-width: 1680px;
  margin: 0 auto;
  padding: 0;
  overflow-x: auto;
}

.statistic-card {
  flex: 1;
  min-width: 180px;
  max-width: 280px;
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statistic-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #e2e8f0;
}

.statistic-card.animate {
  animation: slideInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.statistic-card:nth-child(1).animate {
  animation-delay: 0.1s;
}
.statistic-card:nth-child(2).animate {
  animation-delay: 0.15s;
}
.statistic-card:nth-child(3).animate {
  animation-delay: 0.2s;
}
.statistic-card:nth-child(4).animate {
  animation-delay: 0.25s;
}
.statistic-card:nth-child(5).animate {
  animation-delay: 0.3s;
}
.statistic-card:nth-child(6).animate {
  animation-delay: 0.35s;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  color: white;
}

.card-icon svg {
  width: 20px;
  height: 20px;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 6px;
  font-weight: 500;
  line-height: 1.2;
}

.card-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.number {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1;
}

.unit {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(82, 196, 26, 0.1);
  width: fit-content;
}

.card-trend.up {
  color: #52c41a;
}

.card-trend.down {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.card-trend.stable {
  color: #8c8c8c;
  background: rgba(140, 140, 140, 0.1);
}

.card-trend svg {
  width: 10px;
  height: 10px;
}

/* 深色模式支持 */
.dark .statistic-card {
  background: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--card-foreground));
}

.dark .card-title {
  color: hsl(var(--muted-foreground));
}

.dark .number {
  color: #60a5fa;
}

.dark .unit {
  color: hsl(var(--muted-foreground));
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-grid {
    gap: clamp(12px, 2vw, 24px);
  }

  .statistic-card {
    min-width: 160px;
    max-width: 240px;
  }
}

@media (max-width: 768px) {
  .statistics-grid {
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
    padding: 0 12px;
  }

  .statistic-card {
    flex: 1 1 calc(50% - 6px);
    min-width: 140px;
    max-width: 200px;
    padding: 14px;
  }

  .number {
    font-size: 20px;
  }

  .card-icon {
    width: 36px;
    height: 36px;
  }

  .card-icon svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .statistics-grid {
    flex-direction: column;
    gap: 12px;
  }

  .statistic-card {
    flex: none;
    min-width: auto;
    max-width: none;
  }

  .statistics-overview {
    padding: 16px 12px;
  }
}

/* 动画关键帧 */
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
