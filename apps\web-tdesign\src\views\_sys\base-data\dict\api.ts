import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any>('/rgdc-sys/dict/dictListByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-sys/dict/save', data);
}

// 批量删除
export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-sys/dict/deleteBatch/${data}`);
}

export async function tagList() {
  return requestClient.get<any>('/rgdc-sys/dict/tagList');
}
export async function dictItemListByPage(data: any) {
  return requestClient.post<any>('/rgdc-sys/dict/dictItemListByPage', data);
}

export async function removeDictItemById(data: any) {
  return requestClient.delete<any>(`/rgdc-sys/dict/removeDictItemById/${data}`);
}
export async function saveDictItem(data: any) {
  return requestClient.post<any>('/rgdc-sys/dict/saveDictItem', data);
}
