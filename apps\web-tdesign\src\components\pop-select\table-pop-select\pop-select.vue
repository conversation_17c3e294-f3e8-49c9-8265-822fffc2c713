<script setup lang="tsx">
import type { PropType } from 'vue';

import { Button, Icon, Loading, TagInput } from 'tdesign-vue-next';
import { computed, defineModel, defineProps, onMounted, ref, watch } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import PopTable from './pop-table.vue';

defineOptions({ name: 'SiliconDataTypePopSelect' });
const props = defineProps({
  rowSelectionType: {
    type: Object as PropType<'multiple' | 'single'>,
    default: 'single',
  },
  optLabel: {
    type: String,
    default: 'value',
  },
  optValue: {
    type: String,
    default: 'id',
  },
  columns: {
    type: Array,
    default: () => [],
  },
  api: {
    type: Object as PropType<{
      getByIds: (data?: any) => Promise<any[]>;
      listByPage: (data?: any) => Promise<any[]>;
    }>,
    default: () => {
      return {
        getByIds: () => {
          () => {};
        },
        listByPage: () => {
          () => {};
        },
      };
    },
  },
});
const emit = defineEmits(['valueChange']);
const loading = ref(false);
const popTableRef = ref();
const modelValue = defineModel('modelValue');
const selectedRowData = ref([]);
const reqRunner = {
  getByIds: useRequest(props.getByIds, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    onError: () => {
      loading.value = false;
    },
    onSuccess: (res: any) => {
      loading.value = false;
      selectedRowData.value = res;
    },
  }),
};
const listeners = {
  click: () => {
    /**
     * 点击打开table
     */
    popTableRef.value.show(selectedRowData.value);
  },
};
const emitHanler = {
  selectChange: (ctx: any) => {
    // selectedRowData.value= ctx.selectedRowData;
  },
  valueSelected: (data?: any) => {
    selectedRowData.value = data || [];
    modelValue.value = (data || [])
      .map((item) => item[props.optValue])
      .join(',');
  },
};
const value = computed<string[]>({
  get() {
    return selectedRowData.value.map((item) => item[props.optLabel]);
  },
  // set(val) {
  // modelValue.value = val;
  // }
});
watch(
  modelValue,
  (value, oldValue) => {
    if (value && oldValue != value) {
      loading.value = true;
      reqRunner.getByIds.run(modelValue.value);
    }
  },
  { deep: true },
);
onMounted(() => {
  reqRunner.getByIds.run(modelValue.value);
  loading.value = true;
});
// defineExpose({ emitHanler });
</script>
<template>
  <Loading
    :indicator="false"
    :inherit-color="true"
    :loading="loading"
    class="w-full"
    size="small"
    text="加载中..."
  >
    <!--  :readonly="true"-->
    <TagInput
      v-model="value"
      clearable
      v-bind="$attrs"
      :readonly="true"
      v-on="listeners"
    >
      <template #suffixIcon>
        <Button style="margin-right: -8px" v-on="listeners">
          <Icon name="search" />
        </Button>
      </template>
    </TagInput>
  </Loading>
  <PopTable
    ref="popTableRef"
    :api="api?.listBypage"
    :row-key="optValue"
    :row-selection-type="rowSelectionType"
    v-on="emitHanler"
  />
</template>

<style scoped></style>
