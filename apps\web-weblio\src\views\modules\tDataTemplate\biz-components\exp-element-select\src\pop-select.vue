<script setup lang="tsx">
import type { PropType } from 'vue';

import { Icon } from 'tdesign-icons-vue-next';
import { computed, defineModel, defineProps, onMounted, ref, watch } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { getByIds, listByPage } from '../../../api';
import PopTable from './pop-table.vue';

defineOptions({ name: 'SiliconDataTypePopSelect' });
const props = defineProps({
  rowSelectionType: {
    type: Object as PropType<'multiple' | 'single'>,
    default: 'single',
  },
  optLabel: {
    type: String,
    default: 'dbType',
  },
  optValue: {
    type: String,
    default: 'id',
  },
  api: {
    type: Object as PropType<{
      getByIds: (data?: any) => Promise<any[]>;
      listByPage: (data?: any) => Promise<any[]>;
    }>,
    default: () => {
      getByIds, listByPage;
    },
  },
});
const emit = defineEmits(['valueChange']);
const loading = ref(false);
const popTableRef = ref();
const modelValue = defineModel('modelValue');
const selectedRowData = ref([]);
const reqRunner = {
  getByIds: useRequest(getByIds, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    onError: () => {
      loading.value = false;
    },
    onSuccess: (res: any) => {
      loading.value = false;
      selectedRowData.value = res;
    },
  }),
};
const listeners = {
  click: () => {
    selectedRowData.value = [];
    /**
     * 点击打开table
     */
    popTableRef.value.show(selectedRowData.value);
  },
};
const emitHanler = {
  selectChange: (ctx: any) => {
    // selectedRowData.value= ctx.selectedRowData;
  },
  valueSelected: (data?: any) => {
    emit('valueChange', data);
    selectedRowData.value = data || [];
    modelValue.value = (data || [])
      .map((item) => item[props.optValue])
      .join(',');
  },
};
const value = computed<string[]>({
  get() {
    return selectedRowData.value.map((item) => item[props.optLabel]);
  },
  // set(val) {
  // modelValue.value = val;
  // }
});
watch(
  modelValue,
  (value, oldValue) => {
    if (value && oldValue != value) {
      loading.value = true;
      reqRunner.getByIds.run(modelValue.value);
    }
  },
  { deep: true },
);
onMounted(() => {
  reqRunner.getByIds.run(modelValue.value);
  loading.value = true;
});
// defineExpose({ emitHanler });
</script>
<template>
  <Icon class="cursor-pointer" name="add" v-on="listeners" />
  <PopTable
    ref="popTableRef"
    :api="{ listByPage }"
    :row-key="optValue"
    :row-selection-type="rowSelectionType"
    v-on="emitHanler"
  />
</template>

<style scoped></style>
