<script lang="tsx">
import {ChevronDownIcon} from 'tdesign-icons-vue-next';
import { requestClient } from '#/api/request';
import DynamicTable from '#/components/dynamic-table/index.vue';
import Ketcher from '#/components/ketcher/index.vue';
import LuckySheet from '#/components/luckysheet/index.vue';
import UEditor from '#/components/ueditor/index.vue';
import { jsonToUrlParams } from '#/utils';
import { EventBus, offEvent, onEvent } from '#/utils/eventBus.ts';
import { useAccessStore } from '@vben/stores';
import { ButtonIcon, DeleteIcon, LockOnIcon } from 'tdesign-icons-vue-next';
import {
  Checkbox,
  CheckboxGroup,
  Collapse,
  CollapsePanel,
  Comment,
  DatePicker,
  FormItem,
  Input,
  Typography,
  InputNumber,
  Option,
  Radio,
  RadioGroup,
  Select,
  Textarea,
  TimePicker,
  Upload,
  Button, Dialog, MessagePlugin,
} from 'tdesign-vue-next';
import { defineAsyncComponent, defineComponent, ref } from 'vue';
import Draggable from 'vuedraggable';
import {dataAuthApply} from "#/views/modules/tDataPermission/api.ts";

export default defineComponent({
  name: 'FormItemRender',
  components: {
    Dialog,
    Upload,
    Draggable,
    FormItem,
    Input,
    Textarea,
    RadioGroup,
    Radio,
    Checkbox,
    CheckboxGroup,
    Select,
    Option,
    DatePicker,
    TimePicker,
    InputNumber,
    Collapse,
    CollapsePanel,
    ButtonIcon,
    DeleteIcon,
    LockOnIcon,
    DynamicTable,
    UEditor,
    LuckySheet,
    Ketcher,
    Comment,
    Button,
    Typography,
    Drager: defineAsyncComponent(
      () => import('#/views/modules/formEditer/components/editer/Drager.vue'),
    ),
    FormItemRender: defineAsyncComponent(
      () =>
        import(
          '#/views/modules/formEditer/components/render/FormItemRender.vue'
        ),
    ),
    // DragContent: defineAsyncComponent(
    //   () =>
    //     import('#/views/modules/formEditer/components/editer/DragContent.vue'),
    // ),
  },
  props: {
    eventPrefix: {
      type: String,
      default: '',
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    DailogShow: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const accessStore = useAccessStore();
    const selectedPanel = ref({});
    const asyncOptions = ref([]);
    const formData = ref(props.modelValue);
    const applyDailogShow = ref(props.DailogShow);
    const doHttp = async (data: {
      label: any;
      method: string;
      payload: any;
      url: any;
      value: any;
    }) => {
      if (data?.url) {
        let url = data.url;
        if (data.method === 'GET') {
          url = `${data.url}?${jsonToUrlParams(JSON.parse(data?.payload || '{}'))}`;
        }
        const res = await requestClient[data?.method?.toLowerCase() || 'get']?.<
          any[]
        >(url, data.payload);
        asyncOptions.value = res.map((item: { [x: string]: any }) => {
          return {
            value: `${item[data?.value || 'value']}`,
            label: `${item[data?.label || 'label']}`,
          };
        });
      }
    };
    const collapsePanelClick = (pitem: unknown) => {
      selectedPanel.value = pitem;
      // EventBus.emit(`collapse_${pitem.id}childrenPanel_select`, pitem);
      EventBus.emit(`formEditer_itemClick`, pitem);
      EventBus.emit(`childrenPanel_select`, pitem);
      EventBus.emit(`reSetChildrenPanel_select`, pitem);
    };
    const defHandler = {};
    const eventHandler = {
      reSetChildrenPanel_select: (data: {}) => {
        selectedPanel.value = data;
      },
    } as any;
    const makeEventHandler = () => {
      Object.keys(defHandler).forEach((item) => {
        eventHandler[`${props.eventPrefix}${item}`] = defHandler[item];
      });
    };

    const buildRules = () => {
      // 构建规则
    };
    const uploadSuccess = (context: { fileList: any[] }) => {
      formData.value[props.config.schema.name] = context.fileList.map(
        (item: {
          name: any;
          response: { result: { url: any } };
          status: any;
        }) => {
          return {
            name: item?.name,
            status: item?.status,
            path: item?.response?.result?.url || '',
            url: item?.response?.result?.temporaryUrl || '',
          };
        },
      );
    };

    const isFieldAuthApply = () => {
      applyDailogShow.value = true;
    };

    const fieldAuthApply = async () => {
      applyDailogShow.value = false;
      await dataAuthApply({...props.config.schema.baseInfo, dataScope: props.config.schema.name, dataScopeName: props.config.schema.title})
    };

    const ellipsisState = {
      row: 2,
      suffix: <ChevronDownIcon />,
      expandable: true,
      collapsible: true,
    };

    return {
      makeEventHandler,
      defHandler,
      formData,
      eventHandler,
      asyncOptions,
      selectedPanel,
      doHttp,
      collapsePanelClick,
      accessStore,
      uploadSuccess,
      fieldAuthApply,
      isFieldAuthApply,
      applyDailogShow,
      ellipsisState
    };
  },
  watch: {
    config: {
      handler(val) {
        if (val?.schema?.syncOptions?.url) {
          this.doHttp(val.schema.syncOptions);
        }
      },
      deep: true,
    },
    modelValue: {
      handler(val) {
        try {
          this.formData =
            this.config.component === 'TimePicker' && this.isEdit ? val : val;
        } catch (error) {
          console.error(error);
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.makeEventHandler();
    onEvent(this.eventHandler);
    if (this.config?.isGroup) {
      this.selectedPanel = this.config?.schema?.children?.[0];
      EventBus.emit(`childrenPanel_select`, this.config?.schema?.children?.[0]);
    }
    if (this.config?.schema?.syncOptions?.url) {
      this.doHttp(this.config.schema.syncOptions);
    }
    console.log('isEdit:', this.isEdit);
  },
  unmounted() {
    this.makeEventHandler();
    offEvent(this.eventHandler);
  }
});
</script>

<template>
  <!--    {{ formData }}-->
  <!--  {{ config.schema }}-->
  <!--  name:{{ config.schema.name }}&#45;&#45;&#45;&#45;-->
  <!--  {{ formData[config.schema.name] }}-->
  <!--  {{ config.props }}-->
  <!--    :label-style="config.schema.backgroundColor === 1 ? 'bg-yellow-200' : 'bg-white'"-->
  <!--    :status="config.schema.backgroundColor === 1 ? 'success':''"-->
  <Dialog
    v-model:visible="applyDailogShow"
    :close-btn="false"
    body="申请信息将发送至数据管理员审批，是否确认申请？"
    header="权限申请确认"
    theme="warning"
    @confirm="fieldAuthApply"
  />
  <FormItem
    :rules="config.schema.rules"
    v-if="!config.unFormItem"
    :label="config.schema?.title"
    :help="config.schema.backgroundColor === 1 ? '待审批' : ''"
    :name="config.schema.name"
    class="custom-form-item"
  >
<!--    <Input-->
<!--      v-model="formData[config.schema.name]"-->
<!--      :readonly="isEdit || config.schema?.isReadonly"-->
<!--      v-bind="config.props"-->
<!--      v-if="config.component === 'Input'"-->
<!--      :placeholder="config.schema?.placeholder"-->
<!--      style="width: 100%"-->
<!--    />-->
    <div v-if="config.component === 'Input' && formData[config.schema.name].length < 270" class="showItem">{{formData[config.schema.name]}}</div>
    <Typography v-if="config.component === 'Input' && formData[config.schema.name].length >= 270" :ellipsis="ellipsisState">{{formData[config.schema.name]}}</Typography>
    <InputNumber
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      v-bind="config.props"
      v-if="config.component === 'InputNumber'"
      :placeholder="config.schema?.placeholder"
      style="width: 100%"
    />

    <Textarea
      v-if="config.component === 'Textarea'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      :autosize = true
    />
    <RadioGroup
      v-if="config.component === 'Radio'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    >
      <div
        class="bg-background flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border p-1 pl-2 pr-2"
      >
        <Radio
          v-for="item in [...config.schema.options, ...asyncOptions]"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </Radio>
      </div>
    </RadioGroup>
    <CheckboxGroup
      v-if="config.component === 'Checkbox'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    >
      <div
        class="bg-background flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border p-1 pl-2 pr-2"
      >
        <Checkbox
          v-for="item in [...config.schema.options, ...asyncOptions]"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </Checkbox>
      </div>
    </CheckboxGroup>
    <Select
      v-if="config.component === 'Select'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      :multiple="config.schema?.multiple"
    >
      <Option
        v-for="item in [...config.schema.options, ...asyncOptions]"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </Select>

    <DatePicker
      v-if="config.component === 'DatePicker'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    />
    <DatePicker
      v-if="config.component === 'DateAndTimePicker'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
    />
    <TimePicker
      v-if="config.component === 'TimePicker'"
      v-model="formData[config.schema.name]"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      :readonly="isEdit || config.schema?.isReadonly"
      style="width: 100%"
    />
    <Upload
      :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
      v-if="config.component === 'Upload' && formData[config.schema.name].length != 0"
      v-model="formData[config.schema.name]"
      v-bind="{ ...config.props, ...config.schema, name: 'file' }"
      :accept="config.schema.accept?.join(',')"
      :upload-button="{ content: '上传' }"
      :cancel-upload-button="{ theme: 'default', content: '取消' }"
      style="width: 100%"
      :disabled="isEdit || config.schema?.isReadonly"
      @success="uploadSuccess"
    />

    <DynamicTable
      v-if="config.component === 'DynamicTable'"
      v-model="formData[config.schema.name]"
      :readonly="isEdit || config.schema?.isReadonly"
      v-bind="config.props"
      :placeholder="config.schema?.placeholder"
      style="width: 100%"
    />

    <UEditor
      v-bind="config.props"
      v-if="config.component === 'UEditor'"
      v-model="formData[config.schema.name]"
      style="width: 100%; height: 800px"
    />

    <Ketcher
      v-bind="config.props"
      v-if="config.component === 'Ketcher'"
      v-model="formData[config.schema.name]"
      style="width: 100%; height: 800px"
    />
    <Comment
      v-bind="config.props"
      v-if="config.component === 'Comment'"
      :avatar="config.schema.avatar"
      :author="config.schema.author"
      :datetime="config.schema.datetime"
      :content="config.schema.content"
      style="width: 100%"
    />
    <Button
      variant="text"
      shape="circle"
      theme="primary"
      @click="isFieldAuthApply"
      v-if="config.component === 'LockedItem'">
      <template #icon> <LockOnIcon /></template>
    </Button>
  </FormItem>
  <div v-else class="w-full">

    <FormItem
      v-if="config.component === 'LuckySheet'"
      :rules="config.schema.rules"
      :label="config.schema?.title"
      :name="config.schema.name"
      :help="config.schema.backgroundColor === 1 ? '待审批' : ''"
      class="custom-form-item"
    >
      <LuckySheet
        v-bind="config.props"
        v-if="config.component === 'LuckySheet'"
        v-model="formData[config.schema.name]"
        style="width: 100%; height: 800px"
      />
    </FormItem>

    <FormItem
      v-if="config.component === 'LockedItem'"
      :rules="config.schema.rules"
      :label="config.schema?.title"
      :name="config.schema.name"
      :help="config.schema.backgroundColor === 1 ? '待审批' : ''"
      class="custom-form-item"
    >
    <Button
      variant="text"
      shape="circle"
      theme="primary"
      @click="isFieldAuthApply"
      v-if="config.component === 'LockedItem'">
      <template #icon> <LockOnIcon /></template>
    </Button>
    </FormItem>

    <Collapse
      v-if="config.component === 'Collapse'"
      default-expand-all
      style="width: 100%; margin-bottom: 24px"
    >
      <CollapsePanel
        v-for="pitem in config.schema.children"
        :key="pitem.id"
        :header="pitem.schema.title"
        style="width: 100%"
      >
        <div class="w-full">
          <div
            v-if="isEdit"
            :class="`flex p-1 ${selectedPanel.id == pitem.id ? 'border border-dashed border-blue-400' : ''}`"
          >
            <div
              class="mr-2 h-[100%] w-[20px] justify-center"
              @click.stop="collapsePanelClick(pitem)"
            >
              <ButtonIcon size="20px" />
            </div>
            <!-- v-if="pitem?.schema?.children?.length > 0" -->
            <Drager
              :columns="pitem?.schema?.columns || 1"
              :event-prefix="`collapse_${pitem.id}`"
              :model-value="pitem.schema.children"
              drag-group="vbox"
              style="width: 100%; min-height: 24px"
            />
          </div>
          <div
            v-else
            :class="`grid w-full grid-cols-${pitem?.schema?.columns || 1} w-full gap-1`"
          >
            <FormItemRender
              v-for="item in pitem.schema.children"
              :key="item.id"
              v-model="formData"
              :config="item"
            />
          </div>
        </div>
      </CollapsePanel>
    </Collapse>
  </div>
</template>

<style scoped lang="less">
:deep(.t-upload__card-content) {
  width: 200px;
  height: 200px;
}
:deep(.custom-form-item) {
  .t-form__label {
      min-width: 300px !important;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: -70px;
    }
}

:deep(.t-form__controls) {
  flex: 1;
}
//:deep(.custom-form-item) {
//  display: flex !important;
//  .t-form__label {
//    min-width: 100px !important;
//    overflow: hidden;
//    text-overflow: ellipsis;
//    white-space: nowrap;
//    flex: none;
//    width: auto !important;
//    }
//}
//
//:deep(.t-form__controls) {
//  flex: 1;
//  margin-left: 0px !important;
//}
:deep(.t-collapse-panel__content) {
  background: transparent !important;
  padding: 25px;
}

:deep(.t-collapse-panel__body) {
  background: transparent !important;
}

:deep(.t-form__item) {
  //margin-bottom: 10px;
  margin-right: 0px;
}

:deep(.t-collapse-panel__header) {
  padding: 6px;
}

:deep(.t-upload__flow) {
  width: 100%;
  min-width: 0px !important;
  max-width: none !important;
}
:deep(.t-input__help) {
  color: rgb(255, 0, 0);
}
.showItem {
  color: rgb(0, 0, 0);
}

:deep(.t-collapse-panel__wrapper .t-collapse-panel__header.t-is-clickable) {
  background-color: gainsboro;
}
:deep(.t-form__controls) {
  border-bottom: 1px solid #E6E8EB;
}
.t-upload {
  border-bottom: 2px solid #ffffff;
  margin-bottom: -2px;
}
:deep(.t-form__label) {
  color: #00133399;
}
:deep(.t-form__label--required label::before){
  content : "";
}
</style>
