// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-search/tSearch/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-search/tSearch/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-search/tSearch/deleteBatch/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-search/tSearch/getByIds/${data}`);
}
export async function search(data: any) {
  return requestClient.post<any>('/rgdc-search/tSearch/search', data);
}
export async function advancedSearch(data: any) {
  return requestClient.post<any>('/rgdc-search/tSearch/advancedSearch', data);
}
export async function getCategorys(data: any) {
  return requestClient.post<any>(`/rgdc-search/tDataClassify/listByTree/${data}`);
}
export async function getLabelList() {
  return requestClient.get<any>(`/rgdc-submit/tDataLabelManage/getLabelList`);
}
export async function associate(data: any) {
  return requestClient.post<any>('/rgdc-search/tSearch/associate', data);
}
export async function getSearchHisList() {
  return requestClient.post<any>('/rgdc-submit/tPortalSearchHis/getSearchHisList');
}
export async function deleteSearch(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tPortalSearchHis/deleteOne/${data}`);
}
export async function getOneByFileCode(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tSysAttach/getOneByFileCode/${data}`);
}
export async function getMdByFileCode(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tSysAttach/getMdByFileCode/${data}`);
}
export async function getDescribeMdByFileCode(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tSysAttach/getDescribeMdByFileCode/${data}`);
}
export async function getOneByFileCodeShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tSysAttach/getOneByFileCodeShare`, data);
}
export async function getMdByFileCodeShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tSysAttach/getMdByFileCodeShare`, data);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tSysAttach/getOne/${data}`);
}
export async function getOneShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tSysAttach/getOneShare`, data);
}
export async function listPageByClassify(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataDataset/listPageByClassify', data);
}
export async function getResourcesTypeWithCount(data: any) {
  return requestClient.post<any>(`/rgdc-search/tSearch/getResourcesTypeWithCount`, data);
}
