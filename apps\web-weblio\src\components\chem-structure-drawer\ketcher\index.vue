<script setup>
import {
  defineEmits,
  defineExpose,
  defineProps,
  onMounted,
  onUnmounted,
  ref,
  watch,
} from 'vue';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
});
const emit = defineEmits(['update:modelValue']);

const idKetcher = ref();

// 本地副本，避免直接改 props
const localValue = ref({ ...props.modelValue });

// 监听父组件传入的 modelValue 变化，保持同步
watch(
  () => props.modelValue,
  (val) => {
    localValue.value = { ...val };
  },
  { deep: true },
);

// 用于清理的引用
let initTimer = null;
let observer = null;

const updateModelValue = (newVal) => {
  emit('update:modelValue', { ...newVal });
};

const initKetcher = () => {
  if (initTimer) {
    clearTimeout(initTimer);
  }

  initTimer = setTimeout(() => {
    idKetcher.value?.contentWindow?.ketcher?.setMolecule(
      localValue.value.config,
    );

    idKetcher.value?.contentWindow?.addEventListener(
      'wheel',
      (event) => {
        event.preventDefault();
      },
      { passive: false },
    );

    if (observer) {
      observer.disconnect();
    }

    observer = new MutationObserver(handleMutation);
    observer.observe(idKetcher.value.contentDocument, {
      childList: true,
      subtree: true,
    });

    async function handleMutation(_mutationsList, _observer) {
      // 获取新值
      const config = await idKetcher.value?.contentWindow?.ketcher?.getKet();
      const smiles = await idKetcher.value?.contentWindow?.ketcher?.getSmiles();
      const img = await idKetcher.value?.contentWindow?.ketcher?.generateImage(
        config,
        {
          outputFormat: 'png',
          backgroundColor: '255, 255, 255',
        },
      );
      // 更新本地副本
      localValue.value = { config, smiles, img };
      console.log(localValue.value);
      // 通知父组件
      updateModelValue(localValue.value);
    }
  }, 500);
};

const getSmiles = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getSmiles();
};

const getMolfile = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getMolfile();
};

const getKet = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getKet();
};

function setMolecule(e) {
  return idKetcher.value?.contentWindow?.ketcher?.setMolecule(e);
}

onMounted(() => {
  setMolecule(localValue.value);
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (initTimer) {
    clearTimeout(initTimer);
    initTimer = null;
  }

  if (observer) {
    observer.disconnect();
    observer = null;
  }
});

defineExpose({
  getSmiles,
  getMolfile,
  setMolecule,
  getKet,
});
</script>

<template>
  <iframe
    ref="idKetcher"
    class="frame h-full w-full"
    src="/static/standalone/index.html"
    @load="initKetcher"
  ></iframe>
</template>
<style scoped>
.molecule {
  padding: 30px;
}

.input-search {
  width: 800px;
}

.molecule .left_content {
  width: 800px;
}

.molecule .right_content {
  width: calc(100% - 800px);
  margin-left: 50px;
  margin-top: 50px;
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.radio-group {
  display: grid;
}

.ant-radio-wrapper {
  padding: 10px 0 !important;
}
</style>
