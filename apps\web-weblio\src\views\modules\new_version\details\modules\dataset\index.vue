<script setup lang="tsx">
import { defineProps, onMounted, ref, toRefs } from 'vue';

import DetailHeader from '../common/detail-header';
import { DetailLayoutTabbed } from '../common/detail-layout-tabbed';
import { getOneByDataseCode } from './api';
import DataPreview from './modules/dataPreview';
import DownloadList from './modules/downloadList';
import MarkdownRender from './modules/markdownRender';
import TopCardOfDataset from './modules/top-card-of-dataset';

const props = defineProps<{
  item: any;
}>();

const { item } = toRefs(props);
const topCardData = ref<any>({});
const describeFileStr = ref<any>('');
const downloadListData = ref<any>([]);
const dataPreviewData = ref<any>([]);
const sourceData = ref<any>({});
const data = {
  header: () => <TopCardOfDataset data={topCardData} rowData={item} />,
  menu: [
    {
      title: '数据集介绍',
      content: () => (
        <div class="markdown-container">
          <div class="markdown-render-container">
            <MarkdownRender content={describeFileStr} />
          </div>
          <div class="source-section">
            <div class="source-title">数据来源</div>
            <div class="source-card">{sourceData.value || ''}</div>
          </div>
        </div>
      ),
    },
    {
      title: '数据预览',
      content: () => <DataPreview data={dataPreviewData} />,
    },
    {
      title: '数据集文件',
      content: () => <DownloadList data={downloadListData}></DownloadList>,
    },
  ],
};

const init = async () => {
  try {
    const datasetCode = item.value.baseCode;
    const res = await getOneByDataseCode({
      datasetCode,
    });
    describeFileStr.value = res.describeFileStr;
    downloadListData.value = res.subList;
    topCardData.value = res;
    dataPreviewData.value = res.subList;
    sourceData.value = res.source;
  } catch (error) {
    console.error(error);
  }
};

onMounted(() => {
  init();
});
</script>

<template>
  <DetailHeader />
  <DetailLayoutTabbed :data="data" />
</template>

<style scoped lang="scss">
:deep(.markdown-container) {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 24px;
  width: 100%;
  min-height: 400px;
}

:deep(.source-section) {
  flex: 0 0 300px;
  min-width: 300px;
  max-width: 300px;
}

:deep(.source-title) {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  white-space: nowrap;
}

:deep(.markdown-render-container) {
  flex: 1;
  min-width: 0;
  overflow: auto;
  padding-right: 8px;
}

:deep(.markdown-render-container .markdown-body) {
  max-width: none;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

:deep(.source-card) {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  font-size: 14px;
  color: #666;
  font-weight: 400;
  line-height: 1.6;
  margin: 0;
  text-align: left;
  border: 1px solid #e8e8e8;
  transition: all 0.2s ease;
  word-break: break-word;
  word-wrap: break-word;
  hyphens: auto;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;

  &:hover {
    background: #f5f5f5;
    border-color: #d9d9d9;
  }

  &:empty::before {
    content: '暂无数据来源信息';
    color: #ccc;
    font-style: italic;
  }
}
</style>
