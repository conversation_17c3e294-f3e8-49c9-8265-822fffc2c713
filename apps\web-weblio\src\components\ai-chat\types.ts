/**
 * 聊天消息结构体
 */
export interface ChatMessage {
  /** 消息内容 */
  content: string;
  /** 角色：用户或助手 */
  role: 'assistant' | 'user';
  /** 头像对象 */
  avatar: any;
  /** 气泡位置 */
  placement: 'end' | 'start';
  /** （可选）推理内容，仅 assistant 有 */
  reasoningText?: string;
  /** （可选）分块内容，如结构化数据 */
  chunks?: any;
}

/**
 * 创建用户消息
 * @param content 消息内容
 * @param avatar 用户头像
 */
export function createUserMessage(content: string, avatar: any): ChatMessage {
  return {
    content,
    role: 'user',
    avatar,
    placement: 'end',
  };
}

/**
 * 创建助手消息（初始空内容）
 * @param avatar 助手头像
 */
export function createAssistantMessage(avatar: any): ChatMessage {
  return {
    content: '',
    role: 'assistant',
    avatar,
    placement: 'start',
    reasoningText: '',
  };
}
