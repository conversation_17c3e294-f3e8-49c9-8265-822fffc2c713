import { $t, setupI18n } from '#/locales';
import ep from '@iconify/json/json/ep.json'; // 可以像这样引入更多的图标集
import lucide from '@iconify/json/json/lucide.json';
import { addCollection } from '@iconify/vue';
import { registerAccessDirective } from '@vben/access';
import { preferences } from '@vben/preferences';
import { initStores } from '@vben/stores';
import { useTitle } from '@vueuse/core';
import { createApp, watchEffect } from 'vue';
import VueUeditorWrap from 'vue-ueditor-wrap';

import '@vben/styles';

import 'tdesign-vue-next/es/style/index.css'; // 注册引入的图标集
// import { initComponentAdapter } from './adapter/component';
import App from './app.vue';
import { router } from './router';

async function bootstrap(namespace: string) {
  addCollection(lucide);
  addCollection(ep);
  // 初始化组件适配器
  // initComponentAdapter();
  const app = createApp(App);
  // 国际化 i18n 配置
  await setupI18n(app);

  // 配置 pinia-tore
  await initStores(app, { namespace });

  // 安装权限指令
  registerAccessDirective(app);

  if (window?.__MICRO_APP_ENVIRONMENT__) {
    console.log('Tiny在微前端环境中');
    const { directives } = window.microApp?.getData() || {};
    console.log('微前端配置', window.microApp?.getData());
    app.directive('dust-permission', directives);
  }
  // 配置路由及路由守卫
  app.use(router);

  // 动态更新标题
  watchEffect(() => {
    if (preferences.app.dynamicTitle) {
      const routeTitle = router.currentRoute.value.meta?.title;
      const pageTitle =
        (routeTitle ? `${$t(routeTitle)} - ` : '') + preferences.app.name;
      useTitle(pageTitle);
    }
  });

  app.use(VueUeditorWrap);
  app.mount('#app');
}

export { bootstrap };
