<script setup lang="ts">
import Ketcher from '#/components/ketcher/index.vue';
import { Page } from '@vben/common-ui';
import { ref } from 'vue';

// 中间层页面 主要负责页面间的解耦以及数据传输
const ketcher = ref();

const click = () => {
  console.log(ketcher.value.setMolecule(JSON.stringify(data.value)));
};
const data = ref({
  root: {
    nodes: [
      { $ref: 'mol0' },
      { $ref: 'mol1' },
      { $ref: 'mol2' },
      { $ref: 'mol3' },
      { $ref: 'mol4' },
      { $ref: 'mol5' },
      { $ref: 'mol6' },
      { $ref: 'mol7' },
      { $ref: 'mol8' },
      { $ref: 'mol9' },
      { $ref: 'mol10' },
      { $ref: 'mol11' },
    ],
    connections: [],
    templates: [],
  },
  mol0: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [19.975, -6.7, 0] }],
  },
  mol1: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [5.425_000_000_000_001, -8.025, 0] }],
  },
  mol2: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [7.9, -11.15, 0] }],
  },
  mol3: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [16.400_000_000_000_002, -12.875, 0] }],
  },
  mol4: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [21.85, -11.225_000_000_000_001, 0] }],
  },
  mol5: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [21.225, -7.825, 0] }],
  },
  mol6: {
    type: 'molecule',
    atoms: [
      { label: 'O', location: [16.225, -9.55, 0] },
      {
        label: 'O',
        location: [15.725_000_000_000_001, -10.416_025_403_784_44, 0],
      },
    ],
    bonds: [{ type: 1, atoms: [0, 1] }],
  },
  mol7: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [16.825, -10.25, 0] }],
  },
  mol8: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [23.775_000_000_000_002, -11.25, 0] }],
  },
  mol9: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [18.6, -12.525, 0] }],
  },
  mol10: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [29.575_000_000_000_003, -14, 0] }],
  },
  mol11: {
    type: 'molecule',
    atoms: [{ label: 'O', location: [23.1, -14.8, 0] }],
  },
});
</script>

<template>
  <Page description="动态表格组件示例" title="组件示例">
    <Ketcher ref="ketcher" style="height: 800px" v-model="data" />

    <div class="mt-4">
      <div>{{ JSON.stringify(data) }}</div>
    </div>
  </Page>
</template>
