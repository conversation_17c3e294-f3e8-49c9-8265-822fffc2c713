<script setup lang="ts">
import { onMounted, ref } from 'vue';
import {useRequest} from 'vue-hooks-plus';
import { getDSubstanceClassificationTreeByInchikey } from '../api';
import { useSearchStore } from '#/store/search';
import { computed, reactive } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

interface ClassificationNode {
  id: number;
  cui: string;
  treeNumber: string;
  classificationName: string;
  annotation?: string;
  inchikey?: string;
  depth: number;
  relationDepth: number;
  parentId?: number;
  isRoot: number;
  path: string;
  hierarchyPath: string;
  isLeaf: number;
  level: number;
  children?: ClassificationNode[];
}

// 简单的数据和状态
const treeData = ref<ClassificationNode[]>([]);
const expandedNodes = ref<Set<number>>(new Set([1, 2])); // 默认展开根节点和第二级

const state: any = reactive({
  detailItem: {},
  loading: false,
});

// Store
const searchStore = useSearchStore();
state.detailItem = computed(() => {
  const detail = searchStore.currentDetailItem;
  if (detail) {
    return detail;
  } else {
    const local = localStorage.getItem('currentDetailItem');
    return JSON.parse(local ?? '{}');
  }
});

// 构建树形结构
const buildTree = (nodes: any[]): ClassificationNode[] => {
  const tree: ClassificationNode[] = [];
  const nodeMap = new Map<number, ClassificationNode>();
  
  // 创建所有节点
  nodes.forEach(node => {
    nodeMap.set(node.id, {
      ...node,
      children: []
    });
  });
  
  // 建立父子关系
  nodes.forEach(node => {
    const currentNode = nodeMap.get(node.id);
    if (currentNode) {
      if (node.isRoot === 1 || !node.parentId) {
        tree.push(currentNode);
      } else {
        const parent = nodeMap.get(node.parentId);
        if (parent) {
          parent.children!.push(currentNode);
        }
      }
    }
  });
  
  return tree;
};

// 切换展开状态
const toggle = (nodeId: number) => {
  if (expandedNodes.value.has(nodeId)) {
    expandedNodes.value.delete(nodeId);
  } else {
    expandedNodes.value.add(nodeId);
  }
};

// 获取所有节点ID
const getAllNodeIds = (nodes: ClassificationNode[]): number[] => {
  const ids: number[] = [];
  
  const traverse = (nodeList: ClassificationNode[]) => {
    nodeList.forEach(node => {
      ids.push(node.id);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  
  traverse(nodes);
  return ids;
};

// 全部展开
const expandAll = () => {
  const allIds = getAllNodeIds(treeData.value);
  expandedNodes.value = new Set(allIds);
};

// 全部折叠
const collapseAll = () => {
  expandedNodes.value = new Set();
};

// 获取节点提示
const getTooltip = (node: ClassificationNode) => {
  return `分类: ${node.classificationName}\n编号: ${node.treeNumber}\n层级: ${node.hierarchyPath}`;
};

// 渲染树节点（递归）
const renderNodes = (nodes: ClassificationNode[], level: number = 0, isLastArray: boolean[] = []): any[] => {
  const result: any[] = [];
  
  nodes.forEach((node, index) => {
    const isLast = index === nodes.length - 1;
    const currentIsLastArray = [...isLastArray, isLast];
    
    result.push({
      ...node,
      level,
      hasChildren: node.children && node.children.length > 0,
      isExpanded: expandedNodes.value.has(node.id),
      isLast,
      isLastArray: currentIsLastArray
    });
    
    if (node.children && node.children.length > 0 && expandedNodes.value.has(node.id)) {
      result.push(...renderNodes(node.children, level + 1, currentIsLastArray));
    }
  });
  
  return result;
};

// 网络请求
const reqRunner = {
  listAll: useRequest(getDSubstanceClassificationTreeByInchikey, {
    manual: true,
    debounceWait: 300,
    onError: () => {
      MessagePlugin.error('获取分类数据失败');
    },
    onSuccess: (res: any) => {
      console.log("API返回数据:", res);
      if (res && Array.isArray(res)) {
        // 去重
        const uniqueNodes = res.filter((node: any, index: number, self: any[]) => 
          index === self.findIndex((n: any) => n.id === node.id)
        );
        console.log("去重后数据:", uniqueNodes);
        
        // 构建树
        treeData.value = buildTree(uniqueNodes);
        console.log("构建的树:", treeData.value);
        
        // 默认展开根节点
        treeData.value.forEach(root => {
          expandedNodes.value.add(root.id);
        });
      }
    },
  }),
};

// 初始化
const initializeData = async () => {
  try {
    state.loading = true;
    const {run, loading} = reqRunner.listAll;
    state.loading = loading;
    run({
      inchikey: state.detailItem.baseCode,
    });
  } catch (error) {
    console.error('获取分类数据失败:', error);
    MessagePlugin.error('获取分类数据失败');
  } finally {
    state.loading = false;
  }
};

onMounted(() => {
  initializeData();
});
</script>

<template>
  <div class="classification-tree">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载分类数据中...</div>
    </div>
    
    <template v-else>
      <div class="tree-container">
        <div v-if="treeData.length === 0" class="empty-state">
          <p>暂无分类信息</p>
        </div>
        
        <div v-else class="tree-content">
          <div class="tree-actions">
            <button class="action-btn" @click="expandAll">全部展开</button>
            <button class="action-btn" @click="collapseAll">全部折叠</button>
          </div>
          
          <div 
            v-for="item in renderNodes(treeData)" 
            :key="item.id"
            class="tree-item"
            :class="{ 
              'is-root': item.isRoot === 1, 
              'is-leaf': !item.hasChildren,
              'is-last': item.isLast
            }"
            :style="{ paddingLeft: `${item.level * 20}px` }"
          >
            <!-- 连接线 -->
            <div class="tree-lines" v-if="item.level > 0">
              <div 
                v-for="(isLast, index) in item.isLastArray" 
                :key="index"
                class="line-segment"
                :class="{ 
                  'is-last': isLast, 
                  'is-current': index === item.isLastArray.length - 1 
                }"
                :style="{ left: `${index * 20}px` }"
              >
                <!-- 连接点 -->
                <div 
                  v-if="index === item.isLastArray.length - 1"
                  class="connection-dot"
                ></div>
              </div>
            </div>
            
            <div class="node-content" @click="item.hasChildren && toggle(item.id)" :title="getTooltip(item)">
              <span class="node-toggle" v-if="item.hasChildren">
                <span v-if="item.isExpanded">▼</span>
                <span v-else>▶</span>
              </span>
              <span class="node-toggle placeholder" v-else></span>
              
              <span class="node-label">{{ item.treeNumber }}</span>
              <span v-if="item.treeNumber" class="node-code">({{ item.classificationName }})</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.classification-tree {
  padding: 20px;
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    
    .loading-spinner {
      color: #666;
      font-size: 14px;
    }
  }
  
  .tree-container {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
  
  .tree-content {
    padding: 16px;
    max-height: 500px;
    overflow-y: auto;
  }
  
  .tree-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
    
    .action-btn {
      padding: 6px 12px;
      background: #f0f0f0;
      border: 1px solid #d0d0d0;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      color: #666;
      transition: all 0.2s ease;
      
      &:hover {
        background: #e0e0e0;
        border-color: #c0c0c0;
        color: #333;
      }
      
      &:active {
        background: #d0d0d0;
        transform: translateY(1px);
      }
    }
  }
  
  .tree-item {
    position: relative;
    
    .tree-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
      
      .line-segment {
        position: absolute;
        width: 20px;
        height: 100%;
        
        // 垂直线
        &::before {
          content: '';
          position: absolute;
          border-left: 1px solid #d0d0d0;
          height: 100%;
          left: 10px;
        }
        
        // 水平线
        &.is-current {
          &::after {
            content: '';
            position: absolute;
            border-top: 1px solid #d0d0d0;
            width: 8px;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
          }
        }
        
        &.is-last {
          &::before {
            height: 50%;
          }
        }
        
        // 非当前层级的连接线，只显示垂直线
        &:not(.is-current) {
          &.is-last {
            &::before {
              display: none;
            }
          }
        }
        
        // 连接点
        .connection-dot {
          position: absolute;
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: #d0d0d0;
          top: 50%;
          left: 18px;
          transform: translateY(-50%);
          z-index: 3;
        }
      }
    }
    
    .node-content {
      display: flex;
      align-items: center;
      padding: 4px 0;
      cursor: pointer;
      position: relative;
      z-index: 2;
      background: white;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      .node-toggle {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-size: 12px;
        color: #666;
        
        &.placeholder {
          visibility: hidden;
        }
      }
      
      .node-label {
        font-size: 14px;
        color: #333;
        margin-right: 8px;
      }
      
      .node-code {
        font-size: 12px;
        color: #999;
        font-family: monospace;
      }
    }
    
    &.is-root {
      .node-label {
        font-weight: 600;
        color: #2c5aa0;
      }
    }
    
    &.is-leaf {
      .node-label {
        color: #666;
      }
    }
  }
}
</style> 

