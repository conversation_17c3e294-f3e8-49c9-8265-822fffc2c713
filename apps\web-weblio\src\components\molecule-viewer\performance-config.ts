// 3DMol.js 性能优化配置

export interface Performance3DConfig {
  // WebGL配置
  antialias: boolean;
  alpha: boolean;
  premultipliedAlpha: boolean;
  preserveDrawingBuffer: boolean;
  powerPreference: 'default' | 'high-performance' | 'low-power';
  failIfMajorPerformanceCaveat: boolean;

  // 渲染质量配置
  quality: 'high' | 'low' | 'medium';

  // 样式优化
  stickRadius: number;
  sphereScale: number;

  // 动画配置
  animationDuration: number;
  zoomDuration: number;
}

// 高性能配置（优先流畅度）
export const highPerformanceConfig: Performance3DConfig = {
  antialias: false,
  alpha: false,
  premultipliedAlpha: false,
  preserveDrawingBuffer: false,
  powerPreference: 'high-performance',
  failIfMajorPerformanceCaveat: false,
  quality: 'medium',
  stickRadius: 0.12,
  sphereScale: 0.2,
  animationDuration: 200,
  zoomDuration: 600,
};

// 平衡配置（质量与性能平衡）
export const balancedConfig: Performance3DConfig = {
  antialias: true,
  alpha: false,
  premultipliedAlpha: false,
  preserveDrawingBuffer: false,
  powerPreference: 'default',
  failIfMajorPerformanceCaveat: false,
  quality: 'medium',
  stickRadius: 0.15,
  sphereScale: 0.25,
  animationDuration: 300,
  zoomDuration: 800,
};

// 高质量配置（优先视觉效果）
export const highQualityConfig: Performance3DConfig = {
  antialias: true,
  alpha: true,
  premultipliedAlpha: true,
  preserveDrawingBuffer: false,
  powerPreference: 'default',
  failIfMajorPerformanceCaveat: false,
  quality: 'high',
  stickRadius: 0.2,
  sphereScale: 0.3,
  animationDuration: 500,
  zoomDuration: 1000,
};

// 应用3DMol性能配置的工具函数
export const apply3DPerformanceConfig = (
  viewer: any,
  config: Performance3DConfig = highPerformanceConfig,
) => {
  if (!viewer) return;

  try {
    // 基本性能设置
    viewer.enableFog(false);
    viewer.setBackgroundColor('white');

    // 禁用不必要的功能以提升性能
    if (config.quality === 'low' || config.quality === 'medium') {
      viewer.setClickable(false);
      viewer.enableAutoRotate(false);
    }

    // 设置渲染质量
    if (viewer.setQuality) {
      viewer.setQuality(config.quality);
    }

    // 设置动画时长
    if (viewer.setAnimationDuration) {
      viewer.setAnimationDuration(config.animationDuration);
    }

    // 优化光照设置
    viewer.setAmbientLight(0.7);
    viewer.setDirectionalLight(0.3);
  } catch (error) {
    console.warn('应用3D性能配置时出错:', error);
  }
};

// 获取推荐的性能配置
export const getRecommendedConfig = (): Performance3DConfig => {
  // 检测设备性能
  const canvas = document.createElement('canvas');
  const gl =
    canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

  if (!gl) {
    return highPerformanceConfig; // 无WebGL支持，使用最低配置
  }

  // 检测GPU信息
  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  const renderer = debugInfo
    ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
    : '';

  // 检测是否为移动设备
  const isMobile =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );

  if (isMobile || renderer.includes('Intel')) {
    return highPerformanceConfig; // 移动设备或集显使用高性能配置
  } else if (renderer.includes('NVIDIA') || renderer.includes('AMD')) {
    return balancedConfig; // 独显使用平衡配置
  }

  return balancedConfig; // 默认使用平衡配置
};
