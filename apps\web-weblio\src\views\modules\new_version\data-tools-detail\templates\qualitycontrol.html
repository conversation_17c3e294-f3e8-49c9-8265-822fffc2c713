<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>化工知识协同加工与管理平台</title>
  <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
  <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
  <link rel="stylesheet" href="../static/vue/theme/index.css">
  <script src="../static/vue/min/vue.min.js"></script>
  <script src="../static/vue/element-ui2.15.13_index.js"></script>
  <script src="../static/vue/axios0.26.0_axios.min.js"></script>
  <style>
    html,
    body {
      min-width: 100%;
    }

    .mon_warp {
      margin: 0px;
      width: 100%;
      background-size: cover;
    }

    .mon_body {
      display: none;
      width: 100%;
    }

    .el-menu-vertical-demo {
      height: 100%;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-upload__tip {
      margin-top: 10px;
    }

    .clearfix:before,
    .clearfix:after {
      display: table;
      content: "";
    }

    .clearfix:after {
      clear: both;
    }

    .center {
      border: 1px solid #ccc;
      width: 100%;
      margin: 20px auto 20px;
      border-radius: 20px;
      padding: 30px;
      min-width: 1200px;
    }



    .el-menu-item.is-active {
      background-color: #ecf5ff;
      color: #409EFF;
    }

    .el-menu-item {
      font-size: 14px;
      height: 56px;
      line-height: 56px;
    }

    .el-menu-item:hover {
      background-color: #ecf5ff;
    }

    .center_header {
      display: flex;
      align-items: center;
    }

    .el-table .cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      display: inline-block;
      vertical-align: middle;
    }

    .el-table th>.cell {
      white-space: nowrap;
    }

    .centered-cell {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      height: 100%;
    }
    .center_total{
      font-size: 16px;
      margin-bottom: 10px;
    }
    .center_total p span {
    color: #76839b;
}
.center_total p em {
    color: #606266;
}
  </style>
</head>

<body>
  <div class="header_app" id="header_app"></div>

  <div class="mon_warp clearfix" id="app">
    <div class="mon_body clearfix">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="3">
          <div style="padding-top:20px">
            <el-menu :default-active="menuActive"  class="el-menu-vertical-demo" 
             @select="handleMenuSelect"
            style="padding-top:20px;"
              active-text-color="#409EFF">
               <el-menu-item index="cleanTool">
                   <el-tooltip content="数据汇聚与清洗工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据汇聚与清洗工具
                  </span>
                </el-tooltip>
                  </el-menu-item>
              <el-menu-item index="classiFication">

                 <el-tooltip content="数据整编与分类工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据整编与分类工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="home">
                <el-tooltip content="全文多模态解析重组工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    全文多模态解析重组工具
                  </span>
                </el-tooltip>
              </el-menu-item><el-menu-item index="relationship">
                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    知识对象及关系挖掘工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>

            </el-menu>
          </div>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="21">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <h2>质量控制工具</h2>
            </div>
            <div>
              <p style="font-size: 16px;">
                质量控制工具包括完整性质控工具，确保元数据无缺失、无冗余；规范性质控工具，确保格式、内容符合标准；唯一性质控工具，确保数据保持唯一，符合既定标准；及时性质控工具，确保数据持续更新、不过期。
              </p>

              <div
                style="width: 100%;margin-top:20px;display: flex;justify-content: space-between;align-items: center;height: 20px;">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                  <el-tab-pane label="完整性" name="first"></el-tab-pane>
                  <el-tab-pane label="规范性" name="second"></el-tab-pane>
                  <el-tab-pane label="唯一性" name="third"></el-tab-pane>
                  <el-tab-pane label="合规性" name="fourth"></el-tab-pane>
                </el-tabs>
                <el-button type="success" @click="dialogVisible = true" style="float: right;">任务列表</el-button>
              </div>
              <div class="center"  v-if="iscenter == 1">
                <div class="center_header">
                  <span>导入形式：</span>
                  <div>
                    <el-radio v-model="radio1" label="1" border size="medium">Excel</el-radio>
                    <el-radio v-model="radio1" label="2" border size="medium">MySql数据库</el-radio>
                  </div>
                </div>
                <div class="center_header" style="margin-top: 20px;padding-left: 67px;">


                  <el-button type="primary" :disabled="radio1 == '2'">点击上传</el-button>
                  <span style="margin-left: 74px;">选择数据库：</span>
                  <el-select placeholder="请选择模型存储地址(例如：123.123.123.123)" style="width: 300px;"
                    :disabled="radio1 == '1'">
                    <el-option lable="**********" value="**********"></el-option>
                  </el-select>
                  <span style="margin-left: 74px;">选择数据库表：</span>
                  <el-select placeholder="请选择/请先选择数据库" style="width: 300px;" :disabled="radio1 == '1'">
                    <el-option lable="**********" value="**********"></el-option>
                  </el-select>

                </div>


                <el-table :data="tableData" style="width: 100%;margin-top: 20px;" border :header-cell-style=" {
        backgroundColor: '#f6f6f6',
        fontWeight: 'bold',
        color:'#000000'
      }">
                  <el-table-column prop="publicationType" label="Publication Type" width="180"></el-table-column>
                  <el-table-column prop="authors" label="Authors" width="300" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="articleTitle" label="Article Title" width="300"></el-table-column>
                  <el-table-column prop="volume" label="Volume" width="100"></el-table-column>
                  <el-table-column prop="issue" label="Issue" width="100"></el-table-column>
                  <el-table-column prop="specialIssue" label="Special Issue" width="120"></el-table-column>
                  <el-table-column prop="startPage" label="Start Page" width="100"></el-table-column>
                  <el-table-column prop="endPage" label="End Page" width="100"></el-table-column>
                </el-table>

                <div class="center_header" style="margin-top: 20px;">
                  <span>必备字段：</span>
                  <div>
                    <el-select placeholder="请选择字段" style="width: 300px;">
                      <el-option lable="id" value="id"></el-option>
                      <el-option lable="time" value="time"></el-option>
                    </el-select>
                  </div>
                </div>
                <div class="center_header" style="margin-top: 20px;">
                  <span>任务名称：</span>
                  <div>
                    <el-select placeholder="请选择字段" style="width: 300px;">
                      <el-option lable="id" value="id"></el-option>
                      <el-option lable="time" value="time"></el-option>
                    </el-select>
                  </div>
                </div>
              </div>
             
              <div class="center"  v-if="iscenter == 2">
                  <div class="center_total">  
                 <p> <span>总条目数：<em>12554</em>条</span></p>
                 <p> <span>字段填充率：<em>65%</em></span></p>
                 <p> <span>空置率<em>35%</em>条</span></p>
                </div>
                <h2 style="font-size: 18px;">必备字段：</h2>
                    <el-table
    :data="tableData1"
    style="width: 100%; margin-top: 20px;"
    border
    :header-cell-style="{
      backgroundColor: '#f6f6f6',
      fontWeight: 'bold',
      color: '#000000'
    }"
  >
    <el-table-column prop="index" label="序号" width="60"></el-table-column>
    <el-table-column prop="field" label="字段名"></el-table-column>
    <el-table-column prop="trueCount" label="有值数量" ></el-table-column>
    <el-table-column prop="falseCount" label="空值数量" ></el-table-column>
    <el-table-column prop="truePercentage" label="有值/空值比">
      <template #default="scope">
        <div style="display: flex; align-items: center;">
          <el-progress
            :percentage="scope.row.truePercentage"
            :color="customColors"
            :show-background="true"
            :stroke-width="12"
            style="width: 100px; margin-right: 10px;"
          />
          <span v-html='scope.row.falsePercentage'></span>
        </div>
      </template>
    </el-table-column>
  </el-table>
              </div>


                <div class="center"  v-if="iscenter == 3">
                    <el-table
    :data="tableData1"
    style="width: 100%; margin-top: 20px;"
    border
    :header-cell-style="{
      backgroundColor: '#f6f6f6',
      fontWeight: 'bold',
      color: '#000000'
    }"
  >
    <el-table-column prop="index" label="序号" width="60"></el-table-column>
    <el-table-column prop="field" label="字段名"></el-table-column>
    <el-table-column prop="trueCount" label="规范数量量" ></el-table-column>
    <el-table-column prop="falseCount" label="不规范数据量">
      <template #default="scope">
        <span
          @click="handleClickspan(scope.row)"
          style="color: #409EFF; text-decoration: underline; cursor: pointer;"
       v-html="scope.row.falseCount" >
        </span>
      </template>
    </el-table-column>
    <el-table-column prop="truePercentage" label="规范率">
      <template #default="scope">
        <div style="display: flex; align-items: center;">
          <span v-html='scope.row.falsePercentage'></span>
        </div>
      </template>
    </el-table-column>
  </el-table>
              </div>

                <div class="center"  v-if="iscenter == 4">
                  <div class="center_total">  
                 <p> <span>唯一性：<em>99.76%</em></span></p>
                 <p> <span>总条目数：<em>12554</em>条</span></p>
                 <p> <span>字段填充率：<em>65%</em></span></p>
                 <p> <span>空置率<em>35%</em>条</span></p>
                </div>
                <h2 style="font-size: 18px;">不唯一条目：</h2>
                    <el-table
    :data="tableData1"
    style="width: 100%; margin-top: 20px;"
    border
    :header-cell-style="{
      backgroundColor: '#f6f6f6',
      fontWeight: 'bold',
      color: '#000000'
    }"
  >
    <el-table-column prop="index" label="序号" width="60"></el-table-column>
    <el-table-column prop="field" label="Title"></el-table-column>
    <el-table-column prop="trueCount" label="Author" ></el-table-column>
    <el-table-column prop="falseCount" label="Institute" ></el-table-column>
    <el-table-column prop="truePercentage" label="有值/空值比">
      <template #default="scope">
        <div style="display: flex; align-items: center;">
          <el-progress
            :percentage="scope.row.truePercentage"
            :color="customColors"
            :show-background="true"
            :stroke-width="12"
            style="width: 100px; margin-right: 10px;"
          />
          <span v-html='scope.row.falsePercentage'></span>
        </div>
      </template>
    </el-table-column>
  </el-table>
              </div>
              <div class="center" v-if="iscenter == 5">

     <el-table
    :data="tableData1"
    style="width: 100%; margin-top: 20px;"
    border
    :header-cell-style="{
      backgroundColor: '#f6f6f6',
      fontWeight: 'bold',
      color: '#000000'
    }"
  >
    <el-table-column prop="index" label="序号" width="60"></el-table-column>
    <el-table-column prop="field" label="字段名"></el-table-column>
    <el-table-column prop="trueCount" label="Author" ></el-table-column>
    <el-table-column prop="falseCount" label="不合规类型描述" ></el-table-column>
    <el-table-column prop="truePercentage" label="有值/空值比">
      <template #default="scope">
        <div style="display: flex; align-items: center;">
          <el-progress
            :percentage="scope.row.truePercentage"
            :color="customColors"
            :show-background="true"
            :stroke-width="12"
            style="width: 100px; margin-right: 10px;"
          />
          <span v-html='scope.row.falsePercentage'></span>
        </div>
      </template>
    </el-table-column>
  </el-table>
              </div>
            </div>


            <div style="text-align: right;width: 100%;">
                  <el-button type="primary" style="margin-right: 60px;" @click="statrZK" v-if="iscenter !== 4">开始质控</el-button>
                </div>
    </div>
    </el-card>
    </el-col>
    </el-row>

    <!-- 上传记录对话框 -->
    <!-- <el-dialog title="上传记录" :visible.sync="dialogVisible" width="60%">
      <el-table :data="uploadLists" style="width: 100%;" :border="true" stripe>
        <el-table-column prop="created_at" label="上传时间" width="180"></el-table-column>
        <el-table-column prop="task_name" label="任务名称"></el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button size="mini" @click="handlePreview(scope.row)">预览</el-button>
            <el-button size="mini" type="primary">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog> -->

    <!-- 弹窗 -->
  <el-dialog
    title="不规范数据详情"
   :visible.sync="dialogVisible"
    width="30%"
  >
    <p>不规范数据量:</p>
    <p>字段名:</p>
    <!-- 可以根据需求添加更多详情 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>








  </div>
  </div>

  <div id="dataid" data="{{session.user}}" style="display:none"></div>
  <div class="mon_footer"></div>

  <script src="../static/js/jquery.min.js"></script>
  <script src="../static/js/monitor.js"></script>

  <script>
    let _this = this;
    var $_GET = (function () {
      var url = window.document.location.href.toString(); //获取的完整url
      var u = url.split("?");
      if (typeof (u[1]) == "string") {
        u = u[1].split("&");
        var get = {};
        for (var i in u) {
          var j = u[i].split("=");
          get[j[0]] = j[1];
        }
        return get;
      } else {
        return {};
      }
    })();
    const vm = new Vue({
      el: '#app',
      data: {
        dialogVisible: false,
        uploadLists: [],
        activeName: "first",
        radio1: '1',
        iscenter:1,
        menuActive:'qualitycontrol',
        tableData: [
          {
            publicationType: '',
            authors: 'Hazen, Benjamin; Makins, deepaO7; Hazen, Benjamin',
            articleTitle: 'Data quality for dat INTERNATIONAL',
            volume: '154',
            issue: '',
            specialIssue: '',
            startPage: '72',
            endPage: '90'
          },
          {
            publicationType: '',
            authors: 'Zhao, Rui, Yan, Ru; Yan, Ruqiang; Tian, Ruqiang',
            articleTitle: 'Deep learning and MECHANICAL SY',
            volume: '115',
            issue: '',
            specialIssue: '',
            startPage: '213',
            endPage: '237'
          },
          {
            publicationType: '',
            authors: 'Ming, Weng, Zhou',
            articleTitle: 'COVID-19 and Air EMERGING MAR',
            volume: '56',
            issue: '10',
            specialIssue: 'SI',
            startPage: '2422',
            endPage: '2442'
          },
          {
            publicationType: '',
            authors: 'Tokogron, C. J., A; Tian, Qiuyun',
            articleTitle: 'Structural Health IEEE INTERNET (C',
            volume: '4',
            issue: '3',
            specialIssue: '',
            startPage: '619',
            endPage: '636'
          },
          {
            publicationType: '',
            authors: 'Douzdot, Jason; H; He, Data',
            articleTitle: 'Using Deep Learn IEEE TRANSACT)',
            volume: '48',
            issue: '11',
            specialIssue: '',
            startPage: '111',
            endPage: '20'
          },
          // Add more rows as needed
        ],
         tableData1: [
        {
          index: 1,
          field: 'Title',
          trueCount: 1229,
          falseCount: 25,
          truePercentage: 85.5,
          falsePercentage: 14.5
        },
        {
          index: 2,
          field: 'Author',
          trueCount: 1189,
          falseCount: 65,
          truePercentage: 85.5,
          falsePercentage: 14.5
        },
        {
          index: 3,
          field: 'Institute',
          trueCount: 833,
          falseCount: 421,
          truePercentage: 85.5,
          falsePercentage: 14.5
        },
        {
          index: 4,
          field: 'insertTime',
          trueCount: 1254,
          falseCount: 0,
          truePercentage: 100,
          falsePercentage: 0
        },
        {
          index: 5,
          field: 'updateTime',
          trueCount: 1254,
          falseCount: 0,
          truePercentage: 100,
          falsePercentage: 0
        }
      ],
      customColors: [
        { color: '#409EFF', percentage: 100 }
      ],
      selectedRow: {}

      },
      mounted() {
        $('.mon_body').css({ display: 'revert' });
        this.getTableData(); // 获取表格数据



      },
      methods: {
       handleMenuSelect(index) {
    if (index === 'cleanTool') {
      window.location.href = '/cleanTool'; // 跳转到对应的页面
    } else if (index === 'classiFication') {
      window.location.href = '/classiFication';
    } else if (index === 'relationship') {
      window.location.href = '/relationship';
    } else if (index === 'home') {
      window.location.href = '/'; // 跳转到首页
    } else if (index === 'qualitycontrol') {
      window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
    } 
    this.menuActive = index; // 更新高亮状态
  },
         handleClickspan(row) {
      this.selectedRow = row;
      this.dialogVisible = true;
    },
        //开始质控
        statrZK(){
           if(this.activeName === 'second'){
          this.iscenter = 3
         }else if(this.activeName === 'fourth'){
          this.iscenter = 5
         }else{
          this.iscenter = 2
         }
         
        },
        handleClick(tab, event) {
         if(this.activeName === 'second'){
          this.iscenter = 1
         }else if(this.activeName === 'third'){
          this.iscenter = 4
         }else if(this.activeName === 'fourth'){
          this.iscenter = 5
         }else{
          this.iscenter = 1  
         }

          
        },
        allDownload() {
          const that = this;
          axios({
            url: server_url + '/api/get_zip_by_session_id',
            method: 'GET',
            params: { session_id: that.currentSessionId },
            responseType: 'blob' // 告诉 axios 这是一个二进制文件
          }).then(function (response) {
            const blob = new Blob([response.data]);
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;

            // 可选：给文件命名
            const fileName = `下载文件_${that.currentSessionId || 'unknown'}.zip`;
            link.download = fileName;

            document.body.appendChild(link);
            link.click();
            window.URL.revokeObjectURL(downloadUrl);
            document.body.removeChild(link);
          }).catch(function (error) {
            console.log('文件下载失败：', error);
          });
        },
        handleTextPreview() {
          this.htmlTextContent = this.markcontent || '<p>暂无内容</p>';
          this.showTextDialog = true;
        },
        handleModalPreview(type) {
          // 直接展示 previewImages 而不是 modalData[type]
          this.previewImages = [...this.previewImages];  // 可选：确保响应式更新
          this.showImageDialog = true;
        },
        handleRadioClick(row, column, event) {

          let that = this;
          that.showPdfDialog = false;
          that.previewImages = [];
          that.pdfName = row.name; // 获取当前选中的 PDF 文件名
          //  console.log(that.currentSessionId,row.name);
          //   return.
          that.enterpriseLoading = true; // 开始加载状态
          axios.get(`${server_url}/api/${that.currentSessionId}/${row.name}`)
            .then(function (res) {
              that.enterpriseLoading = false; // 结束加载状态
              if (res.data.code !== 0) {
                that.modalData = {
                  image: [
                    '/static/img/sample1.jpeg',
                    '/static/img/sample2.jpeg',
                    '/static/img/sample3.jpeg',
                    '/static/img/sample4.jpeg',
                  ],
                  table: [
                    // '/static/img/table1.png',
                  ],
                  formula: [
                    // '/static/img/formula1.png',
                  ],
                  text: [
                    '/static/img/text1.png',
                  ],

                },

                  that.previewImages = [];
                that.markcontent = '';
                that.showImageDialog = false; // 关闭图片弹窗
                that.showTextDialog = false; // 关闭正文预览弹窗
                that.$message.error('pdf文件暂无数据，请稍后重试');

                return;
              }
              // that.showImageDialog = true;
              const rawPaths = res.data.data.images || [];
              that.previewImages = rawPaths.map(path =>
                `${server_url}/api/static/preview?filename=${encodeURIComponent(path)}`
              );
              that.modalData.image = that.previewImages.slice(0, 4);
              // that.modalData.table = that.previewImages.slice(4, 8);
              // that.modalData.formula = that.previewImages.slice(8, 12);

              that.markcontent = res.data.data.html_content || '';

            })
            .catch(function (error) {
              that.enterpriseLoading = false; // 结束加载状态
              console.log('获取图片失败', error);
            });
        },

        handlePreview(row) {

          let that = this;
          that.currentSessionId = row.session_id; // ← 存储当前 session_id

          axios.get(server_url + `/api/list_pdfs/${row.session_id}`)
            .then(function (res) {
              that.dialogVisible = false;
              const dataList = res.data.data || []; // 后台返回的数组
              // 将数组转成 { label, value } 结构
              that.previewOptions = dataList.map(item => ({
                name: item,  // 显示内容
                value: item   // 实际值（可一样）
              }));


              if (that.previewOptions.length > 0) {
                that.handleRadioClick({ name: that.previewOptions[0].name }, null, null); // 直接调用图片预览方法
              }
              // 打开弹窗


              // that.showPdfDialog = true;
              // 默认选中第一个
              // that.selectedPreview = that.previewOptions.length > 0 ? that.previewOptions[0].value : '';
            })
            .catch(function (error) {
              that.dialogVisible = false;
              console.log(error);
            });
        },










        getTableData() {
          // 这里可以添加获取表格数据的逻辑
          let that = this;

          axios.get(server_url + '/api/upload_list').then(function (res) {
            if (res.data.code === 0) {

              that.$nextTick(() => {
                that.uploadLists = res.data.data;
                console.log(that.uploadLists);
              });

            }
          }).catch(function (error) {
            console.log(error);
            that.enterpriseLoading = false; // Ensure loading state is turned off in case of an error
          });
        },
      }
    });
  </script>
</body>

</html>