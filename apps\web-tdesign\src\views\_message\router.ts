import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:message-square',
      keepAlive: true,
      order: 1000,
      title: '消息中心',
    },
    name: 'Message',
    path: '/Message',
    children: [
      {
        meta: {
          title: '我的消息',
        },
        name: 'MyMessage',
        path: '/MyMessage',
        component: () => import('#/views/_message/index.vue'),
      },
    ],
  },
];

export default routes;
