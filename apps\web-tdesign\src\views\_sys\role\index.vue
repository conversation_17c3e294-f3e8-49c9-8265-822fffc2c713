<script lang="ts" setup>
import type { PageInfo, TableRowData } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import PutAway from '#/components/put-away/index.vue';
import { removalUnderline } from '#/utils/sort';
import { Page, useVbenModal } from '@vben/common-ui';
import {
  Button,
  Card,
  Col,
  Dropdown,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
} from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';

import { roleDeleteBatch, roleListByPageApi } from './api';
import BindAccountModalComponent from './components/bind-account-modal/index.vue';
import BindMenuModalComponent from './components/bind-menu-modal/index.vue';
import BindPermissionModalComponent from './components/bind-permission-modal/index.vue';
import AccountModal from './components/modal/index.vue';
// const { t } = useI18n();

// 表单控制属性
const formData: any = ref({});
const form = ref();
const hideQuery = ref(true);

// 数据表控制属性
const data: any = ref([]);
const loading = ref(false);
const sort = ref([]);
const pagination: any = ref({
  current: 1,
  pageSize: 20,
  total: 0,
});

const stripe = ref(true);
const bordered = ref(true);
const hover = ref(true);
const tableLayout = ref(false);
const size: any = ref('medium');
const showHeader = ref(true);
const selectedRowKeys = ref([]);
const columnControllerVisible = ref(false);
const columns: any = ref([
  {
    colKey: 'row-select',
    type: 'multiple',
    title: '多选',
    width: 64,
  },
  {
    title: '序号',
    colKey: 'serial-number',
    width: 100,
  },
  {
    colKey: 'code',
    title: '角色标识',
    ellipsis: true,
    sorter: true,
  },

  {
    colKey: 'name',
    title: '角色名称',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'status_text',
    title: '状态',
    ellipsis: true,
    sorter: true,
  },

  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'createBy',
    title: '创建人',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'updateBy',
    title: '更新人',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'remark',
    title: '备注',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: '180',
    fixed: 'right',
  },
]);

const status = ref([]);
const groupColumn = ref(true);
const placement = ref<any['placement']>('top-right');
const customText = ref(true);
const displayColumns = ref<any['displayColumns']>([
  'row-select',
  'serial-number',
  'code',
  'name',
  'status_text',
  'remark',
  'createTime',
  'op',
]);
// 列配置
const columnControllerConfig = computed<any['columnController']>(() => ({
  // 列配置按钮位置
  placement: placement.value,
  hideTriggerButton: true,
  // 用于设置允许用户对哪些列进行显示或隐藏的控制，默认为全部字段
  fields: [
    'row-select',
    'serial-number',
    'code',
    'name',
    'status_text',
    'remark',
    'createTime',
    'updateTime',
    'createBy',
    'updateBy',
    'op',
  ],
  // 弹框组件属性透传
  dialogProps: {
    preventScrollThrough: true,
  },
  // 列配置按钮组件属性透传
  buttonProps: customText.value
    ? {
        content: '',
        theme: '',
        variant: 'text',
      }
    : undefined,
  // 数据字段分组显示
  groupColumns: groupColumn.value
    ? [
        {
          label: '业务字段',
          value: 'a',
          columns: ['code', 'name', 'status_text', 'remark'],
        },
        {
          label: '系统字段',
          value: 'b',
          columns: ['serial-number', 'row-select'],
        },
        {
          label: '记录字段',
          value: 'c',
          columns: ['createTime', 'updateTime', 'createBy', 'updateBy'],
        },
      ]
    : undefined,
}));

const fetchData = async (params: any) => {
  loading.value = true;
  try {
    const { records, total } = await roleListByPageApi(params);

    data.value = records;
    pagination.value = {
      ...pagination.value,
      total,
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const loadData = async () => {
  const params = {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };

  await fetchData(params);
};
const rehandlePageChange = (
  pageInfo: PageInfo,
  newDataSource: TableRowData[],
) => {
  // eslint-disable-next-line no-console
  console.log('分页变化', pageInfo, newDataSource);
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};
const onReset = () => {
  form.value.reset();
  loadData();
};
const onSubmit = async () => {
  loadData();
};

const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRowKeys.value = value;
};

const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

onMounted(async () => {
  loadData();
  status.value = await getDictItems('BASE_STATUS');
});

const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: AccountModal,
});

const [BindAccountModal, bindAccountModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: BindAccountModalComponent,
});

const [BindPermissionModal, bindPermissionModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: BindPermissionModalComponent,
});

const [BindMenuModal, bindMenuModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: BindMenuModalComponent,
});

const add = () => {
  modalApi.setState({ title: '新增' });
  modalApi.setData({ refresh: loadData });
  modalApi.open();
};

const edit = (record: any) => {
  modalApi.setState({ title: '编辑' });
  modalApi.setData({ record, refresh: loadData });
  modalApi.open();
};

const bindAccountModalOpen = (record: any) => {
  bindAccountModalApi.setData({ record, refresh: loadData });
  bindAccountModalApi.open();
};

const bindPermissionModalOpen = (record: any) => {
  bindPermissionModalApi.setData({ record, refresh: loadData });
  bindPermissionModalApi.open();
};

const bindMenuModalOpen = (record: any) => {
  bindMenuModalApi.setData({ record, refresh: loadData });
  bindMenuModalApi.open();
};

const handleRowClick = () => {};

const removeBatch = async () => {
  const ids = selectedRowKeys.value.join(',');
  await roleDeleteBatch(ids);
  selectedRowKeys.value = [];
  loadData();
};

const remove = async (row: any) => {
  await roleDeleteBatch(row.id);
  loadData();
};
</script>

<template>
  <Page description="可用于给账号绑定角色权限" title="系统角色列表">
    <Space class="w-full" direction="vertical">
      <Card>
        <AAA />
        <Form
          ref="form"
          :data="formData"
          :label-width="80"
          colon
          @reset="onReset"
          @submit="onSubmit"
        >
          <Row :gutter="[24, 24]">
            <Col :span="4">
              <FormItem label="角色标识" name="code">
                <Input
                  v-model="formData.code"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入角色标识"
                  type="search"
                />
              </FormItem>
            </Col>
            <Col :span="4">
              <FormItem label="角色名称" name="name">
                <Input
                  v-model="formData.name"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入角色名称"
                  type="search"
                />
              </FormItem>
            </Col>

            <Col :span="4">
              <FormItem label="状态" name="status">
                <Select
                  v-model="formData.status"
                  :options="status"
                  clearable
                  placeholder="选择状态"
                />
              </FormItem>
            </Col>

            <Col v-show="hideQuery" :span="4">
              <FormItem label="备注" name="remark">
                <Input
                  v-model="formData.remark"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入备注"
                />
              </FormItem>
            </Col>
          </Row>
          <Row justify="end">
            <Col :span="24" class="mt-4">
              <Space size="small">
                <Button
                  :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
                  theme="primary"
                  type="submit"
                >
                  搜索
                </Button>
                <Button theme="default" type="reset" variant="base">
                  重置
                </Button>
                <PutAway v-model="hideQuery" variant="text" />
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card>
        <div class="t-row--space-between mb-2 flex items-center justify-center">
          <div class="flex flex-wrap items-center justify-center gap-1">
            <div class="t-card__title ml-2">角色列表</div>
            <div
              v-if="selectedRowKeys && selectedRowKeys.length > 0"
              class="text-[gray]"
            >
              已选择 {{ selectedRowKeys?.length || 0 }} 条数据
            </div>
          </div>
          <div class="flex flex-wrap items-center justify-center gap-2">
            <Button @click="add"> 新增 </Button>
            <Popconfirm
              v-if="selectedRowKeys && selectedRowKeys.length > 0"
              content="确定删除？"
              theme="danger"
              @confirm="removeBatch"
            >
              <Button theme="danger"> 批量删除 </Button>
            </Popconfirm>
            <ColumnDisplay v-model="columnControllerVisible" />
          </div>
        </div>

        <Table
          v-model:column-controller-visible="columnControllerVisible"
          v-model:display-columns="displayColumns"
          :bordered="bordered"
          :column-controller="columnControllerConfig"
          :columns="columns"
          :data="data"
          :hover="hover"
          :loading="loading"
          :pagination="pagination"
          :pagination-affixed-bottom="true"
          :selected-row-keys="selectedRowKeys"
          :show-header="showHeader"
          :size="size"
          :sort="sort"
          :stripe="stripe"
          :table-layout="tableLayout ? 'auto' : 'fixed'"
          cell-empty-content="-"
          drag-sort="col"
          lazy-load
          multiple-sort
          resizable
          row-key="id"
          @drag-sort="onDragSort"
          @page-change="rehandlePageChange"
          @row-click="handleRowClick"
          @select-change="rehandleSelectChange"
          @sort-change="sortChange"
        >
          <template #op="slotProps">
            <Space>
              <Link theme="primary" @click="edit(slotProps.row)"> 编辑 </Link>
              <Popconfirm
                content="确定删除？"
                theme="danger"
                @confirm="remove(slotProps.row)"
              >
                <Link theme="danger"> 删除 </Link>
              </Popconfirm>
              <Dropdown
                :options="[
                  {
                    content: '分配账号',
                    value: 'manage',
                    onClick: () => bindAccountModalOpen(slotProps.row),
                  },
                  {
                    content: '分配权限',
                    value: 'manage',
                    onClick: () => bindPermissionModalOpen(slotProps.row),
                  },
                  {
                    content: '分配菜单',
                    value: 'manage',
                    onClick: () => bindMenuModalOpen(slotProps.row),
                  },
                ]"
                trigger="click"
              >
                <Link theme="primary"> 更多 </Link>
              </Dropdown>
            </Space>
          </template>
        </Table>
      </Card>
    </Space>
    <Modal />
    <BindAccountModal />
    <BindPermissionModal />
    <BindMenuModal />
  </Page>
</template>
