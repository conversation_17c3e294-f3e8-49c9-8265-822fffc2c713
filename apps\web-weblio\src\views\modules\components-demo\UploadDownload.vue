<script setup lang="ts">
import { baseDownloadFile, baseUploadFile } from '#/api';
import { Page } from '@vben/common-ui';
import { Button, Card, Space, Upload } from 'tdesign-vue-next';
import { ref } from 'vue';

const files = ref([]);

const uploadRef = ref();
const download = async () => {
  await baseDownloadFile(
    'http://minioshow.ink8s.cncsys.com.cn/acp-boot/1859055803463200770/189aa31e-e268-4417-9810-fa7c1c4c90ae/micro-app-chrome-plugin.zip',
    {},
  );
};

const upload = async (...args: any) => {
  // 通用上传地址

  const res: any = await baseUploadFile('/rgdc-sys/file/upload', {
    file: args[0][0].raw,
  });
  return { status: 'success', response: { url: res.url } };

  // 单文件上传
  // const res: any = await baseUploadFile('/file/upload', {
  //   file: args[0].raw,
  // });
};

const fileShow = () => {
  console.log('files', files.value);
};

const handleRequestFail = (error: any) => {
  console.log('error', error);
};
</script>

<template>
  <Page description="引入VueOfficeDocx组件" title="PDF">
    <Space direction="vertical">
      <Card title="上传文件示例">
        <Space direction="vertical">
          <Upload
            ref="uploadRef"
            multiple
            theme="file-flow"
            v-model="files"
            :show-upload-progress="false"
            :request-method="upload"
            :on-fail="handleRequestFail"
          />

          <Button @click="fileShow"> 文件返回值 </Button>
        </Space>
      </Card>
      <Card title="下载文件示例">
        <Button @click="download"> 下载 </Button>
      </Card>
    </Space>
  </Page>
</template>

<style scoped></style>
