<script setup>
import { ChevronLeftIcon, ChevronRightIcon } from 'tdesign-icons-vue-next';
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 侧边栏折叠状态，默认收起
const isCollapsed = ref(true);

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 处理URL参数自动跳转
const handleAutoNavigation = () => {
  const toolParam = route.query.tool;
  if (toolParam && route.path === '/data-tools-detail') {
    // 根据tool参数自动跳转到对应的子路由
    router.replace(`/data-tools-detail/${toolParam}`);
  }
};

// 监听路由变化
watch(() => route.query.tool, handleAutoNavigation, { immediate: true });
</script>

<template>
  <div class="data-tools-detail">
    <!-- 左侧菜单 -->
    <div class="sidebar" :class="{ collapsed: isCollapsed }">
      <!-- 折叠按钮 -->
      <button class="collapse-btn" @click="toggleSidebar">
        <ChevronRightIcon v-if="isCollapsed" />
        <ChevronLeftIcon v-else />
      </button>

      <!-- 菜单项 -->
      <div class="menu-items">
        <router-link
          to="/data-tools-detail/analytical-recombination"
          class="menu-item"
          active-class="active"
        >
          <span v-if="!isCollapsed">全文多模态解析重组工具</span>
        </router-link>
        <router-link
          to="/data-tools-detail/relationship"
          class="menu-item"
          active-class="active"
        >
          <span v-if="!isCollapsed">知识对象及关系挖掘工具</span>
        </router-link>
        <router-link
          to="/data-tools-detail/relationship-result"
          class="menu-item"
          active-class="active"
        >
          <span v-if="!isCollapsed">知识对象及关系挖掘工具-结果</span>
        </router-link>
        <router-link
          to="/data-tools-detail/data-cleaning"
          class="menu-item"
          active-class="active"
        >
          <span v-if="!isCollapsed">数据汇聚与清洗工具</span>
        </router-link>
        <router-link
          to="/data-tools-detail/data-classification"
          class="menu-item"
          active-class="active"
        >
          <span v-if="!isCollapsed">数据整编与分类工具</span>
        </router-link>
        <router-link
          to="/data-tools-detail/quality-control"
          class="menu-item"
          active-class="active"
        >
          <span v-if="!isCollapsed">质量控制工具</span>
        </router-link>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 面包屑导航 -->
      <!-- 删除这部分 -->

      <!-- 路由出口，显示子路由组件 -->
      <router-view />

      <!-- 默认欢迎页面 -->
      <div v-if="route.path === '/data-tools-detail'" class="welcome-page">
        <div class="welcome-content">
          <h2>欢迎使用数据工具</h2>
          <p>请从左侧菜单选择您需要的工具开始使用</p>
          <div class="tool-cards">
            <div
              class="tool-card"
              @click="
                router.push('/data-tools-detail/analytical-recombination')
              "
            >
              <i class="icon-document"></i>
              <h3>全文多模态解析重组工具</h3>
              <p>支持多种格式文档的智能解析和重组</p>
            </div>
            <div
              class="tool-card"
              @click="router.push('/data-tools-detail/relationship')"
            >
              <i class="icon-network"></i>
              <h3>知识对象及关系挖掘工具</h3>
              <p>智能挖掘和分析知识对象之间的关系</p>
            </div>
            <div
              class="tool-card"
              @click="router.push('/data-tools-detail/data-cleaning')"
            >
              <i class="icon-clean"></i>
              <h3>数据汇聚与清洗工具</h3>
              <p>高效的数据汇聚、清洗和预处理功能</p>
            </div>
            <div
              class="tool-card"
              @click="router.push('/data-tools-detail/data-classification')"
            >
              <i class="icon-classify"></i>
              <h3>数据整编与分类工具</h3>
              <p>智能数据整编和分类管理</p>
            </div>
            <div
              class="tool-card"
              @click="router.push('/data-tools-detail/quality-control')"
            >
              <i class="icon-quality"></i>
              <h3>质量控制工具</h3>
              <p>全面的数据质量检测和控制</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.data-tools-detail {
  display: flex;
  min-height: 100vh;
  background: #f5f7fa;
}

.sidebar {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  position: relative;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 64px;
}

.collapse-btn {
  position: absolute;
  top: 20px;
  right: -12px;
  width: 24px;
  height: 24px;
  background: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.collapse-btn:hover {
  background: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.menu-items {
  padding: 60px 16px 20px 16px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  margin-bottom: 8px;
  color: #606266;
  text-decoration: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.sidebar.collapsed .menu-item {
  padding: 14px 8px;
  justify-content: center;
  margin: 8px 4px;
}

.menu-item:hover {
  background: #ecf5ff;
  color: #409eff;
}

.menu-item.active {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.main-content {
  flex: 1;
  background: #f5f7fa;
}

.breadcrumb {
  margin-bottom: 20px;
  padding: 12px 0;
  font-size: 14px;
  color: #909399;
}

.breadcrumb a {
  color: #409eff;
  text-decoration: none;
}

.breadcrumb a:hover {
  color: #66b1ff;
}

.welcome-page {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.welcome-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.welcome-content h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 16px;
  font-weight: 600;
}

.welcome-content p {
  font-size: 16px;
  color: #606266;
  margin-bottom: 40px;
  line-height: 1.6;
}

.tool-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

.tool-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 32px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #409eff;
}

.tool-card i {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
  display: block;
}

.tool-card h3 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 12px;
  font-weight: 600;
}

.tool-card p {
  font-size: 14px;
  color: #909399;
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-tools-detail {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .sidebar.collapsed {
    height: 60px;
    width: 100%;
  }

  .menu-items {
    display: flex;
    overflow-x: auto;
    padding: 10px;
  }

  .menu-item {
    min-width: 120px;
    margin-right: 8px;
  }
}
</style>
