import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据搜索',
    },
    name: 'tSearch',
    path: '/tSearch',
    children: [
      {
        meta: {
          title: '数据搜索',
        },
        name: 'tSearchIndex',
        path: '/tSearch/index',
        component: () => import('#/views/modules/tSearch/index.vue'),
      },
      {
        meta: {
          title: '数据搜索结果详细',
        },
        name: 'tSearchResultDetail',
        path: '/tSearch/tSearchResultDetail',
        component: () => import('#/views/modules/tSearch/dataDetails.vue'),
      },
      {
        meta: {
          title: '数据搜索结果详细',
        },
        name: 'tSearchPdf',
        path: '/tSearch/tSearchPdf',
        component: () => import('#/views/modules/tSearch/dataDetailsPdf.vue'),
      },
    ],
  },
];

export default routes;
