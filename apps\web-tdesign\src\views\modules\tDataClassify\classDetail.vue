<script setup lang="ts">
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import {
  AddCircleIcon,
  DeleteIcon,
  RefreshIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Link,
  type PageInfo,
  Popconfirm,
  EnhancedTable,
  Space,
  Table, TreeSelect, Tree,Tag
} from 'tdesign-vue-next';

import { Input } from 'tdesign-vue-next';
import { DatePicker } from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import { Select } from 'tdesign-vue-next';
import { collectByTree } from '#/views/modules/tDataClassify/api';
import {Page} from "@vben/common-ui";
import {useRoute, useRouter} from "vue-router";

const route = useRoute()
const props = defineProps({
});

const treeSelectData: any = ref([]);
const searchForm = ref();
const formData: any = ref({});
const reqRunner = {
  collectByTree: useRequest(collectByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      treeData.value = []
      realList.value = res
      for(const item of res){
        let list = []
        treeDataFormat(item,list)
        treeData.value.push(list[0])
      }
    },
  }),
  searchCollectByTree: useRequest(collectByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      treeData.value = []
      for(const item of res){
        let list = []
        treeDataFormat(item,list)
        treeData.value.push(list[0])
      }
    },
  }),
};
const realList = ref()
const treeDataFormat = (node,list) => {
  let obj = {value:'',label:'',children:[]}
  obj.value = node.classifyCode
  obj.label = node.classifyName + '|' + node.num
  if(node.children){
    for(const item of node.children){
      treeDataFormat(item,obj.children)
    }
  }
  list.push(obj)
}
const reload = async (data?: any) => {
  reqRunner.collectByTree.run(formData.value);
};

const searchMethod = (data?: any) => {
  const { run, loading } = reqRunner.searchCollectByTree;
  run(formData.value);
};
const searchFormSubmit = () => {
  searchMethod();
};
const resetSearch = () => {
  formData.value={}
  searchForm.value.reset();
  reload();
};

onMounted(async () => {
  await reload();
});
const category = ref();
const treeData = ref([]);
const getNum = (value) => {
  return getChildrenNum(realList.value,value)
};
const getChildrenNum = (list,value) => {
  let num = 0
  for(const item of list) {
    if (item.classifyCode == value) {
      num = item.num
      break
    }
    if (item.children) {
      getChildrenNum(item.children)
    }
  }
  return num
};
const router = useRouter()
const toSearch = (value) => {
  router.push({name: 'tSearchIndex', query: {CategoryCode: value}})
};
</script>

<template>
  <Page title="分类汇总">
    <Space :size="8" class="tiny-tdesign-style-patch w-full" direction="vertical">
      <Card>
        <Form ref="searchForm" :data="formData" class="w-full" @reset="resetSearch" @submit="searchFormSubmit">
          <div class="grid w-full grid-cols-3 gap-1">
            <FormItem label="选择分类" name="secretLevel">
              <TreeSelect  v-model="formData.parentCode" :data="realList"
                :keys="{
                    value: 'classifyCode',
                    label: 'classifyName',
                    children: 'children'
                  }"
                placeholder="请选择" clearable
              />
            </FormItem>
            <FormItem  label="类型名称" name="classifyName">
              <Input v-model="formData.classifyName" clearable placeholder="请输入内容" />
            </FormItem>
          </div>
          <div class="mt-2 flex items-center justify-end space-x-2">
            <Button theme="primary" type="submit"><template #icon><SearchIcon /></template>
              查询
            </Button>
            <Button theme="default" type="reset"><template #icon><RefreshIcon /></template>
              重置
            </Button>
          </div>
        </Form>
      </Card>
      <Card>
        <h1 style="font-size: 30px; line-height: 56px;text-align: left;margin-bottom: 20px;font-weight: bold;
        font-family: BlinkMacSystemFont, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;">数据分类汇总</h1>
        <Space>
          <div v-if="treeData.length > 0" class="ml-5">
            <Tree ref="tree" :data="treeData" expand-all line>
              <template #label="{ node }">
                <span>{{ node.label.split('|')[0] }}</span>
                <Tag style="margin-left: 10px" theme="primary" variant="outline" @click="toSearch(node.value)">
<!--                  {{getNum(node.value)}}-->
                  {{ node.label.split('|')[1] }}
                </Tag>
              </template>
            </Tree>
          </div>
        </Space>
      </Card>
    </Space>
  </Page>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
