<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>化工知识协同加工与管理平台</title>
    <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
    <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
    <link rel="stylesheet" href="../static/vue/theme/index.css">
    <script src="../static/vue/min/vue.min.js"></script>
    <script src="../static/vue/element-ui2.15.13_index.js"></script>
    <script src="../static/vue/axios0.26.0_axios.min.js"></script>
    <style>
        html,
        body {
            min-width: 100%;
        }

        .mon_warp {
            margin: 0px;
            width: 100%;
            background-size: cover;
        }

        .mon_body {
            display: none;
            width: 100%;
        }

        .el-menu-vertical-demo {
            height: 100%;
        }

        .el-card {
            margin-top: 20px;
        }

        .el-upload__tip {
            margin-top: 10px;
        }

        .clearfix:before,
        .clearfix:after {
            display: table;
            content: "";
        }

        .clearfix:after {
            clear: both;
        }

        .center {
            border: 1px solid #ccc;
            width: 100%;
            margin: 20px auto 20px;
            border-radius: 20px;
            padding: 20px;
            min-width: 1200px;
        }

        .upload-demo {
            width: 100%;
        }

        .el-upload-dragger {
            width:300px;
            height: 300px;
            padding: 40px;
        }

        .el-upload__text {
            font-size: 16px;
            margin: 20px 0;
        }

        .el-icon-upload {
            font-size: 67px;
            margin: 20px 0;
        }

        .el-menu-item.is-active {
            background-color: #ecf5ff;
            color: #409EFF;
        }

        .el-menu-item {
            font-size: 14px;
            height: 56px;
            line-height: 56px;
        }

        /* .el-menu-item:hover {
            background-color: #ecf5ff;
        } */

        .download-notice {
            font-size: 14px;
            color: #666;
            display: inline-block;
            margin-top: 10px;
        }

        .notice-icon {
            color: #ff9800;
            margin-right: 5px;
        }

        .download-all-container {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .left {
            width: 70%;
        }

        .titleright {
            font-size: 20px;
            color: #808080;
            border-bottom: 1px solid #bfbfbf;
            margin-bottom: 22px;
        }

        .checkboxbig {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }

        .checkbox_li {
            width: 23%;
            font-size: 16px;
        }

        .checkbox_li .el-checkbox-group {
            display: flex;
            flex-direction: column;
        }

        .checkbox_li .el-checkbox {
            margin-top: 10px;
        }

        /* New tab styles */
        .tab-buttons {
            display: flex;
            justify-content: left;
        }

        .tab-button {
            padding: 10px 33px;
            margin: 0 10px;
            cursor: pointer;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
            color: #333;
            background-color: #f5f5f5;
            transition: all 0.3s;
        }

        .tab-button.active {
            background-color: #409EFF;
            color: #fff;
            border-color: #409EFF;
        }

        .np_input_senior {
            width: 100%;
            min-height: 200px;
            margin-top: 15px;
            background: #f6f6f6;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }

        .bre_body {
            padding: 40px 0px 40px 0px;
            width: 100%;
            max-width: 800px;
            margin: auto;
        }

        .bre_content {
            width: 100%;
        }

        .bre_cols {
            width: 100%
        }

        .bre_content+.bre_content {
            margin-top: 15px;
        }

        .bre_col_fields {
            width: 230px;
            float: left;
        }

        .bre_col_values {
            width: calc(100% - 240px);
            float: right;
        }

        .dc_col_condition {
            width: 90px;
            float: left;
        }

        .bre_col_field {
            width: 130px;
            float: left;
            margin-left: 10px;
        }

        .bre_col_match {
            width: 90px;
            float: left;
            margin-left: 10px;
        }

        .bre_col_value {
            width: calc(100% - 294px);
            float: left;
            margin-left: 10px;
        }

        .bre_col_btn {
            width: 44px;
            float: right;
            margin-top: 4px;
        }

        .bre_btn {
            margin-top: 20px;
        }

        .np_table_history {
            margin-top: 15px;
        }

        .mls_card_list p {
            padding: 3px 0px 0px 0px;
            line-height: 22px;
        }

        .mls_card_abstract {
            height: 66px;
        }

        .mls_card_content {
            padding-bottom: 15px;
            font-size: 13px;
            margin-top: 5px;
        }

        .np_title_icon {
            top: 0px;
        }

        .datatitle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 20px;
        }



        /* .tab-button:hover {
            background-color: #e5e5e5;
        } */
    </style>
</head>

<body>
    <div class="header_app" id="header_app"></div>
    {% raw %}
    <div class="mon_warp clearfix" id="app">
        <div class="mon_body clearfix">
            <el-row :gutter="20">
                <!-- 左侧菜单 -->
                <el-col :span="3">
                    <div style="padding-top:20px">
                        <el-menu :default-active="menuActive" class="el-menu-vertical-demo" @select="handleMenuSelect"
                            style="padding-top:20px;" active-text-color="#409EFF">
                            <el-menu-item index="cleanTool">
                                <el-tooltip content="数据汇聚与清洗工具" placement="right">
                                    <span
                                        style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                        数据汇聚与清洗工具
                                    </span>
                                </el-tooltip>
                            </el-menu-item>
                            <el-menu-item index="classiFication">

                                <el-tooltip content="数据整编与分类工具" placement="right">
                                    <span
                                        style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                        数据整编与分类工具
                                    </span>
                                </el-tooltip>
                            </el-menu-item>
                            <el-menu-item index="home">
                                <el-tooltip content="全文多模态解析重组工具" placement="right">
                                    <span
                                        style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                        全文多模态解析重组工具
                                    </span>
                                </el-tooltip>
                            </el-menu-item><el-menu-item index="relationship">
                                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                                    <span
                                        style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                        知识对象及关系挖掘工具
                                    </span>
                                </el-tooltip>
                            </el-menu-item>
                            <el-menu-item index="qualitycontrol">
                                <el-tooltip content="质量控制工具" placement="right">
                                    <span
                                        style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                        质量控制工具
                                    </span>
                                </el-tooltip>
                            </el-menu-item>

                        </el-menu>
                    </div>
                </el-col>

                <!-- 右侧内容 -->
                <el-col :span="21">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <h2>数据整编与分类工具</h2>
                        </div>
                        <div>
                            <p style="font-size: 16px;">
                                数据整编分类工具旨在通过数据关联汇聚、抽取与采集等方法获取目标科技文献的体系化元数据，元数据字段覆盖论文基础信息、作者信息、发文期刊信息、作者所属机构信息、基金资助信息等；此外，为了满足科技文献的多样化使用场景，基于科技文献元数据，融合大模型、深度学习等智能技术对科技文献进行多体系分类。

                            </p>
                            <div style="text-align: right;width: 100%;padding-top: 10px;">
                            </div>
                            <div style="display: flex;justify-content: space-between;">
                                <div class="tab-buttons">
                                    <div class="tab-button" :class="{ active: activeTab === 'tab1' }"
                                        @click="activeTab = 'tab1'">整编</div>
                                    <!-- <div class="tab-button" :class="{ active: activeTab === 'tab2' }"
                                        @click="activeTab = 'tab2'">分类</div> -->
                                </div>

                                <el-button type="success" style="margin-right: 60px;"
                                    @click="startopen">任务列表</el-button>
                            </div>
                            <!-- New tab buttons -->


                            <div class="center" v-if="step1">
                                <el-row :gutter="20">
                                    <!-- 左侧菜单 (30%) -->
                                    <el-col :span="10">

                                        <div
                                            style="width: 100%;border-right: 2px solid rgb(128, 128, 128);padding-right: 30px;">
                                            <div style="margin: 28px;">
                                                <el-checkbox-group v-model="form.checkList"
                                                    style="margin-bottom: 20px;">
                                                    <el-checkbox label="论文">论文</el-checkbox>
                                                    <el-checkbox label="专利" disabled>专利</el-checkbox>
                                                    <el-checkbox label="图书" disabled>图书</el-checkbox>
                                                </el-checkbox-group>
                                            </div>

                                            <div style="width: 100%;">
                                                <el-form :model="form" label-width="80px">
                                                    <el-form-item label="任务名称" required>
                                                        <el-input v-model="task_name" placeholder="请输入"
                                                            style="width: 100%;"></el-input>
                                                    </el-form-item>
                                                    <el-form-item label="DOI" required>
                                                        <el-input v-model="doi" placeholder="请输入"
                                                            style="width: 100%;"></el-input>
                                                    </el-form-item>
                                                    <el-form-item label="DOI列表">

                                                        <el-upload class="upload-demo" drag :show-file-list="false"
                                                            action="/query_file_json"
                                                            :before-upload="($event) =>{beforeAvatarUploadFile($event, 'need_publish_trailer')}"
                                                            multiple>
                                                            <i class="el-icon-upload"></i>
                                                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                                            <div class="el-upload__text"> <el-button type="primary" @click="downLoadTemplate">下载模板</el-button></div>
                                                            <div class="el-upload__tip" slot="tip">仅支持Excel文件上传</div>
                                                        </el-upload>

                                                    </el-form-item>
                                                </el-form>
                                            </div>
                                        </div>


                                    </el-col>

                                    <!-- 右侧内容 (70%) -->
                                    <el-col :span="14">
                                        <div style="padding: 10px;">
                                            <div class="titleright"
                                                v-html="activeTab === 'tab1'?'自定义导出选项，元数据字段：':'自定义分类体系：'">

                                            </div>
                                            <div class="checkboxbig" v-if="activeTab === 'tab1'">
                                                <!-- Area 1: 作者和标题相关 -->
                                                <div class="checkbox_li">
                                                    <el-checkbox :indeterminate="isIndeterminate1" v-model="checkAll1"
                                                        @change="handleCheckAllChange(1, $event)">全选</el-checkbox>
                                                    <div style="margin: 6px 0; border-bottom:1px solid #808080;"></div>
                                                    <el-checkbox-group v-model="checkedCities1"
                                                        @change="handleCheckedCitiesChange(1, $event)">
                                                        <el-checkbox v-for="item in cities1" :label="item.key"
                                                            :key="item.key">{{item.label}}</el-checkbox>
                                                    </el-checkbox-group>
                                                </div>

                                                <!-- Area 2: 摘要和出版相关 -->
                                                <div class="checkbox_li">
                                                    <el-checkbox :indeterminate="isIndeterminate2" v-model="checkAll2"
                                                        @change="handleCheckAllChange(2, $event)">全选</el-checkbox>
                                                    <div style="margin: 6px 0; border-bottom:1px solid #808080;"></div>
                                                    <el-checkbox-group v-model="checkedCities2"
                                                        @change="handleCheckedCitiesChange(2, $event)">
                                                        <el-checkbox v-for="item in cities2" :label="item.key"
                                                            :key="item.key">{{item.label}}</el-checkbox>
                                                    </el-checkbox-group>
                                                </div>

                                                <!-- Area 3: 引用和基金相关 -->
                                                <div class="checkbox_li">
                                                    <el-checkbox :indeterminate="isIndeterminate3" v-model="checkAll3"
                                                        @change="handleCheckAllChange(3, $event)">全选</el-checkbox>
                                                    <div style="margin: 6px 0; border-bottom:1px solid #808080;"></div>
                                                    <el-checkbox-group v-model="checkedCities3"
                                                        @change="handleCheckedCitiesChange(3, $event)">
                                                        <el-checkbox v-for="item in cities3" :label="item.key"
                                                            :key="item.key">{{item.label}}</el-checkbox>
                                                    </el-checkbox-group>
                                                </div>

                                                <!-- Area 4: 标识符相关 -->
                                                <div class="checkbox_li">
                                                    <el-checkbox :indeterminate="isIndeterminate4" v-model="checkAll4"
                                                        @change="handleCheckAllChange(4, $event)">全选</el-checkbox>
                                                    <div style="margin: 6px 0; border-bottom:1px solid #808080;"></div>
                                                    <el-checkbox-group v-model="checkedCities4"
                                                        @change="handleCheckedCitiesChange(4, $event)">
                                                        <el-checkbox v-for="item in cities4" :label="item.key"
                                                            :key="item.key">{{item.label}}</el-checkbox>
                                                    </el-checkbox-group>
                                                </div>
                                            </div>


                                            <div class="checkboxbig" v-else>
                                                <div class="checkbox_li">
                                                    <el-checkbox-group v-model="checkedCities1"
                                                        @change="handleCheckedCitiesChange(1, $event)">
                                                        <el-checkbox v-for="item in pilations" :label="item.key"
                                                            :key="item.key">{{item.label}}</el-checkbox>
                                                    </el-checkbox-group>
                                                </div>
                                            </div>
                                        </div>

                            </div>

                            <div class="center" v-else-if="activeTab === 'tab1' && !step1">
                                <!-- <div class="np_input_senior clearfix">
                                    <div class="bre_body np_from">
                                        <div class="bre_content clearfix" v-for="(item, index) in searchContent">
                                            <div class="bre_cols clearfix" v-if="index == 0">
                                                <div class="bre_col_fields">
                                                    <el-select v-model="item.field">
                                                        <el-option value="Article_Title"
                                                            label="Article_Title"></el-option>
                                                        <el-option value="DOI" label="DOI"></el-option>
                                                        <el-option value="Publication_Year"
                                                            label="Publication_Year"></el-option>
                                                        <el-option value="Source_Title"
                                                            label="Source_Title"></el-option>
                                                    </el-select>
                                                </div>

                                                <div class="bre_col_values">
                                                    <el-input v-model="item.value" placeholder="请输入并检索"></el-input>
                                                </div>
                                            </div>
                                            <div class="bre_cols clearfix" v-else>
                                                <div class="dc_col_condition">
                                                    <el-select v-model="item.condition" placeholder="请选择"
                                                        style="width: 100%;">
                                                        <el-option v-for="item in conditionList" :key="item.value"
                                                            :label="item.label" :value="item.value"></el-option>
                                                    </el-select>
                                                </div>
                                                <div class="bre_col_field">
                                                    <el-select v-model="item.field">
                                                        <el-option value="Article_Title"
                                                            label="Article_Title"></el-option>
                                                        <el-option value="DOI" label="DOI"></el-option>
                                                        <el-option value="Publication_Year"
                                                            label="Publication_Year"></el-option>
                                                        <el-option value="Source_Title"
                                                            label="Source_Title"></el-option>
                                                    </el-select>
                                                </div>
                                                <div class="bre_col_value">
                                                    <el-input v-model="item.value" placeholder="请输入"></el-input>
                                                </div>
                                                <div class="bre_col_btn">
                                                    <el-button size="small" @click="delete_searchContent(item, index)"
                                                        type="danger" icon="el-icon-minus"></el-button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="bre_btn clearfix">
                                            <el-button @click="add_searchContent" size="small" type="primary"><i
                                                    class="el-icon-plus"></i> <span>添加行</span></el-button>
                                            <div style="float: right">
                                                <el-button size="small" type="primary" @click="generalSearch"
                                                    :loading="dataLoading">&nbsp;&nbsp;&nbsp;检&nbsp;&nbsp;索&nbsp;&nbsp;&nbsp;</el-button>
                                                <el-button size="small"
                                                    @click="clearGeneralSQL">&nbsp;&nbsp;&nbsp;清&nbsp;&nbsp;除&nbsp;&nbsp;&nbsp;</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div> -->
                                <div style="text-align:right;">
                                    <el-button type="success" style="margin-right: 44px;margin-top: 20px;"
                                        @click="batchDown">批量导出</el-button>
                                </div>
                                <el-table :data="tableData" stripe size="medium" :header-cell-style="{
    backgroundColor: '#f5f7fa',  
    color: '#333',              
    fontWeight: 'bold',
  }" border header-align="center" style="width: 100%; margin-top: 20px;">
                                    <!-- 动态列 -->
                                    <el-table-column v-for="column in head" :key="column.key" :prop="column.key"
                                        :label="column.label" align="center" show-overflow-tooltip min-width="160">
                                        <template #default="scope">
                                            {{ scope.row[column.key] || '--' }}
                                        </template>
                                    </el-table-column>

                                    <!-- 操作列 -->
                                    <el-table-column label="导出选项" align="center" width="200">
                                        <template #default="scope">
                                            <el-button type="text" size="small"
                                                @click="handleDownload(scope.row,'excel')">EXCEL</el-button>
                                            <el-button type="text" size="small"
                                                @click="handleDownload(scope.row,'json')">文本文件</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>


                            <div class="center" v-else-if="activeTab === 'tab2' && !step1">
                                <!-- <div class="np_input_senior clearfix">
                                    <div class="bre_body np_from">
                                        <div class="bre_content clearfix" v-for="(item, index) in searchContent">
                                            <div class="bre_cols clearfix" v-if="index == 0">
                                                <div class="bre_col_fields">
                                                    <el-select v-model="item.field">
                                                        <el-option value="Article_Title"
                                                            label="Article_Title"></el-option>
                                                        <el-option value="DOI" label="DOI"></el-option>
                                                        <el-option value="Publication_Year"
                                                            label="Publication_Year"></el-option>
                                                        <el-option value="Source_Title"
                                                            label="Source_Title"></el-option>
                                                    </el-select>
                                                </div>

                                                <div class="bre_col_values">
                                                    <el-input v-model="item.value" placeholder="请输入并检索"></el-input>
                                                </div>
                                            </div>
                                            <div class="bre_cols clearfix" v-else>
                                                <div class="dc_col_condition">
                                                    <el-select v-model="item.condition" placeholder="请选择"
                                                        style="width: 100%;">
                                                        <el-option v-for="item in conditionList" :key="item.value"
                                                            :label="item.label" :value="item.value"></el-option>
                                                    </el-select>
                                                </div>
                                                <div class="bre_col_field">
                                                    <el-select v-model="item.field">
                                                        <el-option value="Article_Title"
                                                            label="Article_Title"></el-option>
                                                        <el-option value="DOI" label="DOI"></el-option>
                                                        <el-option value="Publication_Year"
                                                            label="Publication_Year"></el-option>
                                                        <el-option value="Source_Title"
                                                            label="Source_Title"></el-option>
                                                    </el-select>
                                                </div>
                                                <div class="bre_col_value">
                                                    <el-input v-model="item.value" placeholder="请输入"></el-input>
                                                </div>
                                                <div class="bre_col_btn">
                                                    <el-button size="small" @click="delete_searchContent(item, index)"
                                                        type="danger" icon="el-icon-minus"></el-button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="bre_btn clearfix">
                                            <el-button @click="add_searchContent" size="small" type="primary"><i
                                                    class="el-icon-plus"></i> <span>添加行</span></el-button>
                                            <div style="float: right">
                                                <el-button size="small" type="primary" @click="generalSearch"
                                                    :loading="dataLoading">&nbsp;&nbsp;&nbsp;检&nbsp;&nbsp;索&nbsp;&nbsp;&nbsp;</el-button>
                                                <el-button size="small"
                                                    @click="clearGeneralSQL">&nbsp;&nbsp;&nbsp;清&nbsp;&nbsp;除&nbsp;&nbsp;&nbsp;</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div> -->
                                <div style="text-align:right;">
                                    <el-button type="success"
                                        style="margin-right: 44px;margin-top: 20px;">批量导出</el-button>
                                </div>
                                <el-table :data="tableData" stripe size="medium" :header-cell-style="{
        backgroundColor: '#f5f7fa',  
        color: '#333',              
        fontWeight: 'bold'          
      }" border header-align="center" style="width: 100%;margin-top: 20px;">
                                    <el-table-column prop="doi" label="DOI" width="180"></el-table-column>
                                    <el-table-column prop="field1" label="中图法分类" width="180"></el-table-column>
                                    <el-table-column prop="field2" label="基金委学部代码"></el-table-column>
                                    <el-table-column prop="field3" label="教育部学科分类"></el-table-column>
                                    <el-table-column label="导出选项">
                                        <template slot-scope="scope">
                                            <el-button type="text">EXCEL</el-button>
                                            <el-button type="text">文本文件</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                </el-col>
            </el-row>
        </div>


    </div>
    <div style="text-align: right;width: 100%;margin-top: 10px;">
        <el-button type="primary" style="margin-right: 60px;" v-if="activeTab === 'tab1' && step1"
            @click="step1Event">开始整编</el-button>
        <el-button type="primary" style="margin-right: 60px;" v-if="activeTab === 'tab2' && step1"
            @click="stepAnother">开始分类</el-button>
        <el-button type="primary" style="margin-right: 60px;" v-if="step2" @click="closeStart">上一步</el-button>
    </div>
    <div class="download-all-container">
        <div class="download-notice">
            <span class="notice-icon">⚠</span>解构过程中请勿刷新页面
        </div>
    </div>
    </el-card>
    </el-col>
    </el-row>

    <!-- 上传记录对话框 -->
    <el-dialog title="任务列表" :visible.sync="dialogVisible" width="60%">
        <el-table :data="taskList" style="width: 100%;" :border="true" stripe>
            <el-table-column prop="created_at" label="任务时间" width="180"></el-table-column>
             <el-table-column prop="task_name" label="任务名称"></el-table-column>
            <el-table-column prop="file_name" label="文件名称"></el-table-column>
            <el-table-column label="操作" width="180">
                <template slot-scope="scope">
                    <el-button size="mini" @click="handlePreview(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div slot="footer" class="dialog-footer" v-if="total > 10">
            <el-pagination background layout="prev, pager, next" @current-change="queryCurrentChange"
                @size-change="queryHandleSizeChange" :page-size="pageSize" style="text-align: center;" :total="total"
                :current-page="page">
            </el-pagination>
        </div>
    </el-dialog>
    </div>
    </div>
    {% endraw %}
    <div id="dataid" data="{{session.user}}" style="display:none"></div>
    <div class="mon_footer"></div>

    <script src="../static/js/jquery.min.js"></script>
    <script src="../static/js/monitor.js"></script>

    <script>
        let _this = this;

        const vm = new Vue({
            el: '#app',
            data: {
                activeIndex: '3',
                task_name: '',
                menuActive: 'classiFication',
                doi: '',
                fileList: [],
                btnStart: "开始汇聚",
                dialogVisible: false,
                uploadLists: [],
                fileList: [],
                page: 1,
                pageSize: 10,
                assetsTotal: 0,
                checkAll1: false,
                checkedCities1: [

                ],
                cities1: [

                ],
                isIndeterminate1: false,
                checkAll2: false,
                checkedCities2: [
                    '地址',
                    '所属机构',
                    '文献类型',
                    '关键词',
                    'WoS 类别',
                    '研究方向',
                    'WoS 版本 (仅限打印)'
                ],// 默认选中"文献类型"
                cities2: [
                    '地址',
                    '所属机构',
                    '文献类型',
                    '关键词',
                    'WoS 类别',
                    '研究方向',
                    'WoS 版本 (仅限打印)'
                ],
                isIndeterminate2: false,
                checkAll3: false,
                checkedCities3: [

                ],
                cities3: [

                ],
                isIndeterminate3: false,
                checkAll4: false,
                checkedCities4: [], // 默认不选中任何项
                cities4: [

                ],
                pilations: [],
                isIndeterminate4: false,
                form: {
                    checkList: ['论文'],
                    block1Fields: [],
                    block2Fields: [],
                    block3Fields: [],
                    block4Fields: [],
                },
                tableData: [
                    {
                        doi: '10.1001/example1',
                        field1: '数据1-1',
                        field2: '数据1-2',
                        field3: '数据1-3',
                        field4: '数据1-4'
                    },
                    {
                        doi: '10.1002/example2',
                        field1: '数据2-1',
                        field2: '数据2-2',
                        field3: '数据2-3',
                        field4: '数据2-4'
                    },
                    {
                        doi: '10.1003/example3',
                        field1: '数据3-1',
                        field2: '数据3-2',
                        field3: '数据3-3',
                        field4: '数据3-4'
                    },
                    {
                        doi: '10.1004/example4',
                        field1: '数据4-1',
                        field2: '数据4-2',
                        field3: '数据4-3',
                        field4: '数据4-4'
                    },
                    {
                        doi: '10.1005/example5',
                        field1: '数据5-1',
                        field2: '数据5-2',
                        field3: '数据5-3',
                        field4: '数据5-4'
                    }
                ],

                checkAll: false,
                indeterminate: true,
                activeTab: 'tab1', // Default active tab
                step1: true,
                step2: false,
                uploadLists: [],

                taskList: [],
                head: [],
                task_id: null,
                conditionList: [
                    { value: 'AND', label: 'AND' },
                    { value: 'OR', label: 'OR' },
                    { value: '<>', label: 'NOT' },
                ],
                loading: false,
                total: 0,
                dataLoading: false,
                fileName: '', // 新增：文件名称输入框
                searchContent: [
                    {
                        'condition': 'AND',
                        'field': 'Article_Title',
                        'value': '',


                    },
                    {
                        'condition': 'AND',
                        'field': 'Article_Title',
                        'value': '',

                    }
                ],
                files: null, // 用于存储上传的文件对象
                key_list: []
            },
            mounted() {
                $('.mon_body').css({ 'display': 'revert' });
                this.getCheckboxValue()
            },
            methods: {
                downLoadTemplate() {
                    window.open(server_url + '/api/datacomp/download_template')
                },
                queryCurrentChange(val) {
                    this.page = val;
                    this.getTableData();

                },
                queryHandleSizeChange(val) {
                    this.page = 1;
                    this.pageSize = val;
                    this.getTableData();

                },

                //开始整编
                getInfoList() {

                    let that = this
                    if (!that.doi && !that.files) {
                        that.$message.error('需要输入一个DOI或者上传至少一个含有DOI数据的EXCEL文件！');
                        return;
                    }
                    let formData = new FormData();
                    if (that.files) {
                        formData.append('dois_file', that.files); // 附加原始文件
                    }
                    formData.append('doi', that.doi);
                    formData.append('key_list', JSON.stringify([...that.checkedCities1, ...that.checkedCities2, ...that.checkedCities3, ...that.checkedCities4]));
                    formData.append('task_name', that.task_name);
                    axios.post(`${server_url}/api/datacomp/query_by_dois`, formData, {
                        headers: { 'Content-Type': 'multipart/form-data' }
                    }).then(function (res) {

                        const data = res.data
                        if (data.code == 0) {
                            that.$message.success('整编成功！');
                            that.head = data.data.head;
                            that.tableData = data.data.result;
                            that.task_id = data.data.task_id; // 保存 task_id

                            // location.href = `./cleanTask?table_name=${data.data.table_name}&task_name=${data.data.task_name}`;
                        }
                    })
                },
                //上传excel文件
                beforeAvatarUploadFile(file, type = '') {
                    let that = this;
                    const isLt100M = file.size / 1024 / 1024 < 500;
                    if (!isLt100M) {
                        this.$message.error('上传文件大小不能超过 500MB!');
                        return false; // 阻止上传
                    }
                    // 直接存储原始文件对象

                    that.files = file; // 存储 File 对象
                    console.log(that.files);
                    that.$message.success('上传成功！')
                    return true; // 允许上传继续
                },

                //getCheckboxValue
                getCheckboxValue() {
                    let that = this;
                    axios.get(server_url + '/api/datacomp/get_field_dict').then(function (res) {
                        let data = res.data;
                        if (data.code == 0) {
                            that.$nextTick(() => {
                                that.cities1 = data.data.PublicationInfo.field_list || [];
                                that.cities2 = data.data.AffiliationAndContact.field_list || [];
                                that.cities3 = data.data.OtherImportantFields.field_list || [];
                                that.cities4 = data.data.AuthorIDAndFunding.field_list || [];
                                that.pilations = data.data.TitleAndLanguageInfo.field_list || [];
                                // 初始化所有复选框组的选中状态为所有 key 值
                                that.checkedCities1 = that.cities1.map(item => item.key);
                                that.checkedCities2 = that.cities2.map(item => item.key);
                                that.checkedCities3 = that.cities3.map(item => item.key);
                                that.checkedCities4 = that.cities4.map(item => item.key);
                                // pilations 默认不全选，根据需求调整
                                that.checkedCities1 = that.activeTab === 'tab2' ? that.pilations.map(item => item.key) : that.checkedCities1;

                                // 设置全选状态
                                that.checkAll1 = that.cities1.length > 0;
                                that.isIndeterminate1 = false;
                                that.checkAll2 = that.cities2.length > 0;
                                that.isIndeterminate2 = false;
                                that.checkAll3 = that.cities3.length > 0;
                                that.isIndeterminate3 = false;
                                that.checkAll4 = that.cities4.length > 0;
                                that.isIndeterminate4 = false;
                            });
                        } else {
                            console.error('获取字段字典失败:', data.message);
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                },
                stepAnother() {
                    this.step1 = false
                    this.step2 = true


                },
                step1Event() {

                    this.step1 = false
                    this.step2 = true
                    this.getInfoList()


                },
                closeStart() {
                    this.step1 = true
                    this.step2 = false

                },
                generalSearch() { },
                handleUpload() { },
                onSubmit() { },
                handleCheckAllChange(index, val) {
                    switch (index) {
                        case 1:
                            this.checkedCities1 = val ? this.cities1.map(item => item.key) : [];
                            this.isIndeterminate1 = false;
                            break;
                        case 2:
                            this.checkedCities2 = val ? this.cities2.map(item => item.key) : [];
                            this.isIndeterminate2 = false;
                            break;
                        case 3:
                            this.checkedCities3 = val ? this.cities3.map(item => item.key) : [];
                            this.isIndeterminate3 = false;
                            break;
                        case 4:
                            this.checkedCities4 = val ? this.cities4.map(item => item.key) : [];
                            this.isIndeterminate4 = false;
                            break;
                    }
                },
                handleCheckedCitiesChange(index, value) {
                    switch (index) {
                        case 1:
                            let checkedCount1 = value.length;
                            this.checkAll1 = checkedCount1 === (this.activeTab === 'tab1' ? this.cities1.length : this.pilations.length);
                            this.isIndeterminate1 = checkedCount1 > 0 && checkedCount1 < (this.activeTab === 'tab1' ? this.cities1.length : this.pilations.length);
                            break;
                        case 2:
                            let checkedCount2 = value.length;
                            this.checkAll2 = checkedCount2 === this.cities2.length;
                            this.isIndeterminate2 = checkedCount2 > 0 && checkedCount2 < this.cities2.length;
                            break;
                        case 3:
                            let checkedCount3 = value.length;
                            this.checkAll3 = checkedCount3 === this.cities3.length;
                            this.isIndeterminate3 = checkedCount3 > 0 && checkedCount3 < this.cities3.length;
                            break;
                        case 4:
                            let checkedCount4 = value.length;
                            this.checkAll4 = checkedCount4 === this.cities4.length;
                            this.isIndeterminate4 = checkedCount4 > 0 && checkedCount4 < this.cities4.length;
                            break;
                    }
                },
                exportData() { },
                handleMenuSelect(index) {
                    if (index === 'cleanTool') {
                        window.location.href = '/cleanTool'; // 跳转到对应的页面
                    } else if (index === 'classiFication') {
                        window.location.href = '/classiFication';
                    } else if (index === 'relationship') {
                        window.location.href = '/relationship';
                    } else if (index === 'home') {
                        window.location.href = '/'; // 跳转到首页
                    } else if (index === 'qualitycontrol') {
                        window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
                    }
                    this.menuActive = index; // 更新高亮状态
                },
                startopen() {
                    this.dialogVisible = true;
                    this.getTableData();
                },
                handleRemove(file, fileList) {
                    console.log('File removed:', file);
                },
                add_searchContent() {
                    let sc_object = { 'condition': 'AND', 'field': 'Article_Title', 'value': '', 'match': 'like', }
                    this.$set(this.searchContent, this.searchContent.length, sc_object);
                },
                delete_searchContent(row, index) {
                    this.searchContent.splice(index, 1);
                },
                clearGeneralSQL() {
                    this.searchContent = [
                        {
                            'condition': 'AND',
                            'field': 'Article_Title',
                            'value': '',
                            'match': 'like',
                            'match_list': match_str_list
                        },
                        {
                            'condition': 'AND',
                            'field': 'Article_Title',
                            'value': '',
                            'match': 'like',
                            'match_list': match_str_list
                        }
                    ]
                },
                handlePreview(row) {

                    let that = this;
                    that.step1 = false
                    that.step2 = true
                    that.task_id = row.id
                    that.dialogVisible = false
                    let params = {
                        id: row.id
                    };
                    axios.get(server_url + '/api/datacomp/tasks_detail', {
                        params,
                        timeout: 60000
                    }).then(function (res) {
                        const data = res.data;
                        console.log(data.data.result);
                        if (data.code == 0) {
                            that.head = data.data.head;
                            that.tableData = data.data.result.items;
                        }
                    }).catch((error) => { });
                    // location.href = `./demonstration?session_id=${row.session_id}`;
                },
                customUpload(request) { },
                batchDown() {
                    const that = this;
                    axios({
                        url: server_url + '/api/datacomp/download_excel',
                        method: 'GET',
                        params: { task_id: that.task_id },
                        responseType: 'blob'
                    }).then(function (response) {
                        // 根据 value 确定 MIME 类型
                        const mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

                        const blob = new Blob([response.data], { type: mimeType });
                        const downloadUrl = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = downloadUrl;

                        // 从 Content-Disposition 头中提取文件名
                        let fileName = `file.xlsx`; // 默认文件名
                        const disposition = response.headers['content-disposition'];
                        if (disposition && disposition.includes('filename=')) {
                            // 匹配 filename 或 filename*
                            const matches = disposition.match(/filename="(.+)"/) || disposition.match(/filename=([^;]+)/) || disposition.match(/filename\*="UTF-8''(.+)"/);
                            if (matches && matches[1]) {
                                // 尝试解码文件名
                                try {
                                    fileName = decodeURIComponent(matches[1].trim()); // 解码 URL 编码的中文
                                } catch (e) {
                                    console.warn('文件名解码失败，使用原始文件名:', matches[1]);
                                    fileName = matches[1].trim();
                                }
                            }
                        }

                        link.download = fileName;
                        document.body.appendChild(link);
                        link.click();
                        window.URL.revokeObjectURL(downloadUrl);
                        document.body.removeChild(link);
                    }).catch(function (error) {
                        console.log('文件下载失败：', error);
                    });
                },
                handleDownload(row, value) {

                    console.log(row, value);
                    const that = this;
                    axios({
                        url: server_url + '/api/datacomp/download_single',
                        method: 'GET',
                        params: { id: row.id, task_id: that.task_id, file_type: value },
                        responseType: 'blob'
                    }).then(function (response) {
                        // 根据 value 确定 MIME 类型
                        const mimeType = value === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 'application/json';

                        const blob = new Blob([response.data], { type: mimeType });
                        const downloadUrl = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = downloadUrl;

                        // 从 Content-Disposition 头中提取文件名
                        let fileName = `file.${value === 'excel' ? 'xlsx' : 'json'}`; // 简单默认文件名
                        const disposition = response.headers['content-disposition'];
                        if (disposition && disposition.includes('filename=')) {
                            const matches = disposition.match(/filename="(.+)"/) || disposition.match(/filename=([^;]+)/);
                            if (matches && matches[1]) {
                                fileName = matches[1].trim(); // 使用后端返回的文件名
                            }
                        }

                        link.download = fileName;
                        document.body.appendChild(link);
                        link.click();
                        window.URL.revokeObjectURL(downloadUrl);
                        document.body.removeChild(link);
                    }).catch(function (error) {
                        console.log('文件下载失败：', error);
                    });
                },
                getTableData() {
                    let that = this;
                    let params = {
                        "page": that.page,
                        "page_size": that.pageSize,
                        task_name: ''
                    };
                    axios.get(server_url + '/api/datacomp/tasks_list', {
                        params,
                        timeout: 60000
                    }).then(function (res) {
                        const data = res.data;
                        console.log(data);
                        if (data.code == 0) {
                            // that.$message.success('整编成功！');
                            that.taskList = data.data.result;
                            that.total = data.data.total - 0
                        }
                    }).catch((error) => { });
                },
            }
        });
    </script>
</body>

</html>