<script setup lang="tsx">
import { ChevronUpDoubleIcon } from 'tdesign-icons-vue-next';
import { Button, MessagePlugin } from 'tdesign-vue-next';
import { computed, h, onMounted, ref, toRefs } from 'vue';

import OverviewTable from '../../../../../../components/overview-table/OverviewTable.vue';
import DetailHeader from '../common/detail-header';
import DetailLayout from '../common/detail-layout';
import TopCard from '../common/top-card';
import MarkdownRender from '../dataset/modules/markdownRender';
import { getLiteratureDetail } from './api';

const props = defineProps<{
  item: any;
}>();

const chevronUpDoubleIcon = () => h(ChevronUpDoubleIcon, { size: '24px' });

const { item } = toRefs(props);

const detailHeaderData = ref<any>({});

const overviewData = ref<any>([]);
// 控制 PDF 查看状态
const isPdfVisible = ref(false);

const isPdfOrMdVisible = computed(() => {
  return detailHeaderData.value?.isMd === '0';
});

// 获取文献详情数据
const fetchDetailData = async () => {
  // 文献详情请求参数
  const params = {
    baseCode: item.value.baseCode,
    dataType: item.value.dataType,
  };
  const res = await getLiteratureDetail(params);
  detailHeaderData.value = res.headerData;
  detailHeaderData.value.browsingCount = item.value.browsingCount;
  detailHeaderData.value.isFavorite = item.value.isFavorite;
  detailHeaderData.value.actions = item.value?.actions;

  // 示例数据，实际应用中根据 API 返回数据填充
  overviewData.value = [
    { label: '期号 (issn)', value: res.headerData?.issue },
    {
      label: '电子期刊国际标准连续出版物号 (eissn)',
      value: res.headerData?.eissn,
    },
    { label: '期刊标题', value: res.headerData?.publicationTitle },
    { label: '文献出版信息', value: res.headerData?.literature },
    { label: '出版年', value: res.headerData?.publicationYear },
    { label: '文献具体类型', value: res.headerData?.docType },
    { label: '卷号', value: res.headerData?.vol },
    { label: '页码', value: res.headerData?.page },
    { label: '引用次数', value: res.headerData?.citedTimes },
    { label: '摘要', value: res.headerData?.docAbstract },
  ];
  overviewData.value = overviewData.value.filter(
    (item) =>
      item.value !== null && item.value !== undefined && item.value !== '',
  );
};

onMounted(() => {
  fetchDetailData();
});

// 复制到剪贴板的函数
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(
    () => {
      MessagePlugin.success('文本已复制到剪贴板');
    },
    (error) => {
      MessagePlugin.error('无法复制文本');
      console.error('无法复制文本：', error);
    },
  );
};

const data = computed(() => ({
  topCard: () => <TopCard data={detailHeaderData} searchData={item} />,
  menu: [
    {
      title: '概览',
      content: () => <OverviewTable data={overviewData.value} />,
    },
    {
      title: '文字内容',
      content: () =>
        // isPdfVisible.value ? (
        //   <div style="position: relative;">
        //     {/* PDF 查看器 */}
        //     {isPdfOrMdVisible.value && (
        //       <iframe
        //         height="500px"
        //         src={`${detailHeaderData.value?.pdfPath}#toolbar=0`}
        //         title="PDF Viewer"
        //         width="100%"
        //       ></iframe>
        //     )}

        //     {!isPdfOrMdVisible.value && (
        //       <MarkdownRender content={detailHeaderData.value?.mdPath} />
        //     )}

        //     {/* 折叠隐藏按钮 */}
        //     <Button
        //       class="hide-pdf-button"
        //       icon={chevronUpDoubleIcon}
        //       onClick={() => (isPdfVisible.value = false)}
        //       style="position: absolute; top: -35px; right: 0;"
        //     >
        //       折叠隐藏
        //     </Button>
        //   </div>
        // ) : (
          // 初始状态，显示“点击查看文章”按钮
          <div style="display: flex; align-items: center; gap: 12px;">
            <Button
              class="view-article-button"
              onClick={() => {
                if (detailHeaderData.value?.sourceUrl) {
                  window.open(detailHeaderData.value.sourceUrl, '_blank');
                } else {
                  console.warn('sourceUrl 不存在，无法跳转');
                }
              }}
            >
              点击跳转原文
            </Button>

            <Button
            class="copy-link-button"
            onClick={() =>
              copyToClipboard(detailHeaderData.value?.sourceUrl)
            }
          >
            复制原文链接
          </Button>
        </div>
        // ),
    },
    {
      title: '引用方式',
      content: () => (
        <div>
          <div class="citation-container">
            <div class="citation-text">
              {detailHeaderData.value?.referenceDataset}
            </div>
            <Button
              class="copy-button"
              onClick={() =>
                copyToClipboard(detailHeaderData.value?.referenceDataset)
              }
            >
              复制
            </Button>
          </div>
        </div>
      ),
    },
  ],
  right: [
    {
      title: '分类',
      content: () => <div>{item.value?.categoryName}</div>,
    },
    {
      title: '当前版本',
      content: () => <div>{detailHeaderData.value?.version}</div>,
    },
    {
      title: '数据分级',
      content: () => <div>{detailHeaderData.value?.dataLevelName}</div>,
    },
    {
      title: '数据来源',
      content: () => <div>{detailHeaderData.value?.source}</div>,
    },
  ],
}));
</script>

<template>
  <DetailHeader />
  <DetailLayout :data="data" />
</template>

<style scoped lang="scss">
:deep(.citation-container) {
  .citation-text {
    margin: 0;
    color: #333;
  }

  .copy-button {
    margin-left: 95%;
    bottom: 8px;
    right: 8px;
    padding: 7px 10px;
    color: #000;
    background-color: #fff;
    border: 1px solid #e4e4e4;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: #0056b3;
      color: #fff;
    }
  }
}
:deep(.view-article-button) {
  margin-left: 40%;
  padding: 8px 16px;
  font-size: 14px;
  color: #fff;
  background-color: #0269d6;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #0056b3;
  }
}

:deep(.hide-pdf-button) {
  margin-left: 46%;
  padding: 8px 16px;
  font-size: 14px;
  color: #fff;
  background-color: #0269d6;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #0056b3;
  }
}
</style>
