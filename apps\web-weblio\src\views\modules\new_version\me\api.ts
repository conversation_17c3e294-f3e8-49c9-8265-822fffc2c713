import { requestClient } from '#/api/request';

// 我的收藏
export async function getMyFavoritesData(data: any) {
  // /rgdc-submit/tPortalFavorites/listByPage
  return requestClient.post<any[]>(
    '/rgdc-submit/tPortalFavorites/listByPage',
    data,
  );
}
// 删除收藏
export async function deleteBatch(ids: string) {
  return requestClient.delete<any[]>(
    `/rgdc-submit/tPortalFavorites/deleteBatch/${ids}`,
  );
}
// 我的下载
export async function getMyDownloadData(data: any) {
  return requestClient.post<any[]>(
    '/rgdc-submit/tPersonalCenter/myDownloadData',
    data,
  );
}
// 最近浏览
export async function getMyRecentlyViewed(data: any) {
  return requestClient.post<any[]>(
    '/rgdc-submit/tPersonalCenter/myRecentlyViewed',
    data,
  );
}

export async function listByPageApi(data: any) {
  return requestClient.post<any>(
    '/rgdc-workflow/queryInstance/listByPage2',
    data,
  );
}

export async function myTaskByPage2(data: any) {
  return requestClient.post<any>(
    '/rgdc-workflow/queryTask/myTaskByPage2',
    data,
  );
}

export async function queryHisInstance(data: any) {
  return requestClient.post<any>(
    '/rgdc-workflow/queryHisInstance/listByPage2',
    data,
  );
}

export async function executeTask(data: any) {
  return requestClient.post<any>('/rgdc-workflow/task/executeTask', data);
}

export async function claimTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/claimTask`, data);
}
export async function claimTasks(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/claimTasks`, data);
}
// 批量认领任务
export async function batchClaimTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/batchClaimTask`, data);
}

export async function rejectTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/rejectTask`, data);
}
