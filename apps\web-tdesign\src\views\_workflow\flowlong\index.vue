<script setup>
import { onMounted, reactive, ref, unref, watch } from 'vue';
import useClipboard from 'vue-clipboard3';

import { usePreferences } from '@vben/preferences';

import domtoimage from 'dom-to-image';
import JsonEditorVue from 'json-editor-vue';
import { AddIcon, MinusIcon } from 'tdesign-icons-vue-next';
import { <PERSON>ff<PERSON>, But<PERSON>, Drawer, Slider, Space } from 'tdesign-vue-next';

import scWorkflow from './components/index.vue';

// 设置组件名称

const { isDark } = usePreferences();
const zoom = ref(1);
const marks = reactive({});

const drawer = ref(false);

const fatData = defineModel('modelValue');
const data = ref({});
// const data = ref();

watch(
  () => data.value,
  (val) => {
    fatData.value = val;
  },
);

const { toClipboard } = useClipboard();

const copyParseJson = async () => {
  await toClipboard(JSON.stringify(data.value, null, '  '));
};

const copyJson = async () => {
  await toClipboard(JSON.stringify(data.value));
};

// const handleWeel = (e) => {
//   e.preventDefault();
//   if (e.wheelDelta < 0) {
//     zoom.value -= 0.1;
//   } else {
//     zoom.value += 0.1;
//   }

//   if (zoom.value <= 0.1) {
//     zoom.value = 0.1;
//   } else if (zoom.value >= 5) {
//     zoom.value = 5;
//   }
// };

const saveAsPng = async () => {
  const element = document.querySelector('#content-to-capture');
  element.parentElement.style.transform = 'scale(1)';
  // 匹配暗黑主题
  domtoimage
    .toPng(element, { bgcolor: isDark.value ? '#141414' : '#f1f3f6' })
    .then((dataUrl) => {
      const link = document.createElement('a');
      link.download = `tiny-${Date.now()}.png`;
      link.href = dataUrl;
      link.click();
    })
    .catch((error) => {
      console.error('oops, something went wrong!', error);
    });
};

onMounted(() => {
  data.value = { ...unref(fatData) };

  // 键盘按键监听
  // document
  //   .querySelector('#app')
  //   .addEventListener('wheel', (e) => handleWeel(e));
});
</script>
<template>
  <div class="bg-workflow" style="width: 100%; height: 100%; overflow-y: auto">
    <Affix :offset-top="90" style="z-index: 1">
      <div class="btn-container">
        <Space>
          <Button type="primary" @click="() => (drawer = true)">
            查看 JSON
          </Button>
          <Button type="primary" @click="saveAsPng"> 保存图片 </Button>
        </Space>
        <div class="slider">
          <Button
            style="width: 32px; margin-right: 16px"
            type="primary"
            @click="zoom -= 0.1"
          >
            <MinusIcon />
          </Button>
          <Slider
            v-model="zoom"
            :marks="marks"
            :max="5"
            :min="0.1"
            :step="0.1"
          />
          <Button
            style="width: 32px; margin-left: 16px"
            type="primary"
            @click="zoom += 0.1"
          >
            <AddIcon />
          </Button>
        </div>
      </div>
    </Affix>
    <div
      :style="`transform: scale(${zoom})`"
      class="affix-container"
      style="transform-origin: 0 0"
    >
      <sc-workflow
        id="content-to-capture"
        v-model="data.nodeConfig"
        class="workflow"
      />
    </div>
    <Drawer
      v-model:visible="drawer"
      :footer="false"
      attach="body"
      class="drawer"
      close
      size="500px"
    >
      <div>
        <div style="padding: 4px">
          <Space>
            <Button plain type="primary" @click="copyParseJson">
              复制格式化后的 JSON
            </Button>
            <Button plain type="primary" @click="copyJson">
              复制压缩后的 JSON
            </Button>
            <Button plain type="primary" @click="drawer = false">
              关闭弹窗
            </Button>
          </Space>
        </div>
        <JsonEditorVue v-model="data" class="editor" />
      </div>
    </Drawer>
  </div>
</template>

<style scoped>
.affix-container {
  display: flex;
  flex-direction: row-reverse;
  justify-content: center;
}

.editor {
  width: 470px;
  height: calc(100vh - 100px);
}

.editor .jsoneditor-poweredBy,
.editor .jsoneditor-transform,
.editor .jsoneditor-repair,
.editor .full-screen {
  display: none !important;
}

.workflow {
  padding: 10px;
}

.jsoneditor-menu > button.jsoneditor-copy {
  background-position: -48px 0;
}

.el-drawer__body {
  padding: 0 !important;
}

.btn-container {
  display: flex;
  height: 42px;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 16px;
}

.slider {
  display: flex;
  width: 300px;
  margin-left: 16px;
}

.bg-workflow {
  background-color: hsl(var(--background-deep));
}
</style>
