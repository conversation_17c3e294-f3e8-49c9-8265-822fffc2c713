import { requestClient } from '#/api/request';
import { useAppConfig } from '@vben/hooks';
import { useAccessStore } from '@vben/stores';
// multipart/form-data: upload file
import axios from 'axios';

const axiosInstance = axios.create({});
const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

export interface UploadFileParams {
  // Other parameters
  data?: any;
  // File parameter interface field name
  name?: string;
  // file name
  file: Blob | File;
  // file name
  filename?: string;

  [key: string]: any;
}

/**
 * @description:  contentType
 */
export enum ContentTypeEnum {
  // form-data  upload
  FORM_DATA = 'multipart/form-data;charset=UTF-8',
  // form-data qs
  FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8',
  // json
  JSON = 'application/json;charset=UTF-8',
}

export interface UploadApiResult {
  message: string;
  code: number;
  url: string;
}

/**
 * @description:  File Upload
 */
function uploadFile<T = any>(config: any, params: UploadFileParams) {
  const formData = new window.FormData();
  const customFilename = params.name || 'file';
  if (params.filename) {
    formData.append(customFilename, params.file, params.filename);
  } else {
    formData.append(customFilename, params.file);
  }

  if (params.data) {
    Object.keys(params.data).forEach((key) => {
      const value = params.data![key];
      if (Array.isArray(value)) {
        value.forEach((item) => {
          formData.append(`${key}[]`, item);
        });
        return;
      }

      formData.append(key, params.data![key]);
    });
  }
  const accessStore = useAccessStore();

  return axiosInstance.request<T>({
    ...config,
    method: 'POST',
    data: formData,
    headers: {
      'Content-type': ContentTypeEnum.FORM_DATA,
      // @data-ignore
      ignoreCancelToken: true,
      authorization: `Bearer ${accessStore.accessToken}`,
    },
  });
}

/**
 * @description: Upload interface
 */
export function uploadApi(
  params: UploadFileParams,
  onUploadProgress: (progressEvent: any) => void,
) {
  return uploadFile<UploadApiResult>(
    {
      timeout: 1000 * 60 * 60,
      url: `${apiURL}/rgdc-sys/file/upload`,
      onUploadProgress,
    },
    params,
  );
}
export async function baseDownloadFile(
  path: string,
  params: any,
) {
  const res: any = await requestClient.downloadPost(path, params);
  // const res: any = await requestClient.post<any[]>(path, params)
  const { data, headers } = res;

  let fileName;
  try {
    const disposition = headers['content-disposition'];
    const matchArray = disposition.split('=');
    fileName = decodeURI(matchArray[1]);
  } catch {
    fileName = name;
  }
  // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
  // const blob = new Blob([JSON.stringify(data)], ...)

  const blob = new Blob([data], { type: headers['content-type'] });
  const dom: any = document.createElement('a');
  const url = window.URL.createObjectURL(blob);
  dom.href = url;
  dom.download = decodeURI(fileName || path.split('/').pop() || 'download');
  dom.style.display = 'none';
  document.body.append(dom);
  dom.click();
  dom.remove();
  window.URL.revokeObjectURL(url);
}
export async function baseDownloadFileGet(
  path: string,
  params: any,
  name?: string,
) {
  const res: any = await requestClient.download(`${path}`, { params });
  const { data, headers } = res;

  let fileName;
  try {
    const disposition = headers['content-disposition'];
    const matchArray = disposition.split('=');
    fileName = decodeURI(matchArray[1]);
  } catch {
    fileName = name;
  }
  // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
  // const blob = new Blob([JSON.stringify(data)], ...)

  const blob = new Blob([data], { type: headers['content-type'] });
  const dom: any = document.createElement('a');
  const url = window.URL.createObjectURL(blob);
  dom.href = url;
  dom.download = decodeURI(fileName || path.split('/').pop() || 'download');
  dom.style.display = 'none';
  document.body.append(dom);
  dom.click();
  dom.remove();
  window.URL.revokeObjectURL(url);
}

export async function superDownloadFile(
  path: string,
  config: any,
  name?: string,
) {
  const res: any = await requestClient.request(`${path}`, {
    ...config,
    responseType: 'blob',
  });
  const { data, headers } = res;

  let fileName;
  try {
    const disposition = headers['content-disposition'];
    const matchArray = disposition.split('=');
    fileName = decodeURI(matchArray[1]);
  } catch {
    fileName = name;
  }
  // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
  // const blob = new Blob([JSON.stringify(data)], ...)

  const blob = new Blob([data], { type: headers['content-type'] });
  const dom: any = document.createElement('a');
  const url = window.URL.createObjectURL(blob);
  dom.href = url;
  dom.download = decodeURI(fileName || path.split('/').pop() || 'download');
  dom.style.display = 'none';
  document.body.append(dom);
  dom.click();
  dom.remove();
  window.URL.revokeObjectURL(url);
}

export async function baseUploadFile(path: string, data: any) {
  return await requestClient.upload(`${path}`, { ...data });
}
