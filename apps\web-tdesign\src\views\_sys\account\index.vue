<script setup lang="tsx">
import { Page } from '@vben/common-ui';
import { ref } from 'vue';

import EditForm from './components/EditForm.vue';
import EditPws from './components/EditPwd.vue';
import IndexTable from './components/IndexTable.vue';

const editFormRef = ref();
const tableRef = ref();
const editPwdRef = ref();
</script>

<template>
  <Page description="用于登录此系统" title="系统账号列表">
    <EditPws ref="editPwdRef" :out-ref="tableRef" />
    <EditForm ref="editFormRef" :out-ref="tableRef" />
    <IndexTable ref="tableRef" :edit-form-ref="editFormRef" :edit-pwd-ref="editPwdRef"/>
  </Page>
</template>
