import { requestClient } from '#/api/request';

// 质量控制相关API接口

/**
 * 获取上传列表
 */
export async function getUploadList() {
  return requestClient.get('/rgdc-sys/dataTools/quality_control/upload_list');
}

/**
 * 开始质量控制
 * @param data 质量控制参数
 */
export async function startQualityControl(data: any) {
  return requestClient.post('/rgdc-sys/dataTools/quality_control/start', data, {
    timeout: 60_000,
  });
}

/**
 * 获取质量控制结果
 * @param taskId 任务ID
 */
export async function getQualityControlResult(taskId: string) {
  return requestClient.get(`/rgdc-sys/dataTools/quality_control/result/${taskId}`);
}

/**
 * 上传Excel文件
 * @param file 文件
 */
export async function uploadExcelFile(file: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/quality_control/upload_excel', file, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * 获取数据库列表
 */
export async function getDatabaseList() {
  return requestClient.get('/rgdc-sys/dataTools/quality_control/databases');
}

/**
 * 获取数据库表列表
 * @param database 数据库名称
 */
export async function getTableList(database: string) {
  return requestClient.get(`/rgdc-sys/dataTools/quality_control/tables/${database}`);
}

/**
 * 获取字段列表
 * @param database 数据库名称
 * @param table 表名称
 */
export async function getFieldList(database: string, table: string) {
  return requestClient.get(`/rgdc-sys/dataTools/quality_control/fields/${database}/${table}`);
}

/**
 * 下载质量控制报告
 * @param taskId 任务ID
 */
export async function downloadQualityReport(taskId: string) {
  return requestClient.get(`/rgdc-sys/dataTools/quality_control/download/${taskId}`, {
    responseType: 'blob',
  });
}
