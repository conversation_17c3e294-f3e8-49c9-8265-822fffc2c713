// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-sys/tSysDataTools/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-sys/tSysDataTools/save', data);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-sys/tSysDataTools/getOne/${data}`);
}
