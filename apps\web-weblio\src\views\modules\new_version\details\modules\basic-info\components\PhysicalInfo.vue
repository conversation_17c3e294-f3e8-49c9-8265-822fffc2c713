<script setup lang="ts">
import {
  Loading
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref, watch } from 'vue';

import {
  getPhysicalInfoData
} from '../api';
import { useSearchStore } from '#/store/search';

// 定义 props
const props = defineProps({
  physicalData: {
    type: Object,
    default: {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

interface PhysicalInfoData {
  physicalDescription: string,
  colour: string,
  form: string,
  odor: string,
  taste: string,
  boilingPoint: string,
  meltingPoint: string,
  flashPoint: string,
  solubility: string,
  density: string,
  vaporPressure: string,
  stabilityShelfLife: string,
  decomposition: string,
  viscosity: string,
  heatOfCombustion: string,
  surfaceTension: string,
  odorThreshold: string,
  refractiveIndex: string,
  collisionCrossSection: string,
  kovatsRetentionIndex: string,
  ph: string,
  acidBaseIndicatorDiscolorationPh: string,
  explosiveLimit: string,
  maxR: string,
  henrysLawConstant: string,
  exposureLimits: string,
  proportion: string,
  vaporDensity: string,
  ionizationPotential: string,
  dissociationConstants: string,
  pka: string,
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: false,

});

// 定义加载状态，用于控制页面加载动画显示
const loading = ref(props.loading);

// 初始化物理性质数据对象，字段命名与接口定义保持一致，确保类型安全
const data = ref<PhysicalInfoData>({
  physicalDescription: '',
  colour: '',
  form: '',
  odor: '',
  taste: '',
  boilingPoint: '',
  meltingPoint: '',
  flashPoint: '',
  solubility: '',
  density: '',
  vaporPressure: '',
  stabilityShelfLife: '',
  decomposition: '',
  viscosity: '',
  heatOfCombustion: '',
  surfaceTension: '',
  odorThreshold: '',
  refractiveIndex: '',
  collisionCrossSection: '',
  kovatsRetentionIndex: '',
  ph: '',
  acidBaseIndicatorDiscolorationPh: '',
  explosiveLimit: '',
  maxR: '',
  henrysLawConstant: '',
  exposureLimits: '',
  proportion: '',
  vaporDensity: '',
  ionizationPotential: '',
  dissociationConstants: '',
  pka: ''
});

// 监听 props 变化，更新数据
watch(() => props.physicalData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    data.value = {
      physicalDescription: newData.physicalDescription || '',
      colour: newData.colour || newData.color || '',
      form: newData.form || '',
      odor: newData.odor || '',
      taste: newData.taste || '',
      boilingPoint: newData.boilingPoint || '',
      meltingPoint: newData.meltingPoint || '',
      flashPoint: newData.flashPoint || '',
      solubility: newData.solubility || '',
      density: newData.density || '',
      vaporPressure: newData.vaporPressure || '',
      stabilityShelfLife: newData.stabilityShelfLife || '',
      decomposition: newData.decomposition || '',
      viscosity: newData.viscosity || '',
      heatOfCombustion: newData.heatOfCombustion || '',
      surfaceTension: newData.surfaceTension || '',
      odorThreshold: newData.odorThreshold || '',
      refractiveIndex: newData.refractiveIndex || '',
      collisionCrossSection: newData.collisionCrossSection || '',
      kovatsRetentionIndex: newData.kovatsRetentionIndex || '',
      ph: newData.ph || newData.pH || '',
      acidBaseIndicatorDiscolorationPh: newData.acidBaseIndicatorDiscolorationPh || '',
      explosiveLimit: newData.explosiveLimit || '',
      maxR: newData.maxR || '',
      henrysLawConstant: newData.henrysLawConstant || '',
      exposureLimits: newData.exposureLimits || '',
      proportion: newData.proportion || '',
      vaporDensity: newData.vaporDensity || '',
      ionizationPotential: newData.ionizationPotential || '',
      dissociationConstants: newData.dissociationConstants || '',
      pka: newData.pka || ''
    };
  }
}, { immediate: true, deep: true });

// 监听 loading 状态变化
watch(() => props.loading, (newLoading) => {
  loading.value = newLoading;
  console.log("loading", loading.value, 2222);
});

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

onMounted(async () => {
  // 如果父组件没有传递数据，则使用原有的逻辑
  // if (!props.physicalData || Object.keys(props.physicalData).length === 0) {
  //   try {
  //     // 调用物理性质信息查询API请求方法，传入 inchikey 参数
  //     const response = await getPhysicalInfoData({ inchikey: state.detailItem.baseCode });
  //     data.value = response;
  //   } catch (error) {
  //     console.error('获取物理性质信息失败:', error);
  //   } finally {
  //     // 关闭加载状态
  //     loading.value = false;
  //   }
  // }
});

const fieldLabels = {
  physicalDescription: '物理描述：',
  colour: '颜色：',
  form: '形态：',
  odor: '气味：',
  taste: '味道：',
  boilingPoint: '沸点：',
  meltingPoint: '熔点：',
  flashPoint: '闪点：',
  solubility: '溶解度：',
  density: '密度：',
  vaporPressure: '蒸汽压力：',
  stabilityShelfLife: '稳定性/保质期：',
  decomposition: '分解：',
  viscosity: '粘度：',
  heatOfCombustion: '燃烧热：',
  surfaceTension: '表面张力：',
  odorThreshold: '气味阈值：',
  refractiveIndex: '折射率：',
  collisionCrossSection: '碰撞截面：',
  kovatsRetentionIndex: 'Kovats保留指数：',
  ph: 'pH值：',
  acidBaseIndicatorDiscolorationPh: '酸碱指示剂变色pH：',
  explosiveLimit: '爆炸极限：',
  maxR: '最大R值：',
  henrysLawConstant: '亨利定律常数：',
  exposureLimits: '暴露限值：',
  proportion: '比例：',
  vaporDensity: '蒸汽密度：',
  ionizationPotential: '电离势：',
  dissociationConstants: '解离常数：',
  pka: 'pKa值：'
};
</script>

<template>
  <div class="physical-info">
    <!-- 加载状态提示 -->
    <div v-if="loading" class="loading-container">
      <Loading />
    </div>
    <!-- 主体内容展示 -->
    <div v-else class="content">
      <div class="info-items">
        <div v-for="(label, key) in fieldLabels" :key="key">
          <!-- 如果 data[key] 存在，则显示对应信息 -->
          <div v-if="data[key]" class="info-item">
            <!-- 字段标签 -->
            <span class="label">{{ label }}</span>
            <!-- 字段值 -->
            <span class="value">
              {{ data[key] }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.physical-info {
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  font-size: 24px;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.structure-image {
  width: 100%;
  max-width: 400px;
  margin-bottom: 20px;
}

.structure-image img {
  width: 100%;
  height: auto;
}

.info-items {
  width: 80%;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 1px dashed #ddd;
}

.label {
  font-weight: bold;
  min-width: 240px;
  color: #555;
}

.value {
  flex-grow: 1;
  color: #333;
  word-break: break-all;
}

.smiles-link {
  color: #3498db;
  text-decoration: underline;
  cursor: pointer;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
