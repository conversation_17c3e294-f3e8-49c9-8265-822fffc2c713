<script setup lang="ts">
import DynamicTable from '#/components/dynamic-table/index.vue';
import { Page } from '@vben/common-ui';
import { Space } from 'tdesign-vue-next';
import { ref } from 'vue';

// 中间层页面 主要负责页面间的解耦以及数据传输

const data = ref();

const dynamicTable = ref();
</script>

<template>
  <Page description="动态表格组件示例" title="组件示例">
    <Space>
      {{ JSON.stringify(data) }}
    </Space>
    <DynamicTable ref="dynamicTable" v-model="data" />
  </Page>
</template>
