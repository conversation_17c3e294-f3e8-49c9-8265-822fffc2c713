<template>
  <div>
    <!-- 使用搜索组件 -->
    <SearchBox
      v-model:search-mode="searchMode"
      v-model:selected-search-type="selectedSearchType"
      v-model:search-query="searchQuery"
      v-model:ai-query="aiQuery"
      v-model:deep-search-enabled="deepSearchEnabled"
      :hot-search-tags="hotSearchTags"
      :ai-examples="aiExamples"
      @search="handleSearch"
      @ai-chat="handleAiChat"
      @advanced-search="handleAdvancedSearch"
      @formula-search="handleFormulaSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SearchBox from '../../../components/search-box/index.vue'

// 响应式数据
const searchMode = ref<'search' | 'ai'>('search')
const selectedSearchType = ref('all')
const searchQuery = ref('')
const aiQuery = ref('')
const deepSearchEnabled = ref(false)

// 热门检索标签（可配置）
const hotSearchTags = ref([
  '关键词',
  '关键词',
  '关键词',
  '关键词'
])

// AI问答示例
const aiExamples = ref([
  '什么是苯的分子结构？',
  '如何合成阿司匹林？',
  '解释一下化学反应机理',
  '有机化学基础知识'
])

// 处理搜索
const handleSearch = (data: { type: string; query: string }) => {
  console.log('搜索:', data)
  // 这里添加实际的搜索逻辑
}

// 处理AI问答
const handleAiChat = (data: { query: string; deepSearch: boolean }) => {
  console.log('AI问答:', data)
  // 这里添加AI问答逻辑
  // 根据data.deepSearch决定是否使用深度搜索
}

// 处理高级检索
const handleAdvancedSearch = () => {
  console.log('高级检索')
  // 这里添加高级检索逻辑
}

// 处理结构式检索
const handleFormulaSearch = () => {
  console.log('结构式检索')
  // 这里添加结构式检索逻辑
}
</script>


