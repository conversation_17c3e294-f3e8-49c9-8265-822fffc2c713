import { requestClient } from '#/api/request';

// 上传PDF文件进行解析
export async function uploadPdfs(data: FormData, taskName: string) {
  return requestClient.post(`/rgdc-sys/dataTools/upload_pdfs?task_name=${encodeURIComponent(taskName)}`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000, // 2分钟超时
  });
}

// 获取上传记录列表
export async function getUploadList() {
  return requestClient.get('/rgdc-sys/dataTools/upload_list');
}

// 下载文件
export async function downloadFile(sessionId: string) {
  return requestClient.get(`/rgdc-sys/dataTools/get_zip_by_session_id`, {
    params: { session_id: sessionId },
    responseType: 'blob',
  });
}

// 获取任务状态
export async function getTaskStatus(sessionId: string) {
  return requestClient.get(`/rgdc-sys/dataTools/task_status`, {
    params: { session_id: sessionId },
  });
}
