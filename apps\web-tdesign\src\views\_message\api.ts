import { requestClient } from '#/api/request';

export async function listByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-sys/msg/listByPage', data);
}

export async function updateRead(read: any, data: any) {
  return requestClient.put<any>(`/rgdc-sys/msg/updateRead/${read}`, data);
}

// 批量删除
export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-sys/msg/deleteBatch/${data}`);
}
