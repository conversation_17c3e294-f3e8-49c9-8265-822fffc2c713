<script>
import { Button, Dialog, Transfer } from 'tdesign-vue-next';

import config from '../workflow.js';

export default {
  components: {
    <PERSON>alog,
    Button,
    Transfer,
  },
  props: {
    modelValue: { type: Object, default: null },
  },
  data() {
    return {
      userProps: config.user.props,
      roleProps: config.role.props,
      titleMap: ['人员选择', '角色选择'],
      dialogVisible: false,
      showGrouploading: false,
      showUserloading: false,
      keyword: '',
      groupId: '',
      pageSize: config.user.pageSize,
      total: 0,
      currentPage: 1,
      checked: [],
      group: [],
      user: [],
      role: [],
      type: 1,
      selected: [],
      value: [],
      roleValue: [],
      userValue: [],
    };
  },
  computed: {
    selectedIds() {
      return this.selected.map((t) => t.id);
    },
  },
  async mounted() {},
  methods: {
    // 打开赋值
    open(type, data) {
      this.type = type;
      if (type === 2) {
        const res = data?.map((item) => {
          return item.id;
        });
        this.roleValue = res || [];
      }
      if (type === 1) {
        const res = data?.map((item) => {
          return item.id;
        });
        this.userValue = res || [];
      }

      this.value = data || [];
      this.selected = data || [];
      this.dialogVisible = true;

      if (this.type === 1) {
        // this.getGroup();
        this.getUser();
      } else if (this.type === 2) {
        this.getRole();
      }
    },
    // 获取组织
    async getGroup() {
      this.showGrouploading = true;
      const res = await config.group.apiObj.get();
      this.showGrouploading = false;
      const allNode = {
        [config.group.props.key]: '',
        [config.group.props.label]: '所有',
      };
      res.data.unshift(allNode);
      this.group = config.group.parseData(res).rows;
    },
    // 获取用户
    async getUser() {
      this.showUserloading = true;
      const res = await config.user.apiObj();
      this.showUserloading = false;
      this.user = config.user.format(res);
    },
    // 获取角色
    async getRole() {
      this.showGrouploading = true;
      const res = await config.role.apiObj();
      this.showGrouploading = false;
      this.role = config.role.format(res);
    },
    // 组织点击
    groupClick(data) {
      this.keyword = '';
      this.currentPage = 1;
      this.groupId = data[config.group.props.key];
      this.getUser();
    },
    // 用户点击
    userClick(data, checked) {
      // 获取当前选中节点数据
      this.selected = [];
      const nodes = this.user.filter((item) => {
        return data.includes(item.value);
      });
      for (const node of nodes) {
        this.selected.push({
          id: node[config.user.props.value],
          name: node[config.user.props.label],
        });
      }
    },
    // 用户分页点击
    paginationChange() {
      this.getUser();
    },
    // 用户搜索
    search() {
      this.groupId = '';
      this.$refs.groupTree.setCurrentKey(this.groupId);
      this.currentPage = 1;
      this.getUser();
    },
    // 删除已选
    deleteSelected(index) {
      this.selected.splice(index, 1);
      if (this.type === 1) {
        this.$refs.userTree.setCheckedKeys(this.selectedIds);
      } else if (this.type === 2) {
        this.$refs.groupTree.setCheckedKeys(this.selectedIds);
      }
    },
    // 角色点击
    roleClick(data, checked) {
      // 获取当前选中节点数据
      this.selected = [];
      const nodes = this.role.filter((item) => {
        return data.includes(item.code);
      });
      for (const node of nodes) {
        this.selected.push({
          id: node[config.role.props.value],
          name: node[config.role.props.label],
        });
      }
    },
    // 提交保存
    save() {
      this.value.splice(0, this.value.length);
      this.selected.map((item) => {
        this.value.push(item);
      });
      console.log('当前选择点击', this.value);
      this.dialogVisible = false;
    },
  },
};
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    :header="titleMap[type - 1]"
    :width="type === 1 ? 520 : 520"
    :z-index="9999"
    attach="body"
    @close="$emit('closed')"
  >
    <template v-if="type == 1">
      <Transfer
        v-model="userValue"
        :data="user"
        :keys="userProps"
        :titles="['角色', '已选']"
        theme="primary"
        @change="userClick"
      />
    </template>
    <template v-if="type === 2">
      <Transfer
        v-model="roleValue"
        :data="role"
        :keys="roleProps"
        :titles="['角色', '已选']"
        theme="primary"
        @change="roleClick"
      />
    </template>
    <template #footer>
      <Button @click="dialogVisible = false">取 消</Button>
      <Button type="primary" @click="save">确 认</Button>
    </template>
  </Dialog>
</template>

<style>
.sc-user-select {
  display: flex;
}

.sc-user-select__left {
  width: 400px;
}

.sc-user-select__right {
  flex: 1;
}

.sc-user-select__search {
  padding-bottom: 10px;
}

.sc-user-select__select {
  display: flex;
  background: var(--el-color-white);
  border: 1px solid var(--el-border-color-light);
}

.sc-user-select__tree {
  width: 200px;
  height: 300px;
  border-right: 1px solid var(--el-border-color-light);
}

.sc-user-select__user {
  display: flex;
  flex-direction: column;
  width: 200px;
  height: 300px;
}

.sc-user-select__user__list {
  flex: 1;
  overflow: auto;
}

.sc-user-select__user footer {
  height: 36px;
  padding-top: 5px;
  border-top: 1px solid var(--el-border-color-light);
}

.sc-user-select__toicon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
}

.sc-user-select__toicon i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background: #ccc;
  border-radius: 50%;
}

.sc-user-select__selected {
  width: 200px;
  height: 345px;
  background: var(--el-color-white);
  border: 1px solid var(--el-border-color-light);
}

.sc-user-select__selected header {
  height: 43px;
  padding: 0 15px;
  font-size: 12px;
  line-height: 43px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.sc-user-select__selected ul {
  height: 300px;
  overflow: auto;
}

.sc-user-select__selected li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 38px;
  padding: 5px 5px 5px 15px;
}

.sc-user-select__selected li .name {
  display: flex;
  align-items: center;
}

.sc-user-select__selected li .name .el-avatar {
  margin-right: 10px;
  background: #409eff;
}

.sc-user-select__selected li .name label {
}

.sc-user-select__selected li .delete {
  display: none;
}

.sc-user-select__selected li:hover {
  background: var(--el-color-primary-light-9);
}

.sc-user-select__selected li:hover .delete {
  display: inline-block;
}

.sc-user-select-role .sc-user-select__left {
  width: 200px;
}

.sc-user-select-role .sc-user-select__tree {
  height: 343px;
  border: none;
}

.sc-user-select-role .sc-user-select__selected {
}

[data-theme='dark'] .sc-user-select__selected li:hover {
  background: rgb(0 0 0 / 20%);
}

[data-theme='dark'] .sc-user-select__toicon i {
  background: #383838;
}
</style>
