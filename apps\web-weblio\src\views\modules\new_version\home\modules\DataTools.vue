<script setup lang="ts">
defineProps<{
  list: Array<{ desc: string; icon: string; key: string; name: string }>;
  onMore: () => void;
  onToolClick: (item: any) => void;
  title: string;
}>();
</script>

<template>
  <div class="data-tools">
    <div class="header">
      <span class="title">{{ title }}</span>
      <button class="more-btn" @click="onMore">更多</button>
    </div>
    <div class="tool-list">
      <div
        v-for="item in list"
        :key="item.key"
        class="tool-item"
        @click="() => onToolClick(item)"
      >
        <!-- <img :src="item.icon" class="icon" /> -->
        <img :src="item.img" class="icon" />
        <div class="info">
          <div class="tool-title">{{ item.name }}</div>
          <div class="desc">{{ item.describe }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.data-tools {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
}
.more-btn {
  background: none;
  border: none;
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
}
.tool-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.tool-item {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.tool-item:last-child {
  border-bottom: none;
}
.icon {
  width: 40px;
  height: 40px;
  margin-right: 16px;
}
.info {
  flex: 1;
}
.tool-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}
.desc {
  font-size: 14px;
  color: #888;
}
@media (max-width: 900px) {
  .title {
    font-size: 18px;
  }
  .tool-title {
    font-size: 14px;
  }
  .desc {
    font-size: 12px;
  }
  .more-btn {
    font-size: 12px;
  }
}
@media (max-width: 480px) {
  .title {
    font-size: 16px;
  }
  .tool-title {
    font-size: 12px;
  }
  .desc {
    font-size: 10px;
  }
  .more-btn {
    font-size: 10px;
  }
}
</style>
