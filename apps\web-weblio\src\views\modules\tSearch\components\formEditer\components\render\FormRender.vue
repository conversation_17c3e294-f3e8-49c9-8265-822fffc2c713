<script setup lang="ts">
import FormItemRender from '#/views/modules/tSearch/components/formEditer/components/render/FormItemRender.vue';
import {defineEmits, defineOptions, defineProps, ref, watch} from 'vue';

defineOptions({ name: 'FormRender', inheritAttrs: true });
const props = defineProps({
  class: {
    type: String,
    default: '',
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
  formConfig: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['update:modelValue']);
// const formData = ref(props.modelValue);
const formData: any = defineModel();
const showConfig = ref(props.formConfig);
// watch(
//   () => props.modelValue,
//   (val) => {
//     formData.value = val;
//   },
//   { deep: true },
// );
watch(
  () => props.formConfig,
  (val) => {
    showConfig.value = val;
  },
  { deep: true },
);
</script>
<template>
  <div :class="`h-full w-full ${props.class} `">
    <FormItemRender
      v-for="item in showConfig"
      :key="item.id"
      v-model="formData"
      :config="item"
    />
  </div>
</template>

<style scoped></style>
