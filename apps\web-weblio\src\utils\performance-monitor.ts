/**
 * 性能监控工具
 * 用于监控页面性能，识别性能瓶颈
 */

interface PerformanceMetrics {
  // 页面加载性能
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;

  // 内存使用情况
  memoryUsage: number;

  // 渲染性能
  renderTime: number;

  // 组件性能
  componentMetrics: Map<string, ComponentMetric>;
}

interface ComponentMetric {
  name: string;
  mountTime: number;
  updateCount: number;
  averageUpdateTime: number;
  lastUpdateTime: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private observers: PerformanceObserver[] = [];
  private renderTimings: Map<string, number> = new Map();

  constructor() {
    this.metrics = {
      loadTime: 0,
      domContentLoaded: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      memoryUsage: 0,
      renderTime: 0,
      componentMetrics: new Map(),
    };

    this.init();
  }

  private init() {
    // 监控页面加载性能
    this.observePageLoad();

    // 监控绘制性能
    this.observePaint();

    // 监控最大内容绘制
    this.observeLCP();

    // 监控内存使用
    this.observeMemory();
  }

  private observeLCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
      });

      observer.observe({ type: 'largest-contentful-paint', buffered: true });
      this.observers.push(observer);
    }
  }

  private observeMemory() {
    // 定期检查内存使用情况
    setInterval(() => {
      if ('memory' in performance) {
        // @ts-ignore
        this.metrics.memoryUsage =
          performance.memory.usedJSHeapSize / 1024 / 1024; // MB
      }
    }, 5000);
  }

  private observePageLoad() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType(
        'navigation',
      )[0] as PerformanceNavigationTiming;
      this.metrics.loadTime =
        navigation.loadEventEnd - navigation.loadEventStart;
      this.metrics.domContentLoaded =
        navigation.domContentLoadedEventEnd -
        navigation.domContentLoadedEventStart;
    });
  }

  private observePaint() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
          }
        });
      });

      observer.observe({ type: 'paint', buffered: true });
      this.observers.push(observer);
    }
  }

  private updateComponentMetric(componentName: string, duration: number) {
    const existing = this.metrics.componentMetrics.get(componentName);

    if (existing) {
      existing.updateCount++;
      existing.averageUpdateTime =
        (existing.averageUpdateTime * (existing.updateCount - 1) + duration) /
        existing.updateCount;
      existing.lastUpdateTime = duration;
    } else {
      this.metrics.componentMetrics.set(componentName, {
        name: componentName,
        mountTime: duration,
        updateCount: 1,
        averageUpdateTime: duration,
        lastUpdateTime: duration,
      });
    }
  }

  /**
   * 清理监控器
   */
  dispose() {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers = [];
    this.renderTimings.clear();
  }

  /**
   * 结束组件性能测量
   */
  endComponentMeasure(componentName: string) {
    const startTime = this.renderTimings.get(componentName);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.updateComponentMetric(componentName, duration);
      this.renderTimings.delete(componentName);
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取性能警告
   */
  getPerformanceWarnings(): string[] {
    const warnings: string[] = [];

    // 检查页面加载时间
    if (this.metrics.loadTime > 3000) {
      warnings.push(`页面加载时间过长: ${this.metrics.loadTime.toFixed(2)}ms`);
    }

    // 检查首次内容绘制
    if (this.metrics.firstContentfulPaint > 2500) {
      warnings.push(
        `首次内容绘制过慢: ${this.metrics.firstContentfulPaint.toFixed(2)}ms`,
      );
    }

    // 检查最大内容绘制
    if (this.metrics.largestContentfulPaint > 4000) {
      warnings.push(
        `最大内容绘制过慢: ${this.metrics.largestContentfulPaint.toFixed(2)}ms`,
      );
    }

    // 检查内存使用
    if (this.metrics.memoryUsage > 100) {
      warnings.push(`内存使用过高: ${this.metrics.memoryUsage.toFixed(2)}MB`);
    }

    // 检查组件性能
    this.metrics.componentMetrics.forEach((metric) => {
      if (metric.averageUpdateTime > 16) {
        // 60fps = 16.67ms per frame
        warnings.push(
          `组件 ${metric.name} 更新过慢: 平均 ${metric.averageUpdateTime.toFixed(2)}ms`,
        );
      }
    });

    return warnings;
  }

  /**
   * 输出性能报告到控制台
   */
  logPerformanceReport() {
    if (process.env.NODE_ENV === 'development') {
      console.group('🚀 性能监控报告');
      console.log('📊 页面性能:', {
        loadTime: `${this.metrics.loadTime.toFixed(2)}ms`,
        domContentLoaded: `${this.metrics.domContentLoaded.toFixed(2)}ms`,
        firstContentfulPaint: `${this.metrics.firstContentfulPaint.toFixed(2)}ms`,
        largestContentfulPaint: `${this.metrics.largestContentfulPaint.toFixed(2)}ms`,
      });

      console.log('💾 内存使用:', `${this.metrics.memoryUsage.toFixed(2)}MB`);

      if (this.metrics.componentMetrics.size > 0) {
        console.log('🧩 组件性能:');
        this.metrics.componentMetrics.forEach((metric) => {
          console.log(
            `  ${metric.name}: 平均更新时间 ${metric.averageUpdateTime.toFixed(2)}ms, 更新次数 ${metric.updateCount}`,
          );
        });
      }

      const warnings = this.getPerformanceWarnings();
      if (warnings.length > 0) {
        console.warn('⚠️ 性能警告:');
        warnings.forEach((warning) => console.warn(`  ${warning}`));
      }

      console.groupEnd();
    }
  }

  /**
   * 开始组件性能测量
   */
  startComponentMeasure(componentName: string) {
    this.renderTimings.set(componentName, performance.now());
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// Vue组件性能监控装饰器
export function withPerformanceMonitoring(componentName: string) {
  return function (target: any) {
    const originalMounted = target.mounted;
    const originalUpdated = target.updated;

    target.mounted = function () {
      performanceMonitor.startComponentMeasure(`${componentName}:mount`);
      if (originalMounted) {
        originalMounted.call(this);
      }
      performanceMonitor.endComponentMeasure(`${componentName}:mount`);
    };

    target.updated = function () {
      performanceMonitor.startComponentMeasure(`${componentName}:update`);
      if (originalUpdated) {
        originalUpdated.call(this);
      }
      performanceMonitor.endComponentMeasure(`${componentName}:update`);
    };

    return target;
  };
}

// 开发环境下自动输出性能报告
if (process.env.NODE_ENV === 'development') {
  // 页面加载完成后延迟输出报告
  window.addEventListener('load', () => {
    setTimeout(() => {
      performanceMonitor.logPerformanceReport();
    }, 3000);
  });

  // 定期输出性能警告
  setInterval(() => {
    const warnings = performanceMonitor.getPerformanceWarnings();
    if (warnings.length > 0) {
      console.warn('🚨 实时性能警告:', warnings);
    }
  }, 30_000); // 每30秒检查一次
}
