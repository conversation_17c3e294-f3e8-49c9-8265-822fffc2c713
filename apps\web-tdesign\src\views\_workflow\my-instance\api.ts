import { requestClient } from '#/api/request';

export async function listByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-workflow/queryInstance/listByPage2', data);
}

export async function revokeInstance(data: any) {
  return requestClient.post<any>('/rgdc-workflow/instance/revokeInstance', data);
}

export async function undeploy(id: any) {
  return requestClient.delete<any>(`/rgdc-workflow/process/undeploy/${id}`);
}

export async function cascadeRemove(id: any) {
  return requestClient.delete<any>(`/rgdc-workflow/process/cascadeRemove/${id}`);
}
