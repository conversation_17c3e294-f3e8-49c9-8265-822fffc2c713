import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据集数据-元数据',
    },
    name: 'dDatasetBase',
    path: '/dDatasetBase',
    children: [
      {
        meta: {
          title: '数据集数据-元数据编辑',
        },
        name: 'dDatasetBaseIndex',
        path: '/dDatasetBase/index',
        component: () =>
          import('#/views/modules/dDatasetBase/index.vue'),
      },
    ],
  },
];

export default routes;
