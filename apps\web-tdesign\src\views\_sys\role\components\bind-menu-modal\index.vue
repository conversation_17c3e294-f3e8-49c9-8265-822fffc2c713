<script lang="ts" setup>
import { computed, h, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useI18n } from '@vben/locales';

import {
  Button,
  Card,
  Col,
  EnhancedTable,
  Form,
  FormItem,
  Input,
  Popconfirm,
  Row,
  Space,
  Tag,
} from 'tdesign-vue-next';

import ColumnDisplay from '#/components/column-display/index.vue';
import { treeToArray } from '#/utils/dataTransformation';
import { allMenu } from '#/views/_sys/menu/api';

import {
  bindRoleMenuListApi,
  deleteRoleMenu,
  roleBindMenuDeleteBatch,
  saveBindRoleMenuList,
} from '../../api';
// const { t } = useI18n();

// 表单控制属性
const formData: any = ref({});
const record: any = ref({});
const form = ref();

// 数据表控制属性
const data: any = ref([]);
const loading = ref(false);
const stripe = ref(true);
const tableLayout = ref(false);
const size: any = ref('medium');
const showHeader = ref(true);
const selectedRowKeys = ref([]);
const columnControllerVisible = ref(false);
const { t } = useI18n();
const columns: any = ref([
  // 多选
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
  },

  {
    colKey: 'meta.title',
    title: '菜单显示名称',
    ellipsis: true,
    cell: (e: any, { row }: any) => {
      return t(`${row.meta.title}`);
    },
  },
  {
    colKey: 'description',
    title: '描述',
    ellipsis: true,
  },
  {
    colKey: 'code',
    title: '菜单编码',
    ellipsis: true,
  },

  {
    colKey: 'name',
    title: '路由名称',
    ellipsis: true,
  },
  {
    colKey: 'path',
    title: '路由Path',
    ellipsis: true,
  },
  {
    colKey: 'status',
    title: '状态',
    ellipsis: true,
    cell: (e: any, { row }: any) => {
      return row.status === 1
        ? h(
            Tag,
            {
              theme: 'primary',
            },
            '启用',
          )
        : h(
            Tag,
            {
              theme: 'danger',
            },
            '禁用',
          );
    },
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    ellipsis: true,
  },
  {
    colKey: 'description',
    title: '备注',
    ellipsis: true,
  },
]);

const expandAll = ref(true);
const groupColumn = ref(true);
const placement = ref<any['placement']>('top-right');
const customText = ref(true);
const tableRef = ref();
const displayColumns = ref<any['displayColumns']>([
  'row-select',
  'serial-number',
  'code',
  'name',
  'status',
  'description',
  'remark',
  'meta.title',
]);
// 列配置
const columnControllerConfig = computed<any['columnController']>(() => ({
  // 列配置按钮位置
  placement: placement.value,
  hideTriggerButton: true,
  // 用于设置允许用户对哪些列进行显示或隐藏的控制，默认为全部字段
  fields: [
    'row-select',
    'code',
    'name',
    'path',
    'status',
    'description',
    'remark',
    'createTime',
    'updateTime',
  ],
  // 弹框组件属性透传
  dialogProps: {
    preventScrollThrough: true,
  },
  // 列配置按钮组件属性透传
  buttonProps: customText.value
    ? {
        content: '',
        theme: '',
        variant: 'text',
      }
    : undefined,
  // 数据字段分组显示
  groupColumns: groupColumn.value
    ? [
        {
          label: '业务字段',
          value: 'a',
          columns: ['code', 'name', 'path', 'description', 'status'],
        },
        {
          label: '系统字段',
          value: 'b',
          columns: ['drag', 'row-select'],
        },
        {
          label: '记录字段',
          value: 'c',
          columns: ['createTime', 'updateTime'],
        },
      ]
    : undefined,
}));

const refreshExpand = () => {
  expandAll.value ? tableRef.value.expandAll() : tableRef.value.foldAll();
};
const fetchData = async (params: any) => {
  loading.value = true;
  try {
    const res = await allMenu(params);
    data.value = res;
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const showAll = () => {
  expandAll.value = !expandAll.value;
  expandAll.value ? tableRef.value.expandAll() : tableRef.value.foldAll();
};

const loadData = async () => {
  const params = formData.value;
  await fetchData(params);
};

const loadBindMenu = async (params: any) => {
  try {
    loading.value = true;
    const res = await bindRoleMenuListApi(params);
    selectedRowKeys.value = res;
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const onReset = async () => {
  form.value.reset();
  await loadData();
  refreshExpand();
};
const onSubmit = async () => {
  await loadData();
  refreshExpand();
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      record.value = isOpen
        ? { ...modalApi.getData<Record<string, any>>()?.record }
        : {};
      await loadBindMenu(record.value?.code);
      await loadData();
      refreshExpand();
    }
  },

  title: '分配菜单',
});

const saveBindMenu = async (params: any) => {
  await saveBindRoleMenuList(params, record.value.code);
};

const authClick = async (row: any) => {
  await saveBindMenu(row);

  // 全部展开
};

const unAuthClick = async (row: any) => {
  await roleBindMenuDeleteBatch(record.value.code, row);
};

const allDel = async () => {
  await deleteRoleMenu(record.value.code);
  await loadData();
  await loadBindMenu(record.value.code);
  refreshExpand();
};

const handleSelectChange = async (value: any, ctx: any) => {
  selectedRowKeys.value = value;
  if (ctx.type === 'check') {
    // [{code:'code'}]
    if (ctx.currentRowKey === 'CHECK_ALL_BOX') {
      const data = ctx.selectedRowData.map((item: any) => {
        return { code: item.code };
      });
      await authClick(data);
    } else {
      await authClick([{ code: ctx.currentRowData.code }]);
    }
  } else {
    // [id]
    if (ctx.currentRowKey === 'CHECK_ALL_BOX') {
      const array = treeToArray(data.value);
      const param = array.map((item: any) => item.code);
      await unAuthClick(param);
    } else {
      await unAuthClick([ctx.currentRowData.code]);
    }
  }

  await loadBindMenu(record.value.code);
  refreshExpand();
};
</script>

<template>
  <Modal :footer="false" class="w-8/12">
    <Space direction="vertical">
      <Card>
        <Form
          ref="form"
          :data="formData"
          :label-width="80"
          colon
          @reset="onReset"
          @submit="onSubmit"
        >
          <Row :gutter="[24, 24]">
            <Col :span="4">
              <FormItem label="菜单编码" name="code">
                <Input
                  v-model="formData.code"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入菜单编码"
                  type="search"
                />
              </FormItem>
            </Col>
            <Col :span="4">
              <FormItem label="路由名称" name="name">
                <Input
                  v-model="formData.name"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入路由名称"
                  type="search"
                />
              </FormItem>
            </Col>

            <Col :span="4">
              <FormItem label="描述" name="description">
                <Input
                  v-model="formData.description"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入描述"
                />
              </FormItem>
            </Col>
          </Row>
          <Row justify="end">
            <Col :span="24" class="mt-4">
              <Space size="small">
                <Button
                  :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
                  theme="primary"
                  type="submit"
                >
                  搜索
                </Button>
                <Button theme="default" type="reset" variant="base">
                  重置
                </Button>
                <!-- <PutAway v-model="hideQuery" variant="text" /> -->
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card>
        <div class="t-row--space-between mb-2 flex items-center justify-center">
          <div class="flex flex-wrap items-center justify-center gap-1">
            <div class="t-card__title ml-2">绑定菜单列表</div>
            <div
              v-if="selectedRowKeys && selectedRowKeys.length > 0"
              class="text-[gray]"
            >
              已选择 {{ selectedRowKeys?.length || 0 }} 条数据
            </div>
          </div>
          <div class="flex flex-wrap items-center justify-center gap-2">
            <Popconfirm
              content="确认取消该角色全部授权?"
              theme="danger"
              @confirm="allDel"
            >
              <Button theme="danger">取消全部授权</Button>
            </Popconfirm>
            <Button theme="default" @click="showAll"> 展开/收起</Button>
            <ColumnDisplay v-model="columnControllerVisible" />
          </div>
        </div>
        <EnhancedTable
          ref="tableRef"
          v-model:column-controller-visible="columnControllerVisible"
          v-model:display-columns="displayColumns"
          :column-controller="columnControllerConfig"
          :columns="columns"
          :data="data"
          :height="450"
          :loading="loading"
          :scroll="{ type: 'virtual', rowHeight: 48, bufferSize: 5 }"
          :selected-row-keys="selectedRowKeys"
          :show-header="showHeader"
          :size="size"
          :stripe="stripe"
          :table-layout="tableLayout ? 'auto' : 'fixed'"
          :tree="{
            childrenKey: 'children',
            treeNodeColumnIndex: 1,
            indent: 32,
            expandTreeNodeOnClick: false,
          }"
          cell-empty-content="-"
          resizable
          row-key="code"
          @select-change="handleSelectChange"
        />
      </Card>
    </Space>
  </Modal>
</template>
