<script setup lang="ts">
import type { TabOption } from '../../../store/tabs';

import { isLogin } from '#/components/notLoginGo';
import { $t } from '#/locales';
import { useAuthStore } from '#/store';
import { useUserStore } from '@vben/stores';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import SmartTabs from '../../../components/smart-tabs/index.vue';

const userStore = useUserStore();
const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
// src/api/silicon-data-model.ts

const logo = ref('/static/images/02-Vector.png');

// 使用tabs store

// 选中的tab
const selectedTab = ref('home');

// 控制下拉菜单显示
const showDropdown = ref(false);
let hideTimeout: NodeJS.Timeout | null = null;

// tab选项
const tabOptions = ref<TabOption[]>([
  {
    key: 'home',
    label: $t('webLio.header.menu.menu1'),
  },
  {
    key: 'industry-data',
    // label: $t('webLio.header.menu.menu2'),
    label: '基础数据',
  },
  {
    key: 'feature-data',
    label: $t('webLio.header.menu.menu3'),
  },
  {
    key: 'tSysDataTools',
    label: $t('webLio.header.menu.menu4'),
  },
  {
    key: 'data-flow',
    label: $t('webLio.header.menu.menu5'),
  },
  {
    key: 'about',
    label: $t('webLio.header.menu.menu7'),
  },
]);

// 根据路由路径设置选中的tab
const setSelectedTabFromRoute = () => {
  const currentPath = route.path;
  // 去掉开头的 '/' 来匹配tab的key
  const pathKey = currentPath.startsWith('/')
    ? currentPath.slice(1)
    : currentPath;

  // 检查是否存在对应的tab选项
  const matchedTab = tabOptions.value.find((tab) => tab.key === pathKey);
  // 如果没有匹配的tab，默认选中home
  selectedTab.value = matchedTab ? pathKey : 'home';
};

// 处理tab切换
const handleTabChange = (tabKey: string) => {
  router.replace({
    path: `/${tabKey}`,
  });
};

// 处理登录/注册点击
const handleAuth = () => {
  router.push('/auth/login');
};

// 处理个人中心点击
const handleProfile = () => {
  isLogin().then((res) => {
    if (res) {
      router.push('/me');
    }
  });
  showDropdown.value = false;
};

// 处理退出登录点击
const handleLogout = async () => {
  showDropdown.value = false;
  await authStore.logoutWebLio();
  router.push('/auth/login');
};

// 处理鼠标进入事件
const handleMouseEnter = () => {
  if (hideTimeout) {
    clearTimeout(hideTimeout);
    hideTimeout = null;
  }
  showDropdown.value = true;
};

// 处理鼠标离开事件
const handleMouseLeave = () => {
  hideTimeout = setTimeout(() => {
    showDropdown.value = false;
  }, 200); // 200ms延迟，给用户时间移动到下拉菜单
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (hideTimeout) {
    clearTimeout(hideTimeout);
  }
});

// 监听路由变化，更新选中的tab
watch(
  () => route.path,
  () => {
    setSelectedTabFromRoute();
  },
  { immediate: true },
);

onMounted(() => {
  setSelectedTabFromRoute();
});
</script>

<template>
  <div class="weblio-header">
    <div class="weblio-header-left">
      <div class="weblio-header-left-logo">
        <img :src="logo" alt="WebLio Logo" />
      </div>
      <div class="weblio-header-left-title">
        {{ $t('webLio.header.title.title1') }}
      </div>
    </div>
    <div class="weblio-header-center">
      <SmartTabs
        :options="tabOptions"
        v-model="selectedTab"
        @change="handleTabChange"
      />
    </div>
    <div class="weblio-header-right">
      <div
        v-if="userStore?.userInfo?.realName"
        class="user-dropdown"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <div class="user-info">
          <div class="user-avatar">
            <svg viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="8" r="4" fill="currentColor" />
              <path
                d="M12 14c-4.42 0-8 1.79-8 4v2h16v-2c0-2.21-3.58-4-8-4z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div class="user-details">
            <div class="user-name">{{ userStore?.userInfo?.realName }}</div>
            <div class="user-id">
              {{ userStore?.userInfo?.username || userStore?.userInfo?.phone }}
            </div>
          </div>
        </div>
        <div v-show="showDropdown" class="dropdown-menu">
          <div class="dropdown-item" @click="handleProfile">
            <svg class="icon" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                fill="currentColor"
              />
            </svg>
            个人中心
          </div>
          <div class="dropdown-item" @click="handleLogout">
            <svg class="icon" viewBox="0 0 24 24" fill="none">
              <path
                d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"
                fill="currentColor"
              />
            </svg>
            退出登录
          </div>
        </div>
      </div>
      <div v-else class="login-btn" @click="handleAuth">登录/注册</div>
    </div>
  </div>
</template>

<style scoped lang="less">
.weblio-header {
  font-size: 16px; // 字体整体放大
  min-height: 64px; // 增加整体高度
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 12px;
  &-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;

    &-logo {
      img {
        height: 48px; // 图片高度适当增加
        width: auto;
      }
    }

    &-title {
      font-size: 24px; // 字体加大
      font-weight: 600;
      color: #083786;
    }
  }

  &-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 20px;
    :deep(.smart-tabs__tab) {
      font-size: 17px;
    }
  }

  &-right {
    flex-shrink: 0;
    position: relative;

    .login-btn {
      padding: 8px 16px;
      color: #3b82f6;
      cursor: pointer;
      transition: color 0.2s ease;
      font-size: 14px;

      &:hover {
        color: #2563eb;
      }
    }

    .user-dropdown {
      position: relative;
      display: inline-block;
    }

    .user-info {
      padding: 8px 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: background-color 0.2s ease;
      border-radius: 6px;

      &:hover {
        background-color: rgba(59, 130, 246, 0.05);
      }
    }

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      background-color: #f3f4f6;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #9ca3af;

      svg {
        width: 20px;
        height: 20px;
      }
    }

    .user-details {
      flex: 1;
      min-width: 0;
    }

    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      line-height: 1.2;
      margin-bottom: 2px;
    }

    .user-id {
      font-size: 12px;
      color: #666;
      line-height: 1.2;
    }

    .arrow {
      font-size: 10px;
      color: #666;
      transition: transform 0.2s ease;
      flex-shrink: 0;
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      min-width: 120px;
      z-index: 1000;
      margin-top: 4px;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 0;
        right: 0;
        height: 4px;
        background: transparent;
      }
    }

    .dropdown-item {
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      font-size: 14px;
      color: #374151;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        background-color: #f8fafc;
      }

      .icon {
        width: 16px;
        height: 16px;
        color: #666;
        flex-shrink: 0;
      }
    }
  }
}

// 深色主题适配
.dark .weblio-header {
  &-left-title {
    color: #f1f5f9;
  }

  &-right {
    .login-btn {
      color: #60a5fa;

      &:hover {
        color: #93c5fd;
      }
    }

    .user-info {
      &:hover {
        background-color: rgba(96, 165, 250, 0.1);
      }
    }

    .user-name {
      color: #f1f5f9;
    }

    .user-id {
      color: #9ca3af;
    }

    .arrow {
      color: #9ca3af;
    }

    .dropdown-menu {
      background: #374151;
      border-color: #4b5563;
    }

    .dropdown-item {
      color: #d1d5db;

      &:hover {
        background-color: #4b5563;
        color: #f3f4f6;
      }

      .icon {
        color: #9ca3af;
      }
    }
  }

  // 深色主题下的 tabs 样式
  &-center {
    :deep(.smart-tabs__tab) {
      color: #d1d5db;

      &:hover:not(.smart-tabs__tab--disabled):not(.smart-tabs__tab--active) {
        background-color: #374151;
        color: #f3f4f6;
      }

      &.smart-tabs__tab--active {
        background-color: #3b82f6;
        color: white;

        .smart-tabs__icon {
          color: white;
        }
      }

      &.smart-tabs__tab--disabled {
        color: #6b7280;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .weblio-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;

    &-left {
      justify-content: center;
    }

    &-center {
      width: 100%;
      margin: 0;
      max-width: none;

      :deep(.smart-tabs__track) {
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
      }

      :deep(.smart-tabs__tab) {
        padding: 10px 16px;
        font-size: 13px;
        flex: 1;
        min-width: fit-content;
      }
    }

    &-right {
      width: 100%;

      .login-btn {
        text-align: center;
        width: 100%;
      }

      .user-dropdown {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      .user-info {
        justify-content: center;
        width: auto;
      }

      .dropdown-menu {
        right: auto;
        left: 50%;
        transform: translateX(-50%);
        min-width: 140px;
      }
    }
  }
}
</style>
