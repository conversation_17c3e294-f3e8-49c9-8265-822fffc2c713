<script setup lang="tsx">
import {defineProps, onMounted, reactive, ref} from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {getDictItems} from '#/api';
import {Input, Select, Textarea, Upload} from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import { save } from '../api.ts';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
  status: [
    {
      required: true,
      message: '请选择',
    },
  ],
  finishReason: [
    {
      required: true,
      message: '请输入',
    },
  ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    const vali = await form.value.validate();
    if (vali === true) {
      let param = {id:null,status:'',finishReason:'',isDeleted:'0'}
      param.id = formData.value.id
      param.status = formData.value.status
      param.finishReason = formData.value.finishReason
      param.createdId = formData.value.createdId
      reqRunner.save.run(param);
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  if (data) {
    formData.value = data;
    if(data.imageFile){
      imageUrl.value = JSON.parse(data.imageFile)[0].url
    }
    for(const item of feedbackTypeList.value){
      if(item.value == data.feedbackType){
        formData.value.feedbackType = item.label
      }
    }
  }
  modalApi.open();
};

const statusList = ref()
const feedbackTypeList = ref()
const imageUrl = ref()
onMounted(async () => {
  imageUrl.value = ''
  feedbackTypeList.value = await getDictItems('FEEDBACK_TYPE');
  statusList.value = await getDictItems('FEEDBACK_STATUS');
});
defineExpose({
  open,
});
</script>

<template>
  <Modal title="处理" class="w-[40%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES" class="w-full" label-align="left">
      <div class="grid w-[90%] grid-cols-1 gap-1">
        <FormItem label="反馈类型：">
           {{formData.feedbackType}}
        </FormItem>
        <FormItem label="问题描述：">
           {{formData.description}}
        </FormItem>
        <FormItem label="" v-if="imageUrl">
          <img :src="imageUrl">
        </FormItem>
        <FormItem label="联系方式：">
          {{formData.contactWay}}
        </FormItem>
        <FormItem label="反馈状态：" name="status">
          <Select v-model="formData.status" :options="statusList" clearable placeholder="请选择反馈状态"/>
        </FormItem>
        <FormItem label="解决说明：" name="finishReason">
          <Textarea v-model="formData.finishReason" clearable placeholder="请输入"
                    :maxlength="200" :autosize="{ minRows: 5, maxRows: 8 }"/>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
