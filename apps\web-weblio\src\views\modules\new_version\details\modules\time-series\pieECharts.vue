<template>
  <div ref="chart" :style="{ width: '100%', height: '400px' }"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted, ref, watch } from 'vue';

const props = defineProps({
  seriesData: {
    type: Array as () => Array<{
      name: string;
      value: number;
    }>,
    required: true,
  },
  title: {
    type: String,
    default: ''
  }
});

const chart = ref(null);
let myChart = null;

onMounted(() => {
  initChart();
});

const initChart = () => {
  if (!chart.value) return;

  myChart = echarts.init(chart.value);

  const option = {
    title: props.title ? { text: props.title } : undefined,
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '分类',
        type: 'pie',
        radius: '50%',
        data: props.seriesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  myChart.setOption(option);
};

watch(
  () => props.seriesData,
  (newSeriesData) => {
    if (myChart) {
      myChart.setOption({
        series: [
          {
            data: newSeriesData
          }
        ]
      });
    } else {
      initChart();
    }
  }
);
</script>
