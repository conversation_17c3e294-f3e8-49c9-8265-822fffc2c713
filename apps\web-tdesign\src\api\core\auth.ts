import { requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
    captcha?: string;
    captchaKey?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

// 获取图片验证码
export async function getAuthCaptchaImageApi(key: any) {
  return requestClient.get<string>('/rgdc-user/randomImage/' + key);
}

// 获发送短信验证码
export async function sendSmsCodeApi(phoneNumber: any, captcha?: string, checkKey?: string, type?: string) {
  const params: any = { phoneNumber };
  if (captcha && checkKey) {
    params.captcha = captcha;
    params.checkKey = checkKey;
  }
  if (type) {
    params.type = type;
  }
  return requestClient.post<string>('/rgdc-user/sendSmsCode', params);
}

/**
 * 钉钉登录
 */
export async function dingtalkLoginApi(data: any) {
  return requestClient.post<string[]>('/rgdc-user/auth/dingtalkLogin', data);
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/rgdc-user/auth/login', data);
}

/**
 * 手机号登录
 */
export async function phoneNumberLoginApi(data: { 
  phoneNumber: string; 
  smsCode: string; 
  captcha?: string; 
  checkKey?: string; 
}) {
  return requestClient.post<AuthApi.LoginResult>('/rgdc-user/auth/phoneNumberLogin', data);
}

// /**
//  * 刷新accessToken
//  */
// export async function refreshTokenApi() {
//   return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
//     withCredentials: true,
//   });
// }

/**
 * 注册
 */
export async function registerApi(data: any) {
  return requestClient.post('/rgdc-user/auth/register', data);
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return requestClient.post('/rgdc-user/auth/logout');
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/rgdc-user/auth/codes');
}

export async function updateMyPassword(data: any) {
  return requestClient.post('/rgdc-user/account/updateMyPassword', data);
}
