<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import {
  Button,
  Card,
  Checkbox,
  Col,
  Form,
  FormItem,
  Input,
  Row,
  StepItem,
  Steps,
} from 'tdesign-vue-next';

import WorkFlow from '#/views/_workflow/flowlong/index.vue';

const rules: any = {
  processName: [{ required: true, message: '流程名称', type: 'error' }],
  processKey: [{ required: true, message: '流程Key', type: 'error' }],
};

const current = ref(0);
const refForm = ref();
const formData: any = ref({});
const modelContent: any = ref({});

const getFormData = () => {
  const res: any = {};
  res.processName = formData.value.processName;
  res.processKey = formData.value.processKey;
  res.modelContent = modelContent.value;
  res.repeat = formData.value.repeat;
  return res;
};

const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange: (isOpen: any) => {
    // 每次打开重置
    current.value = 0;
    formData.value = {};
    modelContent.value = {};
    if (isOpen && drawerApi.getData()?.record) {
      current.value = 1;
      formData.value.processName = drawerApi.getData().record?.processName;
      formData.value.processKey = drawerApi.getData().record?.processKey;
      modelContent.value = JSON.parse(drawerApi.getData().record?.modelContent);
    } else {
      modelContent.value = {};
    }
  },

  onConfirm() {
    // 确认时回调父组件方法
    drawerApi.getData().onConfirm(getFormData());
  },

  onCancel() {
    drawerApi.setData({});
    drawerApi.close();
  },
});

const checkForm = async () => {
  const vali = await refForm.value.validate();
  if (vali === true) {
    current.value++;
    if (Object.keys(modelContent.value).length === 0) {
      modelContent.value = {
        id: 1,
        name: formData.value.processName,
        key: formData.value.processKey,
        nodeConfig: {
          nodeName: '发起人',
          nodeKey: 'flk0001',
          type: 0,
          nodeAssigneeList: [],
          childNode: {},
        },
      };
    }
  }

  formData.value.repeat = true;
  // 你我皆凡人
};

const nextClick = () => {
  current.value++;
  formData.value.repeat = true;
};

const isEdit = () => {
  return !!drawerApi.getData()?.record;
};
</script>
<template>
  <Drawer :show-confirm-button="current === 2" title="流程定义设计">
    <Row class="h-full max-h-full max-w-full">
      <Col class="h-full" flex="200px">
        <Steps
          :current="current"
          class="steps-demos-extra"
          layout="vertical"
          status="process"
        >
          <StepItem content="基础信息录入" title="基础信息">
            <template v-if="current === 0" #extra>
              <Button size="small" variant="base" @click="checkForm">
                下一步
              </Button>
            </template>
          </StepItem>
          <StepItem content="设计工作流程节点" title="流程设计">
            <template v-if="current === 1" #extra>
              <Button size="small" variant="text" @click="current--">
                上一步
              </Button>
              <Button size="small" variant="base" @click="nextClick">
                下一步
              </Button>
            </template>
          </StepItem>

          <StepItem content="其他设置" title="高级设置">
            <template v-if="current === 2" #extra>
              <Button size="small" @click="current--"> 上一步 </Button>
            </template>
          </StepItem>
        </Steps>
      </Col>

      <Col class="relative h-full" flex="auto">
        <div
          v-if="current === 0"
          class="flex h-full w-full content-center justify-center"
        >
          <Card class="w-2/4">
            <Form
              ref="refForm"
              :data="formData"
              :rules="rules"
              label-align="top"
            >
              <FormItem label="流程Key" name="processKey">
                <Input
                  v-model="formData.processKey"
                  :disabled="isEdit()"
                  placeholder="请输入流程Key"
                />
              </FormItem>
              <FormItem label="流程名称" name="processName">
                <Input
                  v-model="formData.processName"
                  :disabled="isEdit()"
                  placeholder="请输入流程名称"
                />
              </FormItem>
            </Form>
          </Card>
        </div>
        <div v-if="current === 1" class="absolute h-full w-full overflow-auto">
          <WorkFlow v-model="modelContent" />
        </div>
        <div
          v-if="current === 2"
          class="flex h-full w-full content-center justify-center"
        >
          <Card class="w-2/4">
            <Form
              ref="refForm"
              :data="formData"
              :rules="rules"
              label-align="top"
            >
              <FormItem label="是否重复部署" name="repeat">
                <Checkbox v-model="formData.repeat">
                  (勾选)则存在版本+1新增记录,(不勾选)存在流程直接返回
                </Checkbox>
              </FormItem>
            </Form>
          </Card>
        </div>
      </Col>
    </Row>
  </Drawer>
</template>
<style lang="less" scoped>
.steps-demos-extra {
  .t-button + .t-button {
    margin-left: 4px;
  }
}
</style>
