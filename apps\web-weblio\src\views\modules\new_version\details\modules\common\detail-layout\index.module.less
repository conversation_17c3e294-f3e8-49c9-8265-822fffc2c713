.three-col-layout {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background: #f7f8fa;
  font-size: 16px;
}

.three-col-top-card {
  padding: 0 24px;
  margin-bottom: 16px;
}

.three-col-main-area {
  display: flex;
  flex: 1;
  width: 100%;
  min-height: 600px;
  background: var(--background-deep, #f5f6fa);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  gap: 24px;
  padding: 0 24px 24px 24px;
}

.three-col-menu {
  width: 180px;
  min-width: 120px;
  border: none;
  padding: 24px;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(44, 90, 160, 0.06);
  height: 100%;
  margin: 0;
}

@media (min-width: 1200px) {
  .three-col-menu {
    position: sticky;
    top: 24px; // 与 padding 保持一致，防止遮挡
    z-index: 10;
    align-self: flex-start;
  }
  .three-col-right {
    position: sticky;
    top: 24px;
    z-index: 10;
    align-self: flex-start;
  }
}

.three-col-menu-item {
  position: relative;
  padding: 12px 0 12px 20px;
  cursor: pointer;
  border-radius: 8px 0 0 8px;
  color: #333;
  font-size: 1em;
  font-weight: 500;
  transition: background 0.2s, font-weight 0.2s, color 0.2s;
  background: transparent;
  margin: 0;
  &:hover {
    background: #f5f7fa;
    color: #2c5aa0;
    font-weight: 600;
  }
  &.active {
    background: #e5eefa;
    font-weight: 700;
    color: #2c5aa0;
  }
}

.three-col-content {
  flex: 1;
  overflow-y: auto;
  min-height: 600px;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(44, 90, 160, 0.06);
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.three-col-content-block {
  margin-bottom: 0;
}

.three-col-content-title {
  font-size: 1.25em;
  font-weight: bold;
  margin-bottom: 12px;
  color: #222;
}

.three-col-right {
  width: 260px;
  min-width: 160px;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(44, 90, 160, 0.06);
  height: 100%;
  padding: 24px;
  margin: 0;
  border: none;
}

.three-col-right-block {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.three-col-right-block + .three-col-right-block {
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}

// 响应式设计
@media (max-width: 1200px) {
  .three-col-layout {
    font-size: 15px;
  }
  .three-col-main-area {
    gap: 12px;
    padding: 0 12px 12px 12px;
  }
  .three-col-content {
    padding: 24px 8px;
  }
}

@media (max-width: 1024px) {
  .three-col-layout {
    font-size: 14px;
  }
  .three-col-main-area {
    flex-direction: column;
    gap: 8px;
    padding: 0 8px 8px 8px;
  }
  .three-col-menu,
  .three-col-content,
  .three-col-right {
    width: 100%;
    padding: 8px;
    gap: 8px;
    border-radius: 8px;
  }
  .three-col-menu-item {
    font-size: 14px;
    padding: 8px 8px;
  }
  .three-col-content-title {
    font-size: 16px;
  }
  .three-col-right-title {
    font-size: 14px;
  }
  .three-col-top-card {
    padding: 0 8px;
    margin-bottom: 8px;
  }
  .three-col-menu {
    position: sticky;
    top: 0;
    z-index: 10;
    align-self: flex-start;
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    padding: 8px 0 8px 0;
    background: #2c5aa0;
    color: #fff;
    gap: 8px;
  }
  .three-col-menu-item {
    display: inline-block;
    min-width: 80px;
    text-align: center;
    padding: 8px 12px;
    border-radius: 8px;
    margin: 0 4px;
    color: #fff;
    background: transparent;
  }
  .three-col-menu-item.active, .three-col-menu-item:hover {
    background: #fff;
    color: #2c5aa0;
    font-weight: 600;
    border-radius: 8px;
    font-size: 1em;
    border-bottom: 2px solid #2c5aa0;
  }
}

@media (max-width: 768px) {
  .three-col-layout {
    font-size: 13px;
  }
  .three-col-layout {
    padding: 8px !important;
  }
  .three-col-top-card {
    margin-bottom: 8px !important;
    padding: 0 8px !important;
  }
  .three-col-main-area {
    gap: 8px !important;
    padding: 0 8px 8px 8px !important;
  }
  .three-col-menu,
  .three-col-content,
  .three-col-right {
    padding: 8px !important;
    margin: 0 !important;
    gap: 8px !important;
    border-radius: 8px !important;
  }
  .three-col-content-block {
    margin-bottom: 8px !important;
  }
  .three-col-menu-item {
    font-size: 13px;
    padding: 6px 6px;
  }
  .three-col-content-title {
    font-size: 14px;
  }
  .three-col-right-title {
    font-size: 13px;
  }
  .three-col-top-card {
    padding: 0 2px;
    margin-bottom: 4px;
  }
}

@media (max-width: 600px) {
  .three-col-layout {
    font-size: 12px;
  }
  .three-col-main-area {
    gap: 4px;
  }
  .three-col-menu,
  .three-col-right {
    flex-direction: column;
    padding: 4px 0;
    font-size: 12px;
  }
  .three-col-content {
    padding: 4px 0;
    gap: 8px;
  }
} 
