// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataDataset/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataDataset/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tDataDataset/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataDataset/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataDataset/getByIds/${data}`);
}
