<script setup lang="tsx">
import {Page} from '@vben/common-ui';
import {Button, Card, Form, FormItem, MessagePlugin, Tag} from 'tdesign-vue-next';
import {defineModel, defineProps, onMounted, reactive, ref} from 'vue';
import {useRoute} from "vue-router";
import {DownloadIcon, HeartIcon, ShareIcon} from "tdesign-icons-vue-next";
import {useRequest} from "vue-hooks-plus";
import {baseDownloadFile, getDictItems, getUserInfoApi} from "#/api";
import {getOneByOperationCode, saveFavorites} from "#/views/modules/tPortalFavorites/api.ts";
import {getOneByFileCode, getMdByFileCode, getLabelList, getOne} from './api';
import {rsaEncrypt} from "#/utils/rsa.ts";
import { mavonEditor } from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import {getCategorys} from "#/views/modules/tDataBase/api.ts";
import {getByCode, getDatasetList} from '#/views/modules/tData/api.ts';
import ShareForm from './components/ShareForm.vue';


/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },

});

const ShareFormRef = ref<InstanceType<typeof ShareForm> | null>(null);
const route = useRoute()
const pdfUrl = ref()
const prouteCode = ref();
const type = ref();
const markdown = ref()

const labels = ref();
const classes = ref();
const category = ref();
const labelses = ref();
const classess = ref();
const categorys = ref();
const categoryList = ref([]);
const labelsTags = ref([]);
const datasetName = ref('');
const classesName = ref('');
const categoryName = ref('');
const operationFlag = ref('');
const dataset = ref();
const datasets = ref();

onMounted(async () => {
//--------------------------------------------------------------------------------------------------
  //数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  //标签库
  labelses.value = await getLabelList();
  if (route.query.prouteCode) {
    prouteCode.value = route.query.prouteCode;
    const datas = await getOne(route.query.prouteCode);
    operationFlag.value = datas.operationFlag;
    classes.value = datas.classCode ? Number(datas.classCode) : null;
    categorys.value = await getCategorys(classes.value);
    category.value = JSON.parse(datas.categoryCode)
    //数据集
    datasets.value = await getDatasetList({categoryList:category.value});
    category.value = category.value.map(item => Number(item))
    labels.value = JSON.parse(datas.labelCode)
    dataset.value = datas.datasetCode
    categoryList.value = [];
    await getCategoryList(categorys.value)
    let labelsTag;
    labelsTags.value=[];
    datasetName.value = ''
    classesName.value = ''
    categoryName.value = ''
    if (labels.value) {
      for (const item of labels.value) {
        for (const it of labelses.value) {
          if (item == it.value) {
            labelsTag = {};
            labelsTag["key"] = it.value;
            labelsTag["content"] = it.label;
            labelsTag["color"] = it.color;
            labelsTags.value.push(labelsTag);
          }
        }
      }
    }
    if (dataset.value) {
      for (const it of datasets.value) {
        if (dataset.value == it.value) {
          datasetName.value += it.label
        }
      }
    }
    for (const item of category.value) {
      for (const it of categoryList.value) {
        if (item == it.value) {
          categoryName.value += it.label + ','
        }
      }
    }
    classesName.value = classess.value[classes.value].label
    if (categoryName.value) {
      categoryName.value = categoryName.value.slice(0, categoryName.value.length - 1);
    }
//--------------------------------------------------------------------------------------------------
    type.value = route.query.type;
    if (type.value == 'md') {
      markdown.value = await getMdByFileCode(route.query.prouteCode);
    } else {
      const file = await getOneByFileCode(route.query.prouteCode);
      pdfUrl.value = file.filePath
    }
    reqRunner.getFavorite.run(route.query.prouteCode)
  }
});

const getCategoryList = async (list) => {
  for (const item of list) {
    if (item.children && item.children.length > 0) {
      await getCategoryList(item.children)
    }
    categoryList.value.push(item)
  }
};

const addFavorite = async () => {
  reqRunner.addFavorite.run({
    dataType: operationFlag.value,
    operationCode: route.query.prouteCode
  })
};
const reqRunner = {
  downPdf: useRequest(baseDownloadFile, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  addFavorite: useRequest(saveFavorites, {
    manual: true,
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      if(favoriteStyle.value.color){
        favoriteStyle.value = {}
      }else{
        favoriteStyle.value = {
          color : 'red'
        }
      }
    },
  }),
  getFavorite: useRequest(getOneByOperationCode, {
    manual: true,
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      if(res){
        favoriteStyle.value = {
          color : 'red'
        }
      }else{
        favoriteStyle.value = {}
      }
    },
  }),
};
const downDetailsPdf = async () => {
  const { run } = reqRunner.downPdf;
  run(`/tSearch/downDetailsPdf`,{
    operationCode: route.query.prouteCode
  });
};
const favoriteStyle = ref({})
const shareLink = async () => {
  const rsa_baseCode = rsaEncrypt(prouteCode.value).replace(/\+/g, '%252B');
  const user_info = await getUserInfoApi();
  const rsa_username = rsaEncrypt(user_info.username).replace(/\+/g, '%252B');
  const shareurl = `${import.meta.env.VITE_FRONT_URL}/datasharePDF?p=${rsa_baseCode}&q=${rsa_username}&type=${type.value}`
  if(ShareFormRef.value) {
    ShareFormRef.value.open(shareurl);
  }
  // await navigator.clipboard.writeText(shareurl)
  // await MessagePlugin.success('链接复制成功');
}
</script>

<template>
  <ShareForm ref="ShareFormRef" />
  <div class="web-lio-page">
    <div class="web-lio-content">
      <Page title="数据详情">
        <template #extra>
          <Button variant="text" shape="square" :style="{ 'margin-left': '620px' }"  @click="addFavorite">
            <heart-icon :style="favoriteStyle"/>
          </Button>
          <Button variant="text" shape="square" :style="{ 'margin-left': '8px' }" @click="downDetailsPdf">
            <download-icon />
          </Button>
          <Button variant="text" shape="square" :style="{ 'margin-left': '8px' }" @click="shareLink">
            <share-icon />
          </Button>
        </template>
        <div class="border-1 flex h-full w-full flex-col overflow-auto rounded-lg pl-[6%] pr-[6%]">
          <Card class="top-card">
            <Form ref="searchForm" class="w-[95%]">
              <div class="mt-5 grid w-full grid-cols-3 gap-1">
                <FormItem label="分级 :" name="dataType">
                  {{ classesName }}
                </FormItem>
                <FormItem label="分类 :" name="code">
                  {{ categoryName }}
                </FormItem>
                <FormItem label="归属数据集 :" name="dataset">
                  {{ datasetName }}
                </FormItem>
                <FormItem label="标签 :" name="labels">
                  <Tag v-for="item in labelsTags" :key="item.key" :color="item.color" variant="light-outline"
                    style="margin-right: 10px">
                    {{ item.content }}
                  </Tag>
                </FormItem>
              </div>
            </Form>
          </Card>
          <Card style="background-color: #0071bc1a; margin-top: 10px;">
            <div v-if="type == 'md'">
              <div class="editor-container">
                <mavon-editor v-model="markdown" :subfield="false" :toolbarsFlag="false" :editable="false"
                  defaultOpen="preview" style="min-height: 200px; border: none"/>
              </div>
            </div>
            <div v-else-if="type == 'pdf'">
              <iframe :src=pdfUrl width="100%" height="650px" title="PDF文档"/>
            </div>
            <div v-else>
              <img :src=pdfUrl width="100%" height="100%"/>
            </div>
          </Card>
        </div>
      </Page>
    </div>
  </div>
</template>

<style scoped>
.top-card {
  background-image: url('/static/images/top-card.jpeg');
  background-size: cover;
}
.editor-container {
  position: relative;
  z-index: 1; /* 创建新的层叠上下文 */
}
</style>
