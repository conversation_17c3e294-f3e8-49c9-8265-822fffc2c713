<script lang="ts" setup>
import {
  <PERSON><PERSON>,
  Card,
  Cascader, Dialog,
  Form,
  FormItem,
  Input,
  Link,
  MessagePlugin,
  type PageInfo,
  Popconfirm,
  Select,
  Space,
  Table, Textarea,
  Upload
} from 'tdesign-vue-next';

import {BaseTableConfig, Pagination} from '#/utils/constant.ts';
import {removalUnderline} from '#/utils/sort';
import {useVbenModal} from '@vben/common-ui';
import {
  AddCircleIcon,
  ChevronDownDoubleIcon, ChevronUpDoubleIcon, DeleteIcon,
  DownloadIcon,
  Icon,
  RefreshIcon,
  SearchIcon
} from 'tdesign-icons-vue-next';
import {defineEmits, defineExpose, defineProps, onMounted, reactive, ref,} from 'vue';
import {useRequest} from "vue-hooks-plus";
import {baseDownloadFile, baseDownloadFileGet, baseUploadFile, getDictItems} from "#/api";
import {deleteAttachBatch, editItemListByPage, publish, saveAttach, unPublish,} from '../api';
import {getCategorys, getDatasetList, getLabelList} from "#/views/modules/tData/api.ts";
import ColumnDisplay from "#/components/column-display/index.vue";

const emit = defineEmits(['show-relevance']);

// eslint-disable-next-line no-unused-vars
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  relevanceRef: { type: Object, default: null },
});
const tableRef = ref();
const state = reactive({
  tagObj: {},
});

const classess = ref();
const labelses = ref();
const classes = ref();
const category = ref();
const categorys = ref();
const categorysView = ref();
const categoryList = ref([]);
const datasets = ref([]);
/**
 * 表格定义
 */
const columns: any = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
  },
  {
    colKey: 'classCode',
    title: '分级',
    width: 70,
    ellipsis: true,
    sorter: false,
    cell: (h, {row}) => classess.value.find((t) => t.value === row.classCode)?.label,
  },
  {
    colKey: 'categoryCode',
    title: '分类',
    width: 150,
    ellipsis: true,
    sorter: false,
    cell: (h, {row}) => row.categoryCode
      .map(code =>
        categoryList.value.find(c => c.classifyCode === Number(code))?.classifyName
      )
      .filter(Boolean)
      .join("、"),
  },
  {
    colKey: 'datasetCode',
    title: '数据集',
    width: 100,
    ellipsis: true,
    sorter: false,
    cell: (h, {row}) => datasets.value.find((t) => t.value === row.datasetCode)?.label,
  },
  {
    colKey: 'labelCode',
    title: '标签',
    width: 150,
    ellipsis: true,
    sorter: false,
    cell: (h, {row}) => row.labelCode
      .map(code =>
        labelses.value.find(c => c.value === code)?.label
      )
      .filter(Boolean)
      .join("、"),
  },
  {
    colKey: 'remark',
    title: '文件别名',
    ellipsis: true,
    sorter: false,
  },
  {
    colKey: 'fileName',
    title: '文件名',
    ellipsis: true,
    sorter: false,
  },
  {
    colKey: 'createTime',
    title: '上传时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'describeName',
    title: '关联文件',
    ellipsis: true,
    sorter: false,
  },
  {
    colKey: 'describeName',
    title: '描述文件',
    ellipsis: true,
    sorter: false,
  },
  {
    colKey: 'op',
    title: '操作',
    width: 300,
    fixed: 'center',
  },
]);
const data: any = ref([]);
const selectedRowKeys = ref([]);
const tableConfig = ref(BaseTableConfig);
const pagination: any = ref(Pagination);
const formData: any = ref({});
const loading = ref(false);
const sort = ref([]);
const editableRowKeys = ref<any>([]);
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    editableRowKeys.value = [];
    if (isOpen) {
      state.tagObj = modalApi.getData()?.tagObj;
      // eslint-disable-next-line no-use-before-define
      await loadData();
    }
  },
  title: '文件列表',
});
/** -----------------------------------------------  */

/**
 * 网络请求调用定义
 */
const reqRunner = {
  editItemListByPage: async (params: any) => {
    loading.value = true;
    try {
      const {records, total} = await editItemListByPage(params);
      data.value = []
      data.value = records;
      data.value.forEach((row) => {
        row.classCode = row.classCode ? Number(row.classCode) : null;
        if (row.categoryCode != null) {
          row.categoryCode = JSON.parse(row.categoryCode);
          row.categoryCode = row.categoryCode.map(item => Number(item));
        } else {
          row.categoryCode = [];
        }
        if (row.labelCode != null) {
          row.labelCode = JSON.parse(row.labelCode);
        } else {
          row.labelCode = [];
        }
      })
      pagination.value = {
        ...pagination.value,
        total,
      };
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
  upload: useRequest(baseUploadFile, {
    manual: true,
    onError: () => {
    },
    onSuccess: (res: any) => {
      loadData();
      MessagePlugin.success(res);
    },
  }),
  dataExportBatch: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      selectedRowKeys.value = [];
      loadData();
      MessagePlugin.success('下载成功');
    },
  }),
  down: useRequest(baseDownloadFileGet, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      selectedRowKeys.value = [];
      loadData();
      MessagePlugin.success('下载成功');
    },
  }),
  publish: useRequest(publish, {
    manual: true,
    onError: () => {
    },
    onSuccess: (res: any) => {
      loadData();
      MessagePlugin.success(res);
    },
  }),
  unPublish: useRequest(unPublish, {
    manual: true,
    onError: () => {
    },
    onSuccess: (res: any) => {
      loadData();
      MessagePlugin.success(res);
    },
  }),
};

const status = ref([]);
const delFlag = ref([]);
const editId = ref();
/**
 * table初始化方法
 */
const loadData = async () => {
  const params = {
    param: {
      operationCode: state.tagObj?.unstructuredCode,
      operationFlag: formData.value.operationFlag,
      fileName: formData.value.fileName,
      remark: formData.value.remark,
    },
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await reqRunner.editItemListByPage(params);
};
/**
 * 分页栏点击/更改响应方法
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};

/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const selectedRows = ref()
const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRows.value = ctx.selectedRowData
  selectedRowKeys.value = value;
};
/**
 * 列拖动交换位置响应
 * @param newData
 * @param sort
 */
const onDragSort = ({newData, sort}: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

/**
 * vue 生命周期 页面加载结束
 */
onMounted(async () => {
  //数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  classess.value.forEach((op) => {
    if(op.value == 0 || op.value == 1){
      op["disabled"] = true;
    }
  })
  //标签库
  labelses.value = await getLabelList();
  categorysView.value = await getCategorys(2);
  categoryList.value = [];
  await getCategoryList(categorysView.value)
  datasets.value = await getDatasetList({categoryList:category.value});
  loadData();
});

const getCategoryList = async (list) => {
  for (const item of list) {
    if (item.children && item.children.length > 0) {
      await getCategoryList(item.children)
    }
    categoryList.value.push(item)
  }
};

const add = async (...args: any) => {
  await reqRunner.upload.run('/rgdc-submit/tDataUnstructured/upload', {
    unstructuredCode: state.tagObj?.unstructuredCode,
    file: args[0].raw ? args[0].raw : args[0][0].raw,
  });
};

const removeBatch = async () => {
  for (const item of selectedRows.value) {
    if (item.publishStatus == '3') {
      let mess = '数据 ' + item.remark + ' 已发布，暂不能删除'
      MessagePlugin.warning(mess)
      return
    }
  }
  const ids = selectedRowKeys.value.join(',');
  await deleteAttachBatch(ids);
  selectedRowKeys.value = [];
  selectedRows.value = {}
  MessagePlugin.success("删除成功")
  loadData();
};
const remove = async (row: any) => {
  await deleteAttachBatch(row.id);
  MessagePlugin.success("删除成功")
  loadData();
};
const down = async (row: any) => {
  const {run} = reqRunner.down;
  run(`/file/download`, {path: row.filePath});
};
const downBatch = async () => {
  formData.value.ids_list = selectedRowKeys.value
  const {run} = reqRunner.dataExportBatch;
  run(`/tSysAttach/downAttachBatch`, {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  });
};
/**
 * 方法/属性导出
 */
defineExpose({
  loadData,
});
const searchForm = ref();
const resetSearch = () => {
  formData.value = {}
  searchForm.value.reset();
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    loadData();
  }, 0);
};
const searchFormSubmit = () => {
  // 重置分页
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    loadData();
  }, 0);
};
const editMap: any = {};
const edit = (record: any) => {
  emit('show-relevance', record);
};
const save = (record: any) => {
  tableRef.value.validateRowData(record.id).then(async (params: any) => {
    if (params.result.length > 0) {
      const r = params.result[0];
      MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
    } else {
      const current = editMap[record.id];
      if (current?.editedRow) {
        const res = await saveAttach({
          ...current?.editedRow,
        });
        res.classCode = res.classCode ? Number(res.classCode) : null;
        if (res.categoryCode != null) {
          res.categoryCode = JSON.parse(res.categoryCode);
          res.categoryCode = res.categoryCode.map(item => Number(item));
        } else {
          res.categoryCode = [];
        }
        if (res.labelCode != null) {
          res.labelCode = JSON.parse(res.labelCode);
        } else {
          res.labelCode = [];
        }
        console.log(res);
        data.value.splice(current.rowIndex, 1, res);
        MessagePlugin.success("保存成功")
        updateEditState(record.id);
      }
    }
  });
};
const cancel = (record: any) => {
  editId.value = undefined;
  updateEditState(record.id);
};
const updateEditState = (id: string) => {
  const index = editableRowKeys.value.indexOf(id);
  editableRowKeys.value.splice(index, 1);
  tableRef.value?.clearValidateData();
  editId.value = undefined;
};
const onRowEdit: any = (params: any) => {
  const {row, col, value} = params;
  const oldRowData = editMap[row.id]?.editedRow || row;
  const editedRow = {
    ...oldRowData,
    [col.colKey]: value,
  };
  editMap[row.id] = {
    ...params,
    editedRow,
  };
};
const rowData = ref()
const fileCode = ref()
const uploadDescribeFileLink = async (row: any) => {
  fileCode.value = row.fileCode
};
const uploadDescribeFile = async (...args: any) => {
  await reqRunner.upload.run('/tSysAttach/describeUpload', {
    fileCode: fileCode.value,
    file: args[0].raw ? args[0].raw : args[0][0].raw,
  });
};
const uploadMetaFile = async (...args: any) => {
  await reqRunner.upload.run('/tSysAttach/MetaFileUpload', {
    fileCode: fileCode.value,
    file: args[0].raw ? args[0].raw : args[0][0].raw,
  });
};
const publishLink = async (row: any) => {
  if (row.classCode == null || row.categoryCode == null) {
    MessagePlugin.warning("发布前请先选择分级，分类")
  } else {
    // 提交前删除
    let categoryCode = row.categoryCode.join(",")
    let labelCode =  row.labelCode.join(",")

    let param = {
      fileCode: row.fileCode,
      classCode: row.classCode,
      categoryCode: JSON.stringify(categoryCode),
      labelCode: JSON.stringify(labelCode),
      esIndex: import.meta.env.VITE_ES_INDEX
    }
    await reqRunner.publish.run(param);
  }

};

const uPpublishLink = async (row: any) => {
  await reqRunner.unPublish.run({fileCode: row.fileCode,esIndex: import.meta.env.VITE_ES_INDEX});
};

const beforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if (!suffix.value.includes(suffix2)) {
      MessagePlugin.warning("请上传后缀为" + suffix.value + "的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
    return false;
  }
  return true;
};
const suffix = ref('.md,.txt,.pdf,.doc,.docx,.xls,.xlsx,.png,.sdf')
</script>

<template>
  <Modal :footer="false" class="tiny-tdesign-style-patch w-[95%]">
    <Card>
      <Form ref="searchForm" :data="formData" class="w-full" @reset="resetSearch"
            @submit="searchFormSubmit">
        <div class="flex justify-start w-full mb-2">
          <div class="flex items-center justify-start grid w-full grid-cols-3 gap-1 p-3">
            <FormItem label="文件别名" name="remark" style="margin: 0">
              <Input v-model="formData.remark" clearable placeholder="请输入内容"/>
            </FormItem>
            <FormItem label="文件名" name="dataType">
              <Input v-model="formData.fileName" clearable placeholder="请输入内容"/>
            </FormItem>
          </div>
          <div class="flex items-center justify-end mt-2 space-x-2">
            <Button theme="primary" type="submit">
              <template #icon>
                <SearchIcon/>
              </template>
              查询
            </Button>
            <Button theme="default" type="reset">
              <template #icon>
                <RefreshIcon/>
              </template>
              重置
            </Button>
          </div>
        </div>
      </Form>
    </Card>
    <Table ref="tableRef" v-model:column-controller-visible="tableConfig.columnControllerVisible"
           :columns="columns" :data="data" :loading="loading" :pagination="pagination" row-key="id"
           :pagination-affixed-bottom="false" :selected-row-keys="selectedRowKeys" :sort="sort"
           v-bind="tableConfig" :editable-row-keys="editableRowKeys" @drag-sort="onDragSort"
           @page-change="rehandlePageChange"  @row-edit="onRowEdit" @select-change="rehandleSelectChange"
           @sort-change="sortChange">
      <template #topContent="slotProps">
        <div class="mb-2 mt-2 flex w-full justify-start">
          <div class="flex w-full items-center justify-start pl-2">
            <div class="t-card__title mr-2">非结构化数据列表</div>
            <div v-if="selectedRowKeys?.length > 0" class="text-blue-600/80">
              已选择 [{{ selectedRowKeys?.length || 0 }}] 条数据
            </div>
          </div>
          <div class="flex w-full justify-end space-x-2">
            <Popconfirm v-if="selectedRowKeys && selectedRowKeys.length > 0" content="确定删除？"
                        theme="danger" @confirm="removeBatch">
              <Button theme="danger">
                <template #icon>
                  <Icon name="delete"/>
                </template>
                批量删除
              </Button>
            </Popconfirm>
            <Button theme="primary" @click="downBatch">
              <template #icon>
                <DownloadIcon/>
              </template>
              批量下载
            </Button>
            <Button variant="text" @click="loadData">
              <RefreshIcon />
            </Button>
          </div>
        </div>
      </template>
      <template #firstFullRow>
        <div class="w-[100%] bg-white p-1">
          <Upload :request-method="add" :multiple="true" class="w-[100%]" :beforeUpload="beforeUpload">
            <Button :disabled="editId ? true : false" class="w-[100%]" theme="primary"
                    variant="dashed">
              <template #icon>
                <Icon name="upload"/>
              </template>
              上传
            </Button>
          </Upload>
        </div>
      </template>
      <template #op="slotProps">
        <Space size="small">
          <div v-if="(slotProps.row.publishStatus =='0' || slotProps.row.publishStatus =='6')
            && slotProps.row.categoryCode && slotProps.row.datasetCode" style="display: flex;">
            <Link theme="primary" @click="publishLink(slotProps.row)">发布</Link>
          </div>
          <div v-else-if="slotProps.row.publishStatus =='3'">
            <Link theme="primary" @click="uPpublishLink(slotProps.row)">下架</Link>
          </div>
          <Link theme="primary" @click="down(slotProps.row)">下载</Link>
          <Link v-if="slotProps.row.publishStatus !='3'" theme="primary" @click="edit(slotProps.row)">
            编辑
          </Link>
          <Popconfirm v-if="slotProps.row.publishStatus !='3'" content="确定删除？" theme="warning"
                      @confirm="remove(slotProps.row)">
            <Link theme="danger">删除</Link>
          </Popconfirm>
        </Space>
      </template>
      <template #empty>
        <div class="flex-col-center flex p-1">
          <div class="text-sx mb-2">暂无数据</div>
          <Upload :request-method="add" :multiple="true" :beforeUpload="beforeUpload">
            <Button :disabled="editId ? true : false" class="w-[100%]" theme="primary"
                    variant="text">
              <template #icon>
                <Icon name="upload"/>
              </template>
              点击上传文件
            </Button>
          </Upload>
        </div>
      </template>
    </Table>
  </Modal>
</template>
<style scoped lang="less">
:deep(.t-upload .t-upload__trigger) {
  width: 100%;
}
</style>
