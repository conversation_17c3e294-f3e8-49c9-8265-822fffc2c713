import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据基本信息',
    },
    name: 'tDataBase',
    path: '/tDataBase',
    children: [
      {
        meta: {
          title: '数据基本信息编辑',
        },
        name: 'tDataBaseIndex',
        path: '/tDataBase/index',
        component: () =>
          import('#/views/modules/tDataBase/index.vue'),
      },
    ],
  },
];

export default routes;
