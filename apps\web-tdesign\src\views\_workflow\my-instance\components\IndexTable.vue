<script lang="ts" setup>
import {MessagePlugin, type PageInfo, Select, type TableRowData} from 'tdesign-vue-next';

import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import {Icon, RefreshIcon, SearchIcon} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'tdesign-vue-next';
import { defineEmits, defineExpose, defineProps, onMounted, ref } from 'vue';

import { cascadeRemove, listByPageApi,revokeInstance } from '../api';
import {useRequest} from "vue-hooks-plus";

const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
});

/**
 * 表格定义
 */
const columns: any = ref([
  {
    title: '序号',
    colKey: 'serial-number',
    width: 100,
  },
  {
    colKey: 'arg.operation_name',
    title: '数据名称',
    ellipsis: true,
  },
  {
    colKey: 'arg.operation_version',
    title: '数据版本',
    ellipsis: true,
  },
  {
    colKey: 'processName',
    title: '流程名称',
    ellipsis: true,
  },
  {
    colKey: 'currentNodeName',
    title: '当前任务',
    ellipsis: true,
  },
  {
    colKey: 'createTime',
    title: '提交时间',
    sorter: true,
    ellipsis: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: 100,
    fixed: 'center',
  },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item: any) => item.colKey),
);
const formData: any = ref({});
const form = ref();
const data: any = ref([]);
const selectedRowKeys = ref([]);
const tableConfig = ref(BaseTableConfig);
const pagination: any = ref(Pagination);
const loading = ref(false);
const sort = ref([]);
/** -----------------------------------------------  */

/**
 * 网络请求调用定义
 */
const reqRunner = {
  listByPageApi: async (params: any) => {
    loading.value = true;
    try {
      const { records, total } = await listByPageApi(params);
      for(const item of records){
        item.arg = JSON.parse(item.variable)
        data.value.push(item)
      }
      pagination.value = {
        ...pagination.value,
        total,
      };
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
  revoke: useRequest(revokeInstance, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      loadData()
      MessagePlugin.success('撤销成功');
    },
  }),
};


/**
 * table初始化方法
 */
const loadData = async () => {
  data.value = []
  const params = {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await reqRunner.listByPageApi(params);
};
/**
 * 分页栏点击/更改响应方法
 * @param pageInfo
 * @param newDataSource
 */
const rehandlePageChange = (
  pageInfo: PageInfo,
  newDataSource: TableRowData[],
) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};
/**
 * 搜索表单重置方法
 */
const onReset = () => {
  form.value.reset();
  pagination.value = {
    pageSize: pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    loadData();
  }, 0);
};
/**
 * 搜索表单提交方法
 */
const onSubmit = async () => {
  pagination.value = {
    pageSize: pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    loadData();
  }, 0);
};

/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRowKeys.value = value;
};

/**
 * 列拖动交换位置响应
 * @param newData
 * @param sort
 */
const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

/**
 * vue 生命周期 页面加载结束
 */
onMounted(async () => {
  loadData();
  // delFlag.value = await getDictItems('ACCOUNT_DEL_FLAG');
});

/**
 * 编辑按钮响应
 * @param record
 */
const doRevoke = (record: any) => {
  reqRunner.revoke.run({id: record.id,args:JSON.parse(record.variable)});
};

/**
 * 行点击时间
 */
const handleRowClick = () => {};

const refresh = () => {
  loadData();
};
/**
 * 方法/属性导出
 */
defineExpose({
  loadData,
  refresh,
});
</script>

<template>
  <Space class="w-full" direction="vertical" size="small">
    <Card v-if="isSearchForm">
      <Form ref="form" :data="formData" :label-width="80" @reset="onReset" @submit="onSubmit">
        <div class="grid w-full grid-cols-3 gap-1 p-3">
          <FormItem label="数据名称" name="dataName">
            <Input v-model="formData.dataName" clearable placeholder="请输入"/>
          </FormItem>
          <FormItem label="流程名称" name="processName">
            <Input v-model="formData.processName" clearable placeholder="请输入"/>
          </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置
          </Button>
        </div>
      </Form>
    </Card>
    <Card>
      <div class="t-row--space-between mb-2 flex w-full justify-start">
        <div class="flex flex-wrap w-full items-center justify-start gap-1">
          <div class="t-card__title ml-2">流程列表</div>
          <div v-if="selectedRowKeys && selectedRowKeys.length > 0"class="text-[gray]">
            已选择 {{ selectedRowKeys?.length || 0 }} 条数据
          </div>
        </div>
        <div class="flex flex-wrap w-full justify-end gap-2">
          <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
        </div>
      </div>
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="selectedRowKeys"
        :sort="sort"
        v-bind="tableConfig"
        @drag-sort="onDragSort"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
      >
        <template #op="slotProps">
          <Space size="small">
            <Popconfirm content="确定撤回？" theme="warning"  @confirm="doRevoke(slotProps.row)">
              <Link theme="danger" v-if="slotProps.row.actorType !== 0"> 撤回</Link>
            </Popconfirm>
            <!-- <Link theme="danger">删除</Link> -->
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
