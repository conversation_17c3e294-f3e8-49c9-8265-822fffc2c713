<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import Luckysheet from '#/components/luckysheet/index.vue';

const data = ref([
  {
    name: 'Sheet1',
    color: '',
    row: 40,
    column: 30,
    index: 0,
    status: 0,
    order: 1,
    celldata: [],
    config: {},
  },
]);
</script>

<template>
  <Page class="h-full w-full" description="使用LuckySheet实现" title="在线表格">
    <div class="h-full w-full">
      <Luckysheet v-model="data" />
    </div>
  </Page>
</template>

<style scoped></style>
