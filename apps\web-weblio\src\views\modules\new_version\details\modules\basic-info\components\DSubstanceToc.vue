<script setup lang="ts">
import { onMounted, ref } from 'vue';
import {useRequest} from 'vue-hooks-plus';
import { getToxicityInfoByInchikey, getToxicityInfoSpectralByTocId } from '../api';
import { useSearchStore } from '#/store/search';
import { computed, reactive } from 'vue';
import { MessagePlugin, Dialog } from 'tdesign-vue-next';

// 定义数据接口
interface TocItem {
  id: string;
  tocHeading: string;
  url: string;
  inchikey: string;
  tocDescription?: string;
  displayControls?: string;
  nameText?: string;
  urlText?: string;
  referenceDescription?: string;
  referenceText?: string;
  valueText?: string;
  referenceNumber?: string;
  [key: string]: any;
}

// 光谱详情数据接口
interface SpectralItem {
  id: number;
  createdBy?: string;
  createTime?: string;
  updatedBy?: string;
  updateTime?: string;
  isDeleted?: string;
  inchikey?: string;
  tocId?: number;
  spectralName?: string;
  subSpectralName?: string;
  spectralRecognition?: string;
  instrumentType?: string;
  frequency?: string;
  solvent?: string;
  ph?: string;
  changeIntensityPpm?: string;
  thumbnail?: string;
  twoDimensionNmrType?: string;
  field1h13cHsqc?: string;
  offsetIntensityPpm?: string;
  ionizationMode?: string;
  splash?: string;
  peak?: string;
  technology?: string;
  sampleSource?: string;
  catalogNumber?: string;
  copyright?: string;
  instrumentName?: string;
  independentFtRamanSpectrometer?: string;
  spectralSource?: string;
  batchNumber?: string;
  [key: string]: any;
}

// 响应式数据
const tocData = ref<TocItem[]>([]);
const isDialogVisible = ref(false);
const selectedItem = ref<TocItem | null>(null);

// 光谱详情状态
const isSpectralDialogVisible = ref(false);
const spectralData = ref<SpectralItem | null>(null);
const selectedSpectralItem = ref<TocItem | null>(null);

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
   * 加载状态
   */
  loading: false,
});

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 获取图谱信息数据
   */
   getToxicityInfoByInchikey: useRequest(getToxicityInfoByInchikey, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
      MessagePlugin.error('获取图谱信息失败');
    },
    onSuccess: (res: any) => {
      console.log("图谱信息数据", res);
      // 确保res是数组，如果不是则转换为数组
      tocData.value = Array.isArray(res) ? res : [];
    },
  }),
  
  /**
   * 获取光谱详情数据
   */
  getSpectralDetail: useRequest(getToxicityInfoSpectralByTocId, {
    manual: true,
    debounceWait: 300,
    onError: () => {
      MessagePlugin.error('获取光谱详情失败');
    },
    onSuccess: (res: any) => {
      console.log("光谱详情数据", res);
      spectralData.value = res || null;
    },
  }),
};

// 初始化数据的方法
const initializeData = async () => {
  try {
    state.loading = true;
    
    // 这里替换为实际的API调用
    const {run, loading} = reqRunner.getToxicityInfoByInchikey;
    state.loading = loading;
    console.log("state.detailItem", state.detailItem);
    // 调用API获取图谱数据
    run({
      // 当前详情inchikey
      inchikey: state.detailItem.baseCode,
    });
    
  } catch (error) {
    console.error('获取图谱数据失败:', error);
    MessagePlugin.error('获取图谱数据失败');
  } finally {
    state.loading = false;
  }
};

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  // 设置默认图片或占位符
  img.src = '/api/placeholder/80/80?text=No+Image';
};

// 点击图谱卡片处理
const handleCardClick = (item: TocItem) => {
  console.log('点击了图谱卡片:', item);
  selectedItem.value = item;
  isDialogVisible.value = true;
  console.log('弹窗状态:', isDialogVisible.value);
};

// 关闭弹窗
const closeDialog = () => {
  isDialogVisible.value = false;
  selectedItem.value = null;
};

// 点击光谱详情链接
const handleSpectrumDetail = (tocId: string) => {
  if (tocId) {
    console.log('打开光谱详情，tocId:', tocId);
    // 保存当前选中的图谱项
    selectedSpectralItem.value = selectedItem.value;
    // 关闭当前弹窗
    closeDialog();
    // 获取光谱详情数据
    const { run } = reqRunner.getSpectralDetail;
    run({ tocId: tocId });
    // 打开光谱详情弹窗
    isSpectralDialogVisible.value = true;
  }
};

// 关闭光谱详情弹窗
const closeSpectralDialog = () => {
  isSpectralDialogVisible.value = false;
  spectralData.value = null;
  // 恢复图谱详情的选中项
  selectedItem.value = selectedSpectralItem.value;
  selectedSpectralItem.value = null;
  // 重新显示图谱详细信息弹窗
  isDialogVisible.value = true;
};

// 组件挂载时初始化数据
onMounted(() => {
  initializeData();
});
</script>

<template>
  <div class="substance-toc">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载图谱信息中...</div>
    </div>
    
    <template v-else>
      <div class="toc-section">
        <h4>图谱信息 (共 {{ tocData.length }} 个)</h4>
        <div class="compounds-grid">
          <div 
            v-for="item in tocData" 
            :key="item.id" 
            class="compound-card"
            @click="handleCardClick(item)"
          >
            <div class="compound-structure">
              <img 
                :src="item.url" 
                :alt="item.toc_heading"
                @error="handleImageError"
                class="compound-image"
              />
            </div>
            <div class="compound-name">{{ item.toc_heading }}</div>
            <!-- <div v-if="item.tocHeading" class="toc-description">{{ item.tocHeading }}</div> -->
          </div>
        </div>
        
        <div v-if="tocData.length === 0" class="empty-state">
          <p>暂无图谱信息</p>
        </div>
      </div>
    </template>
    
    <!-- 图谱详细信息弹窗 -->
    <Dialog
      v-model:visible="isDialogVisible"
      :header="'图谱详细信息'"
      width="1500px"
      placement="center"
      :show-overlay="true"
      :close-btn="true"
      :close-on-overlay-click="true"
      :close-on-esc-keydown="true"
      :destroy-on-close="true"
      :footer="false"
      mode="modal"
      :z-index="9999"
      @close="closeDialog"
    >
      <div v-if="selectedItem" class="toc-detail">
        <div class="detail-row">
          <div class="label">图谱标题</div>
          <div class="value">{{ selectedItem.tocHeading || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">图谱描述</div>
          <div class="value">{{ selectedItem.tocDescription || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">图谱内容</div>
          <div class="value">{{ selectedItem.displayControls || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">名称小标题</div>
          <div class="value">{{ selectedItem.nameText || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">文献链接</div>
          <div class="value">
            <a v-if="selectedItem.urlText" :href="selectedItem.urlText" target="_blank" class="link">
              {{ selectedItem.urlText }}
            </a>
            <span v-else>-</span>
          </div>
        </div>
        
        <div class="detail-row">
          <div class="label">文献描述</div>
          <div class="value">{{ selectedItem.referenceDescription || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">参考文本</div>
          <div class="value">{{ selectedItem.referenceText || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">详细信息</div>
          <div class="value">{{ selectedItem.valueText || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">文献数量</div>
          <div class="value">{{ selectedItem.referenceNumber || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="label">光谱详情</div>
          <div class="value">
            <a 
              v-if="selectedItem.id" 
              href="javascript:void(0)"
              @click="handleSpectrumDetail(selectedItem.id)"
              class="spectral-link"
            >
              点击查看
            </a>
            <span v-else>-</span>
          </div>
        </div>
      </div>
    </Dialog>
    
    <!-- 光谱详情弹窗 -->
    <Dialog
      v-model:visible="isSpectralDialogVisible"
      :header="`光谱详情`"
      width="800px"
      placement="center"
      :show-overlay="true"
      :close-btn="true"
      :close-on-overlay-click="true"
      :close-on-esc-keydown="true"
      :destroy-on-close="true"
      :footer="false"
      mode="modal"
      :z-index="9999"
      @close="closeSpectralDialog"
    >
      <div v-if="spectralData" class="toc-detail">
          <div class="detail-row">
            <div class="label-large">光谱名</div>
            <div class="value">{{ spectralData.spectralName || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">子光谱名</div>
            <div class="value">{{ spectralData.subSpectralName || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">光谱识别</div>
            <div class="value">{{ spectralData.spectralRecognition || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">仪器类型</div>
            <div class="value">{{ spectralData.instrumentType || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">频率</div>
            <div class="value">{{ spectralData.frequency || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">溶剂</div>
            <div class="value">{{ spectralData.solvent || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">pH</div>
            <div class="value">{{ spectralData.ph || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">变化[ppm]:强度</div>
            <div class="value">{{ spectralData.changeIntensityPpm || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">二维核磁共振谱类型</div>
            <div class="value">{{ spectralData.twoDimensionNmrType || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">1H-13C HSQC</div>
            <div class="value">{{ spectralData.field1h13cHsqc || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">偏移 [ppm] (F2:F1):强度</div>
            <div class="value">{{ spectralData.offsetIntensityPpm || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">电离模式</div>
            <div class="value">{{ spectralData.ionizationMode || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">溅</div>
            <div class="value">{{ spectralData.splash || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">峰</div>
            <div class="value">{{ spectralData.peak || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">技术</div>
            <div class="value">{{ spectralData.technology || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">样本来源</div>
            <div class="value">{{ spectralData.sampleSource || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">目录编号</div>
            <div class="value">{{ spectralData.catalogNumber || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">版权</div>
            <div class="value">{{ spectralData.copyright || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">仪器名称</div>
            <div class="value">{{ spectralData.instrumentName || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">独立 FT-拉曼光谱仪</div>
            <div class="value">{{ spectralData.independentFtRamanSpectrometer || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">光谱来源</div>
            <div class="value">{{ spectralData.spectralSource || '-' }}</div>
          </div>
          
          <div class="detail-row">
            <div class="label-large">批号</div>
            <div class="value">{{ spectralData.batchNumber || '-' }}</div>
          </div>
      </div>
      
      <div v-else class="empty-spectral">
        <p>暂无光谱详情信息</p>
      </div>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.substance-toc {
  padding: 20px;
  
  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    
    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
  
  @keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
  }
  
  .toc-section {
    h4 {
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      &::before {
        content: '▼';
        margin-right: 10px;
      }
    }
  }
  
  .compounds-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }
  
  .compound-card {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .compound-structure {
      width: 100%;
      height: 200px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      position: relative;
      overflow: hidden;
      
      .compound-image {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
        border-radius: 4px;
      }
    }
    
    .compound-name {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 4px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .toc-description {
      font-size: 12px;
      color: #999;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
  
  @media (max-width: 768px) {
    padding: 15px;
    
    .compounds-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
    }
    
    .compound-card {
      padding: 8px;
      
      .compound-structure {
        height: 120px;
      }
      
      .compound-name {
        font-size: 12px;
      }
      
      .toc-description {
        font-size: 10px;
      }
    }
  }
  
  @media (max-width: 480px) {
    .compounds-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.toc-detail {
  max-height: 600px;
  overflow-y: auto;
  
  .detail-row {
    display: flex;
    margin-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 6px;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    .label {
      width: 120px;
      font-weight: 500;
      color: #333;
      background: #f8f9fa;
      padding: 8px 12px;
      border-radius: 4px;
      margin-right: 16px;
      flex-shrink: 0;
      font-size: 14px;
    }

    .label-large {
      width: 200px;
      font-weight: 500;
      color: #333;
      background: #f8f9fa;
      padding: 8px 12px;
      border-radius: 4px;
      margin-right: 16px;
      flex-shrink: 0;
      font-size: 14px;
    }
    
    .value {
      flex: 1;
      padding: 8px 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-all;
      
      .link {
        color: #1890ff;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
      
      .spectral-link {
        color: #1890ff;
        text-decoration: none;
        cursor: pointer;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.spectral-detail {
  max-height: 600px;
  overflow-y: auto;
  
  .spectral-item {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }
}

.empty-spectral {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

// 确保弹窗能正确显示
// :deep(.t-dialog) {
//   z-index: 9999 !important;
// }

// :deep(.t-dialog__overlay) {
//   z-index: 9998 !important;
// }

// :deep(.t-dialog__modal) {
//   z-index: 9999 !important;
// }
</style> 
