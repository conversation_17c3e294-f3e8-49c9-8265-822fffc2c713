<script setup lang="ts">
import type { StatisticItem } from '#/components/statistics-overview/index.vue';
import type { DataItem } from '@vben/types';

import ImageLayout from '#/components/image-layout/index.vue';
import SearchBox from '#/components/search-box/index.vue';
import StatisticsOverview from '#/components/statistics-overview/index.vue';
import { useSearchStore } from '#/store/search';
import { onMounted, onUnmounted, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { getIndustryCharacteristicsData, getStatistics } from './api';

const searchStore = useSearchStore();

// 响应式数据
const searchMode = ref<'ai' | 'search'>('search');
const selectedSearchType = ref('all');
const searchQuery = ref('');
const aiQuery = ref('');
const deepSearchEnabled = ref(false);

// AI问答示例
const aiExamples = ref([
  '什么是苯的分子结构？',
  '如何合成阿司匹林？',
  '解释一下化学反应机理',
  '有机化学基础知识',
]);

// 统计数据 - 初始化默认数据
const statisticsData = ref<StatisticItem[]>([]);

// 图片布局数据
const layoutData = ref();

// 第二个图片布局数据 - 图片在右侧
const rightLayoutData = ref();

// 组件是否已卸载的标志
const isMounted = ref(true);

// 请求取消控制器
const abortControllers = new Set<AbortController>();

// 更新统计数据的函数 - 直接使用res数据，补全icon
const updateStatisticsData = (res: any[]) => {
  if (!isMounted.value || !Array.isArray(res)) return;

  // 颜色和图标池，保证和原有顺序一致
  const colors = [
    '#7c3aed',
    '#ec4899',
    '#06b6d4',
    '#10b981',
    '#f59e0b',
    '#84cc16',
  ];
  const icons = [
    'FolderIcon',
    'DatabaseIcon',
    'HardDriveIcon',
    'FileTextIcon',
    'ToolIcon',
    'UsersIcon',
  ];

  // 直接映射res为StatisticItem格式
  const newStatisticsData: StatisticItem[] = res.map(
    (item: any, index: number) => ({
      key: item.key?.toString() || `stat_${index}`,
      title: item.title || item.label || '未知',
      value: typeof item.count === 'number' ? item.count : 0,
      displayValue:
        typeof item.displayValue === 'number' ? item.displayValue : undefined,
      unit: item.unit || '项',
      icon: item.icon || icons[index % icons.length],
      color: item.color || colors[index % colors.length],
      trend: item.trend,
    }),
  );

  statisticsData.value = newStatisticsData;
};

// 日期格式转换工具函数
const formatDateString = (dateString: null | string | undefined): string => {
  if (!dateString) {
    return new Date().toISOString().slice(0, 10);
  }

  try {
    // 处理 "2025-05-28 22:11:26" 格式
    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) {
      return new Date().toISOString().slice(0, 10);
    }
    return date.toISOString().slice(0, 10); // 返回 YYYY-MM-DD 格式
  } catch (error) {
    console.warn('日期格式转换失败:', dateString, error);
    return new Date().toISOString().slice(0, 10);
  }
};

// 生成唯一ID的计数器，避免使用 Math.random()
let idCounter = 1;
const generateUniqueId = () => {
  return idCounter++;
};

// 将res数据转换为layoutData和rightLayoutData格式的函数
const convertResDataToLayoutData = (res: any) => {
  if (!isMounted.value || !res) {
    console.warn('组件已卸载或API响应数据为空');
    return;
  }

  // 处理统计数据 - 合并industryData和characteristicsData的统计
  const allData = [
    ...(Array.isArray(res.industryData) ? res.industryData : []),
    ...(Array.isArray(res.characteristicsData) ? res.characteristicsData : []),
  ];

  if (allData.length > 0) {
    const colors = [
      '#7c3aed',
      '#ec4899',
      '#06b6d4',
      '#10b981',
      '#f59e0b',
      '#84cc16',
    ];
    const icons = [
      'FolderIcon',
      'DatabaseIcon',
      'HardDriveIcon',
      'FileTextIcon',
      'ToolIcon',
      'UsersIcon',
    ];

    const newStatisticsData: StatisticItem[] = allData.map(
      (item: any, index: number) => ({
        key: item.key?.toString() || `category_${index}`,
        title: item.label || '未知分类',
        value: Array.isArray(item.data) ? item.data.length : 0,
        unit: '项',
        icon: icons[index % icons.length] || 'FolderIcon',
        color: colors[index % colors.length] || '#7c3aed',
      }),
    );

    // 优化：只更新中间的动态数据，避免完整重新赋值
    const originalStart = statisticsData.value.slice(0, 2); // 保留前两项
    const originalEnd = statisticsData.value.slice(-2); // 保留后两项

    const newStatisticsArray = [
      ...originalStart,
      ...newStatisticsData,
      ...originalEnd,
    ];

    // 只在数据真正变化时才更新
    if (
      JSON.stringify(statisticsData.value) !==
      JSON.stringify(newStatisticsArray)
    ) {
      statisticsData.value = newStatisticsArray;
    }
  }

  // 处理layoutData - 使用industryData
  if (Array.isArray(res.industryData) && res.industryData.length > 0) {
    const convertedTabs = res.industryData
      .filter((industry: any) => industry && Array.isArray(industry.data))
      .map((industry: any) => ({
        key: industry.key?.toString() || industry.label || 'default',
        label: industry.label || '未知分类',
        data: industry.data.map((item: any) => ({
          id: Number.parseInt(item.id) || generateUniqueId(), // 使用递增ID替代随机数
          title: item.dataName || '数据项',
          description: item.dataDescri || '暂无描述',
          date: formatDateString(item.updateTime),
        })),
      }));

    if (convertedTabs.length > 0) {
      layoutData.value = {
        title: '行业数据',
        mainImage: '/static/images/data-analysis.jpg',
        imagePosition: 'left' as 'left' | 'right',
        tabs: convertedTabs,
      };
    }
  }

  // 处理rightLayoutData - 使用characteristicsData
  if (
    Array.isArray(res.characteristicsData) &&
    res.characteristicsData.length > 0
  ) {
    const convertedRightTabs = res.characteristicsData
      .filter(
        (characteristics: any) =>
          characteristics && Array.isArray(characteristics.data),
      )
      .map((characteristics: any) => ({
        key:
          characteristics.key?.toString() || characteristics.label || 'default',
        label: characteristics.label || '未知分类',
        data: characteristics.data.map((item: any) => ({
          id: Number.parseInt(item.id) || generateUniqueId(), // 使用递增ID替代随机数
          title: item.dataName || '数据项',
          description: item.dataDescri || '暂无描述',
          date: formatDateString(item.updateTime),
        })),
      }));

    if (convertedRightTabs.length > 0) {
      rightLayoutData.value = {
        title: '特色数据',
        mainImage: '/static/images/innovation.jpg',
        imagePosition: 'right' as 'left' | 'right',
        tabs: convertedRightTabs,
      };
    }
  } else {
    // 如果characteristicsData为空，清空rightLayoutData
    rightLayoutData.value = null;
  }
};

// 使用 useRequest 并添加清理逻辑
const {
  run: runIndustryCharacteristicsData,
  cancel: cancelIndustryCharacteristicsData,
} = useRequest(getIndustryCharacteristicsData, {
  manual: true,
  onSuccess: (res) => {
    if (isMounted.value) {
      convertResDataToLayoutData(res);
    }
  },
  onError: (error) => {
    if (isMounted.value) {
      console.error('获取行业特色数据失败:', error);
    }
  },
});

const { run: runStatisticsData, cancel: cancelStatisticsData } = useRequest(
  getStatistics,
  {
    manual: true,
    onSuccess: (res) => {
      if (isMounted.value) {
        updateStatisticsData(res);
      }
    },
    onError: (error) => {
      if (isMounted.value) {
        console.error('获取统计数据失败:', error);
      }
    },
  },
);

// 初始化数据方法
const initIndustryCharacteristicsData = () => {
  if (isMounted.value) {
    runIndustryCharacteristicsData();
  }
};

const initStatisticsData = () => {
  if (isMounted.value) {
    runStatisticsData();
  }
};

// 处理搜索
const handleSearch = (data: { query: string; type: string }) => {
  console.log(data, 2222);
  // 跳转到搜索页面并传递搜索参数
};

// 处理AI问答
const handleAiChat = (_data: { deepSearch: boolean; query: string }) => {
  // 这里添加AI问答逻辑
  // 根据data.deepSearch决定是否使用深度搜索
};

// 处理高级检索
const handleAdvancedSearch = () => {};

// 处理结构式检索
const handleFormulaSearch = () => {
  // 这里添加结构式检索逻辑
};

// 处理项目点击
const handleItemClick = (_item: DataItem) => {
  // 这里添加跳转到详情页的逻辑
};

// 处理tab切换
const handleTabChange = (_tabKey: string) => {
  // 这里可以添加tab切换的相关逻辑
};

// 处理更多点击
const handleMoreClick = () => {
  // 这里可以添加更多点击的相关逻辑，比如跳转到完整列表页面
};

// 处理统计卡片点击
const handleStatisticClick = (_item: StatisticItem) => {
  // 这里可以添加跳转到具体统计页面的逻辑
};

// 右侧布局组件的事件处理函数
const handleRightItemClick = (_item: DataItem) => {
  // 这里添加跳转到详情页的逻辑
};

const handleRightTabChange = (_tabKey: string) => {
  // 这里可以添加tab切换的相关逻辑
};

const handleRightMoreClick = () => {
  // 这里可以添加更多点击的相关逻辑，比如跳转到技术创新完整列表页面
};

// 动态加载图片布局数据
const loadImageLayoutData = async () => {
  try {
    // 这里可以调用API获取数据
    // const response = await api.getImageLayoutData()
    // layoutData.value = response.data
    // 图片布局数据加载完成
  } catch (error) {
    if (isMounted.value) {
      console.error('图片布局数据加载失败:', error);
    }
  }
};

// 组件挂载时加载数据
onMounted(() => {
  searchStore.setInitCategory(0);
  isMounted.value = true;
  loadImageLayoutData();
  initStatisticsData();
  initIndustryCharacteristicsData();
});

// 组件卸载时清理资源 - 防止内存泄漏
onUnmounted(() => {
  isMounted.value = false;
  // 取消所有进行中的请求
  try {
    cancelIndustryCharacteristicsData?.();
    cancelStatisticsData?.();
  } catch (error) {
    console.warn('取消请求时出错:', error);
  }

  // 取消所有 AbortController
  abortControllers.forEach((controller) => {
    try {
      controller.abort();
    } catch (error) {
      console.warn('取消 AbortController 时出错:', error);
    }
  });
  abortControllers.clear();

  // 清理响应式数据引用，帮助垃圾回收
  layoutData.value = null;
  rightLayoutData.value = null;
});
</script>

<template>
  <div class="home-container">
    <!-- 使用搜索组件 -->
    <SearchBox
      v-model:search-mode="searchMode"
      v-model:selected-search-type="selectedSearchType"
      v-model:search-query="searchQuery"
      v-model:ai-query="aiQuery"
      v-model:deep-search-enabled="deepSearchEnabled"
      :ai-examples="aiExamples"
      @search="handleSearch"
      @ai-chat="handleAiChat"
      @advanced-search="handleAdvancedSearch"
      @formula-search="handleFormulaSearch"
    />

    <!-- 统计数据概览 -->
    <StatisticsOverview
      :data="statisticsData"
      @card-click="handleStatisticClick"
    />

    <!-- 动态图片布局组件 -->
    <div class="image-layout-section">
      <ImageLayout
        v-if="layoutData"
        :title="layoutData.title"
        :main-image="layoutData.mainImage"
        :tabs="layoutData.tabs"
        :image-position="layoutData.imagePosition"
        @item-click="handleItemClick"
        @tab-change="handleTabChange"
        @more-click="handleMoreClick"
      />
    </div>

    <!-- 第二个图片布局组件 - 图片在右侧 -->
    <div class="image-layout-section">
      <ImageLayout
        v-if="rightLayoutData"
        :title="rightLayoutData.title"
        :main-image="rightLayoutData.mainImage"
        :tabs="rightLayoutData.tabs"
        :image-position="rightLayoutData.imagePosition"
        @item-click="handleRightItemClick"
        @tab-change="handleRightTabChange"
        @more-click="handleRightMoreClick"
      />
    </div>
  </div>
</template>

<style scoped>
.home-container {
  width: 100%;
  padding: 0;
}

.image-layout-section {
  margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .home-container {
    padding: 0;
  }
}
</style>
