<script setup>
import { AlarmIcon, CloseIcon, EditIcon } from 'tdesign-icons-vue-next';
import { Button, Drawer, Form, FormItem, Input } from 'tdesign-vue-next';
</script>
<script>
import addNode from './addNode.vue';

export default {
  components: {
    AddNode: addNode,
  },
  inject: ['select'],
  props: {
    modelValue: { type: Object, default: () => {} },
  },
  data() {
    return {
      nodeConfig: {},
      drawer: false,
      isEditTitle: false,
      form: {
        nodeName: '触发器',
        type: 7,
        extendConfig: {
          time: '',
          args: '{}',
          trigger: 'org.cnc.sys.workflow.trigger.TaskTriggerImpl',
        },
      },
    };
  },
  watch: {
    modelValue() {
      this.nodeConfig = this.modelValue;
    },
  },
  mounted() {
    this.nodeConfig = this.modelValue;
  },
  methods: {
    show() {
      this.form = {};

      this.form = JSON.parse(JSON.stringify(this.nodeConfig));
      this.drawer = true;
    },
    editTitle() {
      this.isEditTitle = true;
      this.$nextTick(() => {
        this.$refs.nodeTitle.focus();
      });
    },
    saveTitle() {
      this.isEditTitle = false;
    },
    save() {
      this.$emit('update:modelValue', this.form);
      this.drawer = false;
    },
    delNode() {
      this.$emit('update:modelValue', this.nodeConfig.childNode);
    },
    delUser(index) {
      this.form.nodeAssigneeList.splice(index, 1);
    },
    selectHandle(type, data) {
      this.select(type, data);
    },
    toText(nodeConfig) {
      return nodeConfig.nodeName ?? '触发器';
    },
  },
};
</script>
<template>
  <div class="node-wrap">
    <div class="node-wrap-box" @click="show">
      <div class="title" style="background: #6cf">
        <AlarmIcon class="icon" />
        <span>{{ nodeConfig.nodeName }}</span>

        <CloseIcon class="close" @click="delNode()" />
      </div>
      <div class="content">
        <span v-if="toText(nodeConfig)">{{ toText(nodeConfig) }}</span>
        <span v-else class="placeholder">请配置任务触发器</span>
      </div>
    </div>
    <AddNode v-model="nodeConfig.childNode" />
    <Drawer
      v-model:visible="drawer"
      :size="500"
      attach="body"
      title="抄送人设置"
    >
      <template #header>
        <div class="node-wrap-drawer__title">
          <label v-if="!isEditTitle" @click="editTitle"
            >{{ form.nodeName }}<EditIcon
          /></label>
          <Input
            v-if="isEditTitle"
            ref="nodeTitle"
            v-model="form.nodeName"
            clearable
            @blur="saveTitle"
            @enter="saveTitle"
          />
        </div>
      </template>

      <div style="padding: 0 20px 20px">
        <Form :data="form" label-align="top">
          <!-- <FormItem label="trigger" name="extendConfig.time">
            <Input v-model="form.extendConfig.time" />
          </FormItem> -->
          <FormItem label="参数" name="extendConfig.args">
            <Input v-model="form.extendConfig.args" />
          </FormItem>
          <FormItem label="trigger实体类" name="extendConfig.trigger">
            <Input v-model="form.extendConfig.trigger" />
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button type="primary" @click="save">保存</Button>
        <Button @click="drawer = false">取消</Button>
      </template>
    </Drawer>
  </div>
</template>
