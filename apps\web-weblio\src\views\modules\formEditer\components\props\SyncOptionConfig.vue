<script setup lang="ts">
import { defineEmits, defineOptions, defineProps, ref, watch } from 'vue';

import {
  Button,
  FormItem,
  Input,
  Radio,
  RadioGroup,
  Textarea,
} from 'tdesign-vue-next';

import { requestClient } from '#/api/request';
import { jsonToUrlParams } from '#/utils/index';
import HttpResShow from '#/views/modules/formEditer/components/props/HttpResShow.vue';

defineOptions({ name: 'SyncOptionConfig', inheritAttrs: true });
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['update:modelValue']);
const data = ref(props.modelValue || {});
const httpRes = ref({});
watch(
  () => props.modelValue,
  (val) => {
    data.value = val;
  },
  { deep: true },
);
const test = async () => {
  const res = await doHttp(data.value);
  httpRes.value.open(res);
};

const doHttp = (data) => {
  let url = data.url;
  if (data.method === 'GET') {
    url = `${data.url}?${jsonToUrlParams(JSON.parse(data?.payload || '{}'))}`;
  }
  return requestClient[data.method.toLowerCase()]?.<any[]>(url, data.payload);
};
</script>

<template>
  <div class="grid w-full grid-cols-1">
    <FormItem label="接口地址" name="url">
      <Input v-model="data.url" class="w-full" />
    </FormItem>
    <FormItem label="请求方法" name="method">
      <RadioGroup v-model="data.method" style="width: 100%">
        <div
          class="max-h-[100px] w-full overflow-auto rounded border bg-white p-1 pl-2 pr-2"
        >
          <Radio key="GET" value="GET">GET</Radio>
          <Radio key="POST" value="POST">POST</Radio>
          <Radio key="PUT" value="PUT">PUT</Radio>
        </div>
      </RadioGroup>
    </FormItem>
    <FormItem label="预制参数" name="payload">
      <Textarea v-model="data.payload" class="w-full" />
    </FormItem>
    <div class="grid grid-cols-2 gap-2">
      <FormItem label="标题字段" name="label">
        <Input v-model="data.label" class="w-full" />
      </FormItem>
      <FormItem label="值字段" name="value">
        <Input v-model="data.value" class="w-full" />
      </FormItem>
    </div>
    <Button @click="test">测试</Button>
  </div>
  <HttpResShow ref="httpRes" />
</template>
