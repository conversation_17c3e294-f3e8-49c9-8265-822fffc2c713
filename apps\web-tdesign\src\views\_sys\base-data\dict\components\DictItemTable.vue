<script lang="ts" setup>
import type { PageInfo } from 'tdesign-vue-next';

import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { useVbenModal } from '@vben/common-ui';
import { Icon } from 'tdesign-icons-vue-next';
import {
  Button,
  Input,
  Link,
  MessagePlugin,
  Popconfirm,
  Space,
  Table,
  Tag,
} from 'tdesign-vue-next';
import {
  defineEmits,
  defineExpose,
  defineProps,
  onMounted,
  reactive,
  ref,
} from 'vue';

import { dictItemListByPage, removeDictItemById, saveDictItem } from '../api';
// eslint-disable-next-line no-unused-vars
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['edit', 'add']);
const tableRef = ref();
const state = reactive({
  tagObj: {},
});
/**
 * 表格定义
 */
const columns: any = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
  },
  {
    colKey: 'text',
    title: '标题',
    ellipsis: true,
    sorter: false,
    edit: {
      component: Input,
      props: {
        clearable: true,
        autofocus: true,
        style: 'width:100%',
      },
      rules: [{ required: true, message: '不能为空' }],
      showEditIcon: false,
    },
  },
  {
    colKey: 'value',
    title: '值',
    ellipsis: true,
    sorter: false,
    edit: {
      component: Input,
      props: {
        clearable: true,
        autofocus: false,
        style: 'width:100%',
      },
      rules: [{ required: true, message: '不能为空' }],
      showEditIcon: false,
    },
  },
  {
    colKey: 'remark',
    title: '备注',
    ellipsis: true,
    sorter: false,
    edit: {
      component: Input,
      props: {
        clearable: true,
        autofocus: false,
        style: 'width:100%',
      },
      // 校验规则，此处同 Form 表单
      showEditIcon: false,
    },
  },
  {
    colKey: 'op',
    title: '操作',
    width: 100,
    fixed: 'center',
  },
]);
const data: any = ref([]);
const selectedRowKeys = ref([]);
const tableConfig = ref(BaseTableConfig);
const pagination: any = ref(Pagination);
const loading = ref(false);
const sort = ref([]);
const editableRowKeys = ref<any>([]);
const editMap: any = {};
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    editableRowKeys.value = [];
    if (isOpen) {
      state.tagObj = modalApi.getData()?.tagObj;
      // eslint-disable-next-line no-use-before-define
      await loadData();
    }
  },
  title: '编辑字典项',
});
/** -----------------------------------------------  */

/**
 * 网络请求调用定义
 */
const reqRunner = {
  saveDictItem: async (params: any) => {
    loading.value = true;
    try {
      await saveDictItem(params);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
  dictItemListByPage: async (params: any) => {
    loading.value = true;
    try {
      const { records, total } = await dictItemListByPage(params);
      data.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
};

const status = ref([]);
const delFlag = ref([]);
const editId = ref();
const guid = (prefix?: any) => {
  return (
    prefix +
    'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replaceAll(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c == 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    })
  );
};
/**
 * table初始化方法
 */
const loadData = async () => {
  const params = {
    param: { dictCode: state.tagObj?.code },
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await reqRunner.dictItemListByPage(params);
};
/**
 * 分页栏点击/更改响应方法
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};

/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRowKeys.value = value;
};

/**
 * 列拖动交换位置响应
 * @param newData
 * @param sort
 */
const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

/**
 * vue 生命周期 页面加载结束
 */
onMounted(async () => {
  loadData();
});

/**
 * 新增按钮响应
 */
const add = () => {
  editId.value = guid('tmpCOCOID');
  data.value.unshift({ id: editId.value });
  editableRowKeys.value.push(editId.value);
};
// 更新 editableRowKeys
const updateEditState = (id: string) => {
  const index = editableRowKeys.value.indexOf(id);
  editableRowKeys.value.splice(index, 1);
  tableRef.value?.clearValidateData();
  editId.value = undefined;
};
/**
 * 编辑按钮响应
 * @param record
 */
const edit = (record: any) => {
  editId.value = record.id;
  if (!editableRowKeys.value.includes(record.id)) {
    editableRowKeys.value.push(record.id);
  }
  emit('edit', record);
};

const save = (record: any) => {
  tableRef.value.validateRowData(record.id).then(async (params: any) => {
    if (params.result.length > 0) {
      const r = params.result[0];
      MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
    } else {
      const current = editMap[record.id];
      if (current?.editedRow) {
        if (current?.editedRow.id.startsWith('tmpCOCOID')) {
          delete current?.editedRow.id;
        }
        const res = await saveDictItem({
          ...current?.editedRow,
          dictCode: state?.tagObj?.code,
        });
        data.value.splice(current.rowIndex, 1, res);
        updateEditState(record.id);
      }
    }
  });
};
const cancel = (record: any) => {
  if (editId.value.startsWith('tmpCOCOID')) {
    data.value.splice(0, 1);
  }
  editId.value = undefined;
  updateEditState(record.id);
};
const onRowEdit: any = (params: any) => {
  const { row, col, value } = params;
  const oldRowData = editMap[row.id]?.editedRow || row;
  const editedRow = {
    ...oldRowData,
    [col.colKey]: value,
  };
  editMap[row.id] = {
    ...params,
    editedRow,
  };
};

/**
 * 批量删除按钮响应
 */
const removeBatch = async () => {
  const ids = selectedRowKeys.value.join(',');
  await removeDictItemById(ids);
  selectedRowKeys.value = [];
  loadData();
};

/**
 * 单行删除按钮响应
 * @param row
 */
const remove = async (row: any) => {
  await removeDictItemById(row.id);
  loadData();
};
const detailHandel = (data: any) => {
  // router.replace({ path: '/project-detial/index', query: data });
};
/**
 * 方法/属性导出
 */
defineExpose({
  loadData,
});
</script>

<template>
  <Modal :footer="false" class="tiny-tdesign-style-patch w-[60%]">
    <Table
      ref="tableRef"
      v-model:column-controller-visible="tableConfig.columnControllerVisible"
      :columns="columns"
      :data="data"
      :loading="loading"
      :pagination="pagination"
      :pagination-affixed-bottom="false"
      :selected-row-keys="selectedRowKeys"
      :sort="sort"
      row-key="id"
      v-bind="tableConfig"
      :editable-row-keys="editableRowKeys"
      @drag-sort="onDragSort"
      @page-change="rehandlePageChange"
      @row-edit="onRowEdit"
      @select-change="rehandleSelectChange"
      @sort-change="sortChange"
    >
      <template #topContent="slotProps">
        <div class="t-row--space-between mb-2 flex items-center justify-center">
          <div class="flex flex-wrap items-center justify-center gap-1">
            <div
              v-if="selectedRowKeys && selectedRowKeys.length > 0"
              class="text-[gray]"
            >
              已选择 {{ selectedRowKeys?.length || 0 }} 条数据
            </div>
          </div>
          <div class="flex flex-wrap items-center justify-center gap-2">
            <!--          <Button :disabled="editId ? true : false" @click="add">-->
            <!--            <template #icon>-->
            <!--              <Icon name="add-circle" />-->
            <!--            </template>-->
            <!--            新增-->
            <!--          </Button>-->

            <Popconfirm
              v-if="selectedRowKeys && selectedRowKeys.length > 0"
              content="确定删除？"
              theme="danger"
              @confirm="removeBatch"
            >
              <Button theme="danger">
                <template #icon>
                  <Icon name="delete" />
                </template>
                批量删除
              </Button>
            </Popconfirm>
            <!--          <ColumnDisplay v-model="tableConfig.columnControllerVisible" />-->
          </div>
        </div>
      </template>
      <template #firstFullRow>
        <div class="w-[100%] bg-white p-1">
          <Button
            :disabled="editId ? true : false"
            class="w-[100%]"
            theme="primary"
            variant="dashed"
            @click="add"
          >
            <template #icon>
              <Icon name="add-circle" />
            </template>
            新增
          </Button>
        </div>
      </template>
      <template #items="slotProps">
        <Link theme="primary" @click="detailHandel(slotProps.row)">
          点击查看详细
        </Link>
      </template>

      <template #type="slotProps">
        <Tag>
          {{ slotProps.row.type === 0 ? '字符' : '数字' }}
        </Tag>
      </template>

      <template #op="slotProps">
        <Space size="small">
          <Link
            v-if="!editableRowKeys.includes(slotProps.row.id)"
            theme="primary"
            @click="edit(slotProps.row)"
          >
            编辑
          </Link>
          <Link
            v-if="editableRowKeys.includes(slotProps.row.id)"
            theme="primary"
            @click="save(slotProps.row)"
          >
            保存
          </Link>
          <Link
            v-if="editableRowKeys.includes(slotProps.row.id)"
            theme="primary"
            @click="cancel(slotProps.row)"
          >
            取消
          </Link>
          <Popconfirm
            v-if="!editableRowKeys.includes(slotProps.row.id)"
            content="确定删除？"
            theme="warning"
            @confirm="remove(slotProps.row)"
          >
            <Link theme="danger">删除</Link>
          </Popconfirm>
        </Space>
      </template>
      <template #empty>
        <div class="flex-col-center flex p-1">
          <div class="text-sx mb-2">暂无数据</div>
          <Button
            :disabled="editId ? true : false"
            class="w-[100%]"
            theme="primary"
            variant="text"
            @click="add"
          >
            <template #icon>
              <Icon name="add-circle" />
            </template>
            点击创建新数据
          </Button>
        </div>
      </template>
    </Table>
  </Modal>
</template>
<style scoped lang="less"></style>
