import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据工具列表',
    },
    name: 'tDataTools',
    path: '/tDataTools',
    children: [
      {
        meta: {
          title: '数据工具列表编辑',
        },
        name: 'tDataToolsIndex',
        path: '/tDataTools/index',
        component: () =>
          import('#/views/modules/tDataTools/index.vue'),
      },
    ],
  },
];

export default routes;
