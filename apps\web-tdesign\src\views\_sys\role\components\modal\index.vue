<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Form,
  FormItem,
  type FormProps,
  Input,
  Select,
  Textarea,
} from 'tdesign-vue-next';

import { getDictItems } from '#/api';

import { roleSave } from '../../api';

const formData: any = ref({});
const form = ref();

const status = ref([]);

const FORM_RULES: FormProps['rules'] = {
  code: [
    {
      required: true,
      message: '必填',
    },
  ],
  name: [
    {
      required: true,
      message: '必填',
    },
  ],
  tag: [
    {
      required: true,
      message: '必填',
    },
  ],
  type: [
    {
      required: true,
      message: '必填',
    },
  ],
  power: [
    {
      required: true,
      message: '必填',
    },
  ],
  remark: [
    {
      required: false,
    },
  ],
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    const vali = await form.value.validate();
    if (vali === true) {
      // 验证通过提交请求 并且关闭窗口
      await roleSave(formData.value);
      // 刷新表格
      modalApi.getData().refresh();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    formData.value = isOpen
      ? { ...modalApi.getData<Record<string, any>>()?.record }
      : {};
  },
  title: '新增',
});

onMounted(async () => {
  status.value = await getDictItems('BASE_STATUS');
});
</script>
<template>
  <Modal>
    <Form
      ref="form"
      :data="formData"
      :label-width="120"
      :rules="FORM_RULES"
      colon
      label-align="top"
    >
      <FormItem label="角色标识" name="code">
        <Input v-model="formData.code" placeholder="请输入角色标识" />
      </FormItem>

      <FormItem label="角色名称" name="name">
        <Input
          v-model="formData.name"
          :style="{ minWidth: '134px' }"
          placeholder="请输入角色名称"
        />
      </FormItem>
      <FormItem label="状态" name="status">
        <Select
          v-model="formData.status"
          :options="status"
          clearable
          placeholder="选择状态"
        />
      </FormItem>

      <FormItem label="备注" name="remark">
        <Textarea
          v-model="formData.remark"
          :style="{ minWidth: '134px' }"
          class="form-item-content"
          placeholder="请输入备注"
        />
      </FormItem>
    </Form>
  </Modal>
</template>
