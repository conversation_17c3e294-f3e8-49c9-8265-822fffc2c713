/*! UEditorPlus v2.0.0*/
function initImgName(){for(var a in emotion.SmilmgName){var b=emotion.SmilmgName[a],c=emotion.SmileyBox[a],d="";if(c.length)return;for(var e=1;e<=b[1];e++)d=b[0],e<10&&(d+="0"),d=d+e+".gif",c.push(d)}}function initEvtHandler(a){for(var b=$G(a),c=0,d=0;c<b.childNodes.length;c++){var e=b.childNodes[c];1==e.nodeType&&(domUtils.on(e,"click",function(a){return function(){switchTab(a)}}(d)),d++)}switchTab(0),$G("tabIconReview").style.display="none"}function InsertSmiley(a,b){var c={src:editor.options.emotionLocalization?editor.options.UEDITOR_HOME_URL+"dialogs/emotion/"+a:a};c._src=c.src,editor.execCommand("insertimage",c),b.ctrl<PERSON>ey||dialog.popup.hide()}function switchTab(a){autoHeight(a),0==emotion.tabExist[a]&&(emotion.tabExist[a]=1,createTab("tab"+a));for(var b=$G("tabHeads").getElementsByTagName("span"),c=$G("tabBodys").getElementsByTagName("div"),d=0,e=b.length;d<e;d++)b[d].className="",c[d].style.display="none";b[a].className="focus",c[a].style.display="block"}function autoHeight(a){var b=dialog.getDom("iframe"),c=b.parentNode.parentNode;switch(a){case 0:b.style.height="380px",c.style.height="392px";break;case 1:b.style.height="220px",c.style.height="232px";break;case 2:b.style.height="260px",c.style.height="272px";break;case 3:b.style.height="300px",c.style.height="312px";break;case 4:b.style.height="140px",c.style.height="152px";break;case 5:b.style.height="260px",c.style.height="272px";break;case 6:b.style.height="230px",c.style.height="242px"}}function createTab(a){for(var b,c,d,e,f,g,h="?v=1.1",i=$G(a),j=emotion.SmileyPath+emotion.imageFolders[a],k=5.5,l=iHeight=35,m=3,n=emotion.imageCss[a],o=emotion.imageCssOffset[a],p=['<table class="smileytable">'],q=0,r=emotion.SmileyBox[a].length,s=11;q<r;){p.push("<tr>");for(var t=0;t<s;t++,q++)b=emotion.SmileyBox[a][q],b?(c=j+b+h,d=j+b,e=t<k?0:1,f=o*q*-1-1,g=emotion.SmileyInfor[a][q],p.push('<td  class="'+n+'"   border="1" width="'+m+'%" style="border-collapse:collapse;" align="center"  bgcolor="transparent" onclick="InsertSmiley(\''+d.replace(/'/g,"\\'")+"',event)\" onmouseover=\"over(this,'"+c+"','"+e+'\')" onmouseout="out(this)">'),p.push("<span>"),p.push('<img  style="background-position:left '+f+'px;" title="'+g+'" src="'+emotion.SmileyPath+(editor.options.emotionLocalization?'0.gif" width="':'default/0.gif" width="')+l+'" height="'+iHeight+'"></img>'),p.push("</span>")):p.push('<td width="'+m+'%"   bgcolor="#FFFFFF">'),p.push("</td>");p.push("</tr>")}p.push("</table>"),p=p.join(""),i.innerHTML=p}function over(a,b,c){a.style.backgroundColor="#ACCD3C",$G("faceReview").style.backgroundImage="url("+b+")",1==c&&($G("tabIconReview").className="show"),$G("tabIconReview").style.display="block"}function out(a){a.style.backgroundColor="transparent";var b=$G("tabIconReview");b.className="",b.style.display="none"}function createTabList(a){for(var b={},c=0;c<a;c++)b["tab"+c]=[];return b}function createArr(a){for(var b=[],c=0;c<a;c++)b[c]=0;return b}window.onload=function(){editor.setOpt({emotionLocalization:!1}),emotion.SmileyPath=editor.options.emotionLocalization===!0?"images/":"http://img.baidu.com/hi/",emotion.SmileyBox=createTabList(emotion.tabNum),emotion.tabExist=createArr(emotion.tabNum),initImgName(),initEvtHandler("tabHeads")};