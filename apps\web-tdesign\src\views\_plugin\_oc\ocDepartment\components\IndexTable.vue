<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';

import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import {
  AddCircleIcon,
  Icon,
  RefreshIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  EnhancedTable,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Space,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import OcDepartmentMember from '../../ocDepartmentMember/index.vue';
import { deleteBatch, listByTree } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
/**
 * table 排序字段
 */

const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',

    width: 64,
  },

  { colKey: 'code', ellipsis: true, title: '部门编号' },
  { colKey: 'name', ellipsis: true, title: '部门名称' },
  { colKey: 'name', ellipsis: true, title: '部门名称' },
  { colKey: 'status_text', ellipsis: true, title: '状态' },
  { colKey: 'remark', ellipsis: true, title: '备注' },
  { colKey: 'updateBy', ellipsis: true, title: '最后修改人' },
  { colKey: 'updateTime', ellipsis: true, title: '最后修改时间' },
  { colKey: 'op', width: 160, title: '操作', align: 'center' },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
const expandAll = ref(false);
const tableRef = ref();
const ocDepartmentMemberRef = ref();
/**
 * 展开全部
 */
const showAll = () => {
  expandAll.value = !expandAll.value;
  expandAll.value ? tableRef.value.expandAll() : tableRef.value.foldAll();
};

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByTree: useRequest(listByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      state.dataSource = res;
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      reload();
      initStatus();
    },
  }),
};

const initStatus = () => {
  /**
   * 默认选中全部
   */
  state.selectedRowKeys = [];
  /**
   * 默认不显示删除按钮
   */
  state.delDailogShow = false;
  /**
   * 默认不显示查询
   */
  state.hideQuery = false;
  /**
   * 默认不显示加载
   */
  state.loading = false;
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByTree;
  state.loading = loading;
  run(formData.value);
};
/**
 * 新建按钮相应
 */
const edit = (record?: any) => {
  /**
   * 通知Form组件打开编辑窗口
   */
  props.editFormRef?.open(record ? { ...record } : {});
};
const showMember = (record: any) => {
  ocDepartmentMemberRef.value.open(record);
};
/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};
/**
 * 单条删除
 */
const remove = (record: any) => {
  reqRunner.deleteBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  searchForm.value.reset();
  setTimeout(() => {
    reload();
  }, 0);
}; /**
 * table 行点击响应
 * @param record
 */
const handleRowClick = (record: any) => {};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowKeys = value;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};

onMounted(() => {
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <OcDepartmentMember ref="ocDepartmentMemberRef" />
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Space :size="8" class="tiny-tdesign-style-patch" direction="vertical">
    <!--    查询表单定义区域-->
    <Card v-if="isSearchForm">
      <Form
        ref="searchForm"
        :data="formData"
        class="w-full"
        @reset="resetSearch"
        @submit="searchFormSubmit"
      >
        <!--一列表单布局-->
        <div class="grid w-full grid-cols-3 gap-1">
          <FormItem label="部门编号" name="code">
            <Input v-model="formData.code" clearable placeholder="请输入内容" />
          </FormItem>
          <FormItem label="部门名称" name="name">
            <Input v-model="formData.name" clearable placeholder="请输入内容" />
          </FormItem>
          <FormItem label="状态" name="status">
            <Input
              v-model="formData.status"
              clearable
              placeholder="请输入内容"
            />
          </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置
          </Button>

          <!--          <PutAway v-model="state.hideQuery" variant="text" />-->
        </div>
      </Form>
    </Card>
    <Card>
      <!-- 表格定义区域 -->
      <EnhancedTable
        ref="tableRef"
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :bordered="true"
        :columns="columns"
        :data="state.dataSource"
        :hover="true"
        :loading="state.loading"
        :pagination="false"
        :selected-row-keys="state.selectedRowKeys"
        :sort="state.sort"
        :stripe="true"
        cell-empty-content="-"
        lazy-load
        resizable
        row-key="id"
        table-layout="fixed"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
        v-bind="tableConfig"
        :tree="{
          childrenKey: 'children',
          treeNodeColumnIndex: 1,
          indent: 32,
          checkStrictly: true,
          // expandTreeNodeOnClick: true,
        }"
      >
        <!--        表格顶部按钮区域-->
        <template #topContent>
          <div class="mb-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">部门列表</div>
              <div
                v-if="state.selectedRowKeys?.length > 0"
                class="text-blue-600/80"
              >
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <Button
                v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                theme="danger"
                @click="del"
              >
                <template #icon>
                  <Icon name="delete" />
                </template>
                删除
              </Button>
              <Button theme="default" @click="showAll">
                {{ expandAll ? `收起全部` : `展开全部` }}
              </Button>
              <Button theme="primary" @click="edit">
                <template #icon>
                  <AddCircleIcon />
                </template>
                新增
              </Button>
              <Button variant="text" @click="reload">
                <Icon name="refresh" />
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
            </div>
          </div>
        </template>
        <!--        空数据显示定义-->
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
            <Button
              class="w-[100%]"
              theme="primary"
              variant="text"
              @click="edit"
            >
              <template #icon>
                <Icon name="add-circle" />
              </template>
              点击创建新数据
            </Button>
          </div>
        </template>
        <template #dbType="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)">
              {{ slotProps.row.dbType || '-' }}
            </Link>
          </Space>
        </template>
        <!--        编辑按钮-->
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="showMember(slotProps.row)">
              部门成员
            </Link>
            <Link theme="primary" @click="edit(slotProps.row)">编辑</Link>
            <Popconfirm
              content="确定删除？"
              theme="warning"
              @confirm="remove(slotProps.row)"
            >
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </EnhancedTable>
    </Card>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
