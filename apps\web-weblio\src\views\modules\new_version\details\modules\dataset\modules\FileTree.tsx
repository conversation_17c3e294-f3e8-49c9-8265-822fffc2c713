import type { PropType } from 'vue';

import { useUserStore } from '@vben/stores';
import { ChevronDownIcon, ChevronRightIcon } from 'tdesign-icons-vue-next';
import { DialogPlugin } from 'tdesign-vue-next';
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';

function formatTime(time: string) {
  if (!time) return '-';
  const now = new Date();
  const t = new Date(time.replaceAll('-', '/'));
  const diff = (now.getTime() - t.getTime()) / 1000;
  if (diff < 60 * 60 * 24) return '今天';
  if (diff < 60 * 60 * 24 * 2) return '1天前';
  if (diff < 60 * 60 * 24 * 3) return '2天前';
  if (diff < 60 * 60 * 24 * 15)
    return `${Math.floor(diff / (60 * 60 * 24))}天前`;
  return time.split(' ')[0];
}

export default defineComponent({
  name: 'FileTree',
  props: {
    data: {
      type: Array as PropType<any[]>,
      required: true,
    },
  },
  setup(props) {
    const openFolders = ref<Record<number, boolean>>({});
    const userStore = useUserStore();
    const isLogin = !!userStore.userInfo;
    const router = useRouter();
    const handleDownload = (file: any) => {
      if (isLogin) {
        if (file.originalFileUrl) {
          window.open(file.originalFileUrl, '_blank');
        }
      } else {
        const dialog = DialogPlugin.confirm({
          header: '提示',
          body: '请先登录，是否前往登录页？',
          onConfirm: () => {
            dialog.hide();
            router.push('/auth/login');
          },
        });
      }
    };
    const toggle = (id: number) => {
      openFolders.value[id] = !openFolders.value[id];
    };
    return () => (
      <div class="file-tree-root">
        {/* 表头 */}
        <div class="file-tree-header">
          <div class="file-tree-col file-tree-col-path">文件路径</div>
          <div class="file-tree-col file-tree-col-size">文件大小</div>
          <div class="file-tree-col file-tree-col-time">上传时间</div>
          <div class="file-tree-col file-tree-col-action">操作</div>
        </div>
        {/* 文件夹和文件 */}
        {props.data.map((folder) => (
          <div>
            {/* 文件夹行 */}
            <div
              class={[
                'file-tree-row',
                'file-tree-folder',
                openFolders.value[folder.id] ? 'open' : '',
              ]}
              onClick={() => toggle(folder.id)}
            >
              <div class="file-tree-col file-tree-col-path file-tree-folder-path">
                <span class="file-tree-arrow">
                  {openFolders.value[folder.id] ? (
                    <ChevronDownIcon size="18" />
                  ) : (
                    <ChevronRightIcon size="18" />
                  )}
                </span>
                <span class="file-tree-folder-name">
                  {folder.subName || '-'}
                </span>
              </div>
              <div class="file-tree-col file-tree-col-size">-</div>
              <div class="file-tree-col file-tree-col-time">-</div>
              <div class="file-tree-col file-tree-col-action">-</div>
            </div>
            {/* 文件列表 */}
            {openFolders.value[folder.id] &&
              Array.isArray(folder.purposeList) &&
              folder.purposeList.length > 0 &&
              folder.purposeList.map((file: any) => (
                <div class="file-tree-row file-tree-file">
                  <div class="file-tree-col file-tree-col-path file-tree-file-path">
                    {file.originalFilePath
                      ? file.originalFilePath.split('/').pop()
                      : '-'}
                  </div>
                  <div class="file-tree-col file-tree-col-size">
                    {file.originalFileSize}
                  </div>
                  <div class="file-tree-col file-tree-col-time">
                    {formatTime(file.createTime)}
                  </div>
                  <div class="file-tree-col file-tree-col-action">
                    <span
                      class="file-tree-download"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownload(file);
                      }}
                    >
                      {isLogin ? '下载' : '请登录后下载'}
                    </span>
                  </div>
                </div>
              ))}
          </div>
        ))}
        <style>
          {`
          .file-tree-root {
            width: 100%;
            background: #fff;
            border-radius: 6px;
            font-size: 14px;
            color: var(--td-text-color-primary, #222);
            box-shadow: 0 2px 8px 0 #0000000d;
          }
          .file-tree-header {
            display: flex;
            font-weight: 500;
            padding: 12px 0;
            border-bottom: 1px solid #e7e7e7;
            background: #f7f8fa;
            color: var(--td-text-color-secondary, #666);
          }
          .file-tree-row {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s;
          }
          .file-tree-row.file-tree-folder {
            background: #fff;
            cursor: pointer;
            font-weight: 500;
          }
          .file-tree-row.file-tree-folder.open {
            background: #f5f7fa;
          }
          .file-tree-row.file-tree-file {
            background: #fafbfc;
            padding-left: 36px;
            color: var(--td-text-color-secondary, #666);
            font-weight: 400;
          }
          .file-tree-col {
            flex: 1;
            padding: 0 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .file-tree-col-path {
            flex: 2;
          }
          .file-tree-arrow {
            margin-right: 8px;
            color: var(--td-brand-color, #0052d9);
            font-size: 16px;
            vertical-align: middle;
            user-select: none;
            display: inline-flex;
            align-items: center;
          }
          .file-tree-folder-path {
            display: flex;
            align-items: center;
          }
          .file-tree-folder-name {
            font-size: 15px;
            color: var(--td-text-color-primary, #222);
          }
          .file-tree-file-path {
            color: var(--td-text-color-primary, #222);
          }
          .file-tree-download {
            color: var(--td-brand-color, #0052d9);
            cursor: pointer;
            font-weight: 500;
          }
          `}
        </style>
      </div>
    );
  },
});
