import { requestClient } from '#/api/request';

export async function listByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-workflow/queryProcess/listByPage', data);
}

export async function deploy(data: any) {
  return requestClient.post<any>('/rgdc-workflow/process/deploy', data);
}

export async function undeploy(id: any) {
  return requestClient.delete<any>(`/rgdc-workflow/process/undeploy/${id}`);
}

export async function cascadeRemove(id: any) {
  return requestClient.delete<any>(`/rgdc-workflow/process/cascadeRemove/${id}`);
}
