<script setup lang="ts">
import {MessagePlugin, type PageInfo} from 'tdesign-vue-next';

import {baseDownloadFile, getDictItems} from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { Icon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Select,
  Space,
  Table,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { deleteBatch, listByPage } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  cfgEditFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});
const status = ref([]);
const dataTypes = ref([]);
/**
 * 内部静态数据定义
 */
const state = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],

  /**
   * 被选中行数据
   */
  selectedRowDatas: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
/**
 * table 排序字段
 */

const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
  },

  { colKey: 'name', ellipsis: true, sorter: true, title: '模板名称' },
  { colKey: 'code', ellipsis: true, sorter: true, title: '模板编码' },
  { colKey: 'dataType_text', ellipsis: true, sorter: true, title: '模板类别' },
  { colKey: 'isOnline_text', ellipsis: true, sorter: true, title: '启用状态',},
  { colKey: 'createdBy', ellipsis: true, sorter: true, title: '创建人' },
  { colKey: 'createTime', ellipsis: true, sorter: true, title: '创建时间' },
  { colKey: 'cfg',width: 150, ellipsis: true, title: '模板配置' },
  { colKey: 'op', width: 200, title: '操作', align: 'center' },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
      state.loading = false
    },
    onSuccess: (res: any) => {
      state.loading = false
      if (res.length === 0) {
        reload();
        state.selectedRowKeys = [];
        MessagePlugin.success('删除成功！');
      } else {
        reload();
        state.selectedRowKeys = [];
        MessagePlugin.warning(
          `删除失败，模板【${res.toString()}】下仍存在数据，请检查！`,
        );
      }
    },
  }),
  excelExport: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      MessagePlugin.success('导出成功，请查看下载任务');
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run({
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};
/**
 * 新建按钮响应
 */
const edit = (record?: any) => {
  /**
   * 通知Form组件打开编辑窗口
   */
  props.editFormRef?.open(record ? { ...record } : {});
};
/**
 * 复制新建按钮响应
 */
const copyAdd = () => {
  /**
   * 只需要获取行第一条数据
   */
  const record = state.selectedRowDatas[0] || {};

  /**
   * 复制 一览数据
   */
  const copyRcord = { ...record };
  /**
   * 因为是复制新增，id，code，创建人等需要设置成空
   */
  if (copyRcord) {
    copyRcord.id = null;
    copyRcord.code = null;
    copyRcord.createBy = null;
    copyRcord.createTime = null;
    copyRcord.updateBy = null;
    copyRcord.updateTime = null;
    copyRcord.version = null;
  }
  /**
   * 通知Form组件打开编辑窗口
   */
  props.editFormRef?.open(copyRcord ? { ...copyRcord } : {});
};
const cfgEdit = (record?: any) => {
  record.isTemplateEdit = true
  props.cfgEditFormRef?.open(record ? { ...record } : {});
};
/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.loading = true
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};

/**
 * 单条删除
 */
const remove = (record: any) => {
  state.loading = true
  reqRunner.deleteBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  searchForm.value.reset();
  setTimeout(() => {
    reload();
  }, 0);
}; /**
 * table 行点击响应
 * @param record
 */
const handleRowClick = (record: any) => {};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowKeys = value;
  state.selectedRowDatas = ctx.selectedRowData;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};

const excelExport = async (val: any) => {
  // 下载Excel
  reqRunner.excelExport.run(`/tDataTemplate/excelExport/${val.code}/${val.dataType}`);
};

onMounted(async () => {
  reload();
  status.value = await getDictItems('IS_ONLINE');
  dataTypes.value = await getDictItems('DATA_TYPE');
});
/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Space :size="8" class="tiny-tdesign-style-patch" direction="vertical">
    <!--    查询表单定义区域-->
    <Card v-if="isSearchForm">
      <Form
        ref="searchForm"
        :data="formData"
        class="w-full"
        @reset="resetSearch"
        @submit="searchFormSubmit"
      >
        <!--一列表单布局-->
        <div class="grid w-full grid-cols-3 gap-1">
          <FormItem label="模板名称" name="name">
            <Input v-model="formData.name" clearable placeholder="请输入" />
          </FormItem>
          <FormItem label="模板编号" name="code">
            <Input v-model="formData.code" clearable placeholder="请输入" />
          </FormItem>
          <FormItem label="启用状态" name="isOnline">
            <Select
              :options="status"
              v-model="formData.isOnline"
              clearable
              placeholder="请选择"
            />
          </FormItem>
        </div>
        <div class="grid w-full grid-cols-3 gap-1">
          <FormItem label="模板类别" name="dataType">
            <Select v-model="formData.dataType" :options="dataTypes" clearable placeholder="请选择"/>
          </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <Icon name="search" />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <Icon name="refresh" />
            </template>
            重置
          </Button>

          <!--          <PutAway v-model="state.hideQuery" variant="text" />-->
        </div>
      </Form>
    </Card>
    <Card>
      <!-- 表格定义区域 -->
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :bordered="true"
        :columns="columns"
        :data="state.dataSource"
        :hover="true"
        :loading="state.loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="state.selectedRowKeys"
        :sort="state.sort"
        :stripe="true"
        cell-empty-content="-"
        lazy-load
        resizable
        row-key="id"
        table-layout="fixed"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
        v-bind="tableConfig"
      >
        <!--        表格顶部按钮区域-->
        <template #topContent>
          <div class="mb-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">数据模板列表</div>
              <div
                v-if="state.selectedRowKeys?.length > 0"
                class="text-blue-600/80"
              >
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <Button v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                      theme="danger" @click="del">
                <template #icon>
                  <Icon name="delete" />
                </template>
                删除
              </Button>
<!--              <Button v-if="state.selectedRowKeys && state.selectedRowKeys.length === 1"
                      theme="warning" @click="copyAdd">
                <template #icon>
                  <Icon name="file-copy" />
                </template>
                复制新增
              </Button>-->
              <Button theme="primary" @click="edit">
                <template #icon>
                  <Icon name="add-circle" />
                </template>
                新增
              </Button>
              <Button variant="text" @click="reload">
                <Icon name="refresh" />
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
            </div>
          </div>
        </template>
        <!--        空数据显示定义-->
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
            <Button class="w-[100%]" theme="primary" variant="text" @click="edit">
              <template #icon>
                <Icon name="add-circle" />
              </template>
              点击创建新数据
            </Button>
          </div>
        </template>
        <template #dbType="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)">
              {{ slotProps.row.dbType || '-' }}
            </Link>
          </Space>
        </template>
        <template #cfg="slotProps">
          <Space size="small">
            <Button theme="primary" variant="dashed" @click="cfgEdit(slotProps.row)">
              点击编辑
            </Button>
          </Space>
        </template>
        <template #op="slotProps">
          <Space size="large">
            <Link theme="primary" @click="edit(slotProps.row)">编辑</Link>
            <Popconfirm content="确定删除？" theme="warning" @confirm="remove(slotProps.row)">
              <Link theme="danger">删除</Link>
            </Popconfirm>
            <Link theme="primary" @click="excelExport(slotProps.row)">下载</Link>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
