<script lang="ts" setup>
import type { PageInfo, TableRowData } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant';
import { removalUnderline } from '#/utils/sort';
import { Icon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
} from 'tdesign-vue-next';
import { defineEmits, defineExpose, defineProps, h, onMounted, ref } from 'vue';

import { deleteBatch, listByPageApi } from '../api';

const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['edit', 'add']);
/**
 * 表格定义
 */
const columns: any = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
  },
  {
    title: '序号',
    colKey: 'serial-number',
    width: 100,
  },
  {
    colKey: 'title',
    title: '标题',
    ellipsis: true,
    sorter: true,
    cell: (e: any, { row }: any) => {
      if (row.msgId === null) {
        return row.title;
      }
      // 如果需要跳转页面则在下方添加跳转逻辑
      return h(Link, row.title);
    },
  },

  {
    colKey: 'senderId_text',
    title: '发送人账号名',
    sorter: true,
    ellipsis: true,
  },
  {
    colKey: 'readStatus_text',
    title: '是否已读',
    ellipsis: true,
    sorter: true,
  },

  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    ellipsis: true,
    sorter: true,
  },

  {
    colKey: 'op',
    title: '操作',
    width: 100,
    fixed: 'center',
  },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
const formData: any = ref({});
const form = ref();
const data: any = ref([]);
const selectedRowKeys = ref([]);
const tableConfig = ref(BaseTableConfig);
const pagination: any = ref(Pagination);
const loading = ref(false);
const sort = ref([]);
const status = ref([]);
/** -----------------------------------------------  */

/**
 * 网络请求调用定义
 */
const reqRunner = {
  userListByPageApi: async (params: any) => {
    loading.value = true;
    try {
      const { records, total } = await listByPageApi(params);
      data.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
};

/**
 * table初始化方法
 */
const loadData = async () => {
  const params = {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await reqRunner.userListByPageApi(params);
};
/**
 * 分页栏点击/更改响应方法
 * @param pageInfo
 * @param newDataSource
 */
const rehandlePageChange = (
  pageInfo: PageInfo,
  newDataSource: TableRowData[],
) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};
/**
 * 搜索表单重置方法
 */
const onReset = () => {
  form.value.reset();
  loadData();
};
/**
 * 搜索表单提交方法
 */
const onSubmit = async () => {
  loadData();
};

/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRowKeys.value = value;
};

/**
 * 列拖动交换位置响应
 * @param newData
 * @param sort
 */
const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

/**
 * vue 生命周期 页面加载结束
 */
onMounted(async () => {
  loadData();
  status.value = await getDictItems('MSG_READ');
});

/**
 * 新增按钮响应
 */
const add = () => {
  emit('add');
};

/**
 * 编辑按钮响应
 * @param record
 */
const edit = (record: any) => {
  emit('edit', record);
};

/**
 * 行点击时间
 */
const handleRowClick = () => {};

/**
 * 批量删除按钮响应
 */
const removeBatch = async () => {
  const ids = selectedRowKeys.value.join(',');
  await deleteBatch(ids);
  selectedRowKeys.value = [];
  loadData();
};

/**
 * 单行删除按钮响应
 * @param row
 */
const remove = async (row: any) => {
  await deleteBatch(row.id);
  loadData();
};
/**
 * 方法/属性导出
 */
defineExpose({
  loadData,
});
</script>

<template>
  <Space class="w-full" direction="vertical" size="small">
    <Card v-if="props.isSearchForm">
      <Form
        ref="form"
        :data="formData"
        :label-width="80"
        @reset="onReset"
        @submit="onSubmit"
      >
        <Row :gutter="[24, 24]">
          <Col :span="4">
            <FormItem label="标题" name="title">
              <Input
                v-model="formData.title"
                :style="{ minWidth: '134px' }"
                clearable
                placeholder="请输入标题"
                type="search"
              />
            </FormItem>
          </Col>

          <Col :span="4">
            <FormItem label="内容" name="content">
              <Input
                v-model="formData.content"
                :style="{ minWidth: '134px' }"
                clearable
                placeholder="请输入内容"
              />
            </FormItem>
          </Col>

          <Col :span="4">
            <FormItem label="状态" name="readStatus">
              <Select
                v-model="formData.readStatus"
                :options="status"
                clearable
                placeholder="选择状态"
              />
            </FormItem>
          </Col>
        </Row>
        <Row justify="end">
          <Col :span="24" class="mt-4">
            <Space size="small">
              <Button
                :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
                theme="primary"
                type="submit"
              >
                <template #icon>
                  <Icon name="search" />
                </template>
                搜索
              </Button>
              <Button theme="default" type="reset" variant="base">
                重置
              </Button>
              <!-- <PutAway v-model="hideQuery" variant="text" /> -->
            </Space>
          </Col>
        </Row>
      </Form>
    </Card>
    <Card>
      <div class="t-row--space-between mb-2 flex items-center justify-center">
        <div class="flex flex-wrap items-center justify-center gap-1">
          <div class="t-card__title ml-2">消息列表</div>
          <div
            v-if="selectedRowKeys && selectedRowKeys.length > 0"
            class="text-[gray]"
          >
            已选择 {{ selectedRowKeys?.length || 0 }} 条数据
          </div>
        </div>
        <div class="flex flex-wrap items-center justify-center gap-2">
          <Popconfirm
            v-if="selectedRowKeys && selectedRowKeys.length > 0"
            content="确定删除？"
            theme="danger"
            @confirm="removeBatch"
          >
            <Button theme="danger">
              <template #icon>
                <Icon name="delete" />
              </template>
              批量删除
            </Button>
          </Popconfirm>
          <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
        </div>
      </div>
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="selectedRowKeys"
        :sort="sort"
        v-bind="tableConfig"
        @drag-sort="onDragSort"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
      >
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)"> 查看</Link>
            <Popconfirm
              content="确定删除？"
              theme="danger"
              @confirm="remove(slotProps.row)"
            >
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
