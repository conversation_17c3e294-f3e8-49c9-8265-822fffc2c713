<script setup lang="ts">
import { Card, Space } from 'tdesign-vue-next';
import { onMounted } from 'vue';

onMounted(async () => {});
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <Space
        :size="8"
        class="tiny-tdesign-style-patch w-full pl-[300px] pr-[300px]"
        direction="vertical"
      >
        <Card style="min-height: 130px">
          <div>
            <div class="about-us-title"><i></i><span>联系方式</span></div>
            <div class="contact-information">
              <div class="contact">
                <div class="contact-item">
                  <img src="/static/images/auto-icon001.png" title="" />
                  <div class="contact-item-text">
                    <div class="name">电话</div>
                    <div class="phone">+86-411-84379198</div>
                  </div>
                </div>
                <div class="contact-item">
                  <img src="/static/images/auto-icon002.png" title="" />
                  <div class="contact-item-text">
                    <div class="name">邮箱</div>
                    <div class="phone"><EMAIL></div>
                  </div>
                </div>
                <div class="contact-item">
                  <img src="/static/images/auto-icon003.png" title="" />
                  <div class="contact-item-text">
                    <div class="name">地址</div>
                    <div class="phone text">
                      辽宁省大连市沙河口区中山路457号
                    </div>
                  </div>
                </div>
              </div>
              <div class="map">
                <img src="/static/images/map.png" title="" />
              </div>
            </div>
          </div>
        </Card>
      </Space>
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
.about-us-title {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.about-us-title i {
  width: 5px;
  height: 24px;
  margin-right: 9px;
  background-color: #1760c2;
}
.about-us-title span {
  font-family: MicrosoftYaHei-Bold;
  font-size: 24px;
  color: #333;
  letter-spacing: 0;
  font-weight: 700;
}
.contact-information {
  display: flex;
  justify-content: space-between;
}
.contact-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 20px;
  background: #f7f9fb;
  border-radius: 8px;
}
.contact-item .contact-item-text {
  overflow: hidden;
}
.contact-item .contact-item-text .name {
  font-size: 16px;
  color: #333;
  letter-spacing: 0;
  font-weight: 400;
}
.contact-item .contact-item-text .phone {
  font-family: ArialMT;
  font-size: 16px;
  color: #1760c2;
  letter-spacing: 0;
  font-weight: 400;
}
.contact-information .contact {
  display: flex;
  flex-wrap: wrap;
  width: 305px;
  gap: 18px;
}
.contact-information .map {
  width: calc(100% - 330px);
}
.contact-item > img {
  width: 48px;
  height: 48px;
  margin-right: 16px;
}
.contact-information .map img {
  width: 100%;
  height: 100%;
}
</style>
