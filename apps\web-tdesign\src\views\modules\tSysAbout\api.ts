// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-sys/tSysAbout/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-sys/tSysAbout/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-sys/tSysAbout/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-sys/tSysAbout/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-sys/tSysAbout/getByIds/${data}`);
}
