import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据工具',
    },
    name: 'tSysDataTools',
    path: '/tSysDataTools',
    children: [
      {
        meta: {
          title: '数据工具-详情',
        },
        name: 'tSysDataToolsIndex',
        path: '/tSysDataTools/index',
        component: () =>
          import('#/views/modules/tSysDataTools/index.vue'),
      },
    ],
  },
];

export default routes;
