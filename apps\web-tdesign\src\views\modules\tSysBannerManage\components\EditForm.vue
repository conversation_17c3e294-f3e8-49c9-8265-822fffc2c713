<script setup lang="tsx">
import {computed, defineProps, reactive, ref} from 'vue';
import {useRequest} from 'vue-hooks-plus';
import {useVbenModal} from '@vben/common-ui';

import {
  type ButtonProps,
  Form,
  FormItem,
  type FormProps,
  Input,
  InputNumber,
  MessagePlugin,
  type TdUploadProps,
  Upload,
  type UploadProps
} from 'tdesign-vue-next';

import {save} from '../api.ts';
import {useAppConfig} from "@vben/hooks";
import {useAccessStore} from "@vben/stores";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({
  imgCode: "",
  imgDescribe: "",
  imgPath: "",
  imgSeq: null,

});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
        imgCode: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 64,
             message: '最大长度为64',
           },
         ],
        imgDescribe: [
           {
             max: 900,
             message: '最大长度为900',
           },
         ],
        imgUpload: [
          {
            required: false,
            message: '必填',
          },
        ],
        // imgPath: [
        //    {
        //      required: true,
        //      message: '必填',
        //    },
        //    {
        //      max: 900,
        //      message: '最大长度为900',
        //    },
        //  ],
        imgSeq: [
           {
             required: true,
             message: '必填',
           },
         ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      /**
       * 保存数据
       */
      reqRunner.save.run({ ...state.tagObj, ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});

const files = ref<TdUploadProps['value']>([]);

// onMounted(() => {
//   fileUrls.value.forEach((file, index) => {
//     console.log(`File ${index + 1}:`, file);
//     const url = `${apiURL}/file/download?path=${encodeURI(file.url || '')}`
//     console.log("url111111111111111",url)
//     files.value.push({
//       name: file.name,
//       url: url,
//       status: 'success',
//     });
//   });
//   console.log("files",files)
// });

const uploadRef = ref();
const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
const uploadUrl = computed(() => {
  return `${apiURL}/rgdc-sys/file/upload`; // 使用环境变量配置 API 基础 URL
});

// 定义上传头部信息
const uploadHeaders = computed(() => {
  const accessStore = useAccessStore();
  return {
    Authorization: formatToken(accessStore.accessToken),
  };
});

const formatToken = (token: null | string) => {
  return token ? `Bearer ${token}` : null;
};

const formatResponse: TdUploadProps['formatResponse'] = (res) => {
  if (!res || res.code !== '00000000') {
    return {
      status: 'fail',
      error: '上传失败，原因：文件过大或网络不通',
    };
  } else {
    console.log('上传成功',res);
    console.log('res.url', res.result.url)
    formData.value.imgPath = res.result.url
    console.log("files1111111111111111111111",files)
  }
  return res;
};

// const files = ref<UploadProps['value']>([]);
const fileFail = ref<UploadProps['value']>([]);
const disabled = ref(false);
const uploadAllFilesInOneRequest = ref(false);
const autoUpload = ref(true);
const showImageFileName = ref(true);
const imageViewerProps = ref<UploadProps['imageViewerProps']>({
  closeOnEscKeydown: false,
});
const sizeLimit = ref<UploadProps['sizeLimit']>({
  size: 500,
  unit: 'KB',
});
const abridgeName: UploadProps['abridgeName'] = [6, 6];
const handleFail: UploadProps['onFail'] = ({ file }) => {
  MessagePlugin.error(`文件 ${file.name} 上传失败`);
};
const formatImgResponse: UploadProps['formatResponse'] = () => {
  return {
    name: 'file.name',
    url: 'https://tdesign.gtimg.com/demo/demo-image-1.png',
  };
};
// const formatResponse: UploadProps['formatResponse'] = () => {
//   return {
//     name: 'FileName',
//     error: '网络异常，图片上传失败',
//   };
// };
const uploadFiles: ButtonProps['onClick'] = () => {
  uploadRef.value.uploadFiles();
};
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="w-full"
      label-align="top"
    >
      <div class="grid w-full grid-cols-2 gap-1">
        <FormItem label="图片编码" name="imgCode">
           <Input v-model="formData.imgCode" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="图片描述" name="imgDescribe">
           <Input v-model="formData.imgDescribe" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="图片上传" name="imgUpload">
          <Upload
            ref="uploadRef"
            v-model="files"
            :action="uploadUrl"
            theme="image"
            accept="image/*"
            :auto-upload="autoUpload"
            :disabled="disabled"
            :upload-all-files-in-one-request="uploadAllFilesInOneRequest"
            :show-image-file-name="showImageFileName"
            :format-response="formatResponse"
            :headers="uploadHeaders"
            @fail="handleFail"
          ></Upload>
        </FormItem>
<!--        <FormItem label="图片路径" name="imgPath">-->
<!--           <Input v-model="formData.imgPath" clearable placeholder="请输入内容" />-->
<!--        </FormItem>-->
        <FormItem label="循环顺序" name="imgSeq">
           <InputNumber style="width:100%" :step="1" v-model="formData.imgSeq" clearable placeholder="请输入内容"  />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
