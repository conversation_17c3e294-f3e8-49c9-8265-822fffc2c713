<template>
  <div ref="chart" :style="{ width: '100%', height: '400px' }"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted, ref, watch } from 'vue';

const props = defineProps({
  // 支持多条折线的数据结构：数组形式，每个元素是一个 series 对象
  seriesData: {
    type: Array as () => Array<{
      name: string;
      type: 'line';
      data: number[];
    }>,
    required: true,
  },
  // 可选的 X 轴数据
  xAxisData: {
    type: Array as () => string[],
    default: () => ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  // 可选的图表标题
  title: {
    type: String,
    default: ''
  }
});

const chart = ref(null);
let myChart = null;

onMounted(() => {
  initChart();
});

const initChart = () => {
  if (!chart.value) return;

  myChart = echarts.init(chart.value);

  const option = {
    title: props.title ? { text: props.title } : undefined,
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: props.seriesData.map(item => item.name)
    },
    xAxis: {
      type: 'category',
      data: props.xAxisData
    },
    yAxis: {
      type: 'value'
    },
    series: props.seriesData
  };

  myChart.setOption(option);
};

// 监听数据变化，更新图表
watch(
  () => props.seriesData,
  (newSeriesData) => {
    initChart();
  }
);

// 监听 X 轴数据变化，更新图表
watch(
  () => props.xAxisData,
  () => {
    initChart();
  }
);
</script>
