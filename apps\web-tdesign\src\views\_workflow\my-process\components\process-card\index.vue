<script setup lang="ts">
import { Task1Icon } from 'tdesign-icons-vue-next';
import { Button, Card, Space, Tag } from 'tdesign-vue-next';

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  op: {
    type: Boolean,
    default: true,
  },
});

const emit: any = defineEmits(['edit', 'delete', 'cardClick']);

const handleEdit = () => {
  emit('edit', props.record);
};

const handleClick = () => {
  emit('cardClick', props.record);
};
</script>
<template>
  <Card
    :bordered="false"
    :hover-shadow="true"
    :shadow="true"
    class="w-full"
    @click="handleClick"
  >
    <template #title>
      {{ `流程名称 : ${record.processName}` }}
    </template>
    <template #description>
      {{ `流程Key : ${record.processKey} ` }}
    </template>
    <template #content>
      <div class="flex w-full flex-wrap gap-x-4 gap-y-2">
        <Tag theme="success">
          {{ `流程版本 : ${props.record.processVersion}` }}
        </Tag>
        <Tag theme="warning">
          {{ `创建人 : ${props.record.createBy}` }}
        </Tag>
        <Tag theme="primary" variant="light">
          {{ `创建时间 : ${props.record.createTime}` }}
        </Tag>
      </div>
      <p class="list-card-item_detail--desc">
        {{ props.record.remark }}
      </p>
    </template>
    <template #footer>
      <div class="flex w-[100%] justify-end">
        <Space>
          <Button shape="square" variant="text" @click="handleEdit">
            <Task1Icon />
          </Button>
          <!-- <Popconfirm
            content="确定删除？"
            theme="danger"
            @confirm="handleDelete"
          >
            <Button shape="square" variant="text">
              <DeleteIcon color="red" />
            </Button>
          </Popconfirm> -->
        </Space>
      </div>
    </template>
  </Card>
</template>

<style lang="less" scoped>
.list-card-item {
  display: flex;
  flex-direction: column;
  cursor: pointer;

  &_detail {
    min-height: 140px;

    &--name {
      margin-bottom: var(--td-comp-margin-s);
      font: var(--td-font-title-medium);
      color: var(--td-text-color-primary);
    }

    &--desc {
      color: var(--td-text-color-secondary);
      font: var(--td-font-body-small);
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
