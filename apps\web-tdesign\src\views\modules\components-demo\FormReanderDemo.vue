<script setup lang="ts">
import FormRender from '#/views/modules/formEditer/components/render/FormRender.vue';
import { Page } from '@vben/common-ui';
import { Form } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';

const form: any = ref();
const formConfig = ref();

const formData = ref({});

const setFormData = (list) => {
  list.forEach((item) => {
    formData.value[item.schema.name] = item.schema.value;
    if (item?.schema?.children?.length >= 0) {
      setFormData(item.schema.children);
    }
  });
};
const click = () => {
  const list: any = [
    {
      name: 'item_150810547966',
      component: 'LuckySheet',
      unFormItem: true,
      schema: {
        rules: [
          {
            required: false,
            message: '必填项',
          },
        ],
        title: 'Excel',
        name: 'item_124990061074',
        unFormItem: true,
        value: {},
        isDisabled: false,
        isShow: true,
        isReadonly: false,
        syncOptions: {},
        id: 125988101932,
      },
      id: 124990061074,
      syncOptions: {},
    },
  ];

  // setFormData(formData.value);
  // setTimeout(() => {
  formConfig.value = list;
};

onMounted(() => {
  click();
});
</script>

<template>
  <Page description="Excel导入导出示例" title="Excel">
    <Form ref="form" :data="formData">
      <FormRender
        v-model="formData"
        :form-config="formConfig"
        class="bg-background w-full"
      />
    </Form>

    <pre>
      {{ JSON.stringify(formData, null, 2) }}
    </pre>
  </Page>
</template>
