<script setup lang="ts">
import {
  Button,
  Table,
  Dialog
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref } from 'vue';
import { Pagination } from 'tdesign-vue-next';
import { useRequest } from 'vue-hooks-plus';
import type { PageInfo } from 'tdesign-vue-next';

import {
  getReactionInfoData,
  getReactSchemeInfo,
  getReferenceInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface ReactionInfo {
  id: string;
  reactantsFormulas: string;
  productsFormulas: string;
  classification: string;
  scheme: string;
  stagesCount: string;
  temperature: string;
  time: string;
  pressure: string;
  ph_value: string;
  solvent: string;
  reagent: string;
  catalyst: string;
  productsYields: string;
}
/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});

/**
 * 分页参数
 */
const Paginations = {
  current: 1,
  pageSize: 20,
  total: 0,
};
const pagination: any = ref(Paginations);
// 响应式数据
const reactList = ref<ReactionInfo[][]>([]);
const exampleTitle = ref('');
const experimentalProcedure = ref('');
const referenceList = ref<any>(null);
const schemeList = ref<any>(null);
// 多步反应详细Dialog
const showSchemeDialog = ref(false);
// 实验步骤Dialog
const showStepDialog = ref(false);
// 文献Dialog
const showReferenceDialog = ref(false);

// 表格列定义
const getTableColumns = () => [
  {
    colKey: 'classification',
    title: '反应分类',
    width: 200,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.classification })
  },
  {
    colKey: 'scheme',
    title: '多步反应详细',
    width: 120,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) =>
      row.scheme === '' || row.scheme === null || row.scheme === undefined
        ? h('span', '-')
        : h('span', {
          style: 'color: #3480fe; text-decoration: underline; cursor: pointer',
          onClick: () => openSchemeDialog(row.reactionId, row.scheme) // 传入当前行数据
        }, '查看')
  },
  {
    colKey: 'stagesCount',
    title: '阶段',
    width: 80,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) =>
      row.classification === 'Multi-step reaction'
        ? h('span', '-')
        : h('span', { innerHTML: row.stagesCount })
  },
  {
    colKey: 'temperature',
    title: '温度',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'time',
    title: '时间',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'pressure',
    title: '压力',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'phValue',
    title: 'Ph',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'solvent',
    title: '溶剂',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reagent',
    title: '试剂',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'catalyst',
    title: '催化剂',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'step',
    title: '实验步骤',
    width: 100,
    cell: (h: any, { row }: { row: any }) =>
      (row.exampleTitle === '' || row.exampleTitle === null || row.exampleTitle === undefined) &&
        (row.experimentalProcedure === '' || row.experimentalProcedure === null || row.experimentalProcedure === undefined)
        ? h('span', '-')
        : h('span', {
          style: 'color: #3480fe; text-decoration: underline; cursor: pointer',
          onClick: () => openStepDialog(row.exampleTitle, row.experimentalProcedure) // 传入当前行数据
        }, '查看')
  },
  {
    colKey: 'productsYields',
    title: '产率',
    width: 300,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) =>
      row.productsYields === '' || row.productsYields === null || row.productsYields === undefined
        ? h('span', '-')
        : h('span', { innerHTML: row.productsYields })
  },
  {
    colKey: 'reference',
    title: '文献',
    width: 80,
    cell: (h: any, { row }: { row: any }) =>
      row.citationsId === '' || row.citationsId === null || row.citationsId === undefined
        ? h('span', '-')
        : h('span', {
          style: 'color: #3480fe; text-decoration: underline; cursor: pointer',
          onClick: () => openReferenceDialog(row.citationsId) // 传入当前行数据
        }, '查看')
  },
];
// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});


// 格式化反应公式的计算属性
const formatReactionFormula = (reactants?: string, products?: string) => {
  const formattedReactants = reactants?.replace(/,/g, ' + ') || '';
  const formattedProducts = products?.replace(/,/g, ' + ') || '';
  return `${formattedReactants} → ${formattedProducts}`;
};

const openSchemeDialog = async (rid: any, schemeId: any) => {
  // 调用多步反应详细查询API
  const response = await getReactSchemeInfo(
    { inchikey: state.detailItem.baseCode, reactionId: rid, scheme: schemeId });
  schemeList.value = response;
  showSchemeDialog.value = true;
};

const openStepDialog = async (title: any, procedure: any) => {
  exampleTitle.value = title;
  experimentalProcedure.value = procedure;
  showStepDialog.value = true;
};

const openReferenceDialog = async (id: any) => {
  // 调用反应文献查询API
  const response = await getReferenceInfo({ inchikey: state.detailItem.baseCode, citationId: id });
  referenceList.value = response;
  showReferenceDialog.value = true;
};

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(getReactionInfoData, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => { },
    onSuccess: (res: any) => {
      const { records, total } = res;
      reactList.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };

      if (records.length === 0 && total !== 0) {
        pagination.value.current = 1;
        reload();
      }
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
function reload(data?: any) {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run({
    inchikey: state.detailItem.baseCode,
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize,
  });
}

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};

const formatSchemeList = (schemeList: any) => {
  if (!schemeList) return '';
  return schemeList.replace(/(,\s*)(\d+:)/g, '$1<br />$2');
};

// 行合并逻辑
const getRowspanAndColspan = ({ col, rowIndex }, data) => {
  // 需要合并的列
  const mergeColumns = ['classification', 'scheme', 'step', 'productsYields', 'reference'];

  if (mergeColumns.includes(col.colKey)) {
    const currentClassification = data[rowIndex]?.classification;

    // 检查是否是当前classification的第一条记录
    const isFirstOfClassification = rowIndex === 0 || data[rowIndex - 1]?.classification !== currentClassification;

    if (isFirstOfClassification) {
      // 计算当前classification的总个数
      let classificationCount = 1;
      for (let i = rowIndex + 1; i < data.length; i++) {
        if (data[i]?.classification === currentClassification) {
          classificationCount++;
        } else {
          break;
        }
      }
      return { rowspan: classificationCount, colspan: 1 };
    } else {
      // 不是当前classification的第一条记录，隐藏
      return { rowspan: 0, colspan: 0 };
    }
  }
  return { rowspan: 1, colspan: 1 };
};

onMounted(async () => {
  reload();
});
</script>

<template>
  <div class="substance-react-info">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载反应信息中...</div>
    </div>

    <template v-else>
      <!-- 反应信息区域 -->
      <div class="react-section">

        <!-- 遍历每个反应显示对应的表格 -->
        <div v-for="(react, index) in reactList" class="react-table-container">
          <h1 class="font-style">#{{ (pagination.current - 1) * pagination.pageSize + index + 1 }}</h1>
          <div class="table-content">
            <h3>{{ formatReactionFormula(react[0]?.reactantsFormulas, react[0]?.productsFormulas) }}</h3>
            <h3 class="font-style2">反应详情</h3>
            <!-- 反应产品表格 -->
            <div class="react-table">
              <Table :data="react" :columns="getTableColumns()" :loading="state.loading" :bordered="true" resizable
                :hover="true" :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-"
                :rowspan-and-colspan="raw => getRowspanAndColspan(raw, react)" />
            </div>
          </div>
        </div>

        <div v-if="reactList.length === 0" class="empty-state">
          <p>暂无反应信息</p>
        </div>
        <!-- 分页 -->
        <Pagination v-model:current="pagination.current" :total="pagination.total" :page-size="pagination.pageSize"
          @change="rehandlePageChange" class="mt-4" v-if="pagination.total > 0" />
      </div>
    </template>
  </div>
  <Dialog v-model:visible="showSchemeDialog" header="多步反应详细" :footer="false" width="50vw">
    <!-- 多步反应详细内容 -->
    <div class="dialog-content-scrollable">
      <div>
        <p><span class="title-text" v-html="formatSchemeList(schemeList)"></span></p>
      </div>
    </div>
  </Dialog>

  <Dialog v-model:visible="showStepDialog" header="实验步骤" :footer="false" width="50vw">
    <!-- 实验步骤内容 -->
    <div class="dialog-content-scrollable">
      <p>步骤名称: <span class="title-text" v-html="exampleTitle"></span></p>
      <p class="p-style">步骤描述: <span class="title-text" v-html="experimentalProcedure"></span></p>
    </div>
  </Dialog>

  <Dialog v-model:visible="showReferenceDialog" header="反应文献" :footer="false" width="50vw">
    <!-- 反应文献内容 -->
    <div class="dialog-content-scrollable">
      <div class="reference-item" v-for="(ref, idx) in referenceList" :key="ref.citationsId || idx">
        <p><span class="title-text" v-html="ref.title"></span></p>
        <p class="p-style">摘要: <span class="title-text" v-html="ref.abstractText"></span></p>
      </div>
    </div>
  </Dialog>
</template>

<style scoped lang="scss">
.substance-react-info {
  padding: 20px;

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @keyframes spin {
      0% {
        transform: translateY(-50%) rotate(0deg);
      }

      100% {
        transform: translateY(-50%) rotate(360deg);
      }
    }
  }

  .font-style {
    font-size: 20px;
    font-weight: bold;
  }

  .font-style2 {
    font-size: 16px;
    color: rgba(108, 108, 108, 1);
    padding: 10px 0 5px 0;
  }

  .react-table-container {
    border: 2px solid #e8e8e8;
    margin-bottom: 20px;
    overflow: hidden;
    padding: 20px;
  }

  .table-content {
    padding: 10px 20px;
  }

  :deep(.t-table td),
  :deep(.t-table th) {
    border-right: 1px solid #e6e8eb !important;
  }
}

.dialog-content-scrollable {
  min-height: 50vh;
  max-height: 50vh;
  overflow-y: auto; // 垂直滚动
  padding-right: 8px; // 避免滚动条遮挡内容

  .step-item,
  .reference-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    p {
      margin: 8px 0;
      /* 调整段落间距 */
      line-height: 1.6;
      /* 增加行高 */
    }

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  .title-text {
    font-weight: 600;
  }

  .p-style {
    margin-top: 20px;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #b8b8b8;
    border-radius: 10px;
    border: 1px solid #f8f8f8;

    &:hover {
      background-color: #909090;
    }
  }
}
</style>
