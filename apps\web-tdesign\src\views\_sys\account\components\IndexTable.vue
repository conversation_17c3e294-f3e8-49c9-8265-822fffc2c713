<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import PutAway from '#/components/put-away/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { useUserStore } from '@vben/stores';
const userStore = useUserStore();
import {
  AddCircleIcon,
  DeleteIcon,
  Icon,
  RefreshIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Select,
  Space,
  Table,
  TreeSelect,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useRoute } from 'vue-router';

import { deleteAccountBatch, getAccountPage, getDeptList } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  editPwdRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
/**
 * table 排序字段
 */

const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});
const route = useRoute();

/**
 * 部门数据
 */
 const deptTree = ref([]);

 /**
 * 来源数据
 */
 const sourceSystem = ref([]);

 /**
 * 人员状态数据
 */
 const staffStatus = ref([]);

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
    checkProps: ({ row }: any) => ({ disabled: !!(row.sourceSystem === '01' || row.sourceSystem === '02') }),
  },
  {
    colKey: 'accountNumber',
    title: '账号',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'realName',
    title: '姓名',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'deptName',
    title: '部门',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'phoneNumber',
    title: '手机号',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'email',
    title: '邮箱',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'sourceSystemText',
    title: '来源',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'staffStatusText',
    title: '人员状态',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
    sorter: true,
    width: 180,
  },
  {
    colKey: 'remark',
    title: '备注',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: 170,
    fixed: 'right',
  },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  getAccountPage: useRequest(getAccountPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      const { records, total } = res;
      console.log('records1111', records);
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
  deleteAccountBatch: useRequest(deleteAccountBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      MessagePlugin.success('删除成功');
      reload();
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
function reload(data?: any) {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.getAccountPage;
  state.loading = loading;
  run({
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
}

/**
 * 新建按钮相应
 */
const edit = (record?: any) => {
  /**
   * 通知Form组件打开编辑窗口
   */
  props.editFormRef?.open(record ? { ...record } : {});
};

const editPwd = (record?: any) => {
  /**
   * 通知Form组件打开编辑窗口
   */
  props.editPwdRef?.open(record ? { ...record } : {});
};

/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteAccountBatch.run(state.selectedRowKeys);
};
/**
 * 单条删除
 */
const remove = (record: any) => {
  reqRunner.deleteAccountBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    reload();
  }, 0);
};

function listToTree(list, parentId = '0') {
  return list
    .filter(item => item.parentId === parentId)
    .map(item => ({
      value: item.id,
      label: item.deptName,
      children: listToTree(list, item.id)
    }));
}

/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  searchForm.value.reset();
  setTimeout(() => {
    reload();
  }, 0);
}; /**
 * table 行点击响应
 * @param record
 */
const handleRowClick = (record: any) => {};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowKeys = value;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};

onMounted(async () => {

  // 从路由参数设置来源筛选条件
  if (route.query.sourceSystem) {
    formData.value.sourceSystem = route.query.sourceSystem;
  }
  reload();
  // 获取部门树
  const deptList = await getDeptList({});
  deptTree.value = listToTree(deptList);
  sourceSystem.value = await getDictItems('SOURCE_SYSTEM');
  staffStatus.value = await getDictItems('STAFF_STATUS');
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Space :size="8" class="tiny-tdesign-style-patch w-full" direction="vertical">
    <!--    查询表单定义区域-->
    <Card v-if="isSearchForm && userStore.userInfo && userStore.userInfo?.sourceSystemCode !== '03'">
      <Form
        ref="searchForm"
        :data="formData"
        class="w-full"
        @reset="resetSearch"
        @submit="searchFormSubmit"
      >
        <!--一列表单布局-->
        <div class="grid w-full grid-cols-4 gap-1 p-3">
          <FormItem label="账号" name="accountNumber">
            <Input
              v-model="formData.accountNumber"
              clearable
              placeholder="请输入内容"
            />
          </FormItem>
          <FormItem label="姓名" name="realName">
            <Input
              v-model="formData.realName"
              clearable
              placeholder="请输入姓名"
            />
          </FormItem>
          <FormItem label="部门" name="deptId">
            <TreeSelect
              v-model="formData.deptId"
              :data="deptTree"
              clearable
              placeholder="请选择部门"
              tree-default-expand-all
              style="width: 100%"
            />
          </FormItem>
          <FormItem label="来源" name="sourceSystem" v-show="!route.query.sourceSystem">
            <Select
              v-model="formData.sourceSystem"
              :options="sourceSystem"
              clearable
              placeholder="选择来源"
              style="width: 100%"
            />
          </FormItem>
          <FormItem label="手机号" name="phoneNumber" v-show="route.query.sourceSystem">
            <Input
              v-model="formData.phoneNumber"
              clearable
              placeholder="请输入手机号"
            />
          </FormItem>
        </div>
        <div class="grid w-full grid-cols-4 gap-1 p-3" >
          <FormItem label="手机号" name="phoneNumber" v-show="state.hideQuery && !route.query.sourceSystem">
            <Input
              v-model="formData.phoneNumber"
              clearable
              placeholder="请输入手机号"
            />
          </FormItem>
          <FormItem label="邮箱" name="email" v-show="state.hideQuery">
            <Input
              v-model="formData.email"
              clearable
              placeholder="请输入邮箱"
            />
          </FormItem>
          <FormItem label="人员状态" name="staffStatus" v-show="state.hideQuery">
            <Select
              v-model="formData.staffStatus"
              :options="staffStatus"
              clearable
              placeholder="选择人员状态"
              style="width: 100%"
            />
          </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <Icon name="refresh" />
            </template>
            重置
          </Button>

          <PutAway v-model="state.hideQuery" variant="text" />
        </div>
      </Form>
    </Card>
    <Card>
      <!-- 表格定义区域 -->
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :bordered="true"
        :columns="columns"
        :data="state.dataSource"
        :hover="true"
        :loading="state.loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="state.selectedRowKeys"
        :sort="state.sort"
        :stripe="true"
        cell-empty-content="-"
        lazy-load
        resizable
        row-key="id"
        table-layout="fixed"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
        v-bind="tableConfig"
      >
        <!--        表格顶部按钮区域-->
        <template #topContent>
          <div class="mb-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">账号列表</div>
              <div
                v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                class="text-[gray]"
              >
                已选择 {{ state.selectedRowKeys?.length || 0 }} 条数据
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <Button
                v-if="userStore.userInfo && userStore.userInfo?.roles?.includes('Admin') && 
                state.selectedRowKeys && state.selectedRowKeys.length > 0"
                theme="danger"
                @click="del"
              >
                <template #icon>
                  <DeleteIcon />
                </template>
                删除
              </Button>
              <Button
                v-if="userStore.userInfo && userStore.userInfo?.roles?.includes('Admin')"
                theme="primary"
                @click="edit"
              >
                <template #icon>
                  <AddCircleIcon />
                </template>
                新增
              </Button>
              <Button variant="text" @click="reload">
                <RefreshIcon />
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
            </div>
          </div>
        </template>
        <!--        空数据显示定义-->
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
            <Button
              class="w-[100%]"
              theme="primary"
              variant="text"
              @click="edit"
            >
              <template #icon>
                <Icon name="add-circle" />
              </template>
              点击创建新数据
            </Button>
          </div>
        </template>
        <template #dbType="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)">
              {{ slotProps.row.dbType || '-' }}
            </Link>
          </Space>
        </template>
        <!--        编辑按钮-->
        <template #op="slotProps">
          <Space size="small">
            <Link 
              :disabled="!!(slotProps.row.sourceSystem === '02')" 
              theme="primary" 
              @click="edit(slotProps.row)"
            >
              编辑
            </Link>
            <Link 
              :disabled="(slotProps.row.sourceSystem === '02' && slotProps.row.staffId !== null && slotProps.row.staffId !== '')" 
              theme="primary" 
              @click="editPwd(slotProps.row)"
            >
              修改密码
            </Link>
            <Popconfirm
              :disabled="!!(slotProps.row.sourceSystem === '01' || slotProps.row.sourceSystem === '02')"
              content="确定删除？"
              theme="warning"
              @confirm="remove(slotProps.row)"
            >
              <Link :disabled="!!(slotProps.row.sourceSystem === '01' || slotProps.row.sourceSystem === '02')" theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
