<script setup lang="tsx">
import { defineProps, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {InputNumber, MessagePlugin, Textarea, Upload} from 'tdesign-vue-next';
import { Input } from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import { save } from '../api.ts';
import {useAccessStore} from "@vben/stores";

const props = defineProps({
  outRef: {
    type: {
      reload: Function,
    },
    default: null,
  },
});
const form = ref();
const formData = ref({});
const FORM_RULES: FormProps['rules'] = {
};

const state = reactive({
  tagObj: {},
});

const reqRunner = {
  save: useRequest(save, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      props.outRef?.reload();
      initStatus();
      modalApi.close();
    },
  }),
};
const initStatus = () => {
  formData.value = {};
  titleImg.value = []
  teamImg1.value = []
  teamImg2.value = []
  researchImg.value = []
  productImg.value = []
};
const [Modal, modalApi] = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      initStatus();
    }
  },
  onConfirm: async () => {
    const vali = await form.value.validate();
    if (vali === true) {
      if(titleImg.value.length > 0){
        formData.value.titleImg = JSON.stringify(titleImg.value)
      }
      if(teamImg1.value.length > 0){
        formData.value.teamImg1 = JSON.stringify(teamImg1.value)
      }
      if(teamImg2.value.length > 0){
        formData.value.teamImg2 = JSON.stringify(teamImg2.value)
      }
      if(researchImg.value.length > 0){
        formData.value.researchImg = JSON.stringify(researchImg.value)
      }
      if(productImg.value.length > 0){
        formData.value.productImg = JSON.stringify(productImg.value)
      }
      reqRunner.save.run({ ...formData.value });
    }
  },
  onCancel: () => {
    initStatus();
    modalApi.close();
  },
});
const open = (data?: any) => {
  if (data) {
    formData.value = data;
    if(formData.value.titleImg){
      titleImg.value = JSON.parse(formData.value.titleImg)
    }
    if(formData.value.teamImg1){
      teamImg1.value = JSON.parse(formData.value.teamImg1)
    }
    if(formData.value.teamImg2){
      teamImg2.value = JSON.parse(formData.value.teamImg2)
    }
    if(formData.value.researchImg){
      researchImg.value = JSON.parse(formData.value.researchImg)
    }
    if(formData.value.productImg){
      productImg.value = JSON.parse(formData.value.productImg)
    }
  }
  modalApi.open();
};
defineExpose({
  open,
});


const accessStore = useAccessStore();
const titleImg = ref([])
const imgsuffix = ref('.png,.jpg,.svg')
const beforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!imgsuffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+imgsuffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+imgsuffix.value+"的文件")
    return false;
  }
  return true;
};
const titleImgUploadSuccess = (context: { fileList: any[] }) => {
  titleImg.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const teamImg1 = ref([])
const teamImg1UploadSuccess = (context: { fileList: any[] }) => {
  teamImg1.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const teamImg2 = ref([])
const teamImg2UploadSuccess = (context: { fileList: any[] }) => {
  teamImg2.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const researchImg = ref([])
const researchImgUploadSuccess = (context: { fileList: any[] }) => {
  researchImg.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const productImg = ref([])
const productImgUploadSuccess = (context: { fileList: any[] }) => {
  productImg.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};

</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES" class="w-full" label-align="top">
      <div class="grid w-full grid-cols-3 gap-1">
        <FormItem label="标题图片" name="titleImg">
          <Upload ref="img" v-model="titleImg" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="titleImgUploadSuccess"/>
        </FormItem>
        <FormItem label="团队图片1" name="teamImg1">
          <Upload ref="img" v-model="teamImg1" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="teamImg1UploadSuccess"/>
        </FormItem>
        <FormItem label="团队图片2" name="teamImg2">
          <Upload ref="img" v-model="teamImg2" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="teamImg2UploadSuccess"/>
        </FormItem>
        <FormItem label="研究方向图片" name="researchImg">
          <Upload ref="img" v-model="researchImg" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="researchImgUploadSuccess"/>
        </FormItem>
        <FormItem label="产品图片" name="productImg">
          <Upload ref="img" v-model="productImg" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="productImgUploadSuccess"/>
        </FormItem>
      </div>
      <div class="grid w-full grid-cols-1 gap-1">
        <FormItem label="团队介绍" name="teamIntroduction">
          <Textarea v-model="formData.teamIntroduction" clearable placeholder="请输入内容"  :maxlength="1000"
                    :autosize="{ minRows: 4, maxRows: 8 }"/>
        </FormItem>
        <FormItem label="科研方向介绍" name="researchIntroduction">
          <Textarea v-model="formData.researchIntroduction" clearable placeholder="请输入内容"  :maxlength="1000"
                    :autosize="{ minRows: 4, maxRows: 8 }"/>
        </FormItem>
        <FormItem label="产品介绍" name="productIntroduction">
           <Textarea v-model="formData.productIntroduction" clearable placeholder="请输入内容"  :maxlength="1000"
                     :autosize="{ minRows: 4, maxRows: 8 }"/>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
