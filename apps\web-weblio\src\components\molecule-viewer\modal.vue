<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { getRecommendedConfig } from './performance-config';
import {
  applySimple3DOptimization,
  cacheMolecule,
  getCachedMolecule,
} from './simple-performance';

interface Props {
  modelValue: boolean; // 控制弹窗显示
  smiles?: string;
  defaultViewMode?: '2d' | '3d';
  width?: number | string;
  height?: number | string;
}

const props = withDefaults(defineProps<Props>(), {
  smiles: '',
  defaultViewMode: '2d',
  width: '600px',
  height: '500px',
});

const emit = defineEmits<{
  error: [error: string];
  structureLoaded: [data: { smiles: string; viewMode: '2d' | '3d' }];
  'update:modelValue': [value: boolean];
}>();

// 全局变量声明
declare global {
  interface Window {
    initRDKitModule: () => Promise<any>;
    RDKitModule: any;
    $3Dmol: any;
  }
}

let RDKit: any = null;
let $3Dmol: any = null;

// 响应式数据
const viewMode = ref<'2d' | '3d'>(props.defaultViewMode);
const loading = ref(false);
const error = ref('');
const molData2d = ref<any>(null);
const molData3d = ref<any>(null);

// DOM引用
const modalRef = ref<HTMLElement>();
const viewer2d = ref<HTMLElement>();
const viewer3d = ref<HTMLElement>();

// 3DMol实例
let viewer3dInstance: any = null;

// 获取性能配置
const performanceConfig = getRecommendedConfig();

// 加载RDKit
const loadRDKit = async () => {
  if (!RDKit) {
    try {
      if (!window.initRDKitModule) {
        await new Promise<void>((resolve, reject) => {
          const script = document.createElement('script');
          script.src = '/rdkit/RDKit_minimal.js';
          script.addEventListener('load', () => resolve());
          script.addEventListener('error', () =>
            reject(new Error('无法加载RDKit脚本文件')),
          );
          document.head.append(script);
        });
      }
      RDKit = await window.initRDKitModule();
    } catch (error) {
      console.error('Failed to load RDKit:', error);
      throw new Error('无法加载RDKit库');
    }
  }
  return RDKit;
};

// 加载3DMol库
const load3DMol = async () => {
  if (!$3Dmol) {
    try {
      const molModule = await import('3dmol');
      $3Dmol = molModule.default || molModule;
    } catch (error) {
      console.error('Failed to load 3DMol:', error);
      throw new Error('无法加载3DMol库');
    }
  }
  return $3Dmol;
};

// 生成2D结构
const generate2DStructure = async () => {
  try {
    const rdkit = await loadRDKit();
    const mol = rdkit.get_mol(props.smiles);

    if (!mol || mol.is_valid() === 0) {
      throw new Error('无效的SMILES字符串');
    }

    const svg = mol.get_svg(500, 400);
    mol.delete();

    if (viewer2d.value) {
      viewer2d.value.innerHTML = svg;
    }

    molData2d.value = {
      smiles: props.smiles,
      svg,
    };
  } catch (error) {
    console.error('RDKit 2D generation error:', error);
    throw error;
  }
};

// 生成3D结构
const generate3DStructure = async () => {
  try {
    const ThreeDMol = await load3DMol();
    const rdkit = await loadRDKit();

    // 检查缓存
    const cached = getCachedMolecule(props.smiles);
    let molBlock: string;

    if (cached) {
      molBlock = cached.molBlock;
    } else {
      const mol = rdkit.get_mol(props.smiles);
      if (!mol || mol.is_valid() === 0) {
        throw new Error('无效的SMILES字符串');
      }

      molBlock = mol.get_molblock();
      mol.delete();

      // 缓存结果
      cacheMolecule(props.smiles, { molBlock, timestamp: Date.now() });
    }

    if (viewer3dInstance) {
      viewer3dInstance.clear();
    }

    if (viewer3d.value) {
      viewer3dInstance = ThreeDMol.createViewer(viewer3d.value, {
        defaultcolors: ThreeDMol.elementColors.CPK,
        backgroundColor: 'white',
        // 使用优化的WebGL配置
        antialias: performanceConfig.antialias,
        alpha: performanceConfig.alpha,
        premultipliedAlpha: performanceConfig.premultipliedAlpha,
        preserveDrawingBuffer: performanceConfig.preserveDrawingBuffer,
        powerPreference: performanceConfig.powerPreference,
        failIfMajorPerformanceCaveat:
          performanceConfig.failIfMajorPerformanceCaveat,
      });

      viewer3dInstance.addModel(molBlock, 'mol');

      // 应用简化的性能优化
      applySimple3DOptimization(viewer3dInstance, molBlock);

      // 使用配置的动画时间
      viewer3dInstance.zoomTo({}, performanceConfig.zoomDuration, 1);

      // 已经在applySimple3DOptimization中应用了性能配置

      viewer3dInstance.render();
    }

    molData3d.value = {
      smiles: props.smiles,
      molBlock,
      viewer: viewer3dInstance,
    };
  } catch (error) {
    console.error('3DMol generation error:', error);
    throw error;
  }
};

// 生成结构
const generateStructure = async () => {
  if (!props.smiles.trim()) return;

  loading.value = true;
  error.value = '';

  try {
    await (viewMode.value === '2d'
      ? generate2DStructure()
      : generate3DStructure());

    emit('structureLoaded', {
      smiles: props.smiles,
      viewMode: viewMode.value,
    });
  } catch (error_) {
    const errorMsg =
      error_ instanceof Error ? error_.message : '生成分子结构失败';
    error.value = errorMsg;
    emit('error', errorMsg);
  } finally {
    loading.value = false;
  }
};

// 切换视图模式
const setViewMode = async (mode: '2d' | '3d') => {
  viewMode.value = mode;

  if (mode === '2d' && !molData2d.value) {
    await generateStructure();
  } else if (mode === '3d' && !molData3d.value) {
    await generateStructure();
  }
};

// 关闭弹窗
const closeModal = () => {
  emit('update:modelValue', false);
};

// ESC键关闭
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeModal();
  }
};

// 点击遮罩关闭
const handleMaskClick = (event: MouseEvent) => {
  if (event.target === modalRef.value) {
    closeModal();
  }
};

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.smiles) {
      nextTick(() => {
        generateStructure();
      });
    }
  },
);

// 监听SMILES变化
watch(
  () => props.smiles,
  () => {
    molData2d.value = null;
    molData3d.value = null;
    if (props.modelValue && props.smiles) {
      generateStructure();
    }
  },
);

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);

  if (viewer3dInstance) {
    try {
      viewer3dInstance.clear();
    } catch (error) {
      console.warn('Error during cleanup:', error);
    }
    viewer3dInstance = null;
  }
});
</script>

<template>
  <teleport to="body">
    <div
      v-if="modelValue"
      ref="modalRef"
      class="molecule-modal-overlay"
      @click="handleMaskClick"
    >
      <div class="molecule-modal" @click.stop>
        <!-- 头部 -->
        <div class="modal-header">
          <h3>分子结构查看器</h3>
          <button class="close-btn" @click="closeModal">
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path
                d="M12 4L4 12M4 4l8 8"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>

        <!-- 控制栏 -->
        <div class="modal-controls">
          <div class="view-mode-toggle">
            <button
              class="toggle-btn"
              :class="{ active: viewMode === '2d' }"
              @click="setViewMode('2d')"
            >
              2D
            </button>
            <button
              class="toggle-btn"
              :class="{ active: viewMode === '3d' }"
              @click="setViewMode('3d')"
            >
              3D
            </button>
          </div>

          <div class="smiles-info" v-if="smiles">
            <span class="smiles-label">SMILES:</span>
            <code class="smiles-text">{{ smiles }}</code>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="modal-content">
          <!-- 错误提示 -->
          <div v-if="error" class="error-message">
            {{ error }}
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-overlay">
            <div class="spinner"></div>
            <span>生成中...</span>
          </div>

          <!-- 分子显示区域 -->
          <div class="viewer-container" :class="{ loading }">
            <!-- 2D显示 -->
            <div v-show="viewMode === '2d'" ref="viewer2d" class="viewer-2d">
              <div v-if="!molData2d && !loading" class="placeholder">
                暂无数据
              </div>
            </div>

            <!-- 3D显示 -->
            <div v-show="viewMode === '3d'" ref="viewer3d" class="viewer-3d">
              <div v-if="!molData3d && !loading" class="placeholder">
                暂无数据
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<style scoped lang="scss">
.molecule-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1601;
  padding: 20px;
}

.molecule-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  width: v-bind(width);
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }
}

.close-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;

  &:hover {
    background: #f0f0f0;
    color: #333;
  }
}

.modal-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #e0e0e0;
  gap: 16px;
}

.view-mode-toggle {
  display: flex;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.toggle-btn {
  background: white;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  min-width: 60px;

  &:hover {
    background: #f0f7ff;
  }

  &.active {
    background: #1890ff;
    color: white;
  }

  &:not(:last-child) {
    border-right: 1px solid #d9d9d9;
  }
}

.smiles-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.smiles-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.smiles-text {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #e83e8c;
  word-break: break-all;
  flex: 1;
  min-width: 0;
}

.modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.error-message {
  padding: 16px 20px;
  background: #fff5f5;
  color: #d73a49;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  z-index: 10;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.viewer-container {
  height: v-bind(height);
  position: relative;

  &.loading {
    opacity: 0.5;
  }
}

.viewer-2d,
.viewer-3d {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.placeholder {
  color: #999;
  font-size: 16px;
  text-align: center;
}

.viewer-2d :deep(svg) {
  max-width: 100%;
  max-height: 100%;
}
</style>
