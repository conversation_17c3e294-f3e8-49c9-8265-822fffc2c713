<script setup lang="ts">
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import {
  AddCircleIcon,
  DeleteIcon,
  RefreshIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Link,
  type PageInfo,
  Popconfirm,
  EnhancedTable,
  Space,
  Table, TreeSelect, MessagePlugin,
} from 'tdesign-vue-next';

import { Input } from 'tdesign-vue-next';
import { DatePicker } from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import { Select } from 'tdesign-vue-next';
import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import PutAway from "#/components/put-away/index.vue";
import { deleteBatch, listByTree, searchlistByTree } from '../api';
import {getDictItems } from "#/api";

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});

const levels = ref([]);
const treeSelectData: any = ref([]);

/**
 * table 排序字段
 */

const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
 {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
  },

{ colKey: 'classifyName', ellipsis: true, sorter: false, title: '类型名称' },
{ colKey: 'createTime', ellipsis: true, sorter: false, title: '创建时间' },
{ colKey: 'createdBy', ellipsis: true, sorter: false, title: '创建人' },
{ colKey: 'secretLevelText', ellipsis: true, sorter: false, title: '敏感级别' },
{ colKey: 'sortOrder', ellipsis: true, sorter: false, title: '排序权重' },
{ colKey: 'isDisplayText', ellipsis: true, sorter: false, title: '是否显示' },
{ colKey: 'isHomeDisplayText', ellipsis: true, sorter: false, title: '首页是否显示' },
{ colKey: 'subjectCode', ellipsis: true, sorter: false, title: '学科代码' },
{colKey: 'op', width: 100, title: '操作', align: 'center',  fixed: 'right'},
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByTree: useRequest(listByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      state.dataSource = res;
      treeSelectData.value = res;
    },
  }),
  searchlistByTree: useRequest(searchlistByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      state.dataSource = res;
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      state.selectedRowKeys=[];
      reload();
      initStatus();
      if(res.length > 0){
        MessagePlugin.error('分类: '+res.join(',')+' 仍有数据或数据集绑定, 请删除绑定数据后重试！');
      }
    },
  }),
};

const initStatus = () => {
  /**
   * 默认选中全部
   */
  state.selectedRowKeys = [];
  /**
   * 默认不显示删除按钮
   */
  state.delDailogShow = false;
  /**
   * 默认不显示查询
   */
  state.hideQuery = false;
  /**
   * 默认不显示加载
   */
  state.loading = false;
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByTree;
  state.loading = loading;
  run(formData.value);
};

const searchMethod = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.searchlistByTree;
  state.loading = loading;
  run(formData.value);
};
/**
 * 新建按钮相应
 */
const edit = (record?: any) => {
  props.editFormRef?.open(record ? { ...record } : {});
};
/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};
/**
 * 单条删除
 */
const remove = (record: any) => {
  reqRunner.deleteBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  searchMethod();
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  formData.value={}
  searchForm.value.reset();
  reload();
};
/**
 * table 行点击响应
 * @param record
 */
const handleRowClick = (record: any) => {};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.defaultCurrent = pageInfo.current;
  pagination.value.defaultPageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowKeys = value;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};

onMounted(async () => {
  reload();
  levels.value = await getDictItems('DATA_LEVEL');
  levels.value.forEach((op) => {
    if(op.value == 0 || op.value == 1){
      op["disabled"] = true;
    }
  })
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Space :size="8" class="tiny-tdesign-style-patch w-full" direction="vertical">
    <!--    查询表单定义区域-->
    <Card v-if="isSearchForm">
      <Form ref="searchForm" :data="formData" class="w-full" @reset="resetSearch" @submit="searchFormSubmit">
        <!--一列表单布局-->
        <div class="grid w-full grid-cols-3 gap-1">
            <FormItem label="敏感级别" name="secretLevel">
              <Select v-model="formData.secretLevel" clearable placeholder="请输入内容" :options="levels" />
            </FormItem>
            <FormItem  label="类型名称" name="classifyName">
               <Input v-model="formData.classifyName" clearable placeholder="请输入内容" />
            </FormItem>
            <FormItem  label="上级类型" name="parentCode">
              <TreeSelect v-model="formData.parentCode" :data="treeSelectData" placeholder="请选择上级分类"
                :keys="{
                    value: 'classifyCode',
                    label: 'classifyName',
                    children: 'children'
                  }"
              />
            </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置
          </Button>
        </div>
      </Form>
    </Card>
    <Card>
      <!-- 表格定义区域 -->
      <EnhancedTable
        ref="tableRef"
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :bordered="true"
        :columns="columns"
        :data="state.dataSource"
        :hover="true"
        :loading="state.loading"
        :pagination="false"
        :selected-row-keys="state.selectedRowKeys"
        :sort="state.sort"
        :stripe="true"
        cell-empty-content="-"
        lazy-load
        resizable
        row-key="id"
        table-layout="fixed"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
        v-bind="tableConfig"
        :tree="{
               childrenKey: 'children',
               treeNodeColumnIndex: 1,
               indent: 32,
               checkStrictly: true,
        }"
      >
        <!--        表格顶部按钮区域-->
        <template #topContent>
          <div class="mb-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">分级分类列表</div>
              <div v-if="state.selectedRowKeys?.length > 0" class="text-blue-600/80">
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <Button v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0" theme="danger" @click="del">
                <template #icon>
                  <DeleteIcon/>
                </template>
                删除
              </Button>
              <Button theme="primary" @click="edit">
                <template #icon>
                  <AddCircleIcon/>
                </template>
                新增
              </Button>
              <Button variant="text" @click="reload">
                <RefreshIcon/>
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
            </div>
          </div>
        </template>
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
            <Button class="w-[100%]" theme="primary" variant="text" @click="edit">
              <template #icon>
                <AddCircleIcon/>
              </template>
              点击创建新数据
            </Button>
          </div>
        </template>
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)">编辑</Link>
            <Popconfirm content="确定删除？" theme="warning" @confirm="remove(slotProps.row)">
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </EnhancedTable>
    </Card>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
