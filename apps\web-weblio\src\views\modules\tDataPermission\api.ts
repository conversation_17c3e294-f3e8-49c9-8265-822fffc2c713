// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataPermission/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataPermission/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tDataPermission/deleteBatch/${data}`);
}
export async function getOne(data: any, data1: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataPermission/getOne/${data}/${data1}`);
}
export async function getOneShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tDataPermission/getOneShare`, data);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataPermission/getByIds/${data}`);
}
export async function getAllPCodes(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataPermission/getAllPCodes/${data}`);
}
export async function dataAuthApply(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataPermission/dataAuthApply', data);
}
