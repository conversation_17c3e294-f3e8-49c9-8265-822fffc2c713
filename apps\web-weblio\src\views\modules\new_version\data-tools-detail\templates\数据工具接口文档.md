# 数据工具接口文档

## 概述

本文档描述了数据工具模块提供的所有API接口，包括数据抽取、数据对齐、数据整编等功能。

**基础路径**: `/dataTools`

**数据工具服务地址**: `${tiny.dataToolsHost}`

---

## 1. 数据抽取接口

### 1.1 数据抽取

**接口地址**: `POST /dataTools/extract`

**接口描述**: 数据抽取接口，支持文件上传和数据参数

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| files | MultipartFile[] | 否 | 上传的文件数组 |
| data | JSONObject | 是 | 数据参数对象 |

**支持的文件类型**: `jpeg`, `png`, `jpg`, `svg`, `txt`, `pdf`, `doc`, `docx`, `md`, `json`, `xlsx`, `xls`

**请求示例**:
```bash
curl -X POST "/rgdc-sys/dataTools/extract" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@file1.pdf" \
  -F "files=@file2.xlsx" \
  -F "data={\"param1\":\"value1\",\"param2\":\"value2\"}"
```

**响应格式**: JSON

---

## 2. 数据对齐接口

### 2.1 数据对齐

**接口地址**: `POST /dataTools/align`

**接口描述**: 数据对齐接口，支持文本和图像JSON文件的对齐处理

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| text_json | MultipartFile | 是 | 文本JSON文件 |
| image_json | MultipartFile | 是 | 图像JSON文件 |

**支持的文件类型**: `jpeg`, `png`, `jpg`, `svg`, `txt`, `pdf`, `doc`, `docx`, `md`, `json`, `xlsx`, `xls`

**请求示例**:
```bash
curl -X POST "/rgdc-sys/dataTools/align" \
  -H "Content-Type: multipart/form-data" \
  -F "text_json=@text_data.json" \
  -F "image_json=@image_data.json"
```

**响应格式**: JSON

---

## 3. 文件下载接口

### 3.1 JSON文件下载

**接口地址**: `GET /dataTools/download/{download_url}`

**接口描述**: JSON文件下载接口

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| download_url | String | 是 | 下载URL路径 |

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/download/example_file.json" \
  -H "Accept: application/json"
```

**响应**: 文件流下载

---

## 4. 图像查看接口

### 4.1 图像查看

**接口地址**: `GET /dataTools/view`

**接口描述**: 图像查看接口，返回Base64编码的图像数据

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| view_url | String | 是 | 图像URL路径 |

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/view?view_url=image_path.jpg"
```

**响应格式**: Base64编码的字符串

---

## 5. 数据整编接口

### 5.1 整编字典映射

**接口地址**: `GET /dataTools/get_field_dict`

**接口描述**: 获取整编字典映射信息

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/get_field_dict"
```

**响应格式**: JSON

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "AffiliationAndContact": {
      "field_list": [
        {
          "key": "Addresses",
          "label": "地址"
        }
      ],
      "label": "机构与联系方式"
    }
  },
  "message": "success"
}
```

### 5.2 整编接口

**接口地址**: `POST /dataTools/query_by_dois`

**接口描述**: 通过DOI进行数据整编

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dois_file | MultipartFile | 否 | DOI文件 |
| data | JSONObject | 是 | 整编参数 |

**请求示例**:
```bash
curl -X POST "/rgdc-sys/dataTools/query_by_dois" \
  -H "Content-Type: multipart/form-data" \
  -F "dois_file=@doi_list.xlsx" \
  -F "data={\"task_name\":\"测试任务\",\"key_list\":[\"Authors\",\"Article_Title\",\"DOI\"]}"
```

**响应格式**: JSON

### 5.3 整编任务列表

**接口地址**: `GET /dataTools/tasks_list`

**接口描述**: 获取整编任务列表

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 是 | 页码 |
| page_size | Integer | 是 | 每页显示数量 |
| task_name | String | 否 | 任务名称（支持筛选） |

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/tasks_list?page=1&page_size=10&task_name=测试任务"
```

**响应格式**: JSON

### 5.4 整编任务详情

**接口地址**: `GET /dataTools/tasks_detail`

**接口描述**: 获取整编任务详情

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Integer | 否 | 任务ID |

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/tasks_detail?id=1"
```

**响应格式**: JSON

### 5.5 整编Excel模板下载

**接口地址**: `GET /dataTools/download_template`

**接口描述**: 下载整编Excel表格模板

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/download_template"
```

**响应格式**: JSON

### 5.6 整编任务文件下载（一键下载）

**接口地址**: `GET /dataTools/download_excel`

**接口描述**: 下载整编任务的Excel文件

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| task_id | Integer | 否 | 任务ID |

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/download_excel?task_id=1"
```

**响应**: Excel文件流下载

### 5.7 整编任务文件-单条下载

**接口地址**: `GET /dataTools/download_single`

**接口描述**: 下载整编任务的单个数据项文件

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| task_id | String | 否 | 任务ID |
| id | String | 否 | 数据ID |
| file_type | String | 否 | 文件类型（json或excel） |

**请求示例**:
```bash
curl -X GET "/rgdc-sys/dataTools/download_single?task_id=1&id=123&file_type=json"
```

**响应**: 文件流下载

---

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 00000001 | 请上传规定范围类型的文件 |

**支持的文件类型**: `jpeg`, `png`, `jpg`, `svg`, `txt`, `pdf`, `doc`, `docx`, `md`, `json`, `xlsx`, `xls`

---

## 7. 通用说明

### 7.1 请求头

所有接口都支持以下请求头：

- `Content-Type`: 根据接口类型设置
- `Accept`: 根据响应类型设置

### 7.2 日志记录

所有接口调用都会自动记录以下信息：

- API名称
- 请求参数详情
- 客户端IP地址
- 调用来源

### 7.3 响应格式

成功响应格式：
```json
{
  "code": 0,
  "data": {},
  "message": "success"
}
```

错误响应格式：
```json
{
  "code": 1,
  "message": "错误信息"
}
```

### 7.4 文件上传限制

- 支持的文件类型：`jpeg`, `png`, `jpg`, `svg`, `txt`, `pdf`, `doc`, `docx`, `md`, `json`, `xlsx`, `xls`
- 文件大小限制：根据服务器配置
- 文件编码：UTF-8

---

## 8. 接口调用流程

### 8.1 数据整编流程

1. 调用 `GET /dataTools/get_field_dict` 获取字段字典
2. 调用 `GET /dataTools/download_template` 下载Excel模板
3. 准备DOI数据（单个DOI或Excel文件）
4. 调用 `POST /dataTools/query_by_dois` 提交整编任务
5. 调用 `GET /dataTools/tasks_list` 查看任务列表
6. 调用 `GET /dataTools/tasks_detail` 查看任务详情
7. 调用 `GET /dataTools/download_excel` 下载整编结果

### 8.2 数据抽取流程

1. 准备要抽取的文件
2. 准备抽取参数
3. 调用 `POST /dataTools/extract` 进行数据抽取
4. 调用 `GET /dataTools/download/{download_url}` 下载结果

### 8.3 数据对齐流程

1. 准备文本JSON文件和图像JSON文件
2. 调用 `POST /dataTools/align` 进行数据对齐
3. 获取对齐结果

---

## 9. 注意事项

1. 所有文件上传接口都会进行文件类型验证
2. 下载接口会自动设置正确的Content-Type和文件名
3. 图像查看接口返回Base64编码，前端需要解码显示
4. 整编接口支持批量处理，建议合理控制数据量
5. 所有接口都有完整的日志记录，便于问题排查

---

## 10. 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0 | 2024-01-01 | 初始版本，包含基础数据工具接口 |
| 1.1 | 2024-01-15 | 新增数据整编相关接口 |
| 1.2 | 2024-01-30 | 完善接口文档和错误处理 | 