<script setup lang="tsx">
import { Bubble, Sender } from 'ant-design-x-vue';
import { UserIcon } from 'tdesign-icons-vue-next';
import { onMounted, onUnmounted, ref } from 'vue';

import { useChat } from './useChat';

const isMobile = ref(false);
const ragKey = ref(import.meta.env.VITE_CHAT_RAG_KEY);
const isOnline = ref(false);
const knowledges = ref({ dataset_ids: [], documents_ids: [] });
const userAvatar = { icon: <UserIcon /> };
const aiAvatar = { icon: '🤖' };

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});
onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

const {
  msgs,
  senderLoading,
  inputValue,
  chatListRef,
  inputEnter,
  onStop,
  renderMarkdown,
} = useChat({
  userAvatar,
  aiAvatar,
  ragKey: ragKey.value,
  knowledges: knowledges.value,
  isOnline: isOnline.value,
});
</script>

<template>
  <div class="ai-chat">
    <div class="ai-chat-list" ref="chatListRef">
      <Bubble.List
        :items="
          msgs.map((m) => {
            if (m.role === 'user') {
              return { ...m, avatar: userAvatar, placement: 'end' as const };
            } else if (m.role === 'assistant') {
              return {
                ...m,
                avatar: aiAvatar,
                content: (m.reasoningText || '') + (m.content || ''),
                messageRender: renderMarkdown,
                placement: 'start' as const,
              };
            } else {
              return m;
            }
          })
        "
      />
    </div>
    <div class="ai-chat-footer">
      <Sender
        v-model:value="inputValue"
        :loading="senderLoading"
        @submit="inputEnter"
        @cancel="onStop"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.ai-chat {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #f7f8fa;
}
.ai-chat-list {
  flex: 1 1 0;
  overflow-y: auto;
  padding: 24px 16px 12px 16px;
  background: #fff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.3s;
}
.ai-chat-footer {
  background: #fff;
  border-top: 1px solid #e5e6eb;
  padding: 12px 16px;
  box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.03);
  border-radius: 0 0 8px 8px;
}
:deep(.ant-bubble-avatar) {
  width: 36px !important;
  height: 36px !important;
  min-width: 36px !important;
  min-height: 36px !important;
  max-width: 36px !important;
  max-height: 36px !important;
  border-radius: 50% !important;
  background: #0052d9;
  color: #fff;
  font-weight: bold;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  padding: 0;
  overflow: hidden;
  box-sizing: border-box;
}
:deep(.ant-bubble-item) {
  margin-bottom: 8px;
}
:deep(.ant-bubble-item[reasoning]) {
  background: #f0f5ff;
  color: #0052d9;
  margin-bottom: 4px;
}
</style>
