<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Form,
  FormItem,
  type FormProps,
  Input,
  MessagePlugin,
  Textarea,
} from 'tdesign-vue-next';

import { start } from '../../api';

const formData: any = ref({});
const form = ref();
const FORM_RULES: FormProps['rules'] = {
  businessKey: [
    {
      required: true,
      message: '必填',
    },
  ],
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    const vali = await form.value.validate();

    if (vali === true) {
      // 验证通过提交请求 并且关闭窗口
      try {
        JSON.parse(formData.value.args);
      } catch {
        MessagePlugin.error('参数内请输入JSON格式数据');
        return;
      }
      await start({
        ...formData.value,
        processId: formData.value?.id,
        args: formData.value?.args ? JSON.parse(formData.value.args) : {},
      });
      MessagePlugin.success('发布流程成功');
      modalApi.getData().refresh();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    formData.value = isOpen
      ? {
          ...modalApi.getData<Record<string, any>>()?.record,
          templateIds:
            modalApi
              .getData<Record<string, any>>()
              ?.record?.templateIds?.split(',') || [],
        }
      : {};
  },
  title: '发布',
});

onMounted(async () => {
  // status.value = await getDictItems('BASE_STATUS');
  // promptOptions.value = await promptAllSelect();
  // templateOptions.value = await templateAllSelect();
});
</script>
<template>
  <Modal class="w-[30%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES">
      <FormItem label="流程名称" name="processName">
        <Input
          v-model="formData.processName"
          :disabled="true"
          clearable
          placeholder="请输入流程名称"
        />
      </FormItem>

      <FormItem label="流程ID" name="id">
        <Input
          v-model="formData.id"
          :disabled="true"
          clearable
          placeholder="请输入流程ID"
        />
      </FormItem>

      <FormItem label="流程Key" name="processKey">
        <Input
          v-model="formData.processKey"
          :disabled="true"
          clearable
          placeholder="请输入流程Key"
        />
      </FormItem>

      <FormItem label="流程版本" name="processVersion">
        <Input
          v-model="formData.processVersion"
          :disabled="true"
          clearable
          placeholder="请输入流程版本"
        />
      </FormItem>

      <FormItem label="业务KEY" name="businessKey">
        <Input
          v-model="formData.businessKey"
          clearable
          placeholder="请输入业务KEY"
        />
      </FormItem>

      <FormItem label="参数" name="args">
        <Textarea
          v-model="formData.args"
          class="form-item-content"
          placeholder="请输入JSON格式数据"
        />
      </FormItem>
    </Form>
  </Modal>
</template>
<style lang="less" scoped>
.form-item-content {
  width: 100%;
}
</style>
