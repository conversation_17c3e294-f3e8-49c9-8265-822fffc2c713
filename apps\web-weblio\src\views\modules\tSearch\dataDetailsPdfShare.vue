<script setup lang="tsx">
import {Page} from '@vben/common-ui';
import {Button, Card, Form, FormItem, Tag} from 'tdesign-vue-next';
import {defineProps, onMounted, ref} from 'vue';
import {useRoute} from "vue-router";
import {DownloadIcon} from "tdesign-icons-vue-next";
import {useRequest} from "vue-hooks-plus";
import {baseDownloadFile, getDictItems} from "#/api";
import {getMdByFileCodeShare, getOneByFileCodeShare, getOneShare} from './api';
import WebLioHeader from "#/views/modules/webLio/WebLioHeader.vue";
import WebLioFooter from "#/views/modules/webLio/WebLioFooter.vue";
import {mavonEditor} from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import {getCategorysShare} from "#/views/modules/tDataBase/api.ts";
import {getOneShare as getOneByUnstructuredCodeShare} from "#/views/modules/tDataUnstructured/api.ts";
import {getLabelListShare} from "#/views/modules/tData/api.ts";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },

});

const route = useRoute()
const pdfUrl = ref()
const prouteCode = ref();
const type = ref();
const markdown = ref()

const labels = ref();
const classes = ref();
const category = ref();
const labelses = ref();
const classess = ref();
const categorys = ref();
const categoryList = ref([]);
const labelsTags = ref([]);
const datasetName = ref('');
const classesName = ref('');
const categoryName = ref('');

onMounted(async () => {
  //数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  //标签库
  labelses.value = await getLabelListShare();
  if (route.query.p) {
    prouteCode.value = route.query.p;
    const datas = await getOneShare({rsa_code: route.query.p});
    classes.value = datas.classCode ? Number(datas.classCode) : null;
    categorys.value = await getCategorysShare(classes.value);
    category.value = JSON.parse(datas.categoryCode)
    category.value = category.value.map(item => Number(item))
    labels.value = JSON.parse(datas.labelCode)
    categoryList.value = [];
    await getCategoryList(categorys.value)
    let labelsTag;
    labelsTags.value = [];
    datasetName.value = ''
    classesName.value = ''
    categoryName.value = ''
    if (labels.value) {
      for (const item of labels.value) {
        for (const it of labelses.value) {
          if (item == it.value) {
            labelsTag = {};
            labelsTag["key"] = it.value;
            labelsTag["content"] = it.label;
            labelsTag["color"] = it.color;
            labelsTags.value.push(labelsTag);
          }
        }
      }
    }
    const unstructured = await getOneByUnstructuredCodeShare(datas.operationCode);
    datasetName.value = unstructured.dataType;
    for (const item of category.value) {
      for (const it of categoryList.value) {
        if (item == it.value) {
          categoryName.value += it.label
        }
      }
    }
    classesName.value = classess.value[classes.value].label
    if (categoryName.value) {
      categoryName.value = categoryName.value.slice(0, categoryName.value.length - 1);
    }
    type.value = route.query.type;
    console.log(route.query.p);
    if (type.value == 'md') {
      markdown.value = await getMdByFileCodeShare({
        rsa_code: route.query.p,
        rsa_accountNumber: route.query.q
      });
    } else {
      const file = await getOneByFileCodeShare({
        rsa_code: route.query.p,
        rsa_accountNumber: route.query.q
      });
      pdfUrl.value = file.filePath
    }
  }
});

const getCategoryList = async (list) => {
  for (const item of list) {
    if (item.children && item.children.length > 0) {
      await getCategoryList(item.children)
    }
    categoryList.value.push(item)
  }
};

const reqRunner = {
  downPdf: useRequest(baseDownloadFile, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
};
const downDetailsPdf = async () => {
  const { run } = reqRunner.downPdf;
  run(`/tSearch/downDetailsPdfShare`,{
    operationCode: route.query.p
  });
};
</script>

<template>
  <div class="web-lio-page">
    <WebLioHeader />
    <div class="web-lio-content">
      <Page title="数据详情">
        <template #extra>
          <Button variant="text" shape="square" :style="{ 'margin-left': '8px' }" @click="downDetailsPdf">
            <download-icon />
          </Button>
        </template>
        <div class="border-1 flex h-full w-full flex-col overflow-auto rounded-lg pl-[6%] pr-[6%]">
          <Card class="top-card">
            <Form ref="searchForm" class="w-[95%]">
              <div class="mt-5 grid w-full grid-cols-3 gap-1">
                <FormItem label="分级 :" name="dataType">
                  {{ classesName }}
                </FormItem>
                <FormItem label="分类 :" name="code">
                  {{ categoryName }}
                </FormItem>
                <FormItem label="归属数据集 :" name="dataset">
                  {{ datasetName }}
                </FormItem>
                <FormItem label="标签 :" name="labels">
                  <Tag
                    v-for="item in labelsTags"
                    :key="item.key"
                    :color="item.color"
                    variant="light-outline"
                    style="margin-right: 10px"
                  >
                    {{ item.content }}
                  </Tag>
                </FormItem>
              </div>
            </Form>
          </Card>
          <Card style="background-color: #0071bc1a; margin-top: 10px;">
            <div v-if="type == 'md'">
              <div class="editor-container">
                <mavon-editor v-model="markdown" :subfield="false" :toolbarsFlag="false" :editable="false"
                              defaultOpen="preview" style="min-height: 200px; border: none"/>
              </div>
            </div>
            <div v-else>
              <iframe :src=pdfUrl width="100%" height="650px" title="PDF文档"/>
            </div>
          </Card>
        </div>
      </Page>
    </div>
    <WebLioFooter />
  </div>
</template>

<style scoped>
.top-card {
  background-image: url('/static/images/top-card.jpeg');
  background-size: cover;
}
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #f5f7fa;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
.editor-container {
  position: relative;
  z-index: 1; /* 创建新的层叠上下文 */
}
</style>
