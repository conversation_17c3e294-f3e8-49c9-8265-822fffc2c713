import { requestClient } from '#/api/request';

export async function executeTask(data: any) {
  return requestClient.post<any>('/rgdc-workflow/task/executeTask', data);
}

export async function claimTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/claimTask`, data);
}

export async function rejectTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/rejectTask`, data);
}
// 获取权限审批详情
export async function getPermissionDetail(id: string) {
  return requestClient.get<any>(`/rgdc-workflow/permission/detail/${id}`);
}

// 获取权限审批列表
export async function getPermissionList(data: any) {
  return requestClient.post<any>('/rgdc-workflow/permission/listByPage', data);
}
