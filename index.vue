
const fetchDetailData = async () => {
};

// 新增导出数据函数
const exportData = () => {
  // 示例数据，实际应用中替换为API请求获取的数据
  const dataToExport = [
    { month: '1月', lastYear: 100, thisYear: 150 },
    { month: '2月', lastYear: 140, thisYear: 100 },
    { month: '3月', lastYear: 230, thisYear: 200 },
    { month: '4月', lastYear: 100, thisYear: 140 },
    { month: '5月', lastYear: 130, thisYear: 100 },
  ];

  // 将数据转换为CSV格式
  const csvContent = "data:text/csv;charset=utf-8," + dataToExport.map(e => Object.values(e).join(",")).join("\n");

  // 创建一个隐藏的<a>元素用于下载
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement("a");
  link.setAttribute("href", encodedUri);
  link.setAttribute("download", "production_data.csv");
  document.body.appendChild(link); // Required for FF

  link.click(); // 在Firefox中必须先添加到body中才能点击
  document.body.removeChild(link);
};


</script>

<template>
  <DetailHeader />
  <DetailLayout :data="data">
    <!-- 数据详情部分 -->
    <div v-for="(item, index) in detailBodyData" :key="index">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <select v-model="selectedOption.value">
          {(() => {
            let classificationArray = [];
            if (typeof item.classification === 'string') {
              try {
                classificationArray = JSON.parse(item.classification.replace(/'/g, '"'));
              } catch (e) {
                classificationArray = item.classification.split(',').map(s => s.trim());
              }
            } else if (Array.isArray(item.classification)) {
              classificationArray = item.classification;
            }

            return classificationArray.map((label) => (
              <option key={label} value={label}>
                {label}
              </option>
            ));
          })()}
        </select>
        <button @click="exportData">源数据下载</button>
      </div>
      <LineECharts :seriesData="[
          {
            name: '去年产量',
            type: 'line',
            data: [100, 140, 230, 100, 130]
          },
          {
            name: '今年产量',
            type: 'line',
            data: [150, 100, 200, 140, 100]
          }
        ]"
        :xAxisData="['1月', '2月', '3月', '4月', '5月']"
        title="" 
      />
    </div>
  </DetailLayout>
</template>

<style scoped lang="scss">
/* 样式保持不变 */
</style>