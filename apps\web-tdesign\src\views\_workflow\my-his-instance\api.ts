import { requestClient } from '#/api/request';

export async function listByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-workflow/queryHisInstance/listByPage2', data);
}

export async function revoke(data: any) {
  return requestClient.post<any>('/rgdc-workflow/instance/revokeInstance', data);
}

export async function undeploy(id: any) {
  return requestClient.delete<any>(`/rgdc-workflow/process/undeploy/${id}`);
}

export async function cascadeRemove(id: any) {
  return requestClient.delete<any>(`/rgdc-workflow/process/cascadeRemove/${id}`);
}
