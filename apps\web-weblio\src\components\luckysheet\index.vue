<script setup lang="ts">
import { defineEmits, defineProps, onMounted, ref, nextTick } from 'vue';
import Spreadsheet from 'x-data-spreadsheet';
const spreadsheetInstance = ref<any>(null); // 用于保存 Spreadsheet 实例
const showSpreadsheet = ref(true);
// const props = defineProps({
//   class: {
//     type: String,
//     default: () => '',
//   },
//   modelValue: {
//     type: String,
//     default: '',
//   },
// });
const model: any = defineModel();
// const emits = defineEmits(['update:modelValue']);

// const data = ref(props.modelValue);

const xSpreadsheet = ref();

onMounted(() => {
  // console.log('xSpreadsheet', model.value);
  // const options: any = {
  //   view: {
  //     height: () => xSpreadsheet.value.clientHeight,
  //     width: () => xSpreadsheet.value.clientWidth - 50,
  //   },
  // };

  // const s = new Spreadsheet(xSpreadsheet.value, options)
  //   .loadData(model.value != null ? JSON.parse(model.value) : {}) // load data
  //   .change((data) => {
  //     console.log(JSON.stringify(data));
  //     model.value = JSON.stringify(data);
  //   });

  // s.validate();

  // console.log('xSpreadsheet', model.value);
  const options: any = {
    view: {
      height: () => xSpreadsheet.value.clientHeight,
      width: () => xSpreadsheet.value.clientWidth - 50,
    },
  };

  // 保存实例到 ref
  nextTick(() => {
    spreadsheetInstance.value = new Spreadsheet(xSpreadsheet.value, options)
      .loadData(model.value != null ? (Array.isArray(model.value) ? model.value : JSON.parse(model.value)) : {})
      .change((data) => {
        // console.log(JSON.stringify(spreadsheetInstance.value.getData()));
        model.value = JSON.stringify(spreadsheetInstance.value.getData());
      });

  // 示例：如何主动获取所有 sheet 数据
  // const allSheetsData = spreadsheetInstance.value.getData();
  // console.log('All sheets data:', allSheetsData);

  }).then(() => {
    setTimeout(() => {
      showSpreadsheet.value = false;
    }, 300);
  });
});

// 如果需要暴露获取所有 sheet 数据的方法
function getAllSheetsData() {
  if (spreadsheetInstance.value) {
    return spreadsheetInstance.value.getData();
  }
  return null;
}
</script>

<template>
  <div  :class="[
        'h-full', 'w-full',
        { 'disable-children': showSpreadsheet }
      ]" ref="xSpreadsheet"></div>
</template>

<style scoped lang="less">
.disable-children :deep(.x-spreadsheet-overlayer-content) {
  display: none;
}
</style>
