<script setup lang="ts">
import { getDictItems } from '#/api';
import AiChat from '#/components/ai-chat/index.vue';
import { useSearchStore } from '#/store/search';
// 新增导入
import ChemStructureDrawer from '#/components/chem-structure-drawer/index.vue';
import { HelpCircleIcon } from 'tdesign-icons-vue-next';
import { Drawer } from 'tdesign-vue-next';
// 新增
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';

// interface Props/Emits 提前
interface Props {
  searchMode?: 'ai' | 'search';
  selectedSearchType?: string;
  searchQuery?: string;
  aiQuery?: string;
  deepSearchEnabled?: boolean;
  aiExamples?: string[];
  showAdvancedLinks?: boolean;
}
interface Emits {
  (e: 'update:searchMode', value: 'ai' | 'search'): void;
  (e: 'update:selectedSearchType', value: string): void;
  (e: 'update:searchQuery', value: string): void;
  (e: 'update:aiQuery', value: string): void;
  (e: 'update:deepSearchEnabled', value: boolean): void;
  (e: 'search', data: { query: string; type: string }): void;
  (e: 'aiChat', data: { deepSearch: boolean; query: string }): void;
  (e: 'advancedSearch'): void;
  (e: 'formulaSearch'): void;
}
const props = withDefaults(defineProps<Props>(), {
  searchMode: 'search',
  selectedSearchType: 'all',
  searchQuery: '',
  aiQuery: '',
  deepSearchEnabled: false,
  hotSearchTags: () => ['关键词', '关键词', '关键词', '关键词'],
  aiExamples: () => [
    '什么是苯的分子结构？',
    '如何合成阿司匹林？',
    '解释一下化学反应机理',
    '有机化学基础知识',
  ],
  searchTypeOptions: () => [
    { value: '', label: '全部' },
    { value: 'name', label: '名称/分子式' },
    { value: 'cas', label: 'CAS' },
    { value: 'smiles', label: 'SMILES' },
    { value: 'inchi', label: 'InChI' },
    { value: 'doi', label: 'DOI' },
    { value: 'topic', label: '话题/主题' },
  ],
  showAdvancedLinks: true,
});
const emit = defineEmits<Emits>();
const router = useRouter();
const hotSearchTags = ref<any[]>([]);
const searchTypeOptions = ref<any[]>([]);
const showChemDrawer = ref(false);
const chemDrawerData = ref();
const searchStore = useSearchStore();

// 响应式相关
const windowWidth = ref(window.innerWidth);
const isMobile = computed(() => windowWidth.value <= 768);
const isTablet = computed(
  () => windowWidth.value <= 1024 && windowWidth.value > 768,
);

// 动态计算抽屉尺寸
const drawerSize = computed(() => {
  if (isMobile.value) {
    return '100%'; // 移动端全屏
  } else if (isTablet.value) {
    return '80%'; // 平板端80%
  } else {
    return '700px'; // 桌面端固定宽度
  }
});
const handleAdvancedSearch = () => {
  router.push({
    path: '/advanced-search',
  });
  emit('advancedSearch');
};
const handleFormulaSearch = () => {
  // childModalRef.value?.open();
  showChemDrawer.value = true;
  emit('formulaSearch');
};
const initHotSearchTags = async () => {
  try {
    const res = await getDictItems('HOT_SEARCH');
    hotSearchTags.value = res;
  } catch (error) {
    console.error(error);
  }
};
const initSearchTypeOptions = async () => {
  try {
    const res = await getDictItems('RESOURCE_TYPE');
    searchTypeOptions.value = [{ value: 'all', label: '全部' }, ...res];
  } catch (error) {
    console.error(error);
  }
};

// 使用computed进行双向绑定，性能更好
const currentSearchMode = computed({
  get: () => props.searchMode,
  set: (value) => emit('update:searchMode', value),
});

const currentSelectedSearchType = computed({
  get: () => props.selectedSearchType,
  set: (value) => emit('update:selectedSearchType', value),
});

const currentSearchQuery = computed({
  get: () => props.searchQuery,
  set: (value) => emit('update:searchQuery', value),
});

const currentAiQuery = computed({
  get: () => props.aiQuery,
  set: (value) => emit('update:aiQuery', value),
});

const currentDeepSearchEnabled = computed({
  get: () => props.deepSearchEnabled,
  set: (value) => emit('update:deepSearchEnabled', value),
});

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

onMounted(() => {
  initHotSearchTags();
  initSearchTypeOptions();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 获取搜索占位符
const getSearchPlaceholder = () => {
  const placeholders: Record<string, string> = {
    all: '名称/分子式/CAS/SMILES/InChI/DOI/话题/主题',
    name: '请输入化合物名称或分子式',
    cas: '请输入CAS号',
    smiles: '请输入SMILES字符串',
    inchi: '请输入InChI标识符',
    doi: '请输入DOI',
    topic: '请输入话题或主题关键词',
  };
  return placeholders[currentSelectedSearchType.value] || placeholders.all;
};

const showAiDialog = ref(false);
// 切换搜索模式
const switchMode = (mode: 'ai' | 'search') => {
  if (mode === 'ai') {
    showAiDialog.value = true;
    return;
  }
  currentSearchMode.value = mode;
};

// 处理搜索
const handleSearch = () => {
  searchStore.setAll({
    searchMode: currentSearchMode.value,
    selectedSearchType: currentSelectedSearchType.value,
    aiQuery: currentAiQuery.value,
    deepSearchEnabled: currentDeepSearchEnabled.value,
  });
  router.push({ path: '/search-page' });
  emit('search');
};

// 快速搜索
const quickSearch = (tag: any) => {
  searchStore.setSearchQuery(tag.label);
  handleSearch();
};

// 快速AI问题
const quickAiQuestion = (question: string) => {
  currentAiQuery.value = question;
};
const handleHelp = () => {
  router.push({ path: '/user-guide' });
};
</script>

<template>
  <div class="searchAndAiBox">
    <div class="search-container">
      <!-- 搜索模式切换 -->
      <div class="mode-tabs">
        <button
          class="mode-tab"
          :class="[{ active: currentSearchMode === 'search' }]"
          @click="switchMode('search')"
        >
          检索
        </button>
        <button class="mode-tab" @click="switchMode('ai')">AI问答</button>
      </div>

      <!-- 搜索框区域 -->
      <div class="search-box-container">
        <!-- 普通检索模式 -->
        <div v-if="currentSearchMode === 'search'" class="search-input-wrapper">
          <div class="search-input-container">
            <!-- 下拉选择框 -->
            <div class="search-type-selector">
              <select
                v-model="currentSelectedSearchType"
                class="search-type-select"
              >
                <option
                  v-for="option in searchTypeOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
              <div class="dropdown-icon">▼</div>
            </div>
            <!-- 搜索输入框 -->
            <input
              v-model="currentSearchQuery"
              type="text"
              class="search-input"
              :placeholder="getSearchPlaceholder()"
              @keyup.enter="handleSearch"
              @blur="
                (e) => {
                  searchStore.setSearchQuery(e?.target?.value || '');
                }
              "
            />

            <!-- 搜索按钮 -->
            <button class="search-btn" @click="handleSearch">检索</button>
          </div>

          <!-- 高级检索链接 -->
          <div v-if="showAdvancedLinks" class="advanced-search-link">
            <a
              href="#"
              class="advanced-link"
              @click.prevent="handleAdvancedSearch"
            >
              高级检索 >
            </a>
            <a
              href="#"
              class="formula-link"
              @click.prevent="handleFormulaSearch"
            >
              结构式检索 >
            </a>
          </div>
          <HelpCircleIcon
            style="color: #fff; font-size: 24px; cursor: pointer"
            @click="handleHelp"
          />
        </div>
      </div>

      <!-- 热门检索 -->
      <div v-if="currentSearchMode === 'search'" class="hot-searches">
        <span class="hot-label">热门检索：</span>
        <div class="hot-tags">
          <button
            v-for="tag in hotSearchTags"
            :key="tag"
            class="hot-tag"
            @click="quickSearch(tag)"
          >
            {{ tag.label }}
          </button>
        </div>
      </div>

      <!-- AI问答示例 -->
      <div v-else class="ai-examples">
        <span class="examples-label">示例问题：</span>
        <div class="example-tags">
          <button
            v-for="example in aiExamples"
            :key="example"
            class="example-tag"
            @click="quickAiQuestion(example)"
          >
            {{ example }}
          </button>
        </div>
      </div>
    </div>
    <!-- AI问答弹窗 -->
    <Drawer
      v-model:visible="showAiDialog"
      placement="right"
      :size="drawerSize"
      :header="true"
      :footer="false"
      :close-on-overlay-click="true"
      class="ai-drawer"
      :class="[{ 'ai-drawer--mobile': isMobile }]"
    >
      <template #header>
        <div class="ai-drawer-header">
          <span class="ai-drawer-title">AI问答</span>
          <button
            class="ai-drawer-close"
            @click="showAiDialog = false"
            aria-label="关闭"
          >
            ✕
          </button>
        </div>
      </template>
      <template #body>
        <div class="ai-dialog-body ai-dialog-body--no-padding">
          <AiChat />
        </div>
      </template>
    </Drawer>
    <ChemStructureDrawer
      v-model="chemDrawerData"
      :visible="showChemDrawer"
      @update:visible="showChemDrawer = $event"
      @ok="
        (data) => {
          chemDrawerData = data;
          searchStore.setSearchQuery(data.smiles || '');
          handleSearch();
          showChemDrawer = false;
        }
      "
      @cancel="
        () => {
          showChemDrawer = false;
        }
      "
    />
  </div>
</template>

<style scoped lang="less">
/* 抽屉组件样式 */
:deep(.t-drawer__content) {
  display: flex;
  flex-direction: column;
}

:deep(.t-drawer__body) {
  padding: 0 !important;
  flex: 1;
  overflow: hidden;
}
:deep(.t-drawer__header) {
  padding: 0 !important;
}

.ai-drawer-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  min-height: 60px;
}

.ai-drawer-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.ai-drawer-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 18px;
  color: #6b7280;
  transition: all 0.2s ease;

  &:hover {
    background: #f3f4f6;
    color: #374151;
  }

  &:active {
    background: #e5e7eb;
  }
}

/* 移动端适配 */
.ai-drawer--mobile {
  :deep(.t-drawer__content) {
    width: 100% !important;
    height: 100% !important;
  }

  .ai-drawer-header {
    padding: 12px 16px;
    min-height: 56px;
  }

  .ai-drawer-title {
    font-size: 16px;
  }

  .ai-drawer-close {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }
}

/* 平板端适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .ai-drawer {
    :deep(.t-drawer__content) {
      width: 80% !important;
    }
  }
}

/* 移动端样式优化 */
@media (max-width: 768px) {
  .ai-drawer {
    :deep(.t-drawer__content) {
      width: 100% !important;
      height: 100% !important;
      border-radius: 0;
    }

    :deep(.t-drawer__mask) {
      background: rgba(0, 0, 0, 0.6);
    }
  }

  .ai-drawer-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .ai-drawer-header {
    padding: 10px 12px;
    min-height: 52px;
  }

  .ai-drawer-title {
    font-size: 15px;
  }

  .ai-drawer-close {
    width: 26px;
    height: 26px;
    font-size: 15px;
  }
}
.searchAndAiBox {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 320px;
  background-image: url('/static/images/home_bg_1.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: right center;
  position: relative;
  padding-left: 5%;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(25, 55, 109, 0.8);
  }
}
.ai-dialog-mask {
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: center;
}
.ai-dialog {
  background: #fff;
  border-radius: 12px;
  min-width: 900px;
  max-width: 100vw;
  min-height: 600px;
  max-height: 100vh;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.18);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.ai-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  font-size: 17px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
  background: #f7fafc;
  height: 48px;
  min-height: 48px;
  max-height: 48px;
  position: relative;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
.ai-dialog-fullscreen {
  background: none;
  border: none;
  font-size: 20px;
  color: #888;
  cursor: pointer;
  position: relative;
  z-index: 10;
  margin: 0;
}
.ai-dialog-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #888;
  cursor: pointer;
  line-height: 1;
  position: relative;
  z-index: 10;
  margin: 0;
}
.ai-dialog-body,
.ai-dialog-body--no-padding {
  flex: 1 1 0;
  min-height: 0;
  overflow: hidden;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0 !important;
  margin: 0 !important;
}
.ai-dialog-body iframe {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  border: none;
  display: block;
}

.search-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1000px;
  padding: 0 20px;
}

.mode-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 2px;

  .mode-tab {
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;

    &:first-child {
      border-radius: 25px 0 0 25px;
    }

    &:last-child {
      border-radius: 0 25px 25px 0;
    }

    &.active {
      background: #fff;
      color: #1a365d;
      border-color: #fff;
    }

    &:hover:not(.active) {
      background: rgba(255, 255, 255, 0.15);
      color: #fff;
    }
  }
}

.search-box-container {
  margin-bottom: 25px;
}

.search-input-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .search-input-container {
    flex: 1;
    display: flex;
    background: #fff;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .search-type-selector {
      position: relative;
      border-right: 1px solid #e2e8f0;

      .search-type-select {
        padding: 15px 40px 15px 20px;
        border: none;
        background: transparent;
        font-size: 16px;
        color: #2d3748;
        cursor: pointer;
        appearance: none;
        min-width: 120px;

        &:focus {
          outline: none;
        }
      }

      .dropdown-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #718096;
        font-size: 12px;
      }
    }

    .search-input {
      flex: 1;
      padding: 15px 20px;
      border: none;
      font-size: 16px;
      color: #2d3748;

      &:focus {
        outline: none;
      }

      &::placeholder {
        color: #a0aec0;
      }
    }

    .search-btn {
      padding: 15px 30px;
      background: #3182ce;
      color: #fff;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.3s ease;

      &:hover {
        background: #2c5aa0;
      }
    }
  }
}

.ai-chat-wrapper {
  .ai-input-container {
    .ai-input-box {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      &.ai-input-box--fullscreen {
        border-radius: 0 !important;
      }

      .ai-input {
        width: 100%;
        padding: 20px;
        border: none;
        font-size: 16px;
        color: #2d3748;
        resize: none;
        line-height: 1.5;

        &:focus {
          outline: none;
        }

        &::placeholder {
          color: #a0aec0;
        }
      }

      .ai-input-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-top: 1px solid #e2e8f0;
        background: #f7fafc;

        .left-controls {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .input-tips {
            font-size: 12px;
            color: #718096;
          }

          .deep-search-control {
            display: flex;
            align-items: center;
            gap: 8px;

            .deep-search-label {
              font-size: 13px;
              color: #374151;
              font-weight: 500;
            }
          }
        }

        .ai-send-btn {
          padding: 8px 12px;
          background: #3182ce;
          color: #fff;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          &:hover:not(:disabled) {
            background: #2c5aa0;
          }

          &:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

.hot-searches,
.ai-examples {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;

  .hot-label,
  .examples-label {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    white-space: nowrap;
  }

  .hot-tags,
  .example-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .hot-tag,
  .example-tag {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

.advanced-search-link {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 8px;
  .advanced-link,
  .formula-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 14px;
    margin: 0 15px;
    transition: color 0.3s ease;

    &:hover {
      color: #fff;
    }
  }
}

.iframe-loading {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  z-index: 2;
  font-size: 18px;
  color: #3182ce;
  min-height: 300px; // 保证有高度，内容居中
}

// 响应式设计
@media (max-width: 1024px) {
  .searchAndAiBox {
    padding-left: 3%;
  }

  .search-container {
    max-width: 600px;
  }
}

@media (max-width: 768px) {
  .searchAndAiBox {
    padding: 30px 15px;
    height: auto;
    min-height: 400px;
    justify-content: center;
    align-items: flex-start;
    padding-top: 60px;
  }

  .search-container {
    padding: 0;
    max-width: 100%;
    width: 100%;
  }

  .mode-tabs {
    margin-bottom: 25px;
    justify-content: center;

    .mode-tab {
      padding: 12px 24px;
      font-size: 15px;
      min-width: 80px;
    }
  }

  .search-input-container {
    flex-direction: column;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

    .search-type-selector {
      border-right: none;
      border-bottom: 1px solid #e2e8f0;

      .search-type-select {
        padding: 16px 40px 16px 20px;
        font-size: 15px;
        font-weight: 500;
      }

      .dropdown-icon {
        right: 18px;
        font-size: 14px;
      }
    }

    .search-input {
      padding: 16px 20px;
      font-size: 15px;

      &::placeholder {
        color: #9ca3af;
        font-size: 14px;
      }
    }

    .search-btn {
      padding: 16px 20px;
      border-radius: 0 0 12px 12px;
      font-size: 16px;
      font-weight: 600;
      background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);

      &:hover {
        background: linear-gradient(135deg, #2c5aa0 0%, #2a4d96 100%);
      }
    }
  }

  .ai-input-box {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

    .ai-input {
      padding: 20px;
      font-size: 15px;
      line-height: 1.6;

      &::placeholder {
        color: #9ca3af;
      }
    }

    .ai-input-actions {
      padding: 15px 20px;

      .left-controls {
        gap: 10px;

        .input-tips {
          font-size: 13px;
          color: #6b7280;
        }

        .deep-search-control {
          gap: 10px;

          .deep-search-label {
            font-size: 14px;
          }
        }
      }

      .ai-send-btn {
        padding: 10px 14px;
        border-radius: 10px;
        background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #2c5aa0 0%, #2a4d96 100%);
        }
      }
    }
  }

  .hot-searches,
  .ai-examples {
    margin-bottom: 25px;
    gap: 12px;

    .hot-label,
    .examples-label {
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 8px;
      color: rgba(255, 255, 255, 0.95);
    }

    .hot-tags,
    .example-tags {
      gap: 10px;
      justify-content: flex-start;
    }

    .hot-tag,
    .example-tag {
      padding: 8px 16px;
      font-size: 13px;
      border-radius: 18px;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.4);
      backdrop-filter: blur(10px);
      font-weight: 500;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.6);
        transform: translateY(-1px);
      }
    }
  }

  .advanced-search-link {
    margin-top: 20px;

    .advanced-link,
    .formula-link {
      display: inline-block;
      margin: 0 20px 8px 0;
      font-size: 14px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      padding: 8px 0;
      border-bottom: 1px solid transparent;

      &:hover {
        color: #fff;
        border-bottom-color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .search-input-wrapper {
    flex-direction: column;
    align-items: stretch;
    .search-input-container {
      flex-direction: column;
      align-items: stretch;
      .advanced-search-link {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        margin-left: 0;
        margin-top: 10px;
        gap: 18px;
        min-width: 0;
        .advanced-link,
        .formula-link {
          font-size: 14px;
          min-width: 90px;
          padding: 6px 0;
          text-align: center;
          border-radius: 16px;
          background: #f5f7fa;
          color: #1890ff;
          transition:
            background 0.2s,
            color 0.2s;
          &:hover {
            background: #e6f7ff;
            color: #1761a0;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .searchAndAiBox {
    padding: 20px 12px;
    padding-top: 40px;
    min-height: 350px;
  }

  .search-container {
    padding: 0;
  }

  .mode-tabs {
    margin-bottom: 20px;

    .mode-tab {
      padding: 10px 20px;
      font-size: 14px;
      min-width: 70px;
    }
  }

  .search-input-container {
    .search-type-selector {
      .search-type-select {
        padding: 14px 35px 14px 16px;
        font-size: 14px;
      }
    }

    .search-input {
      padding: 14px 16px;
      font-size: 14px;
    }

    .search-btn {
      padding: 14px 16px;
      font-size: 15px;
    }
  }

  .ai-input-box {
    .ai-input {
      padding: 16px;
      font-size: 14px;
    }

    .ai-input-actions {
      padding: 12px 16px;

      .left-controls {
        gap: 8px;

        .input-tips {
          font-size: 12px;
        }

        .deep-search-control {
          gap: 8px;

          .deep-search-label {
            font-size: 13px;
          }
        }
      }
    }
  }

  .hot-searches,
  .ai-examples {
    margin-bottom: 20px;

    .hot-label,
    .examples-label {
      font-size: 14px;
    }

    .hot-tags,
    .example-tags {
      gap: 8px;
    }

    .hot-tag,
    .example-tag {
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 15px;
    }
  }

  .advanced-search-link {
    text-align: left;

    .advanced-link,
    .formula-link {
      display: block;
      margin: 0 0 10px 0;
      font-size: 13px;
    }
  }
}

@media (max-width: 375px) {
  .searchAndAiBox {
    padding: 15px 10px;
    padding-top: 30px;
  }

  .mode-tabs {
    .mode-tab {
      padding: 8px 16px;
      font-size: 13px;
      min-width: 60px;
    }
  }

  .search-input-container {
    .search-type-selector {
      .search-type-select {
        padding: 12px 30px 12px 14px;
        font-size: 13px;
      }
    }

    .search-input {
      padding: 12px 14px;
      font-size: 13px;
    }

    .search-btn {
      padding: 12px 14px;
      font-size: 14px;
    }
  }

  .hot-searches,
  .ai-examples {
    .hot-tag,
    .example-tag {
      padding: 5px 10px;
      font-size: 11px;
    }
  }
}
</style>
