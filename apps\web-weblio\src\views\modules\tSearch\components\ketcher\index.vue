<script setup>
import {
  defineExpose,
  defineModel,
  onMounted,
  onUnmounted,
  onUpdated,
  ref,
} from 'vue';

const idKetcher = ref();

const modelValue = defineModel();

// 用于清理的引用
let initTimer = null;
let observer = null;

const initKetcher = () => {
  // 清理之前的定时器
  if (initTimer) {
    clearTimeout(initTimer);
  }

  initTimer = setTimeout(() => {
    idKetcher.value?.contentWindow?.ketcher?.setMolecule(
      modelValue.value.config,
    );

    // 阻止默认滚轮事件
    idKetcher.value?.contentWindow?.addEventListener(
      'wheel',
      (event) => {
        event.preventDefault();
      },
      { passive: false },
    );

    // 清理之前的观察器
    if (observer) {
      observer.disconnect();
    }

    // 创建新的观察器
    observer = new MutationObserver(handleMutation);
    observer.observe(idKetcher.value.contentDocument, {
      childList: true,
      subtree: true,
    });

    async function handleMutation(_mutationsList, _observer) {
      // iframe内容变化时的处理逻辑
      modelValue.value.config =
        await idKetcher.value?.contentWindow?.ketcher?.getKet();
      modelValue.value.smiles =
        await idKetcher.value?.contentWindow?.ketcher?.getSmiles();
      modelValue.value.img =
        await idKetcher.value?.contentWindow?.ketcher?.generateImage(
          await idKetcher.value?.contentWindow?.ketcher?.getKet(),
          {
            outputFormat: 'png', // 生成图片类型
            backgroundColor: '255, 255, 255', // 背景颜色
          },
        );
    }
  }, 500);
};

const getSmiles = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getSmiles();
};

const getMolfile = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getMolfile();
};

const getKet = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getKet();
};

function setMolecule(e) {
  return idKetcher.value?.contentWindow?.ketcher?.setMolecule(e);
}

onMounted(() => {
  // console.log('onMounted.value', modelValue.value);
  setMolecule(modelValue.value);
});

onUpdated(() => {
  // console.log('onUpdated.value', modelValue.value);
  // setMolecule(modelValue.value);
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (initTimer) {
    clearTimeout(initTimer);
    initTimer = null;
  }

  if (observer) {
    observer.disconnect();
    observer = null;
  }
});

defineExpose({
  getSmiles,
  getMolfile,
  setMolecule,
  getKet,
});
</script>

<template>
  <iframe
    ref="idKetcher"
    class="frame h-full w-full"
    src="/static/standalone/index.html"
    @load="initKetcher"
  ></iframe>
</template>

<style scoped>
.molecule {
  padding: 30px;
}

.input-search {
  width: 800px;
}

.molecule .left_content {
  width: 800px;
}

.molecule .right_content {
  width: calc(100% - 800px);
  margin-left: 50px;
  margin-top: 50px;
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.radio-group {
  display: grid;
}

.ant-radio-wrapper {
  padding: 10px 0 !important;
}
</style>
