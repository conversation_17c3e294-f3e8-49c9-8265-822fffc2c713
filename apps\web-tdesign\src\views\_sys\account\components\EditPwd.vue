<script setup lang="tsx">
import { defineProps, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { useVbenModal } from '@vben/common-ui';

import { Input } from 'tdesign-vue-next';
import { Form, FormItem, type FormProps, MessagePlugin } from 'tdesign-vue-next';

import { getDictItems } from '#/api';

import { updatePassword } from '../api.ts';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
  password: [
    {
      required: true,
      message: '必填',
    },
  ],
  rePassword: [
    {
      required: true,
      message: '必填',
    },
  ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  updatePassword: useRequest(updatePassword, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    },
  }),
};
const status = ref([]);
const delFlag = ref([]);
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: async (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (isOpen) {
      status.value = await getDictItems('ACCOUNT_STATUS');
      delFlag.value = await getDictItems('ACCOUNT_DEL_FLAG');
    } else {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      // 验证两次密码是否一致
      if (formData.value.password !== formData.value.rePassword) {
        MessagePlugin.error('两次输入的密码不一致');
        return;
      }
      /**
       * 保存数据
       */
      reqRunner.updatePassword.run({ id: state.tagObj.accountId, ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = { ...data };
    formData.value = {};
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});
</script>

<template>
  <Modal class="w-[400px]" title="修改密码">
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="w-full"
      label-align="top"
    >
      <div class="grid w-full grid-cols-1 gap-1">
        <FormItem label="密码" name="password">
          <Input
            v-model="formData.password"
            clearable
            placeholder="请输入内容"
            type="password"
          />
        </FormItem>
        <FormItem label="再次输入" name="password">
          <Input
            v-model="formData.rePassword"
            clearable
            placeholder="请输入内容"
            type="password"
          />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
