// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tSysAttach/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tSysAttach/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tSysAttach/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tSysAttach/getOne/${data}`);
}
export async function getOneShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tSysAttach/getOnegetOneShare`, data);
}
export async function getOneByFileCode(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tSysAttach/getOneByFileCode/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tSysAttach/getByIds/${data}`);
}
