<script setup lang="ts">
import { onMounted, ref } from 'vue';
import {useRequest} from 'vue-hooks-plus';
import { getUpDownInfoByInchikey } from '../api';
import { useSearchStore } from '#/store/search';
import { computed, reactive } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

// 定义数据接口
interface SubstanceItem {
  id: string;
  name: string;
  imageUrl: string;
  casNumber?: string;
}

interface UpDownStreamData {
  upstream: SubstanceItem[];
  downstream: SubstanceItem[];
}

// 响应式数据
const upDownStreamData = ref<UpDownStreamData>({
  upstream: [],
  downstream: []
});


/**
 * 内部静态数据定义
 */
 const state: any = reactive({
  /**
   * 当前详情数据
   */
   detailItem: {},
   /**
   * 加载状态
   */
  loading: false,

});


// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

/**
 * 网络访问方法定义
 */
 const reqRunner = {
  /**
   * 获取全部上下游数据
   */
  listAll: useRequest(getUpDownInfoByInchikey, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
     console.log("上下游数据", res, 2222);
     // 转换后台数据格式：将字符串转换为数组
     const transformedData = {
       upstream: res.upstreamInformation ? JSON.parse(res.upstreamInformation) : [],
       downstream: res.downstreamInformation ? JSON.parse(res.downstreamInformation) : []
     };
     upDownStreamData.value = transformedData;
   },
  }),
};

// 初始化数据的方法
const initializeData = async () => {
  try {
    state.loading = true;
    
    // 这里替换为实际的API调用
    const {run, loading} = reqRunner.listAll;
    state.loading = loading;
    // 调用API获取上下游数据
    run({
        // 当前详情inchikey
        inchikey: state.detailItem.baseCode,
    });
    
  } catch (error) {
    console.error('获取上下游数据失败:', error);
    MessagePlugin.error('获取上下游数据失败');
  } finally {
    state.loading = false;
  }
};

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  // 设置默认图片或占位符
  img.src = '/api/placeholder/80/80?text=No+Image';
};

// 组件挂载时初始化数据
onMounted(() => {
  initializeData();
});
</script>

<template>
  <div class="upstream-downstream">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载中...</div>
    </div>
    
    <template v-else>
      <div class="upstream-section">
        <h4>上游原料 (共 {{ upDownStreamData.upstream.length }} 个)</h4>
        <div class="compounds-grid upstream-grid">
          <div 
            v-for="item in upDownStreamData.upstream" 
            :key="item.id" 
            class="compound-card"
          >
            <div class="compound-structure">
              <img 
                :src="item.imageUrl" 
                :alt="item.name"
                @error="handleImageError"
                class="compound-image"
              />
            </div>
            <div class="compound-name">{{ item.name }}</div>
            <div v-if="item.casNumber" class="cas-number">CAS: {{ item.casNumber }}</div>
          </div>
        </div>
      </div>
      
      <div class="downstream-section">
        <h4>下游产品 (共 {{ upDownStreamData.downstream.length }} 个)</h4>
        <div class="compounds-grid downstream-grid">
          <div 
            v-for="item in upDownStreamData.downstream" 
            :key="item.id" 
            class="compound-card"
          >
            <div class="compound-structure">
              <img 
                :src="item.imageUrl" 
                :alt="item.name"
                @error="handleImageError"
                class="compound-image"
              />
            </div>
            <div class="compound-name">{{ item.name }}</div>
            <div v-if="item.casNumber" class="cas-number">CAS: {{ item.casNumber }}</div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.upstream-downstream {
  padding: 20px;
  
  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    
    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
  
  @keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
  }
  
  .upstream-section,
  .downstream-section {
    margin-bottom: 30px;
    
    h4 {
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      &::before {
        content: '▼';
        margin-right: 10px;
      }
    }
  }
  
  .compounds-grid {
    display: grid;
    gap: 15px;
    
    &.upstream-grid {
      grid-template-columns: repeat(4, 1fr);
    }
    
    &.downstream-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  .compound-card {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .compound-structure {
      width: 100%;
      height: 250px;
      // background: #f8f9fa;
      // border: 1px solid #e9ecef;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      position: relative;
      overflow: hidden;
      
      .compound-image {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
        border-radius: 4px;
      }
    }
    
    .compound-name {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 4px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .cas-number {
      font-size: 12px;
      color: #999;
    }
  }
  
  @media (max-width: 768px) {
    padding: 15px;
    
    .compounds-grid {
      gap: 10px;
      
      &.upstream-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      &.downstream-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
    
    .compound-card {
      padding: 8px;
      
      .compound-structure {
        height: 100px;
      }
      
      .compound-name {
        font-size: 12px;
      }
      
      .cas-number {
        font-size: 10px;
      }
    }
  }
  
  @media (max-width: 480px) {
    .compounds-grid {
      &.upstream-grid {
        grid-template-columns: repeat(1, 1fr);
      }
      
      &.downstream-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}
</style> 
