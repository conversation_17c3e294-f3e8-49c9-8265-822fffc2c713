<script setup lang="tsx">
import FormRender from '#/views/modules/tSearch/components/formEditer/components/render/FormRender.vue';
import {
  getByCode,
  getDatasetList,
  getLabelList,
} from '#/views/modules/tData/api.ts';
import { useVbenDrawer } from '@vben/common-ui';
import { Button, Card, Form, FormItem, Tag } from 'tdesign-vue-next';
import { defineModel, defineProps, onMounted, reactive, ref } from 'vue';
import {
  getCategorys,
  getOne,
  getRealData,
} from '#/views/modules/tDataBase/api.ts';
import { getDictItems } from '#/api';
import { getOneByFileCode } from '#/views/modules/tSysAttach/api.ts';
import {
  getDescribeMdByFileCode,
  getMdByFileCode,
} from '#/views/modules/tSearch/api.ts';
import { mavonEditor } from 'mavon-editor';
import 'mavon-editor/dist/css/index.css';
import { getOneByFileCode as getOneByFileCodeUnstructured } from '#/views/modules/tDataUnstructured/api.ts';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});

/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenDrawer({
  footer: false,
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      initStatus();
    }
  },
  onConfirm: async () => {},
  onCancel: () => {
    initStatus();
    modalApi.close();
  },
});
const cancel = () => {
  modalApi.close();
};

const data = ref();
const datacfg = ref();
const record = ref();
const code = ref();
const prouteCode = ref();
const metadataCodeList = ref([]);
const isReadonly = ref(true);
const isfile = ref();
const type = ref();
const realData: any = defineModel('realData', {
  type: Array,
  default: () => [
    {
      metadata_code: '',
      content_data: '',
    },
  ],
});

const analysis = (temp?: any, item?: any) => {
  item.schema.children[0].schema.children.forEach((item1) => {
    if (isReadonly.value) {
      item1.schema.placeholder = '';
    }
    if (item1.component === 'Collapse') {
      analysis(temp, item1);
    } else {
      let isValue = true;
      for (let i = 0; i < realData.value.length; i++) {
        const key = realData.value[i];
        if (
          key.metadata_code == item1.schema.name &&
          metadataCodeList.value.includes(item1.schema.name)
        ) {
          if (item1.component === 'Upload') {
            temp[item1.schema.name] = JSON.parse(key.content_data);
          } else {
            temp[item1.schema.name] = key.content_data;
          }
          item1.schema.isReadonly = isReadonly.value;
          item1.schema.backgroundColor = 1;
          isValue = false;
          break;
        } else {
          item1.schema.backgroundColor = 0;
        }
      }
      if (isValue) {
        data.value[item1.schema?.name] = item1.schema.value;
        item1.schema.isReadonly = isReadonly.value;
      }
    }
  });
};
const setFormData = (list) => {
  list.forEach((item) => {
    if (item.component === 'Collapse') {
      analysis(data.value, item);
    } else {
      let isValue = true;
      for (let i = 0; i < realData.value.length; i++) {
        const key = realData.value[i];
        if (
          key.metadata_code == item.schema.name &&
          metadataCodeList.value.includes(item.schema.name)
        ) {
          if (item.component === 'Upload') {
            data.value[item.schema.name] = JSON.parse(key.content_data);
          } else {
            data.value[item.schema.name] = key.content_data;
          }
          item.schema.isReadonly = isReadonly.value;
          item.schema.backgroundColor = 1;
          isValue = false;
          break;
        } else {
          item.schema.backgroundColor = 0;
        }
      }
      if (isValue) {
        data.value[item.schema?.name] = item.schema.value;
        item.schema.isReadonly = isReadonly.value;
      }
    }
  });
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});

/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};

function convertTimestampToDate(timestamp: number): string {
  // 创建 Date 对象
  const date = new Date(timestamp);

  // 获取日期、时间部分
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  // 格式化输出
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
const pdfUrl = ref();
const describeMarkdown = ref();
const open = async (datas?: any) => {
  if (datas) {
    isfile.value = datas.isfile;
    if (isfile.value) {
      type.value = datas.fileType;
      isShowFile.value = false;
      permissionTypes.value = await getDictItems('DATA_PERMISSION_TYPE');
      startTime.value = convertTimestampToDate(datas.startTime);
      endTime.value = convertTimestampToDate(datas.endTime);
      for (const it of permissionTypes.value) {
        if (datas.permissionType == it.value) {
          permissionType.value = it.label;
        }
      }
      remark.value = datas.remark;
      prouteCode.value = datas.baseCode;
      console.log('prouteCode', prouteCode.value);
      describeMarkdown.value = await getDescribeMdByFileCode(prouteCode.value);

      //数据分级
      classess.value = await getDictItems('DATA_LEVEL');
      //标签库
      labelses.value = await getLabelList();
      classes.value = datas.classCode ? Number(datas.classCode) : null;
      //获取分类列表
      categorys.value = await getCategorys(classes.value);
      category.value = JSON.parse(datas.categoryCode);
      category.value = category.value.map((item) => Number(item));
      labels.value = JSON.parse(datas.labelCode);
      categoryList.value = [];
      await getCategoryList(categorys.value);
      await formatDataForFile();
    } else {
      permissionTypes.value = await getDictItems('DATA_PERMISSION_TYPE');
      startTime.value = convertTimestampToDate(datas.startTime);
      endTime.value = convertTimestampToDate(datas.endTime);
      for (const it of permissionTypes.value) {
        if (datas.permissionType == it.value) {
          permissionType.value = it.label;
        }
      }
      remark.value = datas.remark;
      prouteCode.value = datas.baseCode;
      console.log('prouteCode', prouteCode.value);
      metadataCodeList.value = datas.metadataCodeList;
      console.log('metadataCodeList', metadataCodeList.value);
      //数据分级
      classess.value = await getDictItems('DATA_LEVEL');
      classess.value.forEach((op) => {
        if (op.value == 0 || op.value == 1) {
          op['disabled'] = true;
        }
      });
      //标签库
      labelses.value = await getLabelList();
      if (prouteCode.value) {
        const datas = await getOne(prouteCode.value);
        classes.value = datas.classCode ? Number(datas.classCode) : null;
        //获取分类列表
        categorys.value = await getCategorys(classes.value);
        category.value = JSON.parse(datas.categoryCode);
        //数据集
        datasets.value = await getDatasetList({categoryList:category.value});
        category.value = category.value.map((item) => Number(item));
        labels.value = JSON.parse(datas.labelCode);
        dataset.value = datas.datasetCode;
        approval.value = datas;
        categoryList.value = [];
        await getCategoryList(categorys.value);
        formatData();
        state.tagObj = datas;
        code.value = datas.templateCode;

        realData.value = await getRealData({
          temp_code: datas.templateCode,
          operation_code: datas.baseCode,
        });
        data.value = {};
        record.value = await getByCode(code.value);
        let list = JSON.parse(record.value?.cfg);
        setFormData(list);
        console.log('list', list);
        datacfg.value = list;
      }
    }
  }
  modalApi.open();
};

/**
 * 导出资源
 */
defineExpose({
  open,
});

onMounted(async () => {});
const approval = ref({});
// const labelsName = ref('');
const labelsTags = ref([]);
const datasetName = ref('');
const classesName = ref('');
const categoryName = ref('');
const formatData = () => {
  let labelsTag;
  // labelsName.value = ''
  labelsTags.value = [];
  datasetName.value = '';
  classesName.value = '';
  categoryName.value = '';
  if (labels.value) {
    for (const item of labels.value) {
      for (const it of labelses.value) {
        if (item == it.value) {
          // labelsName.value += it.label + ','
          labelsTag = {};
          labelsTag['key'] = it.value;
          labelsTag['content'] = it.label;
          labelsTag['color'] = it.color;
          labelsTags.value.push(labelsTag);
        }
      }
    }
  }
  if (dataset.value) {
    for (const item of dataset.value) {
      for (const it of datasets.value) {
        if (item == it.value) {
          datasetName.value += it.label + ',';
        }
      }
    }
  }
  for (const item of category.value) {
    for (const it of categoryList.value) {
      if (item == it.value) {
        categoryName.value += it.label + ',';
      }
    }
  }
  classesName.value = classess.value[classes.value].label;
  // if (labelsName.value) {
  //   labelsName.value = labelsName.value.slice(0, labelsName.value.length - 1);
  // }
  if (datasetName.value) {
    datasetName.value = datasetName.value.slice(
      0,
      datasetName.value.length - 1,
    );
  }
  if (categoryName.value) {
    categoryName.value = categoryName.value.slice(
      0,
      categoryName.value.length - 1,
    );
  }
};
const formatDataForFile = async () => {
  let labelsTag;
  labelsTags.value = [];
  datasetName.value = '';
  classesName.value = '';
  categoryName.value = '';
  if (labels.value) {
    for (const item of labels.value) {
      for (const it of labelses.value) {
        if (item == it.value) {
          labelsTag = {};
          labelsTag['key'] = it.value;
          labelsTag['content'] = it.label;
          labelsTag['color'] = it.color;
          labelsTags.value.push(labelsTag);
        }
      }
    }
  }
  const unstructured = await getOneByFileCodeUnstructured(prouteCode.value);
  datasetName.value = unstructured.dataType;
  for (const item of category.value) {
    for (const it of categoryList.value) {
      if (item == it.value) {
        categoryName.value += it.label + ',';
      }
    }
  }
  classesName.value = classess.value[classes.value].label;
  if (categoryName.value) {
    categoryName.value = categoryName.value.slice(
      0,
      categoryName.value.length - 1,
    );
  }
};
const getCategoryList = async (list) => {
  for (const item of list) {
    if (item.children && item.children.length > 0) {
      await getCategoryList(item.children);
    }
    categoryList.value.push(item);
  }
};

const labels = ref();
const classes = ref();
const category = ref();
const labelses = ref();
const dataset = ref();
const datasets = ref();
const classess = ref();
const categorys = ref();
const categoryList = ref([]);
const startTime = ref();
const endTime = ref();
const permissionType = ref();
const permissionTypes = ref();
const remark = ref();
const isShowFile = ref();

const markdown = ref();
const showFile = async () => {
  isShowFile.value = true;
  if (type.value == 'md') {
    markdown.value = await getMdByFileCode(prouteCode.value);
  } else {
    const file = await getOneByFileCode(prouteCode.value);
    pdfUrl.value = file.filePath;
  }
};
const hideFile = async () => {
  isShowFile.value = false;
};
</script>

<template>
  <Modal :header="false" title="待审批数据一览" class="w-[90%]">
    <div
      v-if="isfile"
      class="border-1 mt-2 flex h-full w-full flex-col overflow-auto rounded-lg p-2"
    >
      <Card>
        <Form ref="searchForm" class="w-[95%]">
          <div class="mt-5 grid w-full grid-cols-3 gap-1">
            <FormItem label="分级:" name="dataType">
              {{ classesName }}
            </FormItem>
            <FormItem label="分类:" name="code">
              {{ categoryName }}
            </FormItem>
            <FormItem label="标签:" name="labels">
              <!--              {{ labelsName }}-->
              <Tag
                v-for="item in labelsTags"
                :key="item.key"
                :color="item.color"
                variant="light-outline"
                style="margin-right: 10px"
              >
                {{ item.content }}
              </Tag>
            </FormItem>
            <FormItem label="归属数据集:" name="dataset">
              {{ datasetName }}
            </FormItem>
            <FormItem label="权限开始时间:" name="startTime">
              {{ startTime }}
            </FormItem>
            <FormItem label="权限失效时间:" name="endTime">
              {{ endTime }}
            </FormItem>
            <FormItem label="权限类型:" name="permissionType">
              {{ permissionType }}
            </FormItem>
            <FormItem label="权限申请备注:" name="remark">
              {{ remark }}
            </FormItem>
          </div>
        </Form>
      </Card>
      <Card style="margin-top: 10px">
        <div class="editor-container">
          <mavon-editor
            v-model="describeMarkdown"
            :subfield="false"
            :toolbarsFlag="false"
            :editable="false"
            defaultOpen="preview"
            style="min-height: 200px; border: none"
          />
        </div>
        <Button v-if="!isShowFile" @click="showFile" style="margin-top: 10px">
          查看原文件
        </Button>
        <Button v-if="isShowFile" @click="hideFile" style="margin-top: 10px">
          隐藏原文件
        </Button>
        <div v-if="type == 'md'">
          <div class="editor-container" v-if="isShowFile">
            <mavon-editor
              v-model="markdown"
              :subfield="false"
              :toolbarsFlag="false"
              :editable="false"
              defaultOpen="preview"
              style="min-height: 200px; border: none"
            />
          </div>
        </div>
        <div v-else>
          <iframe
            v-if="isShowFile"
            :src="pdfUrl"
            width="100%"
            height="650px"
            title="PDF文档"
            style="margin-top: 10px"
          />
        </div>
      </Card>
    </div>
    <div
      v-else
      class="border-1 mt-2 flex h-full w-full flex-col overflow-auto rounded-lg p-2"
    >
      <Card>
        <Form ref="searchForm" class="w-[95%]">
          <div class="mt-5 grid w-full grid-cols-3 gap-1">
            <FormItem label="分级:" name="dataType">
              {{ classesName }}
            </FormItem>
            <FormItem label="分类:" name="code">
              {{ categoryName }}
            </FormItem>
            <FormItem label="标签:" name="labels">
              <!--              {{ labelsName }}-->
              <Tag
                v-for="item in labelsTags"
                :key="item.key"
                :color="item.color"
                variant="light-outline"
                style="margin-right: 10px"
              >
                {{ item.content }}
              </Tag>
            </FormItem>
            <FormItem label="归属数据集:" name="dataset">
              {{ datasetName }}
            </FormItem>
            <FormItem label="权限开始时间:" name="startTime">
              {{ startTime }}
            </FormItem>
            <FormItem label="权限失效时间:" name="endTime">
              {{ endTime }}
            </FormItem>
            <FormItem label="权限类型:" name="permissionType">
              {{ permissionType }}
            </FormItem>
            <FormItem label="权限申请备注:" name="remark">
              {{ remark }}
            </FormItem>
          </div>
          <!--          <div class="mt-5 grid w-full grid-cols-3 gap-1">-->
          <!--          </div>-->
        </Form>
      </Card>
      <Card style="margin-top: 10px">
        <Form
          ref="form"
          :data="data"
          :colon="true"
          class="gap-3"
          label-align="right"
        >
          <div
            class="bg-background flex w-full flex-col rounded-lg shadow"
            style="position: relative"
          >
            <div class="flex-1 rounded-lg p-2">
              <FormRender
                v-model="data"
                :form-config="datacfg"
                class="bg-background flex w-full flex-wrap gap-2 p-2"
              />
            </div>
          </div>
        </Form>
      </Card>
    </div>
    <div class="flex flex-wrap items-center justify-end gap-10 p-10">
      <Button theme="default" size="large" @click="cancel"> 关闭 </Button>
    </div>
  </Modal>
</template>

<style scoped>
::v-deep .t-input.t-is-readonly {
  border: none !important;
  box-shadow: none !important;
}

::v-deep .t-textarea__inner {
  resize: none;
  border: none !important;
  box-shadow: none !important;
}

.editor-container {
  position: relative;
  z-index: 1; /* 创建新的层叠上下文 */
}
</style>
