/*! UEditorPlus v2.0.0*/
var CodeMirror=function(){function a(f,j){function r(a){return a>=0&&a<Yb.size}function u(a){return m(Yb,a)}function A(a,b){hc=!0;for(var c=b-a.height,d=a;d;d=d.parent)d.height+=c}function B(a){var b={line:0,ch:0};ga(b,{line:Yb.size-1,ch:u(Yb.size-1).text.length},X(a),b,b),bc=!0}function M(a){var b=[];return Yb.iter(0,Yb.size,function(a){b.push(a.text)}),b.join("\n")}function N(a){function b(a){var c=pb(a,!0);if(c&&!E(c,g)){Vb||ea(),g=c,Fa(d,c),bc=!1;var e=ya();(c.line>=e.to||c.line<e.from)&&(f=setTimeout(Ab(function(){b(a)}),150))}}Ea(a.shiftKey);for(var c=w(a);c!=Fb;c=c.parentNode)if(c.parentNode==Jb&&c!=Kb)return;for(var c=w(a);c!=Fb;c=c.parentNode)if(c.parentNode==Mb)return Bb.onGutterClick&&Bb.onGutterClick(vc,J(Mb.childNodes,c)+oc,a),t(a);var d=pb(a);switch(x(a)){case 3:return void(S&&!L&&qb(a));case 2:return void(d&&Ia(d.line,d.ch,!0))}if(!d)return void(w(a)==Ib&&t(a));Vb||ea();var e=+new Date;if(_b&&_b.time>e-400&&E(_b.pos,d))return t(a),setTimeout(ua,20),Qa(d.line);if($b&&$b.time>e-400&&E($b.pos,d))return _b={time:e,pos:d},t(a),Pa(d);$b={time:e,pos:d};var f,g=d;if(R&&!E(lc.from,lc.to)&&!F(d,lc.from)&&!F(lc.to,d)){U&&(Nb.draggable=!0);var h=y(Eb,"mouseup",Ab(function(b){U&&(Nb.draggable=!1),ac=!1,h(),Math.abs(a.clientX-b.clientX)+Math.abs(a.clientY-b.clientY)<10&&(t(b),Ia(d.line,d.ch,!0),ua())}),!0);return void(ac=!0)}t(a),Ia(d.line,d.ch,!0);var i=y(Eb,"mousemove",Ab(function(a){clearTimeout(f),t(a),b(a)}),!0),h=y(Eb,"mouseup",Ab(function(a){clearTimeout(f);var b=pb(a);b&&Fa(d,b),t(a),ua(),bc=!0,i(),h()}),!0)}function V(a){for(var b=w(a);b!=Fb;b=b.parentNode)if(b.parentNode==Mb)return t(a);var c=pb(a);c&&(_b={time:+new Date,pos:c},t(a),Pa(c))}function $(a){function b(a,b){var d=new FileReader;d.onload=function(){f[b]=d.result,++g==e&&(c=Ka(c),Ab(function(){var a=la(f.join(""),c,c);Fa(c,a)})())},d.readAsText(a)}a.preventDefault();var c=pb(a,!0),d=a.dataTransfer.files;if(c&&!Bb.readOnly)if(d&&d.length&&window.FileReader&&window.File)for(var e=d.length,f=Array(e),g=0,h=0;h<e;++h)b(d[h],h);else try{var f=a.dataTransfer.getData("Text");if(f){var i=la(f,c,c),j=lc.from,k=lc.to;Fa(c,i),ac&&la("",j,k),ua()}}catch(a){}}function _(a){var b=pa();H(b),a.dataTransfer.setDragImage(W,0,0),a.dataTransfer.setData("Text",b)}function aa(a){var d,e,f=Z[a.keyCode],g=Q[Bb.keyMap].auto;if(null==f||a.altGraphKey)return g&&(Bb.keyMap=g),null;if(a.altKey&&(f="Alt-"+f),a.ctrlKey&&(f="Ctrl-"+f),a.metaKey&&(f="Cmd-"+f),a.shiftKey&&(d=b("Shift-"+f,Bb.extraKeys,Bb.keyMap))?e=!0:d=b(f,Bb.extraKeys,Bb.keyMap),"string"==typeof d&&(d=P.propertyIsEnumerable(d)?P[d]:null),!g||!d&&c(a)||(Bb.keyMap=g),!d)return!1;if(e){var h=Zb;Zb=null,d(vc),Zb=h}else d(vc);return t(a),!0}function ba(a){Vb||ea();var b=a.keyCode;if(T&&27==b&&(a.returnValue=!1),Ea(16==b||a.shiftKey),!Bb.onKeyEvent||!Bb.onKeyEvent(vc,s(a))){var c=aa(a);window.opera&&(wc=c?a.keyCode:null,!c&&(L?a.metaKey:a.ctrlKey)&&88==a.keyCode&&ma(""))}}function ca(a){if(window.opera&&a.keyCode==wc)return wc=null,void t(a);if((!Bb.onKeyEvent||!Bb.onKeyEvent(vc,s(a)))&&(!window.opera||a.which||!aa(a))){if(Bb.electricChars&&Tb.electricChars){var b=String.fromCharCode(null==a.charCode?a.keyCode:a.charCode);Tb.electricChars.indexOf(b)>-1&&setTimeout(Ab(function(){Sa(lc.to.line,"smart")}),75)}ra()}}function da(a){Bb.onKeyEvent&&Bb.onKeyEvent(vc,s(a))||16==a.keyCode&&(Zb=null)}function ea(){Bb.readOnly||(Vb||(Bb.onFocus&&Bb.onFocus(vc),Vb=!0,Fb.className.search(/\bCodeMirror-focused\b/)==-1&&(Fb.className+=" CodeMirror-focused"),gc||ta(!0)),qa(),rb())}function fa(){Vb&&(Bb.onBlur&&Bb.onBlur(vc),Vb=!1,Fb.className=Fb.className.replace(" CodeMirror-focused","")),clearInterval(Sb),setTimeout(function(){Vb||(Zb=null)},150)}function ga(a,b,c,d,e){if(tc){var f=[];for(Yb.iter(a.line,b.line+1,function(a){f.push(a.text)}),tc.addChange(a.line,c.length,f);tc.done.length>Bb.undoDepth;)tc.done.shift()}ka(a,b,c,d,e)}function ha(a,b){var c=a.pop();if(c){var d=[],e=c.start+c.added;Yb.iter(c.start,e,function(a){d.push(a.text)}),b.push({start:c.start,added:c.old.length,old:d});var f=Ka({line:c.start+c.old.length-1,ch:I(d[d.length-1],c.old[c.old.length-1])});ka({line:c.start,ch:0},{line:e-1,ch:u(e-1).text.length},c.old,f,f),bc=!0}}function ia(){ha(tc.done,tc.undone)}function ja(){ha(tc.undone,tc.done)}function ka(a,b,c,d,e){function f(a){return a<=Math.min(b.line,b.line+s)?a:a+s}var g=!1,h=rc.length;Bb.lineWrapping||Yb.iter(a.line,b.line,function(a){if(a.text.length==h)return g=!0,!0}),(a.line!=b.line||c.length>1)&&(hc=!0);var j=b.line-a.line,k=u(a.line),l=u(b.line);if(0==a.ch&&0==b.ch&&""==c[c.length-1]){var m=[],n=null;a.line?(n=u(a.line-1),n.fixMarkEnds(l)):l.fixMarkStarts();for(var o=0,p=c.length-1;o<p;++o)m.push(i.inheritMarks(c[o],n));j&&Yb.remove(a.line,j,ic),m.length&&Yb.insert(a.line,m)}else if(k==l)if(1==c.length)k.replace(a.ch,b.ch,c[0]);else{l=k.split(b.ch,c[c.length-1]),k.replace(a.ch,null,c[0]),k.fixMarkEnds(l);for(var m=[],o=1,p=c.length-1;o<p;++o)m.push(i.inheritMarks(c[o],k));m.push(l),Yb.insert(a.line+1,m)}else if(1==c.length)k.replace(a.ch,null,c[0]),l.replace(null,b.ch,""),k.append(l),Yb.remove(a.line+1,j,ic);else{var m=[];k.replace(a.ch,null,c[0]),l.replace(null,b.ch,c[c.length-1]),k.fixMarkEnds(l);for(var o=1,p=c.length-1;o<p;++o)m.push(i.inheritMarks(c[o],k));j>1&&Yb.remove(a.line+1,j-1,ic),Yb.insert(a.line+1,m)}if(Bb.lineWrapping){var q=Ib.clientWidth/mb()-3;Yb.iter(a.line,a.line+c.length,function(a){if(!a.hidden){var b=Math.ceil(a.text.length/q)||1;b!=a.height&&A(a,b)}})}else Yb.iter(a.line,o+c.length,function(a){var b=a.text;b.length>h&&(rc=b,h=b.length,kc=null,g=!1)}),g&&(h=0,rc="",kc=null,Yb.iter(0,Yb.size,function(a){var b=a.text;b.length>h&&(h=b.length,rc=b)}));for(var r=[],s=c.length-j-1,o=0,t=Ub.length;o<t;++o){var v=Ub[o];v<a.line?r.push(v):v>b.line&&r.push(v+s)}var w=a.line+Math.min(c.length,500);vb(a.line,w),r.push(w),Ub=r,xb(100),dc.push({from:a.line,to:b.line+1,diff:s});var x={from:a,to:b,text:c};if(ec){for(var y=ec;y.next;y=y.next);y.next=x}else ec=x;Ga(d,e,f(lc.from.line),f(lc.to.line)),Jb.style.height=Yb.height*lb()+2*nb()+"px"}function la(a,b,c){function d(d){if(F(d,b))return d;if(!F(c,d))return e;var f=d.line+a.length-(c.line-b.line)-1,g=d.ch;return d.line==c.line&&(g+=a[a.length-1].length-(c.ch-(c.line==b.line?b.ch:0))),{line:f,ch:g}}b=Ka(b),c=c?Ka(c):b,a=X(a);var e;return na(a,b,c,function(a){return e=a,{from:d(lc.from),to:d(lc.to)}}),e}function ma(a,b){na(X(a),lc.from,lc.to,function(a){return"end"==b?{from:a,to:a}:"start"==b?{from:lc.from,to:lc.from}:{from:lc.from,to:a}})}function na(a,b,c,d){var e=1==a.length?a[0].length+b.ch:a[a.length-1].length,f=d({line:b.line+a.length-1,ch:e});ga(b,c,a,f.from,f.to)}function oa(a,b){var c=a.line,d=b.line;if(c==d)return u(c).text.slice(a.ch,b.ch);var e=[u(c).text.slice(a.ch)];return Yb.iter(c+1,d,function(a){e.push(a.text)}),e.push(u(d).text.slice(0,b.ch)),e.join("\n")}function pa(){return oa(lc.from,lc.to)}function qa(){xc||Wb.set(Bb.pollInterval,function(){yb(),sa(),Vb&&qa(),zb()})}function ra(){function a(){yb();var c=sa();c||b?(xc=!1,qa()):(b=!0,Wb.set(60,a)),zb()}var b=!1;xc=!0,Wb.set(20,a)}function sa(){if(gc||!Vb||Y(Hb))return!1;var a=Hb.value;if(a==yc)return!1;Zb=null;for(var b=0,c=Math.min(yc.length,a.length);b<c&&yc[b]==a[b];)++b;return b<yc.length?lc.from={line:lc.from.line,ch:lc.from.ch-(yc.length-b)}:mc&&E(lc.from,lc.to)&&(lc.to={line:lc.to.line,ch:Math.min(u(lc.to.line).text.length,lc.to.ch+(a.length-b))}),ma(a.slice(b),"end"),yc=a,!0}function ta(a){E(lc.from,lc.to)?a&&(yc=Hb.value=""):(yc="",Hb.value=pa(),Hb.select())}function ua(){Bb.readOnly||Hb.focus()}function va(){if(Pb.getBoundingClientRect){var a=Pb.getBoundingClientRect();if(!T||a.top!=a.bottom){var b=window.innerHeight||Math.max(document.body.offsetHeight,document.documentElement.offsetHeight);(a.top<0||a.bottom>b)&&Pb.scrollIntoView()}}}function wa(){var a=ib(lc.inverted?lc.from:lc.to),b=Bb.lineWrapping?Math.min(a.x,Nb.offsetWidth):a.x;return xa(b,a.y,b,a.yBot)}function xa(a,b,c,d){var e=ob(),f=nb(),g=lb();b+=f,d+=f,a+=e,c+=e;var h=Ib.clientHeight,i=Ib.scrollTop,j=!1,k=!0;b<i?(Ib.scrollTop=Math.max(0,b-2*g),j=!0):d>i+h&&(Ib.scrollTop=d+g-h,j=!0);var l=Ib.clientWidth,m=Ib.scrollLeft,n=Bb.fixedGutter?Lb.clientWidth:0;return a<m+n?(a<50&&(a=0),Ib.scrollLeft=Math.max(0,a-10-n),j=!0):c>l+m-3&&(Ib.scrollLeft=c+10-l,j=!0,c>Jb.clientWidth&&(k=!1)),j&&Bb.onScroll&&Bb.onScroll(vc),k}function ya(){var a=lb(),b=Ib.scrollTop-nb(),c=Math.max(0,Math.floor(b/a)),d=Math.ceil((b+Ib.clientHeight)/a);return{from:o(Yb,c),to:o(Yb,d)}}function za(a,b){if(!Ib.clientWidth)return void(oc=pc=nc=0);var c=ya();if(!(a!==!0&&0==a.length&&c.from>=oc&&c.to<=pc)){var d=Math.max(c.from-100,0),e=Math.min(Yb.size,c.to+100);oc<d&&d-oc<20&&(d=oc),pc>e&&pc-e<20&&(e=Math.min(Yb.size,pc));for(var f=a===!0?[]:Aa([{from:oc,to:pc,domStart:0}],a),g=0,h=0;h<f.length;++h){var i=f[h];i.from<d&&(i.domStart+=d-i.from,i.from=d),i.to>e&&(i.to=e),i.from>=i.to?f.splice(h--,1):g+=i.to-i.from}if(g!=e-d){f.sort(function(a,b){return a.domStart-b.domStart});var j=lb(),k=Lb.style.display;Qb.style.display=Lb.style.display="none",Ba(d,e,f),Qb.style.display="";var l=d!=oc||e!=pc||qc!=Ib.clientHeight+j;if(l&&(qc=Ib.clientHeight+j),oc=d,pc=e,nc=p(Yb,d),Kb.style.top=nc*j+"px",Jb.style.height=Yb.height*j+2*nb()+"px",Qb.childNodes.length!=pc-oc)throw new Error("BAD PATCH! "+JSON.stringify(f)+" size="+(pc-oc)+" nodes="+Qb.childNodes.length);if(Bb.lineWrapping){kc=Ib.clientWidth;var m=Qb.firstChild;Yb.iter(oc,pc,function(a){if(!a.hidden){var b=Math.round(m.offsetHeight/j)||1;a.height!=b&&(A(a,b),hc=!0)}m=m.nextSibling})}else null==kc&&(kc=gb(rc)),kc>Ib.clientWidth?(Nb.style.width=kc+"px",Jb.style.width="",Jb.style.width=Ib.scrollWidth+"px"):Nb.style.width=Jb.style.width="";return Lb.style.display=k,(l||hc)&&Ca(),Da(),!b&&Bb.onUpdate&&Bb.onUpdate(vc),!0}}}function Aa(a,b){for(var c=0,d=b.length||0;c<d;++c){for(var e=b[c],f=[],g=e.diff||0,h=0,i=a.length;h<i;++h){var j=a[h];e.to<=j.from&&e.diff?f.push({from:j.from+g,to:j.to+g,domStart:j.domStart}):e.to<=j.from||e.from>=j.to?f.push(j):(e.from>j.from&&f.push({from:j.from,to:e.from,domStart:j.domStart}),e.to<j.to&&f.push({from:e.to+g,to:j.to+g,domStart:j.domStart+(e.to-j.from)}))}a=f}return a}function Ba(a,b,c){function d(a){var b=a.nextSibling;return a.parentNode.removeChild(a),b}if(c.length){for(var e=0,f=Qb.firstChild,g=0;g<c.length;++g){for(var h=c[g];h.domStart>e;)f=d(f),e++;for(var i=0,j=h.to-h.from;i<j;++i)f=f.nextSibling,e++}for(;f;)f=d(f)}else Qb.innerHTML="";var k=c.shift(),f=Qb.firstChild,i=a,l=lc.from.line,m=lc.to.line,n=l<a&&m>=a,o=Eb.createElement("div");Yb.iter(a,b,function(a){var b=null,d=null;n?(b=0,m==i&&(n=!1,d=lc.to.ch)):l==i&&(m==i?(b=lc.from.ch,d=lc.to.ch):(n=!0,b=lc.from.ch)),k&&k.to==i&&(k=c.shift()),!k||k.from>i?(a.hidden?o.innerHTML="<pre></pre>":o.innerHTML=a.getHTML(b,d,!0,sc),Qb.insertBefore(o.firstChild,f)):f=f.nextSibling,++i})}function Ca(){if(Bb.gutter||Bb.lineNumbers){var a=Kb.offsetHeight,b=Ib.clientHeight;Lb.style.height=(a-b<2?b:a)+"px";var c=[],d=oc;Yb.iter(oc,Math.max(pc,oc+1),function(a){if(a.hidden)c.push("<pre></pre>");else{var b=a.gutterMarker,e=Bb.lineNumbers?d+Bb.firstLineNumber:null;b&&b.text?e=b.text.replace("%N%",null!=e?e:""):null==e&&(e=" "),c.push(b&&b.style?'<pre class="'+b.style+'">':"<pre>",e);for(var f=1;f<a.height;++f)c.push("<br/>&#160;");c.push("</pre>")}++d}),Lb.style.display="none",Mb.innerHTML=c.join("");for(var e=String(Yb.size).length,f=Mb.firstChild,g=D(f),h="";g.length+h.length<e;)h+=" ";h&&f.insertBefore(Eb.createTextNode(h),f.firstChild),Lb.style.display="",Nb.style.marginLeft=Lb.offsetWidth+"px",hc=!1}}function Da(){var a=lc.inverted?lc.from:lc.to,b=(lb(),ib(a,!0)),c=C(Fb),d=C(Qb);Gb.style.top=b.y+d.top-c.top+"px",Gb.style.left=b.x+d.left-c.left+"px",E(lc.from,lc.to)?(Pb.style.top=b.y+"px",Pb.style.left=(Bb.lineWrapping?Math.min(b.x,Nb.offsetWidth):b.x)+"px",Pb.style.display=""):Pb.style.display="none"}function Ea(a){Zb=a?Zb||(lc.inverted?lc.to:lc.from):null}function Fa(a,b){var c=Zb&&Ka(Zb);c&&(F(c,a)?a=c:F(b,c)&&(b=c)),Ga(a,b),cc=!0}function Ga(a,b,c,d){if(zc=null,null==c&&(c=lc.from.line,d=lc.to.line),!E(lc.from,a)||!E(lc.to,b)){if(F(b,a)){var e=b;b=a,a=e}a.line!=c&&(a=Ha(a,c,lc.from.ch)),b.line!=d&&(b=Ha(b,d,lc.to.ch)),E(a,b)?lc.inverted=!1:E(a,lc.to)?lc.inverted=!1:E(b,lc.from)&&(lc.inverted=!0),E(a,b)?E(lc.from,lc.to)||dc.push({from:c,to:d+1}):E(lc.from,lc.to)?dc.push({from:a.line,to:b.line+1}):(E(a,lc.from)||(a.line<c?dc.push({from:a.line,to:Math.min(b.line,c)+1}):dc.push({from:c,to:Math.min(d,a.line)+1})),E(b,lc.to)||(b.line<d?dc.push({from:Math.max(c,a.line),to:d+1}):dc.push({from:Math.max(a.line,d),to:b.line+1}))),lc.from=a,lc.to=b,fc=!0}}function Ha(a,b,c){function d(b){for(var d=a.line+b,e=1==b?Yb.size:-1;d!=e;){var f=u(d);if(!f.hidden){var g=a.ch;return(g>c||g>f.text.length)&&(g=f.text.length),{line:d,ch:g}}d+=b}}var e=u(a.line);return e.hidden?a.line>=b?d(1)||d(-1):d(-1)||d(1):a}function Ia(a,b,c){var d=Ka({line:a,ch:b||0});(c?Fa:Ga)(d,d)}function Ja(a){return Math.max(0,Math.min(a,Yb.size-1))}function Ka(a){if(a.line<0)return{line:0,ch:0};if(a.line>=Yb.size)return{line:Yb.size-1,ch:u(Yb.size-1).text.length};var b=a.ch,c=u(a.line).text.length;return null==b||b>c?{line:a.line,ch:c}:b<0?{line:a.line,ch:0}:a}function La(a,b){function c(){for(var b=f+a,c=a<0?-1:Yb.size;b!=c;b+=a){var d=u(b);if(!d.hidden)return f=b,h=d,!0}}function d(b){if(g==(a<0?0:h.text.length)){if(b||!c())return!1;g=a<0?h.text.length:0}else g+=a;return!0}var e=lc.inverted?lc.from:lc.to,f=e.line,g=e.ch,h=u(f);if("char"==b)d();else if("column"==b)d(!0);else if("word"==b)for(var i=!1;!(a<0)||d();){if(K(h.text.charAt(g)))i=!0;else if(i){a<0&&(a=1,d());break}if(a>0&&!d())break}return{line:f,ch:g}}function Ma(a,b){var c=a<0?lc.from:lc.to;(Zb||E(lc.from,lc.to))&&(c=La(a,b)),Ia(c.line,c.ch,!0)}function Na(a,b){E(lc.from,lc.to)?a<0?la("",La(a,b),lc.to):la("",lc.from,La(a,b)):la("",lc.from,lc.to),cc=!0}function Oa(a,b){var c=0,d=ib(lc.inverted?lc.from:lc.to,!0);null!=zc&&(d.x=zc),"page"==b?c=Ib.clientHeight:"line"==b&&(c=lb());var e=jb(d.x,d.y+c*a+2);Ia(e.line,e.ch,!0),zc=d.x}function Pa(a){for(var b=u(a.line).text,c=a.ch,d=a.ch;c>0&&K(b.charAt(c-1));)--c;for(;d<b.length&&K(b.charAt(d));)++d;Fa({line:a.line,ch:c},{line:a.line,ch:d})}function Qa(a){Fa({line:a,ch:0},{line:a,ch:u(a).text.length})}function Ra(a){if(E(lc.from,lc.to))return Sa(lc.from.line,a);for(var b=lc.to.line-(lc.to.ch?0:1),c=lc.from.line;c<=b;++c)Sa(c,a)}function Sa(a,b){if(b||(b="add"),"smart"==b)if(Tb.indent)var c=ub(a);else b="prev";var d,e=u(a),f=e.indentation(Bb.tabSize),g=e.text.match(/^\s*/)[0];"prev"==b?d=a?u(a-1).indentation(Bb.tabSize):0:"smart"==b?d=Tb.indent(c,e.text.slice(g.length),e.text):"add"==b?d=f+Bb.indentUnit:"subtract"==b&&(d=f-Bb.indentUnit),d=Math.max(0,d);var h=d-f;if(h){var i="",j=0;if(Bb.indentWithTabs)for(var k=Math.floor(d/Bb.tabSize);k;--k)j+=Bb.tabSize,i+="\t";for(;j<d;)++j,i+=" "}else{if(lc.from.line!=a&&lc.to.line!=a)return;var i=g}la(i,{line:a,ch:0},{line:a,ch:g.length})}function Ta(){Tb=a.getMode(Bb,Bb.mode),Yb.iter(0,Yb.size,function(a){a.stateAfter=null}),Ub=[0],xb()}function Ua(){var a=Bb.gutter||Bb.lineNumbers;Lb.style.display=a?"":"none",a?hc=!0:Qb.parentNode.style.marginLeft=0}function Va(a,b){if(Bb.lineWrapping){Fb.className+=" CodeMirror-wrap";var c=Ib.clientWidth/mb()-3;Yb.iter(0,Yb.size,function(a){if(!a.hidden){var b=Math.ceil(a.text.length/c)||1;1!=b&&A(a,b)}}),Nb.style.width=Jb.style.width=""}else Fb.className=Fb.className.replace(" CodeMirror-wrap",""),kc=null,rc="",Yb.iter(0,Yb.size,function(a){1==a.height||a.hidden||A(a,1),a.text.length>rc.length&&(rc=a.text)});dc.push({from:0,to:Yb.size})}function Wa(){for(var a='<span class="cm-tab">',b=0;b<Bb.tabSize;++b)a+=" ";return a+"</span>"}function Xa(){sc=Wa(),za(!0)}function Ya(){Ib.className=Ib.className.replace(/\s*cm-s-\w+/g,"")+Bb.theme.replace(/(^|\s)\s*/g," cm-s-")}function Za(){this.set=[]}function $a(a,b,c){function d(a,b,c,d){u(a).addMark(new g(b,c,d,e.set))}a=Ka(a),b=Ka(b);var e=new Za;if(a.line==b.line)d(a.line,a.ch,b.ch,c);else{d(a.line,a.ch,null,c);for(var f=a.line+1,h=b.line;f<h;++f)d(f,null,null,c);d(b.line,null,b.ch,c)}return dc.push({from:a.line,to:b.line+1}),e}function _a(a){a=Ka(a);var b=new h(a.ch);return u(a.line).addMark(b),b}function ab(a,b,c){return"number"==typeof a&&(a=u(Ja(a))),a.gutterMarker={text:b,style:c},hc=!0,a}function bb(a){"number"==typeof a&&(a=u(Ja(a))),a.gutterMarker=null,hc=!0}function cb(a,b){var c=a,d=a;return"number"==typeof a?d=u(Ja(a)):c=n(a),null==c?null:b(d,c)?(dc.push({from:c,to:c+1}),d):null}function db(a,b){return cb(a,function(a){if(a.className!=b)return a.className=b,!0})}function eb(a,b){return cb(a,function(a,c){if(a.hidden!=b)return a.hidden=b,A(a,b?0:1),!b||lc.from.line!=c&&lc.to.line!=c||Ga(Ha(lc.from,lc.from.line,lc.from.ch),Ha(lc.to,lc.to.line,lc.to.ch)),hc=!0})}function fb(a){if("number"==typeof a){if(!r(a))return null;var b=a;if(a=u(a),!a)return null}else{var b=n(a);if(null==b)return null}var c=a.gutterMarker;return{line:b,handle:a,text:a.text,markerText:c&&c.text,markerClass:c&&c.style,lineClass:a.className}}function gb(a){return Ob.innerHTML="<pre><span>x</span></pre>",Ob.firstChild.firstChild.firstChild.nodeValue=a,Ob.firstChild.firstChild.offsetWidth||10}function hb(a,b){var c="";if(Bb.lineWrapping){var d=a.text.indexOf(" ",b+2);c=H(a.text.slice(b+1,d<0?a.text.length:d+(T?5:0)))}Ob.innerHTML="<pre>"+a.getHTML(null,null,!1,sc,b)+'<span id="CodeMirror-temp-'+Ec+'">'+H(a.text.charAt(b)||" ")+"</span>"+c+"</pre>";var e=document.getElementById("CodeMirror-temp-"+Ec),f=e.offsetTop,g=e.offsetLeft;if(T&&b&&0==f&&0==g){var h=document.createElement("span");h.innerHTML="x",e.parentNode.insertBefore(h,e.nextSibling),f=h.offsetTop}return{top:f,left:g}}function ib(a,b){var c,d=lb(),e=d*(p(Yb,a.line)-(b?nc:0));if(0==a.ch)c=0;else{var f=hb(u(a.line),a.ch);c=f.left,Bb.lineWrapping&&(e+=Math.max(0,f.top))}return{x:c,y:e,yBot:e+d}}function jb(a,b){function c(a){var b=hb(h,a);if(j){var c=Math.round(b.top/d);return Math.max(0,b.left+(c-k)*Ib.clientWidth)}return b.left}b<0&&(b=0);var d=lb(),e=mb(),f=nc+Math.floor(b/d),g=o(Yb,f);if(g>=Yb.size)return{line:Yb.size-1,ch:u(Yb.size-1).text.length};var h=u(g),i=h.text,j=Bb.lineWrapping,k=j?f-p(Yb,g):0;if(a<=0&&0==k)return{line:g,ch:0};for(var l,m=0,n=0,q=i.length,r=Math.min(q,Math.ceil((a+k*Ib.clientWidth*.9)/e));;){var s=c(r);if(!(s<=a&&r<q)){l=s,q=r;break}r=Math.min(q,Math.ceil(1.2*r))}if(a>l)return{line:g,ch:q};for(r=Math.floor(.8*q),s=c(r),s<a&&(m=r,n=s);;){if(q-m<=1)return{line:g,ch:l-a>a-n?m:q};var t=Math.ceil((m+q)/2),v=c(t);v>a?(q=t,l=v):(m=t,n=v)}}function kb(a){var b=ib(a,!0),c=C(Nb);return{x:c.left+b.x,y:c.top+b.y,yBot:c.top+b.yBot}}function lb(){if(null==Cc){Cc="<pre>";for(var a=0;a<49;++a)Cc+="x<br/>";Cc+="x</pre>"}var b=Qb.clientHeight;return b==Bc?Ac:(Bc=b,Ob.innerHTML=Cc,Ac=Ob.firstChild.offsetHeight/50||1,Ob.innerHTML="",Ac)}function mb(){return Ib.clientWidth==Fc?Dc:(Fc=Ib.clientWidth,Dc=gb("x"))}function nb(){return Nb.offsetTop}function ob(){return Nb.offsetLeft}function pb(a,b){var c,d,e=C(Ib,!0);try{c=a.clientX,d=a.clientY}catch(a){return null}if(!b&&(c-e.left>Ib.clientWidth||d-e.top>Ib.clientHeight))return null;var f=C(Nb,!0);return jb(c-f.left,d-f.top)}function qb(a){function b(){var a=X(Hb.value).join("\n");a!=e&&Ab(ma)(a,"end"),Gb.style.position="relative",Hb.style.cssText=d,gc=!1,ta(!0),qa()}var c=pb(a);if(c&&!window.opera){(E(lc.from,lc.to)||F(c,lc.from)||!F(c,lc.to))&&Ab(Ia)(c.line,c.ch);var d=Hb.style.cssText;Gb.style.position="absolute",Hb.style.cssText="position: fixed; width: 30px; height: 30px; top: "+(a.clientY-5)+"px; left: "+(a.clientX-5)+"px; z-index: 1000; background: white; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",gc=!0;var e=Hb.value=pa();if(ua(),Hb.select(),S){v(a);var f=y(window,"mouseup",function(){f(),setTimeout(b,20)},!0)}else setTimeout(b,50)}}function rb(){clearInterval(Sb);var a=!0;Pb.style.visibility="",Sb=setInterval(function(){Pb.style.visibility=(a=!a)?"":"hidden"},650)}function sb(a){function b(a,b,c){if(a.text)for(var d,e=a.styles,f=g?0:a.text.length-1,i=g?0:e.length-2,j=g?e.length:-2;i!=j;i+=2*h){var k=e[i];if(null==e[i+1]||e[i+1]==m){for(var l=g?0:k.length-1,p=g?k.length:-1;l!=p;l+=h,f+=h)if(f>=b&&f<c&&o.test(d=k.charAt(l))){var q=Gc[d];if(">"==q.charAt(1)==g)n.push(d);else{if(n.pop()!=q.charAt(0))return{pos:f,match:!1};if(!n.length)return{pos:f,match:!0}}}}else f+=h*k.length}}var c=lc.inverted?lc.from:lc.to,d=u(c.line),e=c.ch-1,f=e>=0&&Gc[d.text.charAt(e)]||Gc[d.text.charAt(++e)];if(f){for(var g=(f.charAt(0),">"==f.charAt(1)),h=g?1:-1,i=d.styles,j=e+1,k=0,l=i.length;k<l;k+=2)if((j-=i[k].length)<=0){var m=i[k+1];break}for(var n=[d.text.charAt(e)],o=/[(){}[\]]/,k=c.line,l=g?Math.min(k+100,Yb.size):Math.max(-1,k-100);k!=l;k+=h){var d=u(k),p=k==c.line,q=b(d,p&&g?e+1:0,p&&!g?e:d.text.length);if(q)break}q||(q={pos:null,match:!1});var m=q.match?"CodeMirror-matchingbracket":"CodeMirror-nonmatchingbracket",r=$a({line:c.line,ch:e},{line:c.line,ch:e+1},m),s=null!=q.pos&&$a({line:k,ch:q.pos},{line:k,ch:q.pos+1},m),t=Ab(function(){r.clear(),s&&s.clear()});a?setTimeout(t,800):jc=t}}function tb(a){for(var b,c,d=a,e=a-40;d>e;--d){if(0==d)return 0;var f=u(d-1);if(f.stateAfter)return d;var g=f.indentation(Bb.tabSize);(null==c||b>g)&&(c=d-1,b=g)}return c}function ub(a){var b=tb(a),c=b&&u(b-1).stateAfter;return c=c?d(Tb,c):e(Tb),Yb.iter(b,a,function(a){a.highlight(Tb,c,Bb.tabSize),a.stateAfter=d(Tb,c)}),b<a&&dc.push({from:b,to:a}),a<Yb.size&&!u(a).stateAfter&&Ub.push(a),c}function vb(a,b){var c=ub(a);Yb.iter(a,b,function(a){a.highlight(Tb,c,Bb.tabSize),a.stateAfter=d(Tb,c)})}function wb(){for(var a=+new Date+Bb.workTime,b=Ub.length;Ub.length;){if(u(oc).stateAfter)var c=Ub.pop();else var c=oc;if(!(c>=Yb.size)){var f=tb(c),g=f&&u(f-1).stateAfter;g=g?d(Tb,g):e(Tb);var h=0,i=Tb.compareStates,j=!1,k=f,l=!1;if(Yb.iter(k,Yb.size,function(b){var e=b.stateAfter;if(+new Date>a)return Ub.push(k),xb(Bb.workDelay),j&&dc.push({from:c,to:k+1}),l=!0;var f=b.highlight(Tb,g,Bb.tabSize);if(f&&(j=!0),b.stateAfter=d(Tb,g),i){if(e&&i(e,g))return!0}else if(f===!1&&e){if(++h>3&&(!Tb.indent||Tb.indent(e,"")==Tb.indent(g,"")))return!0}else h=0;++k}),l)return;j&&dc.push({from:c,to:k+1})}}b&&Bb.onHighlightComplete&&Bb.onHighlightComplete(vc)}function xb(a){Ub.length&&Xb.set(a,Ab(wb))}function yb(){bc=cc=ec=null,dc=[],fc=!1,ic=[]}function zb(){var a,b=!1;fc&&(b=!wa()),dc.length?a=za(dc,!0):(fc&&Da(),hc&&Ca()),b&&wa(),fc&&(va(),rb()),Vb&&!gc&&(bc===!0||bc!==!1&&fc)&&ta(cc),fc&&Bb.matchBrackets&&setTimeout(Ab(function(){jc&&(jc(),jc=null),E(lc.from,lc.to)&&sb(!1)}),20);var c=ec,d=ic;fc&&Bb.onCursorActivity&&Bb.onCursorActivity(vc),c&&Bb.onChange&&vc&&Bb.onChange(vc,c);for(var e=0;e<d.length;++e)d[e](vc);a&&Bb.onUpdate&&Bb.onUpdate(vc)}function Ab(a){return function(){Hc++||yb();try{var b=a.apply(this,arguments)}finally{--Hc||zb()}return b}}var Bb={},Cb=a.defaults;for(var Db in Cb)Cb.hasOwnProperty(Db)&&(Bb[Db]=(j&&j.hasOwnProperty(Db)?j:Cb)[Db]);var Eb=Bb.document,Fb=Eb.createElement("div");Fb.className="CodeMirror"+(Bb.lineWrapping?" CodeMirror-wrap":""),Fb.innerHTML='<div style="overflow: hidden; position: relative; width: 3px; height: 0px;"><textarea style="position: absolute; padding: 0; width: 1px;" wrap="off" autocorrect="off" autocapitalize="off"></textarea></div><div class="CodeMirror-scroll" tabindex="-1"><div style="position: relative"><div style="position: relative"><div class="CodeMirror-gutter"><div class="CodeMirror-gutter-text"></div></div><div class="CodeMirror-lines"><div style="position: relative"><div style="position: absolute; width: 100%; height: 0; overflow: hidden; visibility: hidden"></div><pre class="CodeMirror-cursor">&#160;</pre><div></div></div></div></div></div></div>',f.appendChild?f.appendChild(Fb):f(Fb);var Gb=Fb.firstChild,Hb=Gb.firstChild,Ib=Fb.lastChild,Jb=Ib.firstChild,Kb=Jb.firstChild,Lb=Kb.firstChild,Mb=Lb.firstChild,Nb=Lb.nextSibling.firstChild,Ob=Nb.firstChild,Pb=Ob.nextSibling,Qb=Pb.nextSibling;Ya(),/AppleWebKit/.test(navigator.userAgent)&&/Mobile\/\w+/.test(navigator.userAgent)&&(Hb.style.width="0px"),U||(Nb.draggable=!0),null!=Bb.tabindex&&(Hb.tabIndex=Bb.tabindex),Bb.gutter||Bb.lineNumbers||(Lb.style.display="none");try{gb("x")}catch(Rb){throw Rb.message.match(/runtime/i)&&(Rb=new Error("A CodeMirror inside a P-style element does not work in Internet Explorer. (innerHTML bug)")),Rb}var Sb,Tb,Ub,Vb,Wb=new z,Xb=new z,Yb=new l([new k([new i("")])]);Ta();var Zb,$b,_b,ac,bc,cc,dc,ec,fc,gc,hc,ic,jc,kc,lc={from:{line:0,ch:0},to:{line:0,ch:0},inverted:!1},mc=!1,nc=0,oc=0,pc=0,qc=0,rc="",sc=Wa();Ab(function(){B(Bb.value||""),bc=!1})();var tc=new q;y(Ib,"mousedown",Ab(N)),y(Ib,"dblclick",Ab(V)),y(Nb,"dragstart",_),y(Nb,"selectstart",t),S||y(Ib,"contextmenu",qb),y(Ib,"scroll",function(){za([]),Bb.fixedGutter&&(Lb.style.left=Ib.scrollLeft+"px"),Bb.onScroll&&Bb.onScroll(vc)}),y(window,"resize",function(){za(!0)}),y(Hb,"keyup",Ab(da)),y(Hb,"input",ra),y(Hb,"keydown",Ab(ba)),y(Hb,"keypress",Ab(ca)),y(Hb,"focus",ea),y(Hb,"blur",fa),y(Ib,"dragenter",v),y(Ib,"dragover",v),y(Ib,"drop",Ab($)),y(Ib,"paste",function(){ua(),ra()}),y(Hb,"paste",ra),y(Hb,"cut",Ab(function(){ma("")}));var uc;try{uc=Eb.activeElement==Hb}catch(Rb){}uc?setTimeout(ea,20):fa();var vc=Fb.CodeMirror={getValue:M,setValue:Ab(B),getSelection:pa,replaceSelection:Ab(ma),focus:function(){ua(),ea(),ra()},setOption:function(a,b){var c=Bb[a];Bb[a]=b,"mode"==a||"indentUnit"==a?Ta():"readOnly"==a&&b?(fa(),Hb.blur()):"theme"==a?Ya():"lineWrapping"==a&&c!=b?Ab(Va)():"tabSize"==a&&Ab(Xa)(),"lineNumbers"!=a&&"gutter"!=a&&"firstLineNumber"!=a&&"theme"!=a||Ab(Ua)()},getOption:function(a){return Bb[a]},undo:Ab(ia),redo:Ab(ja),indentLine:Ab(function(a,b){r(a)&&Sa(a,null==b?"smart":b?"add":"subtract")}),indentSelection:Ab(Ra),historySize:function(){return{undo:tc.done.length,redo:tc.undone.length}},clearHistory:function(){tc=new q},matchBrackets:Ab(function(){sb(!0)}),getTokenAt:Ab(function(a){return a=Ka(a),u(a.line).getTokenAt(Tb,ub(a.line),a.ch)}),getStateAfter:function(a){return a=Ja(null==a?Yb.size-1:a),ub(a+1)},cursorCoords:function(a){return null==a&&(a=lc.inverted),kb(a?lc.from:lc.to)},charCoords:function(a){return kb(Ka(a))},coordsChar:function(a){var b=C(Nb);return jb(a.x-b.left,a.y-b.top)},markText:Ab($a),setBookmark:_a,setMarker:Ab(ab),clearMarker:Ab(bb),setLineClass:Ab(db),hideLine:Ab(function(a){return eb(a,!0)}),showLine:Ab(function(a){return eb(a,!1)}),onDeleteLine:function(a,b){if("number"==typeof a){if(!r(a))return null;a=u(a)}return(a.handlers||(a.handlers=[])).push(b),a},lineInfo:fb,addWidget:function(a,b,c,d,e){a=ib(Ka(a));var f=a.yBot,g=a.x;if(b.style.position="absolute",Jb.appendChild(b),"over"==d)f=a.y;else if("near"==d){var h=Math.max(Ib.offsetHeight,Yb.height*lb()),i=Math.max(Jb.clientWidth,Nb.clientWidth)-ob();a.yBot+b.offsetHeight>h&&a.y>b.offsetHeight&&(f=a.y-b.offsetHeight),g+b.offsetWidth>i&&(g=i-b.offsetWidth)}b.style.top=f+nb()+"px",b.style.left=b.style.right="","right"==e?(g=Jb.clientWidth-b.offsetWidth,b.style.right="0px"):("left"==e?g=0:"middle"==e&&(g=(Jb.clientWidth-b.offsetWidth)/2),b.style.left=g+ob()+"px"),c&&xa(g,f,g+b.offsetWidth,f+b.offsetHeight)},lineCount:function(){return Yb.size},clipPos:Ka,getCursor:function(a){return null==a&&(a=lc.inverted),G(a?lc.from:lc.to)},somethingSelected:function(){return!E(lc.from,lc.to)},setCursor:Ab(function(a,b,c){null==b&&"number"==typeof a.line?Ia(a.line,a.ch,c):Ia(a,b,c)}),setSelection:Ab(function(a,b,c){(c?Fa:Ga)(Ka(a),Ka(b||a))}),getLine:function(a){if(r(a))return u(a).text},getLineHandle:function(a){if(r(a))return u(a)},setLine:Ab(function(a,b){r(a)&&la(b,{line:a,ch:0},{line:a,ch:u(a).text.length})}),removeLine:Ab(function(a){r(a)&&la("",{line:a,ch:0},Ka({line:a+1,ch:0}))}),replaceRange:Ab(la),getRange:function(a,b){return oa(Ka(a),Ka(b))},execCommand:function(a){return P[a](vc)},moveH:Ab(Ma),deleteH:Ab(Na),moveV:Ab(Oa),toggleOverwrite:function(){mc=!mc},posFromIndex:function(a){var b,c=0;return Yb.iter(0,Yb.size,function(d){var e=d.text.length+1;return e>a?(b=a,!0):(a-=e,void++c)}),Ka({line:c,ch:b})},indexFromPos:function(a){if(a.line<0||a.ch<0)return 0;var b=a.ch;return Yb.iter(0,a.line,function(a){b+=a.text.length+1}),b},operation:function(a){return Ab(a)()},refresh:function(){za(!0)},getInputField:function(){return Hb},getWrapperElement:function(){return Fb},getScrollerElement:function(){return Ib},getGutterElement:function(){return Lb}},wc=null,xc=!1,yc="",zc=null;Za.prototype.clear=Ab(function(){for(var a=1/0,b=-(1/0),c=0,d=this.set.length;c<d;++c){var e=this.set[c],f=e.marked;if(f&&e.parent){var g=n(e);a=Math.min(a,g),b=Math.max(b,g);for(var h=0;h<f.length;++h)f[h].set==this.set&&f.splice(h--,1)}}a!=1/0&&dc.push({from:a,to:b+1})}),Za.prototype.find=function(){for(var a,b,c=0,d=this.set.length;c<d;++c)for(var e=this.set[c],f=e.marked,g=0;g<f.length;++g){var h=f[g];if(h.set==this.set&&(null!=h.from||null!=h.to)){var i=n(e);null!=i&&(null!=h.from&&(a={line:i,ch:h.from}),null!=h.to&&(b={line:i,ch:h.to}))}}return{from:a,to:b}};var Ac,Bc,Cc,Dc,Ec=Math.floor(16777215*Math.random()).toString(16),Fc=0,Gc={"(":")>",")":"(<","[":"]>","]":"[<","{":"}>","}":"{<"},Hc=0;for(var Ic in O)O.propertyIsEnumerable(Ic)&&!vc.propertyIsEnumerable(Ic)&&(vc[Ic]=O[Ic]);return vc}function b(a,b,c){function d(a,b,c){var e=b[a];if(null!=e)return e;if(null==c&&(c=b.fallthrough),null==c)return b.catchall;if("string"==typeof c)return d(a,Q[c]);for(var f=0,g=c.length;f<g;++f)if(e=d(a,Q[c[f]]),null!=e)return e;return null}return b?d(a,b,c):d(a,Q[c])}function c(a){var b=Z[a.keyCode];return"Ctrl"==b||"Alt"==b||"Shift"==b||"Mod"==b}function d(a,b){if(b===!0)return b;if(a.copyState)return a.copyState(b);var c={};for(var d in b){var e=b[d];e instanceof Array&&(e=e.concat([])),c[d]=e}return c}function e(a,b,c){return!a.startState||a.startState(b,c)}function f(a,b){this.pos=this.start=0,this.string=a,this.tabSize=b||8}function g(a,b,c,d){this.from=a,this.to=b,this.style=c,this.set=d}function h(a){this.from=a,this.to=a,this.line=null}function i(a,b){this.styles=b||[a,null],this.text=a,this.height=1,this.marked=this.gutterMarker=this.className=this.handlers=null,this.stateAfter=this.parent=this.hidden=null}function j(a,b,c,d){for(var e=0,f=0,g=0;f<b;e+=2){var h=c[e],i=f+h.length;0==g?(i>a&&d.push(h.slice(a-f,Math.min(h.length,b-f)),c[e+1]),i>=a&&(g=1)):1==g&&(i>b?d.push(h.slice(0,b-f),c[e+1]):d.push(h,c[e+1])),f=i}}function k(a){this.lines=a,this.parent=null;for(var b=0,c=a.length,d=0;b<c;++b)a[b].parent=this,d+=a[b].height;this.height=d}function l(a){this.children=a;for(var b=0,c=0,d=0,e=a.length;d<e;++d){var f=a[d];b+=f.chunkSize(),c+=f.height,f.parent=this}this.size=b,this.height=c,this.parent=null}function m(a,b){for(;!a.lines;)for(var c=0;;++c){var d=a.children[c],e=d.chunkSize();if(b<e){a=d;break}b-=e}return a.lines[b]}function n(a){if(null==a.parent)return null;for(var b=a.parent,c=J(b.lines,a),d=b.parent;d;b=d,d=d.parent){var e=0;for(d.children.length;d.children[e]!=b;++e)c+=d.children[e].chunkSize()}return c}function o(a,b){var c=0;a:do{for(var d=0,e=a.children.length;d<e;++d){var f=a.children[d],g=f.height;if(b<g){a=f;continue a}b-=g,c+=f.chunkSize()}return c}while(!a.lines);for(var d=0,e=a.lines.length;d<e;++d){var h=a.lines[d],i=h.height;if(b<i)break;b-=i}return c+d}function p(a,b){var c=0;a:do{for(var d=0,e=a.children.length;d<e;++d){var f=a.children[d],g=f.chunkSize();if(b<g){a=f;continue a}b-=g,c+=f.height}return c}while(!a.lines);for(var d=0;d<b;++d)c+=a.lines[d].height;return c}function q(){this.time=0,this.done=[],this.undone=[]}function r(){v(this)}function s(a){return a.stop||(a.stop=r),a}function t(a){a.preventDefault?a.preventDefault():a.returnValue=!1}function u(a){a.stopPropagation?a.stopPropagation():a.cancelBubble=!0}function v(a){t(a),u(a)}function w(a){return a.target||a.srcElement}function x(a){return a.which?a.which:1&a.button?1:2&a.button?3:4&a.button?2:void 0}function y(a,b,c,d){
if("function"==typeof a.addEventListener){if(a.addEventListener(b,c,!1),d)return function(){a.removeEventListener(b,c,!1)}}else{var e=function(a){c(a||window.event)};if(a.attachEvent("on"+b,e),d)return function(){a.detachEvent("on"+b,e)}}}function z(){this.id=null}function A(a,b,c){null==b&&(b=a.search(/[^\s\u00a0]/),b==-1&&(b=a.length));for(var d=0,e=0;d<b;++d)"\t"==a.charAt(d)?e+=c-e%c:++e;return e}function B(a){return a.currentStyle?a.currentStyle:window.getComputedStyle(a,null)}function C(a,b){for(var c=a.ownerDocument.body,d=0,e=0,f=!1,g=a;g;g=g.offsetParent){var h=g.offsetLeft,i=g.offsetTop;g==c?(d+=Math.abs(h),e+=Math.abs(i)):(d+=h,e+=i),b&&"fixed"==B(g).position&&(f=!0)}for(var j=b&&!f?null:c,g=a.parentNode;g!=j;g=g.parentNode)null!=g.scrollLeft&&(d-=g.scrollLeft,e-=g.scrollTop);return{left:d,top:e}}function D(a){return a.textContent||a.innerText||a.nodeValue||""}function E(a,b){return a.line==b.line&&a.ch==b.ch}function F(a,b){return a.line<b.line||a.line==b.line&&a.ch<b.ch}function G(a){return{line:a.line,ch:a.ch}}function H(a){return W.textContent=a,W.innerHTML}function I(a,b){if(!b)return a?a.length:0;if(!a)return b.length;for(var c=a.length,d=b.length;c>=0&&d>=0&&a.charAt(c)==b.charAt(d);--c,--d);return d+1}function J(a,b){if(a.indexOf)return a.indexOf(b);for(var c=0,d=a.length;c<d;++c)if(a[c]==b)return c;return-1}function K(a){return/\w/.test(a)||a.toUpperCase()!=a.toLowerCase()}a.defaults={value:"",mode:null,theme:"default",indentUnit:2,indentWithTabs:!1,tabSize:4,keyMap:"default",extraKeys:null,electricChars:!0,onKeyEvent:null,lineWrapping:!1,lineNumbers:!1,gutter:!1,fixedGutter:!1,firstLineNumber:1,readOnly:!1,onChange:null,onCursorActivity:null,onGutterClick:null,onHighlightComplete:null,onUpdate:null,onFocus:null,onBlur:null,onScroll:null,matchBrackets:!1,workTime:100,workDelay:200,pollInterval:100,undoDepth:40,tabindex:null,document:window.document};var L=/Mac/.test(navigator.platform),M=(/Win/.test(navigator.platform),{}),N={};a.defineMode=function(b,c){a.defaults.mode||"null"==b||(a.defaults.mode=b),M[b]=c},a.defineMIME=function(a,b){N[a]=b},a.getMode=function(b,c){if("string"==typeof c&&N.hasOwnProperty(c)&&(c=N[c]),"string"==typeof c)var d=c,e={};else if(null!=c)var d=c.name,e=c;var f=M[d];return f?f(b,e||{}):(window.console&&console.warn("No mode "+d+" found, falling back to plain text."),a.getMode(b,"text/plain"))},a.listModes=function(){var a=[];for(var b in M)M.propertyIsEnumerable(b)&&a.push(b);return a},a.listMIMEs=function(){var a=[];for(var b in N)N.propertyIsEnumerable(b)&&a.push({mime:b,mode:N[b]});return a};var O=a.extensions={};a.defineExtension=function(a,b){O[a]=b};var P=a.commands={selectAll:function(a){a.setSelection({line:0,ch:0},{line:a.lineCount()-1})},killLine:function(a){var b=a.getCursor(!0),c=a.getCursor(!1),d=!E(b,c);d||a.getLine(b.line).length!=b.ch?a.replaceRange("",b,d?c:{line:b.line}):a.replaceRange("",b,{line:b.line+1,ch:0})},deleteLine:function(a){var b=a.getCursor().line;a.replaceRange("",{line:b,ch:0},{line:b})},undo:function(a){a.undo()},redo:function(a){a.redo()},goDocStart:function(a){a.setCursor(0,0,!0)},goDocEnd:function(a){a.setSelection({line:a.lineCount()-1},null,!0)},goLineStart:function(a){a.setCursor(a.getCursor().line,0,!0)},goLineStartSmart:function(a){var b=a.getCursor(),c=a.getLine(b.line),d=Math.max(0,c.search(/\S/));a.setCursor(b.line,b.ch<=d&&b.ch?0:d,!0)},goLineEnd:function(a){a.setSelection({line:a.getCursor().line},null,!0)},goLineUp:function(a){a.moveV(-1,"line")},goLineDown:function(a){a.moveV(1,"line")},goPageUp:function(a){a.moveV(-1,"page")},goPageDown:function(a){a.moveV(1,"page")},goCharLeft:function(a){a.moveH(-1,"char")},goCharRight:function(a){a.moveH(1,"char")},goColumnLeft:function(a){a.moveH(-1,"column")},goColumnRight:function(a){a.moveH(1,"column")},goWordLeft:function(a){a.moveH(-1,"word")},goWordRight:function(a){a.moveH(1,"word")},delCharLeft:function(a){a.deleteH(-1,"char")},delCharRight:function(a){a.deleteH(1,"char")},delWordLeft:function(a){a.deleteH(-1,"word")},delWordRight:function(a){a.deleteH(1,"word")},indentAuto:function(a){a.indentSelection("smart")},indentMore:function(a){a.indentSelection("add")},indentLess:function(a){a.indentSelection("subtract")},insertTab:function(a){a.replaceSelection("\t","end")},transposeChars:function(a){var b=a.getCursor(),c=a.getLine(b.line);b.ch>0&&b.ch<c.length-1&&a.replaceRange(c.charAt(b.ch)+c.charAt(b.ch-1),{line:b.line,ch:b.ch-1},{line:b.line,ch:b.ch+1})},newlineAndIndent:function(a){a.replaceSelection("\n","end"),a.indentLine(a.getCursor().line)},toggleOverwrite:function(a){a.toggleOverwrite()}},Q=a.keyMap={};Q.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharRight",Backspace:"delCharLeft",Tab:"indentMore","Shift-Tab":"indentLess",Enter:"newlineAndIndent",Insert:"toggleOverwrite"},Q.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Alt-Up":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Down":"goDocEnd","Ctrl-Left":"goWordLeft","Ctrl-Right":"goWordRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delWordLeft","Ctrl-Delete":"delWordRight","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll",fallthrough:"basic"},Q.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goWordLeft","Alt-Right":"goWordRight","Cmd-Left":"goLineStart","Cmd-Right":"goLineEnd","Alt-Backspace":"delWordLeft","Ctrl-Alt-Backspace":"delWordRight","Alt-Delete":"delWordRight","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll",fallthrough:["basic","emacsy"]},Q["default"]=L?Q.macDefault:Q.pcDefault,Q.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageUp","Shift-Ctrl-V":"goPageDown","Ctrl-D":"delCharRight","Ctrl-H":"delCharLeft","Alt-D":"delWordRight","Alt-Backspace":"delWordLeft","Ctrl-K":"killLine","Ctrl-T":"transposeChars"},a.fromTextArea=function(b,c){function d(){b.value=h.getValue()}function e(){d(),b.form.submit=g,b.form.submit(),b.form.submit=e}if(c||(c={}),c.value=b.value,!c.tabindex&&b.tabindex&&(c.tabindex=b.tabindex),b.form){var f=y(b.form,"submit",d,!0);if("function"==typeof b.form.submit){var g=b.form.submit;b.form.submit=e}}b.style.display="none";var h=a(function(a){b.parentNode.insertBefore(a,b.nextSibling)},c);return h.save=d,h.getTextArea=function(){return b},h.toTextArea=function(){d(),b.parentNode.removeChild(h.getWrapperElement()),b.style.display="",b.form&&(f(),"function"==typeof b.form.submit&&(b.form.submit=g))},h},a.copyState=d,a.startState=e,f.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return 0==this.pos},peek:function(){return this.string.charAt(this.pos)},next:function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eat:function(a){var b=this.string.charAt(this.pos);if("string"==typeof a)var c=b==a;else var c=b&&(a.test?a.test(b):a(b));if(c)return++this.pos,b},eatWhile:function(a){for(var b=this.pos;this.eat(a););return this.pos>b},eatSpace:function(){for(var a=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>a},skipToEnd:function(){this.pos=this.string.length},skipTo:function(a){var b=this.string.indexOf(a,this.pos);if(b>-1)return this.pos=b,!0},backUp:function(a){this.pos-=a},column:function(){return A(this.string,this.start,this.tabSize)},indentation:function(){return A(this.string,null,this.tabSize)},match:function(a,b,c){function d(a){return c?a.toLowerCase():a}if("string"!=typeof a){var e=this.string.slice(this.pos).match(a);return e&&b!==!1&&(this.pos+=e[0].length),e}if(d(this.string).indexOf(d(a),this.pos)==this.pos)return b!==!1&&(this.pos+=a.length),!0},current:function(){return this.string.slice(this.start,this.pos)}},a.StringStream=f,g.prototype={attach:function(a){this.set.push(a)},detach:function(a){var b=J(this.set,a);b>-1&&this.set.splice(b,1)},split:function(a,b){if(this.to<=a&&null!=this.to)return null;var c=this.from<a||null==this.from?null:this.from-a+b,d=null==this.to?null:this.to-a+b;return new g(c,d,this.style,this.set)},dup:function(){return new g(null,null,this.style,this.set)},clipTo:function(a,b,c,d,e){null!=this.from&&this.from>=b&&(this.from=Math.max(d,this.from)+e),null!=this.to&&this.to>b&&(this.to=d<this.to?this.to+e:b),a&&d>this.from&&(d<this.to||null==this.to)&&(this.from=null),c&&(b<this.to||null==this.to)&&(b>this.from||null==this.from)&&(this.to=null)},isDead:function(){return null!=this.from&&null!=this.to&&this.from>=this.to},sameSet:function(a){return this.set==a.set}},h.prototype={attach:function(a){this.line=a},detach:function(a){this.line==a&&(this.line=null)},split:function(a,b){if(a<this.from)return this.from=this.to=this.from-a+b,this},isDead:function(){return this.from>this.to},clipTo:function(a,b,c,d,e){(a||b<this.from)&&(c||d>this.to)?(this.from=0,this.to=-1):this.from>b&&(this.from=this.to=Math.max(d,this.from)+e)},sameSet:function(a){return!1},find:function(){return this.line&&this.line.parent?{line:n(this.line),ch:this.from}:null},clear:function(){if(this.line){var a=J(this.line.marked,this);a!=-1&&this.line.marked.splice(a,1),this.line=null}}},i.inheritMarks=function(a,b){var c=new i(a),d=b&&b.marked;if(d)for(var e=0;e<d.length;++e)if(null==d[e].to&&d[e].style){var f=c.marked||(c.marked=[]),g=d[e],h=g.dup();f.push(h),h.attach(c)}return c},i.prototype={replace:function(a,b,c){var d=[],e=this.marked,f=null==b?this.text.length:b;if(j(0,a,this.styles,d),c&&d.push(c,null),j(f,this.text.length,this.styles,d),this.styles=d,this.text=this.text.slice(0,a)+c+this.text.slice(f),this.stateAfter=null,e)for(var g=c.length-(f-a),h=0,i=e[h];h<e.length;++h)i.clipTo(null==a,a||0,null==b,f,g),i.isDead()&&(i.detach(this),e.splice(h--,1))},split:function(a,b){var c=[b,null],d=this.marked;j(a,this.text.length,this.styles,c);var e=new i(b+this.text.slice(a),c);if(d)for(var f=0;f<d.length;++f){var g=d[f],h=g.split(a,b.length);h&&(e.marked||(e.marked=[]),e.marked.push(h),h.attach(e))}return e},append:function(a){var b=this.text.length,c=a.marked,d=this.marked;if(this.text+=a.text,j(0,a.text.length,a.styles,this.styles),d)for(var e=0;e<d.length;++e)null==d[e].to&&(d[e].to=b);if(c&&c.length){d||(this.marked=d=[]);a:for(var e=0;e<c.length;++e){var f=c[e];if(!f.from)for(var g=0;g<d.length;++g){var h=d[g];if(h.to==b&&h.sameSet(f)){h.to=null==f.to?null:f.to+b,h.isDead()&&(h.detach(this),c.splice(e--,1));continue a}}d.push(f),f.attach(this),f.from+=b,null!=f.to&&(f.to+=b)}}},fixMarkEnds:function(a){var b=this.marked,c=a.marked;if(b)for(var d=0;d<b.length;++d){var e=b[d],f=null==e.to;if(f&&c)for(var g=0;g<c.length;++g)if(c[g].sameSet(e)){f=!1;break}f&&(e.to=this.text.length)}},fixMarkStarts:function(){var a=this.marked;if(a)for(var b=0;b<a.length;++b)null==a[b].from&&(a[b].from=0)},addMark:function(a){a.attach(this),null==this.marked&&(this.marked=[]),this.marked.push(a),this.marked.sort(function(a,b){return(a.from||0)-(b.from||0)})},highlight:function(a,b,c){var d,e=new f(this.text,c),g=this.styles,h=0,i=!1,j=g[0];for(""==this.text&&a.blankLine&&a.blankLine(b);!e.eol();){var k=a.token(e,b),l=this.text.slice(e.start,e.pos);if(e.start=e.pos,h&&g[h-1]==k?g[h-2]+=l:l&&(!i&&(g[h+1]!=k||h&&g[h-2]!=d)&&(i=!0),g[h++]=l,g[h++]=k,d=j,j=g[h]),e.pos>5e3){g[h++]=this.text.slice(e.pos),g[h++]=null;break}}return g.length!=h&&(g.length=h,i=!0),h&&g[h-2]!=d&&(i=!0),i||g.length<5&&this.text.length<10&&null},getTokenAt:function(a,b,c){for(var d=this.text,e=new f(d);e.pos<c&&!e.eol();){e.start=e.pos;var g=a.token(e,b)}return{start:e.start,end:e.pos,string:e.current(),className:g||null,state:b}},indentation:function(a){return A(this.text,null,a)},getHTML:function(a,b,c,d,e){function f(a,b){a&&(i&&T&&" "==a.charAt(0)&&(a=" "+a.slice(1)),i=!1,b?h.push('<span class="',b,'">',H(a).replace(/\t/g,d),"</span>"):h.push(H(a).replace(/\t/g,d)))}function g(){l&&(r+=1,s=r<l.length?l[r]:null)}var h=[],i=!0;c&&h.push(this.className?'<pre class="'+this.className+'">':"<pre>");var j=this.styles,k=this.text,l=this.marked;a==b&&(a=null);var m=k.length;if(null!=e&&(m=Math.min(e,m)),k||null!=e)if(l||null!=a){var n,o=0,p=0,q="",r=-1,s=null;for(g();o<m;){var t=m,u="";for(null!=a&&(a>o?t=a:(null==b||b>o)&&(u=" CodeMirror-selected",null!=b&&(t=Math.min(t,b))));s&&null!=s.to&&s.to<=o;)g();for(s&&(s.from>o?t=Math.min(t,s.from):(u+=" "+s.style,null!=s.to&&(t=Math.min(t,s.to))));;){var v=o+q.length,w=n;if(u&&(w=n?n+u:u),f(v>t?q.slice(0,t-o):q,w),v>=t){q=q.slice(t-o),o=t;break}o=v,q=j[p++],n="cm-"+j[p++]}}null!=a&&null==b&&f(" ","CodeMirror-selected")}else for(var p=0,x=0;x<m;p+=2){var y=j[p],n=j[p+1],z=y.length;x+z>m&&(y=y.slice(0,m-x)),x+=z,f(y,n&&"cm-"+n)}else f(" ",null!=a&&null==b?"CodeMirror-selected":null);return c&&h.push("</pre>"),h.join("")},cleanUp:function(){if(this.parent=null,this.marked)for(var a=0,b=this.marked.length;a<b;++a)this.marked[a].detach(this)}},k.prototype={chunkSize:function(){return this.lines.length},remove:function(a,b,c){for(var d=a,e=a+b;d<e;++d){var f=this.lines[d];if(this.height-=f.height,f.cleanUp(),f.handlers)for(var g=0;g<f.handlers.length;++g)c.push(f.handlers[g])}this.lines.splice(a,b)},collapse:function(a){a.splice.apply(a,[a.length,0].concat(this.lines))},insertHeight:function(a,b,c){this.height+=c,this.lines.splice.apply(this.lines,[a,0].concat(b));for(var d=0,e=b.length;d<e;++d)b[d].parent=this},iterN:function(a,b,c){for(var d=a+b;a<d;++a)if(c(this.lines[a]))return!0}},l.prototype={chunkSize:function(){return this.size},remove:function(a,b,c){this.size-=b;for(var d=0;d<this.children.length;++d){var e=this.children[d],f=e.chunkSize();if(a<f){var g=Math.min(b,f-a),h=e.height;if(e.remove(a,g,c),this.height-=h-e.height,f==g&&(this.children.splice(d--,1),e.parent=null),0==(b-=g))break;a=0}else a-=f}if(this.size-b<25){var i=[];this.collapse(i),this.children=[new k(i)]}},collapse:function(a){for(var b=0,c=this.children.length;b<c;++b)this.children[b].collapse(a)},insert:function(a,b){for(var c=0,d=0,e=b.length;d<e;++d)c+=b[d].height;this.insertHeight(a,b,c)},insertHeight:function(a,b,c){this.size+=b.length,this.height+=c;for(var d=0,e=this.children.length;d<e;++d){var f=this.children[d],g=f.chunkSize();if(a<=g){if(f.insertHeight(a,b,c),f.lines&&f.lines.length>50){for(;f.lines.length>50;){var h=f.lines.splice(f.lines.length-25,25),i=new k(h);f.height-=i.height,this.children.splice(d+1,0,i),i.parent=this}this.maybeSpill()}break}a-=g}},maybeSpill:function(){if(!(this.children.length<=10)){var a=this;do{var b=a.children.splice(a.children.length-5,5),c=new l(b);if(a.parent){a.size-=c.size,a.height-=c.height;var d=J(a.parent.children,a);a.parent.children.splice(d+1,0,c)}else{var e=new l(a.children);e.parent=a,a.children=[e,c],a=e}c.parent=a.parent}while(a.children.length>10);a.parent.maybeSpill()}},iter:function(a,b,c){this.iterN(a,b-a,c)},iterN:function(a,b,c){for(var d=0,e=this.children.length;d<e;++d){var f=this.children[d],g=f.chunkSize();if(a<g){var h=Math.min(b,g-a);if(f.iterN(a,h,c))return!0;if(0==(b-=h))break;a=0}else a-=g}}},q.prototype={addChange:function(a,b,c){this.undone.length=0;var d=+new Date,e=this.done[this.done.length-1];if(d-this.time>400||!e||e.start>a+b||e.start+e.added<a-e.added+e.old.length)this.done.push({start:a,added:b,old:c});else{var f=0;if(a<e.start){for(var g=e.start-a-1;g>=0;--g)e.old.unshift(c[g]);e.added+=e.start-a,e.start=a}else e.start<a&&(f=a-e.start,b+=f);for(var g=e.added-f,h=c.length;g<h;++g)e.old.push(c[g]);e.added<b&&(e.added=b)}this.time=d}},a.e_stop=v,a.e_preventDefault=t,a.e_stopPropagation=u,a.connect=y,z.prototype={set:function(a,b){clearTimeout(this.id),this.id=setTimeout(b,a)}};var R=function(){if(/MSIE [1-8]\b/.test(navigator.userAgent))return!1;var a=document.createElement("div");return"draggable"in a}(),S=/gecko\/\d{7}/i.test(navigator.userAgent),T=/MSIE \d/.test(navigator.userAgent),U=/WebKit\//.test(navigator.userAgent),V="\n";!function(){var a=document.createElement("textarea");a.value="foo\nbar",a.value.indexOf("\r")>-1&&(V="\r\n")}(),null!=document.documentElement.getBoundingClientRect&&(C=function(a,b){try{var c=a.getBoundingClientRect();c={top:c.top,left:c.left}}catch(d){c={top:0,left:0}}if(!b)if(null==window.pageYOffset){var e=document.documentElement||document.body.parentNode;null==e.scrollTop&&(e=document.body),c.top+=e.scrollTop,c.left+=e.scrollLeft}else c.top+=window.pageYOffset,c.left+=window.pageXOffset;return c});var W=document.createElement("pre");"\na"==H("a")?H=function(a){return W.textContent=a,W.innerHTML.slice(1)}:"\t"!=H("\t")&&(H=function(a){return W.innerHTML="",W.appendChild(document.createTextNode(a)),W.innerHTML}),a.htmlEscape=H;var X=3!="\n\nb".split(/\n/).length?function(a){for(var b,c=0,d=[];(b=a.indexOf("\n",c))>-1;)d.push(a.slice(c,"\r"==a.charAt(b-1)?b-1:b)),c=b+1;return d.push(a.slice(c)),d}:function(a){return a.split(/\r?\n/)};a.splitLines=X;var Y=window.getSelection?function(a){try{return a.selectionStart!=a.selectionEnd}catch(b){return!1}}:function(a){try{var b=a.ownerDocument.selection.createRange()}catch(c){}return!(!b||b.parentElement()!=a)&&0!=b.compareEndPoints("StartToEnd",b)};a.defineMode("null",function(){return{token:function(a){a.skipToEnd()}}}),a.defineMIME("text/plain","null");var Z={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",91:"Mod",92:"Mod",93:"Mod",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63276:"PageUp",63277:"PageDown",63275:"End",63273:"Home",63234:"Left",63232:"Up",63235:"Right",63233:"Down",63302:"Insert",63272:"Delete"};return a.keyNames=Z,function(){for(var a=0;a<10;a++)Z[a+48]=String(a);for(var a=65;a<=90;a++)Z[a]=String.fromCharCode(a);for(var a=1;a<=12;a++)Z[a+111]=Z[a+63235]="F"+a}(),a}();CodeMirror.defineMode("xml",function(a,b){function c(a,b){function c(c){return b.tokenize=c,c(a,b)}var e=a.next();if("<"==e){if(a.eat("!"))return a.eat("[")?a.match("CDATA[")?c(f("atom","]]>")):null:a.match("--")?c(f("comment","-->")):a.match("DOCTYPE",!0,!0)?(a.eatWhile(/[\w\._\-]/),c(g(1))):null;if(a.eat("?"))return a.eatWhile(/[\w\._\-]/),b.tokenize=f("meta","?>"),"meta";s=a.eat("/")?"closeTag":"openTag",a.eatSpace(),r="";for(var h;h=a.eat(/[^\s\u00a0=<>\"\'\/?]/);)r+=h;return b.tokenize=d,"tag"}return"&"==e?(a.eatWhile(/[^;]/),a.eat(";"),"atom"):(a.eatWhile(/[^&<]/),null)}function d(a,b){var d=a.next();return">"==d||"/"==d&&a.eat(">")?(b.tokenize=c,s=">"==d?"endTag":"selfcloseTag","tag"):"="==d?(s="equals",null):/[\'\"]/.test(d)?(b.tokenize=e(d),b.tokenize(a,b)):(a.eatWhile(/[^\s\u00a0=<>\"\'\/?]/),"word")}function e(a){return function(b,c){for(;!b.eol();)if(b.next()==a){c.tokenize=d;break}return"string"}}function f(a,b){return function(d,e){for(;!d.eol();){if(d.match(b)){e.tokenize=c;break}d.next()}return a}}function g(a){return function(b,d){for(var e;null!=(e=b.next());){if("<"==e)return d.tokenize=g(a+1),d.tokenize(b,d);if(">"==e){if(1==a){d.tokenize=c;break}return d.tokenize=g(a-1),d.tokenize(b,d)}}return"meta"}}function h(){for(var a=arguments.length-1;a>=0;a--)t.cc.push(arguments[a])}function i(){return h.apply(null,arguments),!0}function j(a,b){var c=w.doNotIndent.hasOwnProperty(a)||t.context&&t.context.noIndent;t.context={prev:t.context,tagName:a,indent:t.indented,startOfLine:b,noIndent:c}}function k(){t.context&&(t.context=t.context.prev)}function l(a){if("openTag"==a)return t.tagName=r,i(o,m(t.startOfLine));if("closeTag"==a){var b=!1;return b=!t.context||t.context.tagName!=r,b&&(u="error"),i(n(b))}return i()}function m(a){return function(b){return"selfcloseTag"==b||"endTag"==b&&w.autoSelfClosers.hasOwnProperty(t.tagName.toLowerCase())?i():"endTag"==b?(j(t.tagName,a),i()):i()}}function n(a){return function(b){return a&&(u="error"),"endTag"==b?(k(),i()):(u="error",i(arguments.callee))}}function o(a){return"word"==a?(u="attribute",i(o)):"equals"==a?i(p,o):"string"==a?(u="error",i(o)):h()}function p(a){return"word"==a&&w.allowUnquoted?(u="string",i()):"string"==a?i(q):h()}function q(a){return"string"==a?i(q):h()}var r,s,t,u,v=a.indentUnit,w=b.htmlMode?{autoSelfClosers:{br:!0,img:!0,hr:!0,link:!0,input:!0,meta:!0,col:!0,frame:!0,base:!0,area:!0},doNotIndent:{pre:!0},allowUnquoted:!0}:{autoSelfClosers:{},doNotIndent:{},allowUnquoted:!1},x=b.alignCDATA;return{startState:function(){return{tokenize:c,cc:[],indented:0,startOfLine:!0,tagName:null,context:null}},token:function(a,b){if(a.sol()&&(b.startOfLine=!0,b.indented=a.indentation()),a.eatSpace())return null;u=s=r=null;var c=b.tokenize(a,b);if(b.type=s,(c||s)&&"comment"!=c)for(t=b;;){var d=b.cc.pop()||l;if(d(s||c))break}return b.startOfLine=!1,u||c},indent:function(a,b,e){var f=a.context;if(a.tokenize!=d&&a.tokenize!=c||f&&f.noIndent)return e?e.match(/^(\s*)/)[0].length:0;if(x&&/<!\[CDATA\[/.test(b))return 0;for(f&&/^<\//.test(b)&&(f=f.prev);f&&!f.startOfLine;)f=f.prev;return f?f.indent+v:0},compareStates:function(a,b){if(a.indented!=b.indented||a.tokenize!=b.tokenize)return!1;for(var c=a.context,d=b.context;;c=c.prev,d=d.prev){if(!c||!d)return c==d;if(c.tagName!=d.tagName)return!1}},electricChars:"/"}}),CodeMirror.defineMIME("application/xml","xml"),CodeMirror.defineMIME("text/html",{name:"xml",htmlMode:!0}),CodeMirror.defineMode("javascript",function(a,b){function c(a,b,c){return b.tokenize=c,c(a,b)}function d(a,b){for(var c,d=!1;null!=(c=a.next());){if(c==b&&!d)return!1;d=!d&&"\\"==c}return d}function e(a,b,c){return K=a,L=c,b}function f(a,b){var f=a.next();if('"'==f||"'"==f)return c(a,b,g(f));if(/[\[\]{}\(\),;\:\.]/.test(f))return e(f);if("0"==f&&a.eat(/x/i))return a.eatWhile(/[\da-f]/i),e("number","number");if(/\d/.test(f))return a.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),e("number","number");if("/"==f)return a.eat("*")?c(a,b,h):a.eat("/")?(a.skipToEnd(),e("comment","comment")):b.reAllowed?(d(a,"/"),a.eatWhile(/[gimy]/),e("regexp","string")):(a.eatWhile(P),e("operator",null,a.current()));if("#"==f)return a.skipToEnd(),e("error","error");if(P.test(f))return a.eatWhile(P),e("operator",null,a.current());a.eatWhile(/[\w\$_]/);var i=a.current(),j=O.propertyIsEnumerable(i)&&O[i];return j&&b.kwAllowed?e(j.type,j.style,i):e("variable","variable",i)}function g(a){return function(b,c){return d(b,a)||(c.tokenize=f),e("string","string")}}function h(a,b){for(var c,d=!1;c=a.next();){if("/"==c&&d){b.tokenize=f;break}d="*"==c}return e("comment","comment")}function i(a,b,c,d,e,f){this.indented=a,this.column=b,this.type=c,this.prev=e,this.info=f,null!=d&&(this.align=d)}function j(a,b){for(var c=a.localVars;c;c=c.next)if(c.name==b)return!0}function k(a,b,c,d,e){var f=a.cc;for(R.state=a,R.stream=e,R.marked=null,R.cc=f,a.lexical.hasOwnProperty("align")||(a.lexical.align=!0);;){var g=f.length?f.pop():N?u:t;if(g(c,d)){for(;f.length&&f[f.length-1].lex;)f.pop()();return R.marked?R.marked:"variable"==c&&j(a,d)?"variable-2":b}}}function l(){for(var a=arguments.length-1;a>=0;a--)R.cc.push(arguments[a])}function m(){return l.apply(null,arguments),!0}function n(a){var b=R.state;if(b.context){R.marked="def";for(var c=b.localVars;c;c=c.next)if(c.name==a)return;b.localVars={name:a,next:b.localVars}}}function o(){R.state.context||(R.state.localVars=S),R.state.context={prev:R.state.context,vars:R.state.localVars}}function p(){R.state.localVars=R.state.context.vars,R.state.context=R.state.context.prev}function q(a,b){var c=function(){var c=R.state;c.lexical=new i(c.indented,R.stream.column(),a,null,c.lexical,b)};return c.lex=!0,c}function r(){var a=R.state;a.lexical.prev&&(")"==a.lexical.type&&(a.indented=a.lexical.indented),a.lexical=a.lexical.prev)}function s(a){return function(b){return b==a?m():";"==a?l():m(arguments.callee)}}function t(a){return"var"==a?m(q("vardef"),C,s(";"),r):"keyword a"==a?m(q("form"),u,t,r):"keyword b"==a?m(q("form"),t,r):"{"==a?m(q("}"),B,r):";"==a?m():"function"==a?m(I):"for"==a?m(q("form"),s("("),q(")"),E,s(")"),r,t,r):"variable"==a?m(q("stat"),x):"switch"==a?m(q("form"),u,q("}","switch"),s("{"),B,r,r):"case"==a?m(u,s(":")):"default"==a?m(s(":")):"catch"==a?m(q("form"),o,s("("),J,s(")"),t,r,p):l(q("stat"),u,s(";"),r)}function u(a){return Q.hasOwnProperty(a)?m(w):"function"==a?m(I):"keyword c"==a?m(v):"("==a?m(q(")"),u,s(")"),r,w):"operator"==a?m(u):"["==a?m(q("]"),A(u,"]"),r,w):"{"==a?m(q("}"),A(z,"}"),r,w):m()}function v(a){return a.match(/[;\}\)\],]/)?l():l(u)}function w(a,b){if("operator"==a&&/\+\+|--/.test(b))return m(w);if("operator"==a)return m(u);if(";"!=a)return"("==a?m(q(")"),A(u,")"),r,w):"."==a?m(y,w):"["==a?m(q("]"),u,s("]"),r,w):void 0}function x(a){return":"==a?m(r,t):l(w,s(";"),r)}function y(a){if("variable"==a)return R.marked="property",m()}function z(a){if("variable"==a&&(R.marked="property"),Q.hasOwnProperty(a))return m(s(":"),u)}function A(a,b){function c(d){return","==d?m(a,c):d==b?m():m(s(b))}return function(d){return d==b?m():l(a,c)}}function B(a){return"}"==a?m():l(t,B)}function C(a,b){return"variable"==a?(n(b),m(D)):m()}function D(a,b){return"="==b?m(u,D):","==a?m(C):void 0}function E(a){return"var"==a?m(C,G):";"==a?l(G):"variable"==a?m(F):l(G)}function F(a,b){return"in"==b?m(u):m(w,G)}function G(a,b){return";"==a?m(H):"in"==b?m(u):m(u,s(";"),H)}function H(a){")"!=a&&m(u)}function I(a,b){return"variable"==a?(n(b),m(I)):"("==a?m(q(")"),o,A(J,")"),r,t,p):void 0}function J(a,b){if("variable"==a)return n(b),m()}var K,L,M=a.indentUnit,N=b.json,O=function(){function a(a){return{type:a,style:"keyword"}}var b=a("keyword a"),c=a("keyword b"),d=a("keyword c"),e=a("operator"),f={type:"atom",style:"atom"};return{"if":b,"while":b,"with":b,"else":c,"do":c,"try":c,"finally":c,"return":d,"break":d,"continue":d,"new":d,"delete":d,"throw":d,"var":a("var"),"const":a("var"),"let":a("var"),"function":a("function"),"catch":a("catch"),"for":a("for"),"switch":a("switch"),"case":a("case"),"default":a("default"),"in":e,"typeof":e,"instanceof":e,"true":f,"false":f,"null":f,undefined:f,NaN:f,Infinity:f}}(),P=/[+\-*&%=<>!?|]/,Q={atom:!0,number:!0,variable:!0,string:!0,regexp:!0},R={state:null,column:null,marked:null,cc:null},S={name:"this",next:{name:"arguments"}};return r.lex=!0,{startState:function(a){return{tokenize:f,reAllowed:!0,kwAllowed:!0,cc:[],lexical:new i((a||0)-M,0,"block",(!1)),localVars:null,context:null,indented:0}},token:function(a,b){if(a.sol()&&(b.lexical.hasOwnProperty("align")||(b.lexical.align=!1),b.indented=a.indentation()),a.eatSpace())return null;var c=b.tokenize(a,b);return"comment"==K?c:(b.reAllowed="operator"==K||"keyword c"==K||K.match(/^[\[{}\(,;:]$/),b.kwAllowed="."!=K,k(b,c,K,L,a))},indent:function(a,b){if(a.tokenize!=f)return 0;var c=b&&b.charAt(0),d=a.lexical,e=d.type,g=c==e;return"vardef"==e?d.indented+4:"form"==e&&"{"==c?d.indented:"stat"==e||"form"==e?d.indented+M:"switch"!=d.info||g?d.align?d.column+(g?0:1):d.indented+(g?0:M):d.indented+(/^(?:case|default)\b/.test(b)?M:2*M)},electricChars:":{}"}}),CodeMirror.defineMIME("text/javascript","javascript"),CodeMirror.defineMIME("application/json",{name:"javascript",json:!0}),CodeMirror.defineMode("css",function(a){function b(a,b){return g=b,a}function c(a,c){var g=a.next();return"@"==g?(a.eatWhile(/[\w\\\-]/),b("meta",a.current())):"/"==g&&a.eat("*")?(c.tokenize=d,d(a,c)):"<"==g&&a.eat("!")?(c.tokenize=e,e(a,c)):"="!=g?"~"!=g&&"|"!=g||!a.eat("=")?'"'==g||"'"==g?(c.tokenize=f(g),c.tokenize(a,c)):"#"==g?(a.eatWhile(/[\w\\\-]/),b("atom","hash")):"!"==g?(a.match(/^\s*\w*/),b("keyword","important")):/\d/.test(g)?(a.eatWhile(/[\w.%]/),b("number","unit")):/[,.+>*\/]/.test(g)?b(null,"select-op"):/[;{}:\[\]]/.test(g)?b(null,g):(a.eatWhile(/[\w\\\-]/),b("variable","variable")):b(null,"compare"):void b(null,"compare")}function d(a,d){for(var e,f=!1;null!=(e=a.next());){if(f&&"/"==e){d.tokenize=c;break}f="*"==e}return b("comment","comment")}function e(a,d){for(var e,f=0;null!=(e=a.next());){if(f>=2&&">"==e){d.tokenize=c;break}f="-"==e?f+1:0}return b("comment","comment")}function f(a){return function(d,e){for(var f,g=!1;null!=(f=d.next())&&(f!=a||g);)g=!g&&"\\"==f;return g||(e.tokenize=c),b("string","string")}}var g,h=a.indentUnit;return{startState:function(a){return{tokenize:c,baseIndent:a||0,stack:[]}},token:function(a,b){if(a.eatSpace())return null;var c=b.tokenize(a,b),d=b.stack[b.stack.length-1];return"hash"==g&&"rule"==d?c="atom":"variable"==c&&("rule"==d?c="number":d&&"@media{"!=d||(c="tag")),"rule"==d&&/^[\{\};]$/.test(g)&&b.stack.pop(),"{"==g?"@media"==d?b.stack[b.stack.length-1]="@media{":b.stack.push("{"):"}"==g?b.stack.pop():"@media"==g?b.stack.push("@media"):"{"==d&&"comment"!=g&&b.stack.push("rule"),c},indent:function(a,b){var c=a.stack.length;return/^\}/.test(b)&&(c-="rule"==a.stack[a.stack.length-1]?2:1),a.baseIndent+c*h},electricChars:"}"}}),CodeMirror.defineMIME("text/css","css"),CodeMirror.defineMode("htmlmixed",function(a,b){function c(a,b){var c=g.token(a,b.htmlState);return"tag"==c&&">"==a.current()&&b.htmlState.context&&(/^script$/i.test(b.htmlState.context.tagName)?(b.token=e,b.localState=h.startState(g.indent(b.htmlState,"")),b.mode="javascript"):/^style$/i.test(b.htmlState.context.tagName)&&(b.token=f,b.localState=i.startState(g.indent(b.htmlState,"")),b.mode="css")),c}function d(a,b,c){var d=a.current(),e=d.search(b);return e>-1&&a.backUp(d.length-e),c}function e(a,b){return a.match(/^<\/\s*script\s*>/i,!1)?(b.token=c,b.curState=null,b.mode="html",c(a,b)):d(a,/<\/\s*script\s*>/,h.token(a,b.localState))}function f(a,b){return a.match(/^<\/\s*style\s*>/i,!1)?(b.token=c,b.localState=null,b.mode="html",c(a,b)):d(a,/<\/\s*style\s*>/,i.token(a,b.localState))}var g=CodeMirror.getMode(a,{name:"xml",htmlMode:!0}),h=CodeMirror.getMode(a,"javascript"),i=CodeMirror.getMode(a,"css");return{startState:function(){var a=g.startState();return{token:c,localState:null,mode:"html",htmlState:a}},copyState:function(a){if(a.localState)var b=CodeMirror.copyState(a.token==f?i:h,a.localState);return{token:a.token,localState:b,mode:a.mode,htmlState:CodeMirror.copyState(g,a.htmlState)}},token:function(a,b){return b.token(a,b)},indent:function(a,b){return a.token==c||/^\s*<\//.test(b)?g.indent(a.htmlState,b):a.token==e?h.indent(a.localState,b):i.indent(a.localState,b)},compareStates:function(a,b){return g.compareStates(a.htmlState,b.htmlState)},electricChars:"/{}:"}}),CodeMirror.defineMIME("text/html","htmlmixed");