import { requestClient } from '#/api/request';

export async function generationEntityListByPageApi(data: any) {
  return requestClient.post<any>('/generation/entity/listByPage', data);
}

export async function generationEntitySave(data: any) {
  return requestClient.post<any>('/generation/entity/save', data);
}

// 批量删除
export async function generationEntityDeleteBatch(data: any) {
  return requestClient.delete<any>(`/generation/entity/deleteBatch/${data}`);
}

export async function tagList() {
  return requestClient.get<any>('/generation/entity/tagList');
}
