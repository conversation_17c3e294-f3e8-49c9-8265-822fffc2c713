export const WidgetConfig = [
  {
    name: '单行文本',
    component: 'Input',
    props: {
      clearable: true,
    },
    schema: {
      rules: [{ required: false, message: '必填项' }, { unique: false }],
      title: '单行文本',
      name: 'input_1',
      value: '',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },
  {
    name: '数字输入框',
    component: 'InputNumber',
    props: {
      clearable: true,
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '数字输入框',
      name: 'input_1',
      value: '',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },
  {
    name: '多行文本',
    component: 'Textarea',
    props: { clearable: true },
    schema: {
      rules: [{ required: false, message: '必填项' }, { unique: false }],
      title: '多行文本',
      name: 'input_1',
      value: '',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },
  {
    name: '单选框',
    component: 'Radio',
    props: {
      clearable: true,
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      needOptions: true,
      title: '单选框',
      name: 'input_1',
      value: '',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
      syncOptions: {},
      options: [
        {
          label: '选项1',
          value: '1',
          id: '1',
        },
        {
          label: '选项2',
          value: '2',
          id: '2',
        },
      ],
    },
  },
  {
    name: '多选框',
    component: 'Checkbox',
    props: {
      clearable: true,
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      needOptions: true,
      title: '多选框',
      name: 'input_1',
      value: [],
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
      syncOptions: {},
      options: [
        {
          label: '选项1',
          value: '1',
          id: '1',
        },
        {
          label: '选项2',
          value: '2',
          id: '2',
        },
      ],
    },
  },
  {
    name: '下拉框',
    component: 'Select',
    props: {
      clearable: true,
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      needOptions: true,
      title: '下拉框',
      name: 'input_1',
      value: '',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
      multiple: false,
      syncOptions: {},
      options: [
        {
          label: '选项1',
          value: '1',
          id: '1',
        },
        {
          label: '选项2',
          value: '2',
          id: '2',
        },
      ],
    },
  },
  {
    name: '日期框',
    component: 'DatePicker',
    props: {
      clearable: true,
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '日期框',
      name: 'input_1',
      value: '',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },
  {
    name: '时间框',
    component: 'TimePicker',
    props: {
      clearable: true,
      placeholder: '请输入',
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '时间框',
      name: 'input_1',
      value: '00:00:00',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },
  {
    name: '日期时间框',
    component: 'DateAndTimePicker',
    props: {
      clearable: true,
      placeholder: '请输入',
      enableTimePicker: true,
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '日期时间框',
      name: 'input_1',
      value: '',
      icon: '',
      placeholder: '请输入',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },
  {
    name: '文件/图片上传',
    component: 'Upload',
    props: {
      'abridge-name': [10, 10],
    },
    schema: {
      rules: [{ required: false, message: '必填项' }],
      action: '/rgdc-sys/file/upload',
      title: '文件上传',
      name: 'input_1',
      theme: 'image-flow',
      value: [],
      icon: '',
      multiple: false,
      accept: ['image/*'],
      max: 1,
      placeholder: '选择文件并上传',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      autoUpload: true,
      showImageFileName: false,
      isReadonly: false,
    },
  },
  {
    name: '折叠面板',
    component: 'Collapse',
    unFormItem: true,
    schema: {
      needPanel: true,
      children: [
        {
          component: 'CollapsePanel',
          unFormItem: true,
          isGroup: true,
          schema: {
            title: '面板1',
            columns: 1,
            children: [],
          },
        },
      ],
    },
  },
  {
    name: '富文本编辑器',
    component: 'UEditor',
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '富文本编辑器',
      name: 'input_1',
      value: '',
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },

  {
    name: 'Excel',
    component: 'LuckySheet',
    unFormItem: true,
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: 'Excel',
      name: 'input_1',
      unFormItem: true,
      value: "{}",
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },

  {
    name: '化学结构式编辑器',
    component: 'Ketcher',
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '化学结构式编辑器',
      name: 'input_1',
      value: '',
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },

  {
    name: '动态表格',
    component: 'DynamicTable',
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '动态表格',
      name: 'input_1',
      value: {
        columns: [
          {
            colKey: 'col-1',
            title: '列1',
          },
          {
            colKey: 'col-2',
            title: '列2',
          },
          {
            colKey: 'op',
            title: '操作',
            fixed: 'right',
            width: 150,
          },
        ],
        data: [],
      },
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },

  {
    name: '评论',
    component: 'Comment',
    props: {},
    schema: {
      rules: [{ required: false, message: '必填项' }],
      title: '评论',
      name: 'input_1',
      value: null,
      icon: '',
      placeholder: '请输入',
      avatar: 'https://tdesign.gtimg.com/site/avatar.jpg',
      author: '评论作者名',
      datetime: '今天16:38',
      content: '这里是评论者写的评论内容。',
      maxLength: 100,
      isDisabled: false,
      isShow: true,
      isReadonly: false,
    },
  },
];
