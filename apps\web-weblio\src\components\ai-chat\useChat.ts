import type { ChatMessage } from './types';

import { XStream } from 'ant-design-x-vue';
import { ref } from 'vue';

import { chatMessages, stopChatMessage } from './api';
import { renderMarkdown } from './markdown';
import { scrollToBottom } from './scroll';
import { createAssistantMessage, createUserMessage } from './types';

/**
 * 聊天核心逻辑组合式函数
 * 封装消息列表、流处理、发送/停止消息、滚动等
 * @param options 聊天配置项
 */
export function useChat(options: {
  aiAvatar: any; // AI 头像
  isOnline: boolean; // 是否联网
  knowledges: { dataset_ids: string[]; documents_ids: string[] }; // 知识库信息
  ragKey: string; // 工作流 key
  userAvatar: any; // 用户头像
}) {
  // 聊天消息列表
  const msgs = ref<ChatMessage[]>([]);
  // 发送中 loading 状态
  const senderLoading = ref(false);
  // 当前节点标题
  const nodeTitle = ref('');
  // 是否打印结束
  const printEnd = ref(false);
  // 当前会话/任务 id
  const selectedConversationId = ref('');
  const selectedTaskId = ref('');
  const currentTaskId = ref('');
  // 输入框内容
  const inputValue = ref('');
  // 是否正在回答
  const isAnswer = ref(false);
  // 流控制器
  let streamController: AbortController | null = null;
  // 聊天列表 DOM 引用
  const chatListRef = ref<HTMLElement | null>(null);

  /**
   * 停止流式响应
   */
  const stopStream = () => {
    senderLoading.value = false;
    isAnswer.value = false;
    printEnd.value = false;
    nodeTitle.value = '';
    selectedTaskId.value = '';
    if (streamController) {
      streamController.abort();
      streamController = null;
    }
  };

  /**
   * 发送消息并处理流式响应
   * @param value 用户输入内容
   */
  const inputEnter = async (value: string) => {
    senderLoading.value = true;
    isAnswer.value = true;
    // 添加用户消息和 AI 占位消息
    msgs.value.push(
      createUserMessage(value, options.userAvatar),
      createAssistantMessage(options.aiAvatar),
    );
    inputValue.value = '';
    scrollToBottom(chatListRef);
    // 发送消息
    streamController = new AbortController();
    const response = await chatMessages({
      query: value,
      bizFix: 'chatFC',
      inputs: {
        is_internet: options.isOnline ? '1' : '0',
        is_rag:
          options.knowledges.dataset_ids.length > 0 ||
          options.knowledges.documents_ids.length > 0
            ? '1'
            : '0',
        dataset_ids: options.knowledges.dataset_ids.join(','),
        document_ids: options.knowledges.documents_ids.join(','),
      },
      workflow: options.ragKey,
      conversationId: selectedConversationId.value,
      signal: streamController.signal,
    });
    if (!response.body) {
      const lastMsg = msgs.value[msgs.value.length - 1];
      if (lastMsg) lastMsg.content = 'AI 回复失败（无响应流）';
      senderLoading.value = false;
      isAnswer.value = false;
      return;
    }
    // 处理流式响应
    const stream = XStream({ readableStream: response.body });
    try {
      for await (const chunk of stream) {
        if (isAnswer.value) {
          const data = JSON.parse(chunk?.data || '{}');
          selectedTaskId.value = data?.task_id;
          currentTaskId.value = data?.task_id;
          if (data.event) {
            switch (data.event) {
              case 'error': {
                // 错误事件
                console.error(data?.data?.error);
                break;
              }
              case 'message':
              case 'text_chunk': {
                // 文本流片段
                if (chunk?.data) {
                  const tmp =
                    JSON.parse(chunk?.data)?.answer ||
                    JSON.parse(chunk?.data)?.data?.text ||
                    '';
                  // 分离 <think> 思考内容和正式回复内容
                  let reasoningPart = '';
                  let answerPart = '';
                  if (tmp.includes('<think>') && tmp.includes('</think>')) {
                    const after = tmp.split('<think>')[1];
                    const [reasoning, answer] = after.split('</think>');
                    reasoningPart = reasoning;
                    answerPart = answer;
                  } else if (tmp.includes('<think>')) {
                    reasoningPart = tmp.split('<think>')[1];
                  } else if (tmp.includes('</think>')) {
                    answerPart = tmp.split('</think>')[1];
                  } else {
                    answerPart = tmp;
                  }
                  // 拼接到 assistant 的 reasoningText 和 content
                  const lastMsg = msgs.value[msgs.value.length - 1];
                  if (lastMsg) {
                    lastMsg.reasoningText =
                      (lastMsg.reasoningText || '') + reasoningPart;
                    lastMsg.content = (lastMsg.content || '') + answerPart;
                    scrollToBottom(chatListRef);
                  }
                }
                break;
              }
              case 'node_finished': {
                // 节点处理结束
                if (data?.data?.title === '大模型总结回答') {
                  printEnd.value = true;
                }
                if (data?.data?.title === '提取数据') {
                  const lastMsg = msgs.value[msgs.value.length - 1];
                  if (lastMsg) lastMsg.chunks = data?.data?.outputs?.output;
                }
                break;
              }
              case 'node_started': {
                // 节点开始
                nodeTitle.value = data?.data?.title;
                break;
              }
              case 'workflow_finished': {
                // 工作流结束
                if (data?.data?.status === 'failed') {
                  console.error(data?.data?.error);
                }
                stopStream();
                break;
              }
              case 'workflow_started': {
                // 工作流开始
                selectedConversationId.value = data?.conversation_id;
                selectedTaskId.value = data?.task_id;
                break;
              }
            }
          }
        } else {
          stopStream();
          return;
        }
      }
    } catch {
      // 捕获 abort 或流中断异常，优雅退出
      stopStream();
    }
    senderLoading.value = false;
    isAnswer.value = false;
  };

  /**
   * 停止当前对话流
   */
  const onStop = async () => {
    if (selectedTaskId.value) {
      await stopChatMessage(selectedTaskId.value, {
        workflow: options.ragKey,
      });
    }
    stopStream();
  };

  return {
    msgs, // 聊天消息列表
    senderLoading, // 发送 loading
    nodeTitle, // 当前节点标题
    printEnd, // 是否打印结束
    selectedConversationId, // 当前会话 id
    selectedTaskId, // 当前任务 id
    currentTaskId, // 当前任务 id
    inputValue, // 输入框内容
    isAnswer, // 是否正在回答
    chatListRef, // 聊天列表 DOM
    inputEnter, // 发送消息
    onStop, // 停止对话
    stopStream, // 停止流
    renderMarkdown, // Markdown 渲染
  };
}
