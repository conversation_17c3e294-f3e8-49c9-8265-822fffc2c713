<script setup lang="ts">
import { EventBus } from '#/utils/eventBus';
import WidgetList from '#/views/modules/formEditer/components/editer/WidgetList.vue';
import ProxyEditForm from '#/views/modules/formEditer/components/props/PropsEditer.vue';
import { Form } from 'tdesign-vue-next';
import { defineEmits, defineOptions, defineProps, ref, watch } from 'vue';

import Drager from './Drager.vue';

defineOptions({ name: 'FormEdit', inheritAttrs: true });
const props = defineProps({
  class: {
    type: String,
    default: '',
  },
  dataType: {
    type: String,
    default: '',
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['update:modelValue']);
const data = ref(props.modelValue || []);
const formPropsEdit = (data: any) => {
  console.info('data', data.value);
  EventBus.emit(`formEditer_itemClick`, data.value);
};

const reset = () => {};
watch(
  () => props.modelValue,
  (val) => {
    data.value = val;
  },
  { deep: true },
);
// watch(
//   () => data,
//   (val) => {
//     console.info('data change', val.value);
//     emits('update:modelValue', val.value);
//   },
//   { deep: true },
// );
</script>

<template>
  <div :class="`flex h-full w-full flex-col border ${props.class}`">
    <div class="flex h-full w-full overflow-hidden">
      <div class="h-full w-[280px] border-r p-1">
        <WidgetList drag-group="vbox" @reset="reset" />
      </div>
      <div class="h-full flex-1 overflow-y-auto border-r p-1">
        <Form>
          <Drager :model-value="data" drag-group="vbox"/>
        </Form>
      </div>
      <div class="w-min-[300px] h-full w-[20%] p-1">
        <ProxyEditForm class="w-full" />
      </div>
      <!--      <div class="h-full w-full p-1">-->
      <!--        {{ JSON.stringify(data) }}-->
      <!--      </div>-->
    </div>
  </div>
</template>

<style scoped></style>
