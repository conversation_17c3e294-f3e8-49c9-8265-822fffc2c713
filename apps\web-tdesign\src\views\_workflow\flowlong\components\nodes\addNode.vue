<script setup>
import {
  AddIcon,
  CatalogIcon,
  RocketIcon,
  TreeRoundDotIcon,
  UserAddIcon,
} from 'tdesign-icons-vue-next';
import { Button, Popup } from 'tdesign-vue-next';
</script>

<script>
export default {
  props: {
    modelValue: { type: Object, default: () => {} },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    getNodeKey() {
      return `flk${Date.now()}`;
    },
    addType(type) {
      let node = {};
      switch (type) {
        case 1: {
          node = {
            nodeName: '审核人',
            nodeKey: this.getNodeKey(),
            type: 1, // 节点类型
            setType: 1, // 审核人类型 1，选择成员 3，选择角色
            nodeAssigneeList: [], // 审核人员，根据 setType 确定成员还是角色
            examineLevel: 1, // 指定主管层级
            directorLevel: 1, // 自定义连续主管审批层级
            selectMode: 1, // 发起人自选类型
            termAuto: false, // 审批期限超时自动审批
            term: 0, // 审批期限
            termMode: 1, // 审批期限超时后执行类型
            examineMode: 1, // 多人审批时审批方式
            directorMode: 0, // 连续主管审批方式
            childNode: this.modelValue,
          };

          break;
        }
        case 2: {
          node = {
            nodeName: '抄送人',
            nodeKey: this.getNodeKey(),
            type: 2,
            userSelectFlag: true,
            nodeAssigneeList: [],
            childNode: this.modelValue,
          };

          break;
        }
        case 4: {
          node = {
            nodeName: '条件路由',
            nodeKey: this.getNodeKey(),
            type: 4,
            conditionNodes: [
              {
                nodeName: '条件1',
                nodeKey: this.getNodeKey(),
                type: 3,
                priorityLevel: 1,
                conditionMode: 1,
                conditionList: [],
              },
              {
                nodeName: '条件2',
                nodeKey: this.getNodeKey(),
                type: 3,
                priorityLevel: 2,
                conditionMode: 1,
                conditionList: [],
              },
            ],
            childNode: this.modelValue,
          };

          break;
        }
        case 7: {
          node = {
            nodeName: '触发器',
            nodeKey: this.getNodeKey(),
            type: 7,
            extendConfig: {
              time: '',
              args: '{}',
              trigger: 'org.cnc.sys.workflow.trigger.TaskTriggerImpl',
            },
          };
          break;
        }
        // No default
      }
      this.$emit('update:modelValue', node);
    },
  },
};
</script>
<template>
  <div class="add-node-btn-box">
    <div class="add-node-btn">
      <Popup placement="right-start" trigger="click">
        <Button shape="circle" type="primary">
          <AddIcon />
        </Button>
        <template #content>
          <div class="add-node-popover-body">
            <ul style="display: inline">
              <li class="cursor-pointer" @click="addType(1)">
                <UserAddIcon style="font-size: 24px; color: #ff943e" />
                <p>审批节点</p>
              </li>
              <li class="cursor-pointer" @click="addType(2)">
                <RocketIcon style="font-size: 24px; color: #3296fa" />
                <p>抄送节点</p>
              </li>
              <li class="cursor-pointer" @click="addType(4)">
                <TreeRoundDotIcon style="font-size: 24px; color: #15bc83" />
                <p>条件分支</p>
              </li>

              <li class="cursor-pointer" @click="addType(7)">
                <CatalogIcon style="font-size: 24px; color: #3296fa" />
                <p>任务触发器</p>
              </li>
            </ul>
          </div>
        </template>
      </Popup>
    </div>
  </div>
</template>
