// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-sys/tDataService/listByPage1', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-sys/tDataService/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-sys/tDataService/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-sys/tDataService/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-sys/tDataService/getByIds/${data}`);
}
export async function getServiceHome() {
  return requestClient.get<any>(`/rgdc-sys/tDataService/getServiceHome`);
}
