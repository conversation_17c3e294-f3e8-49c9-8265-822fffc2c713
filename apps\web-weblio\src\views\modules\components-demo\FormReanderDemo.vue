<script setup lang="ts">
import FormRender from '#/views/modules/formEditer/components/render/FormRender.vue';
import { Page } from '@vben/common-ui';
import { Form } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';

const form: any = ref();
const formConfig = ref();

const formData = ref({});

const setFormData = (list) => {
  list.forEach((item) => {
    formData.value[item.schema.name] = item.schema.value;
    if (item?.schema?.children?.length >= 0) {
      setFormData(item.schema.children);
    }
  });
};
const click = () => {
  const list: any = [
    {
      name: 'item_131428808766',
      component: 'Collapse',
      unFormItem: true,
      schema: {
        needPanel: true,
        children: [
          {
            component: 'CollapsePanel',
            unFormItem: true,
            isGroup: true,
            schema: {
              title: '面板1',
              columns: 1,
              children: [
                {
                  name: 'item_103431071461',
                  component: 'Select',
                  props: {
                    clearable: true,
                  },
                  schema: {
                    rules: [
                      {
                        required: false,
                        message: '必填项',
                      },
                    ],
                    needOptions: true,
                    title: '下拉框',
                    name: 'item_140288438015',
                    value: '',
                    icon: '',
                    placeholder: '请输入',
                    maxLength: 100,
                    isDisabled: false,
                    isShow: true,
                    syncOptions: {},
                    options: [
                      {
                        label: '选项1',
                        value: '1',
                        id: '1',
                      },
                      {
                        label: '选项2',
                        value: '2',
                        id: '2',
                      },
                    ],
                    id: 192_337_685_988,
                  },
                  id: 130_274_007_346,
                },
                {
                  name: 'item_143794218421',
                  component: 'Select',
                  props: {
                    clearable: true,
                  },
                  schema: {
                    rules: [
                      {
                        required: false,
                        message: '必填项',
                      },
                    ],
                    needOptions: true,
                    title: '下拉框',
                    name: 'item_176121215609',
                    value: '',
                    icon: '',
                    placeholder: '请输入',
                    maxLength: 100,
                    isDisabled: false,
                    isShow: true,
                    syncOptions: {},
                    options: [
                      {
                        label: '选项1',
                        value: '1',
                        id: '1',
                      },
                      {
                        label: '选项2',
                        value: '2',
                        id: '2',
                      },
                    ],
                    id: 155_558_171_727,
                  },
                  id: 182_469_336_333,
                },
                {
                  name: 'item_109015447502',
                  component: 'Select',
                  props: {
                    clearable: true,
                  },
                  schema: {
                    rules: [
                      {
                        required: false,
                        message: '必填项',
                      },
                    ],
                    needOptions: true,
                    title: '下拉框',
                    name: 'item_136142124983',
                    value: '',
                    icon: '',
                    placeholder: '请输入',
                    maxLength: 100,
                    isDisabled: false,
                    isShow: true,
                    syncOptions: {},
                    options: [
                      {
                        label: '选项1',
                        value: '1',
                        id: '1',
                      },
                      {
                        label: '选项2',
                        value: '2',
                        id: '2',
                      },
                    ],
                    id: 115_829_686_963,
                  },
                  id: 148_287_301_896,
                },
                {
                  name: 'item_132682551183',
                  component: 'Select',
                  props: {
                    clearable: true,
                  },
                  schema: {
                    rules: [
                      {
                        required: false,
                        message: '必填项',
                      },
                    ],
                    needOptions: true,
                    title: '下拉框',
                    name: 'item_135201380808',
                    value: '',
                    icon: '',
                    placeholder: '请输入',
                    maxLength: 100,
                    isDisabled: false,
                    isShow: true,
                    syncOptions: {},
                    options: [
                      {
                        label: '选项1',
                        value: '1',
                        id: '1',
                      },
                      {
                        label: '选项2',
                        value: '2',
                        id: '2',
                      },
                    ],
                    id: 112_246_621_722,
                  },
                  id: 181_657_534_183,
                },
                {
                  name: 'item_147112271200',
                  component: 'Select',
                  props: {
                    clearable: true,
                  },
                  schema: {
                    rules: [
                      {
                        required: false,
                        message: '必填项',
                      },
                    ],
                    needOptions: true,
                    title: '下拉框',
                    name: 'item_175214369555',
                    value: '',
                    icon: '',
                    placeholder: '请输入',
                    maxLength: 100,
                    isDisabled: false,
                    isShow: true,
                    syncOptions: {},
                    options: [
                      {
                        label: '选项1',
                        value: '1',
                        id: '1',
                      },
                      {
                        label: '选项2',
                        value: '2',
                        id: '2',
                      },
                    ],
                    id: 123_256_319_929,
                  },
                  id: 115_330_800_896,
                },
              ],
              name: 'item_190243974690',
              id: 140_570_339_791,
            },
            id: 189_409_337_476,
            name: 'item_126634593870',
          },
        ],
        name: 'item_124288541944',
        syncOptions: {},
        id: 105_018_143_472,
      },
      id: 198_732_333_771,
      syncOptions: {},
    },
    {
      name: 'item_184439512837',
      component: 'LuckySheet',
      schema: {
        rules: [
          {
            required: false,
            message: '必填项',
          },
        ],
        title: 'LuckySheet',
        name: 'item_102796702308',
        value: [
          {
            name: 'Sheet1',
            color: '',
            row: 40,
            column: 30,
            index: 0,
            status: 1,
            order: 1,
            celldata: [],
            config: {},
            jfgird_select_save: [],
            luckysheet_select_save: [
              {
                left: 74,
                width: 73,
                top: 480,
                height: 19,
                left_move: 74,
                width_move: 73,
                top_move: 480,
                height_move: 19,
                row: [24, 24],
                column: [1, 1],
                row_focus: 24,
                column_focus: 1,
              },
            ],
            data: [
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                {
                  v: 22_222_222_222,
                  ct: {
                    fa: 'General',
                    t: 'n',
                  },
                  m: '22222222222',
                },
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                {
                  m: 'aa啊啊啊啊啊啊啊啊啊啊啊',
                  ct: {
                    fa: 'General',
                    t: 'g',
                  },
                  v: 'aa啊啊啊啊啊啊啊啊啊啊啊',
                },
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
              [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              ],
            ],
            load: '1',
            visibledatarow: [
              20, 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280,
              300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540,
              560, 580, 600, 620, 640, 660, 680, 700, 720, 740, 760, 780, 800,
            ],
            visibledatacolumn: [
              74, 148, 222, 296, 370, 444, 518, 592, 666, 740, 814, 888, 962,
              1036, 1110, 1184, 1258, 1332, 1406, 1480, 1554, 1628, 1702, 1776,
              1850, 1924, 1998, 2072, 2146, 2220,
            ],
            ch_width: 2340,
            rh_height: 880,
            luckysheet_selection_range: [],
            zoomRatio: 1,
          },
        ],
        isDisabled: false,
        isShow: true,
        isReadonly: false,
        syncOptions: {},
        id: 189_664_950_712,
      },
      id: 173_336_599_042,
      syncOptions: {},
    },
  ];

  // setFormData(formData.value);
  // setTimeout(() => {
  formConfig.value = list;
};

onMounted(() => {
  click();
});
</script>

<template>
  <Page description="Excel导入导出示例" title="Excel">
    <Form ref="form" :data="formData">
      <FormRender
        v-model="formData"
        :form-config="formConfig"
        class="bg-background w-full"
      />
    </Form>

    <pre>
      {{ JSON.stringify(formData, null, 2) }}
    </pre>
  </Page>
</template>
