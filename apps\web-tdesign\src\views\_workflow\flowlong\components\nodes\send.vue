<script setup>
import { CloseIcon, EditIcon, SendIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  Drawer,
  Form,
  FormItem,
  Input,
  Space,
  Tag,
} from 'tdesign-vue-next';
</script>
<script>
import addNode from './addNode.vue';

export default {
  components: {
    AddNode: addNode,
  },
  inject: ['select'],
  props: {
    modelValue: { type: Object, default: () => {} },
  },
  data() {
    return {
      nodeConfig: {},
      drawer: false,
      isEditTitle: false,
      form: {},
    };
  },
  watch: {
    modelValue() {
      this.nodeConfig = this.modelValue;
    },
  },
  mounted() {
    this.nodeConfig = this.modelValue;
  },
  methods: {
    show() {
      this.form = {};
      this.form = JSON.parse(JSON.stringify(this.nodeConfig));
      this.drawer = true;
    },
    editTitle() {
      this.isEditTitle = true;
      this.$nextTick(() => {
        this.$refs.nodeTitle.focus();
      });
    },
    saveTitle() {
      this.isEditTitle = false;
    },
    save() {
      this.$emit('update:modelValue', this.form);
      this.drawer = false;
    },
    delNode() {
      this.$emit('update:modelValue', this.nodeConfig.childNode);
    },
    delUser(index) {
      this.form.nodeAssigneeList.splice(index, 1);
    },
    selectHandle(type, data) {
      this.select(type, data);
    },
    toText(nodeConfig) {
      if (
        nodeConfig.nodeAssigneeList &&
        nodeConfig.nodeAssigneeList.length > 0
      ) {
        const users = nodeConfig.nodeAssigneeList
          .map((item) => item.name)
          .join('、');
        return users;
      } else {
        return nodeConfig.userSelectFlag ? '发起人自选' : false;
      }
    },
  },
};
</script>
<template>
  <div class="node-wrap">
    <div class="node-wrap-box" @click="show">
      <div class="title" style="background: #3296fa">
        <SendIcon class="icon" />
        <span>{{ nodeConfig.nodeName }}</span>

        <CloseIcon class="close" @click="delNode()" />
      </div>
      <div class="content">
        <span v-if="toText(nodeConfig)">{{ toText(nodeConfig) }}</span>
        <span v-else class="placeholder">请选择人员</span>
      </div>
    </div>
    <AddNode v-model="nodeConfig.childNode" />
    <Drawer
      v-model:visible="drawer"
      :size="500"
      attach="body"
      title="抄送人设置"
    >
      <template #header>
        <div class="node-wrap-drawer__title">
          <label v-if="!isEditTitle" @click="editTitle"
            >{{ form.nodeName }}<EditIcon
          /></label>
          <Input
            v-if="isEditTitle"
            ref="nodeTitle"
            v-model="form.nodeName"
            clearable
            @blur="saveTitle"
            @enter="saveTitle"
          />
        </div>
      </template>

      <div style="padding: 0 20px 20px">
        <Form label-align="top">
          <FormItem label="选择要抄送的人员">
            <Button
              type="primary"
              @click="selectHandle(1, form.nodeAssigneeList)"
            >
              选择人员
            </Button>
          </FormItem>
          <div class="tags-list">
            <Space>
              <Tag
                v-for="(user, index) in form.nodeAssigneeList"
                :key="user.id"
                closable
                @close="delUser(index)"
              >
                {{ user.name }}
              </Tag>
            </Space>
          </div>
          <FormItem label="">
            <Checkbox
              v-model="form.userSelectFlag"
              label="允许发起人自选抄送人"
            />
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button type="primary" @click="save">保存</Button>
        <Button @click="drawer = false">取消</Button>
      </template>
    </Drawer>
  </div>
</template>
