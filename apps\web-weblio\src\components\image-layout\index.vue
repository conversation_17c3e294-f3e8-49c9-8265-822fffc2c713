<script setup lang="ts">
import { computed, shallowRef, watchEffect } from 'vue';

// 数据项接口
interface DataItem {
  id?: number;
  title: string;
  description: string;
  date: string;
}

// Tab接口
interface TabItem {
  key: string;
  label: string;
  data: DataItem[];
}

// Props接口
interface ImageLayoutProps {
  title: string;
  mainImage: string;
  tabs: TabItem[];
  imagePosition?: 'left' | 'right';
  placeholderImage?: string;
}

// 事件接口
interface ImageLayoutEmits {
  itemClick: [item: DataItem];
  tabChange: [tabKey: string];
  moreClick: [];
}

const props = withDefaults(defineProps<ImageLayoutProps>(), {
  imagePosition: 'left',
  placeholderImage: '/static/images/placeholder.jpg',
});

const emit = defineEmits<ImageLayoutEmits>();

// 当前激活的tab - 使用 shallowRef 提升性能
const activeTab = shallowRef('');

// 初始化 activeTab - 避免在模板中计算
watchEffect(() => {
  if (props.tabs.length > 0 && !activeTab.value) {
    activeTab.value = props.tabs[0]?.key || '';
  }
});

// 当前tab的数据 - 使用计算属性缓存
const currentTabData = computed(() => {
  const currentTab = props.tabs.find((tab) => tab.key === activeTab.value);
  return currentTab?.data || [];
});

// 缓存日期格式化结果 - 避免重复计算
const dateFormatCache = new Map<string, { day: string; yearMonth: string }>();

// 优化的格式化日期显示函数
const formatDateDisplay = (dateString: string) => {
  // 先检查缓存
  if (dateFormatCache.has(dateString)) {
    return dateFormatCache.get(dateString)!;
  }

  let result: { day: string; yearMonth: string };

  try {
    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) {
      result = { day: '--', yearMonth: '--/--' };
    } else {
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      result = { day, yearMonth: `${year}/${month}` };
    }
  } catch {
    result = { day: '--', yearMonth: '--/--' };
  }

  // 缓存结果，限制缓存大小
  if (dateFormatCache.size > 100) {
    // 清理一半的缓存
    const entries = [...dateFormatCache.entries()];
    dateFormatCache.clear();
    entries.slice(-50).forEach(([key, value]) => {
      dateFormatCache.set(key, value);
    });
  }

  dateFormatCache.set(dateString, result);
  return result;
};

// 预计算所有数据项的格式化日期 - 批量处理
const formattedDataItems = computed(() => {
  return currentTabData.value.map((item) => ({
    ...item,
    formattedDate: formatDateDisplay(item.date),
  }));
});

// 布局配置 - 避免在模板中重复判断
const layoutConfig = computed(() => ({
  isImageLeft: props.imagePosition === 'left',
  isImageRight: props.imagePosition === 'right',
  containerClass: props.imagePosition === 'right' ? 'image-right' : '',
}));

// 切换tab - 优化事件处理
const switchTab = (tabKey: string) => {
  if (activeTab.value !== tabKey) {
    activeTab.value = tabKey;
    emit('tabChange', tabKey);
  }
};

// 处理图片加载错误 - 添加防抖
let imageErrorTimeout: NodeJS.Timeout | null = null;
const handleImageError = (event: Event) => {
  if (imageErrorTimeout) return;

  imageErrorTimeout = setTimeout(() => {
    const target = event.target as HTMLImageElement;
    if (target.src !== props.placeholderImage) {
      target.src = props.placeholderImage;
    }
    imageErrorTimeout = null;
  }, 100);
};

// 处理项目点击 - 优化事件处理
const handleItemClick = (item: DataItem) => {
  emit('itemClick', item);
};

// 处理更多按钮点击
const handleMoreClick = () => {
  emit('moreClick');
};

// 导出类型
export type { DataItem, TabItem };
</script>

<template>
  <div class="image-layout-container">
    <div class="layout-content" :class="layoutConfig.containerClass">
      <!-- 图片部分组件 -->
      <div v-if="layoutConfig.isImageLeft" class="left-section image-section">
        <h2 class="section-title">{{ title }}</h2>
        <div class="main-image-container">
          <img
            :src="mainImage"
            :alt="title"
            class="main-image"
            loading="lazy"
            @error="handleImageError"
          />
        </div>
      </div>

      <!-- 数据部分组件 -->
      <div
        class="data-section"
        :class="[layoutConfig.isImageLeft ? 'right-section' : 'left-section']"
      >
        <!-- Tabs切换 -->
        <div class="tabs-container">
          <div class="tabs">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              class="tab-button"
              :class="{ active: activeTab === tab.key }"
              @click="() => switchTab(tab.key)"
            >
              {{ tab.label }}
            </button>
            <div class="more-button">
              <button class="more-btn" @click="handleMoreClick">More</button>
            </div>
          </div>
        </div>

        <!-- 数据列表 - 使用优化的数据 -->
        <div class="data-list">
          <div
            v-for="(item, index) in formattedDataItems"
            :key="item.id || `item-${index}`"
            class="data-item"
            @click="() => handleItemClick(item)"
          >
            <div class="item-number">
              <div class="day-number">
                {{ item.formattedDate.day }}
              </div>
              <div class="year-month">
                {{ item.formattedDate.yearMonth }}
              </div>
            </div>
            <div class="item-content">
              <h3 class="item-title">{{ item.title }}</h3>
              <p class="item-description">{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 图片部分组件 (右侧位置) -->
      <div v-if="layoutConfig.isImageRight" class="right-section image-section">
        <h2 class="section-title">{{ title }}</h2>
        <div class="main-image-container">
          <img
            :src="mainImage"
            :alt="title"
            class="main-image"
            loading="lazy"
            @error="handleImageError"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 统一box-sizing设置，避免遮挡问题 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

.image-layout-container {
  width: 100%;
  background: transparent;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  padding: 24px 16px;
  box-sizing: border-box;
  max-width: 100vw;
}

.layout-content {
  display: flex;
  min-height: 500px;
  height: 500px;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
}

/* 基础区域样式 */
.left-section,
.right-section {
  flex: 0 0 50%;
  max-width: 50%;
  box-sizing: border-box;
}

/* 图片区域样式 */
.image-section {
  background: rgba(25, 55, 109, 0.8);
  background-image: url('/static/images/home_bg_1.jpg');
  background-size: cover;
  background-position: center;
  padding: 56px 48px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 数据区域样式 */
.data-section {
  display: flex;
  flex-direction: column;
  background: #f7fafc;
  min-width: 0;
}

.image-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(25, 55, 109, 0.85);
  pointer-events: none;
}

.section-title {
  color: #ffffff;
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 48px 0;
  text-align: center;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.main-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.main-image {
  width: 100%;
  height: 380px;
  object-fit: cover;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.main-image:hover {
  transform: scale(1.02);
}

.tabs-container {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 0 12px;
  box-sizing: border-box;
  flex-shrink: 0;
}

.tabs {
  display: flex;
  align-items: center;
  padding: 0 40px;
  gap: 12px;
  box-sizing: border-box;
  flex-wrap: nowrap;
  max-width: 100%;
}

.tab-button {
  padding: 12px 24px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.9);
  color: #718096;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.tab-button:hover {
  color: #3182ce;
  background: #ffffff;
  border-color: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.2);
}

.tab-button.active {
  color: #ffffff;
  background: #3182ce;
  border-color: #3182ce;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(49, 130, 206, 0.3);
}

.more-button {
  margin-left: auto;
}

.more-btn {
  padding: 12px 24px;
  border: 1px solid #3182ce;
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  color: #ffffff;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(49, 130, 206, 0.25);
}

.more-btn:hover {
  background: linear-gradient(135deg, #2c5aa0 0%, #2a4d96 100%);
  border-color: #2c5aa0;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(49, 130, 206, 0.35);
}

.more-btn:active {
  transform: translateY(0);
}

.data-list {
  flex: 1;
  padding: 40px;
  background: #ffffff;
  box-sizing: border-box;
  min-height: 0;
  min-width: 0;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 200px);
  width: 100%;
}

.data-item {
  display: flex;
  align-items: flex-start;
  padding: 28px;
  margin-bottom: 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 28px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  min-width: 0;
  width: 100%;
}

.data-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #3182ce, #2c5aa0);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.data-item:hover {
  background: #f7fafc;
  border-color: #3182ce;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(49, 130, 206, 0.15);
}

.data-item:hover::before {
  opacity: 1;
}

.data-item:last-child {
  margin-bottom: 0;
}

.item-number {
  flex: 0 0 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  text-align: center;
}

.day-number {
  font-size: 48px;
  font-weight: 300;
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 4px;
}

.year-month {
  font-size: 14px;
  font-weight: 500;
  color: #718096;
  line-height: 1;
}

.item-content {
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.item-title {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
  line-height: 1.4;
  transition: color 0.3s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.data-item:hover .item-title {
  color: #3182ce;
}

.item-description {
  font-size: 15px;
  color: #718096;
  line-height: 1.6;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 滚动条样式 - 与系统风格一致 */
.data-list::-webkit-scrollbar {
  width: 6px;
}

.data-list::-webkit-scrollbar-track {
  background: #edf2f7;
  border-radius: 3px;
}

.data-list::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3182ce, #2c5aa0);
  border-radius: 3px;
}

.data-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2c5aa0, #2a4d96);
}

/* 响应式设计 - 调整间距 */
@media (max-width: 1024px) {
  .image-layout-container {
    padding: 20px 12px;
  }

  .layout-content {
    flex-direction: column;
    height: auto;
  }

  .layout-content.image-right {
    flex-direction: column;
  }

  /* 在窄屏时，确保图片区域总是在上方 */
  .layout-content.image-right .left-section.data-section {
    order: 2;
  }

  .layout-content.image-right .right-section.image-section {
    order: 1;
  }

  .left-section,
  .right-section {
    flex: none;
    max-width: 100%;
  }

  .image-section {
    min-height: 320px;
    padding: 40px 32px;
  }

  .main-image {
    height: 240px;
  }

  .tabs {
    flex-wrap: wrap;
    padding: 0 24px;
  }

  .data-list {
    padding: 32px 24px;
  }
}

@media (max-width: 768px) {
  .image-layout-container {
    padding: 16px 8px;
  }

  .left-section,
  .right-section {
    flex: none;
    max-width: 100%;
  }

  .image-section {
    padding: 32px 24px;
  }

  .section-title {
    font-size: 28px;
    margin-bottom: 32px;
  }

  .main-image-container {
    border-radius: 6px;
  }

  .main-image {
    border-radius: 4px;
  }

  .data-item {
    padding: 24px;
    margin-bottom: 16px;
    gap: 20px;
    border-radius: 6px;
  }

  .item-number {
    flex: 0 0 60px;
  }

  .day-number {
    font-size: 36px;
  }

  .year-month {
    font-size: 12px;
  }

  .item-title {
    font-size: 18px;
  }

  .item-description {
    font-size: 14px;
  }

  .tab-button {
    padding: 12px 24px;
    font-size: 14px;
    border-radius: 4px;
  }

  .more-btn {
    padding: 12px 24px;
    font-size: 13px;
    border-radius: 4px;
  }

  .tabs-container {
    padding: 16px 0 12px;
  }

  .data-list {
    padding: 28px 20px;
  }
}

@media (max-width: 480px) {
  .image-layout-container {
    padding: 12px 4px;
  }

  .tabs {
    gap: 8px;
    padding: 0 16px;
  }

  .tab-button {
    padding: 10px 20px;
    font-size: 13px;
    min-width: auto;
  }

  .more-btn {
    padding: 10px 20px;
    font-size: 12px;
  }

  .data-item {
    padding: 20px;
    gap: 16px;
  }

  .item-number {
    flex: 0 0 50px;
  }

  .day-number {
    font-size: 28px;
  }

  .year-month {
    font-size: 10px;
  }

  .section-title {
    font-size: 24px;
    margin-bottom: 28px;
  }

  .left-section,
  .right-section {
    flex: none;
    max-width: 100%;
  }

  .image-section {
    padding: 28px 20px;
  }

  .data-list {
    padding: 24px 16px;
  }
}
</style>
