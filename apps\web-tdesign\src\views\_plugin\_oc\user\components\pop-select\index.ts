import popselect from './pop-select.vue';

const withInstall = <T>(component: T, alias?: string) => {
  (component as Record<string, unknown>).install = (app: App) => {
    const compName = component?.name || component?.displayName;
    if (!compName) return;
    app.component(compName, component);
    if (alias) {
      app.config.globalProperties[alias] = component;
    }
  };
  return component as any;
};
export const UserPopSelect = withInstall(popselect);
