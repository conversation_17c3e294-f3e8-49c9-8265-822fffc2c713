<script setup lang="ts">
import {
  BookIcon,
  CheckCircleIcon,
  DataBaseIcon,
  FileIcon,
  InfoCircleIcon,
  LinkIcon,
  LockOnIcon,
  SearchIcon,
  SettingIcon,
  StarIcon,
  ToolsIcon,
  UserIcon,
} from 'tdesign-icons-vue-next';
import { onMounted, onUnmounted, ref } from 'vue';

// 页面数据
const guideData = ref({
  overview: {
    title: '石化化工数据中心概述',
    description:
      '石化化工数据中心是大连化学物理研究所旗下的数据库，包含超过1亿种化合物的结构信息、石化化工行业数据和特色数据等。',
    subDatabases: [
      { name: '基础数据库', examples: '化合物结构、理化性质' },
      { name: '文献库', examples: '期刊论文、专利文献' },
      { name: '行业数据库', examples: '生产工艺、设备参数' },
      { name: '特色数据库', examples: '安全数据、环保数据' },
    ],
  },
  searchMethods: {
    title: '检索方式',
    methods: [
      {
        name: '关键词检索',
        description: '通过输入化合物名称、分子式等关键词进行检索',
        example: '示例: 输入"aspirin"可获取阿司匹林的相关信息',
        placeholder: '请输入关键词...',
      },
      {
        name: '结构式检索',
        description: '通过绘制化合物结构或输入SMILES表达式进行检索',
        steps: ['绘制化合物结构', '或输入SMILES表达式', '点击检索按钮'],
      },
      {
        name: '高级检索',
        description: '使用AND、OR、NOT等运算符进行复杂条件检索',
      },
    ],
  },
  searchResults: {
    title: '检索结果详情',
    steps: [
      {
        number: 1,
        title: '结果列表',
        description:
          '检索结果以列表形式展示,包含化合物名称、结构示意图和基本信息',
      },
      {
        number: 2,
        title: '进入详情页',
        description: '点击化合物名称或结构示意图进入详情页面',
      },
      {
        number: 3,
        title: '详情页面内容',
        description: '查看详细信息',
        tags: [
          '基本信息',
          '化学结构',
          '理化性质',
          '编码信息',
          '化学反应',
          '安全信息',
          '相关文献',
          '右侧信息目录',
        ],
      },
    ],
  },
});

// 滚动监听
const visibleSections = ref(new Set());
const observer = ref<IntersectionObserver | null>(null);

onMounted(() => {
  // 创建交叉观察器
  observer.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          visibleSections.value.add(entry.target.id);
        }
      });
    },
    {
      threshold: 0.3,
      rootMargin: '-50px 0px',
    },
  );

  // 观察所有section
  const sections = ['overview', 'search-methods', 'search-results'];
  sections.forEach((section) => {
    const element = document.querySelector(`#${section}`);
    if (element) {
      observer.value?.observe(element);
    }
  });
});

onUnmounted(() => {
  observer.value?.disconnect();
});
</script>

<template>
  <div class="user-guide-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据库使用指南</h1>
      <p class="page-subtitle">了解如何使用石化化工数据中心进行高效检索</p>
    </div>

    <!-- 石化化工数据中心概述 -->
    <section id="overview" class="guide-section">
      <div
        class="guide-card"
        :class="{ 'fade-in': visibleSections.has('overview') }"
      >
        <div class="card-header">
          <div class="header-icon">
            <DataBaseIcon size="24" />
          </div>
          <h2 class="card-title">{{ guideData.overview.title }}</h2>
        </div>

        <div class="card-content">
          <p class="description">{{ guideData.overview.description }}</p>

          <div class="sub-databases">
            <div class="sub-title">
              <div class="sub-icon">
                <ToolsIcon size="20" />
              </div>
              <span>四个子库</span>
            </div>
            <div class="database-list">
              <div
                v-for="(db, index) in guideData.overview.subDatabases"
                :key="index"
                class="database-item"
              >
                <div class="db-icon">
                  <DataBaseIcon v-if="db.name === '基础数据库'" size="16" />
                  <FileIcon v-else-if="db.name === '文献库'" size="16" />
                  <ToolsIcon v-else-if="db.name === '行业数据库'" size="16" />
                  <InfoCircleIcon v-else size="16" />
                </div>
                <div class="db-content">
                  <span class="db-name">{{ db.name }}</span>
                  <span class="db-examples">({{ db.examples }})</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 检索方式 -->
    <section id="search-methods" class="guide-section">
      <div
        class="guide-card"
        :class="{ 'fade-in': visibleSections.has('search-methods') }"
      >
        <div class="card-header">
          <div class="header-icon">
            <SearchIcon size="24" />
          </div>
          <h2 class="card-title">{{ guideData.searchMethods.title }}</h2>
          <div class="header-badge">
            <InfoCircleIcon size="16" />
          </div>
        </div>

        <div class="card-content">
          <div class="search-methods-grid">
            <div
              v-for="(method, index) in guideData.searchMethods.methods"
              :key="index"
              class="method-item"
            >
              <div class="method-header">
                <div class="method-icon">
                  <SearchIcon v-if="method.name === '关键词检索'" size="20" />
                  <ToolsIcon
                    v-else-if="method.name === '结构式检索'"
                    size="20"
                  />
                  <SettingIcon v-else size="20" />
                </div>
                <h3 class="method-title">{{ method.name }}</h3>
                <div v-if="method.name === '高级检索'" class="method-badge">
                  <InfoCircleIcon size="16" />
                </div>
              </div>

              <div class="method-content">
                <p class="method-description">{{ method.description }}</p>

                <!-- 关键词检索示例 -->
                <div v-if="method.example" class="method-example">
                  <p class="example-text">{{ method.example }}</p>
                  <div class="example-input">
                    <input
                      type="text"
                      :placeholder="method.placeholder"
                      class="search-input"
                    />
                  </div>
                </div>

                <!-- 结构式检索步骤 -->
                <div v-if="method.steps" class="method-steps">
                  <div
                    v-for="(step, stepIndex) in method.steps"
                    :key="stepIndex"
                    class="step-item"
                  >
                    <div class="step-icon">
                      <CheckCircleIcon size="14" />
                    </div>
                    <span class="method-step-number">{{ stepIndex + 1 }}.</span>
                    <span class="step-text">{{ step }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 检索结果详情 -->
    <section id="search-results" class="guide-section">
      <div
        class="guide-card"
        :class="{ 'fade-in': visibleSections.has('search-results') }"
      >
        <div class="card-header">
          <div class="header-icon">
            <FileIcon size="24" />
          </div>
          <h2 class="card-title">{{ guideData.searchResults.title }}</h2>
          <div class="header-badge">
            <InfoCircleIcon size="16" />
          </div>
        </div>

        <div class="card-content">
          <div class="results-flow">
            <div
              v-for="(step, index) in guideData.searchResults.steps"
              :key="index"
              class="flow-step"
              :style="{ animationDelay: `${index * 0.2}s` }"
            >
              <div class="step-circle">
                <span>{{ step.number }}</span>
              </div>

              <div class="step-content">
                <h3 class="step-title">{{ step.title }}</h3>
                <p class="step-description">{{ step.description }}</p>

                <!-- 详情页面标签 -->
                <div v-if="step.tags" class="step-tags">
                  <div
                    v-for="(tag, tagIndex) in step.tags"
                    :key="tagIndex"
                    class="tag-item"
                  >
                    <div class="tag-icon">
                      <UserIcon v-if="tag === '基本信息'" size="12" />
                      <ToolsIcon v-else-if="tag === '化学结构'" size="12" />
                      <StarIcon v-else-if="tag === '理化性质'" size="12" />
                      <DataBaseIcon v-else-if="tag === '编码信息'" size="12" />
                      <BookIcon v-else-if="tag === '化学反应'" size="12" />
                      <LockOnIcon v-else-if="tag === '安全信息'" size="12" />
                      <FileIcon v-else-if="tag === '相关文献'" size="12" />
                      <LinkIcon v-else size="12" />
                    </div>
                    <span class="tag-text">{{ tag }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页面底部 -->
    <div class="page-footer">
      <div class="footer-content">
        <div class="footer-icon">
          <CheckCircleIcon size="24" />
        </div>
        <p class="footer-text">
          通过以上教程，您可以充分利用数据库进行化学信息检索和分析。
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.user-guide-page {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 32px 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 0 32px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.guide-section {
  margin-bottom: 32px;
  padding: 0 32px;
}

.guide-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.7s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: 1px solid #f0f0f0;

  &.fade-in {
    opacity: 1;
    transform: translateY(0);
  }

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8f5ff;
  background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
  margin: -32px -32px 24px -32px;
  padding: 24px 32px 16px 32px;
  border-radius: 12px 12px 0 0;
}

.header-icon {
  margin-right: 16px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--td-brand-color-hover);
  color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  flex: 1;
}

.header-badge {
  color: var(--td-brand-color-hover);
  margin-left: 8px;
}

.card-content {
  color: #333;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 24px;
  color: #555;
}

.sub-databases {
  margin-top: 24px;
}

.sub-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #1a1a1a;
  font-size: 1.1rem;
}

.sub-icon {
  margin-right: 8px;
  color: var(--td-brand-color-hover);
}

.database-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.database-item {
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--td-brand-color-hover);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;

  &:hover {
    background: #e8f5ff;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
  }
}

.db-icon {
  color: var(--td-brand-color-hover);
  flex-shrink: 0;
}

.db-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.db-name {
  font-weight: 600;
  color: #1a1a1a;
  margin-right: 8px;
}

.db-examples {
  color: #666;
  font-size: 0.9rem;
}

// 检索方式样式
.search-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.method-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--td-brand-color-hover);
  }
}

.method-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.method-icon {
  margin-right: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--td-brand-color-hover);
  color: white;
  border-radius: 8px;
}

.method-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  flex: 1;
}

.method-badge {
  color: var(--td-brand-color-hover);
}

.method-content {
  color: #555;
}

.method-description {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 16px;
}

.method-example {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.example-text {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 12px;
  font-style: italic;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  outline: none;
  transition: border-color 0.3s ease;

  &:focus {
    border-color: var(--td-brand-color-hover);
  }
}

.method-steps {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.step-icon {
  color: var(--td-brand-color-hover);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.method-step-number {
  font-weight: 600;
  color: var(--td-brand-color-hover);
  margin-right: 8px;
  min-width: 20px;
  line-height: 1;
  display: flex;
  align-items: center;
}

.step-text {
  font-size: 0.9rem;
  color: #555;
  line-height: 1.4;
  flex: 1;
}

// 检索结果详情样式
.results-flow {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.flow-step {
  display: flex;
  align-items: flex-start;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}

.step-circle {
  width: 48px;
  height: 48px;
  background: var(--td-brand-color-hover);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin-right: 20px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);

  span {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    text-align: center;
  }
}

.step-content {
  background: #f0f8ff;
  border-radius: 12px;
  padding: 20px;
  flex: 1;
  border: 1px solid #e8f5ff;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--td-brand-color-hover);
  margin: 0 0 8px 0;
}

.step-description {
  font-size: 0.95rem;
  color: #555;
  line-height: 1.5;
  margin-bottom: 16px;
}

.step-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  background: white;
  color: var(--td-brand-color-hover);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e8f5ff;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    background: var(--td-brand-color-hover);
    color: white;
    transform: scale(1.05);
  }
}

.tag-icon {
  color: inherit;
  flex-shrink: 0;
}

.tag-text {
  font-size: 0.85rem;
  font-weight: 500;
}

// 页面底部
.page-footer {
  text-align: center;
  margin-top: 48px;
  padding: 0 32px;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.footer-icon {
  color: var(--td-brand-color-hover);
}

.footer-text {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

// 动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-guide-page {
    padding: 16px 0;
  }

  .page-header {
    margin-bottom: 32px;
    padding: 0 16px;
  }

  .page-title {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .guide-section {
    padding: 0 16px;
  }

  .guide-card {
    padding: 24px;
    margin: 0 8px;
  }

  .card-header {
    margin: -24px -24px 20px -24px;
    padding: 20px 24px 12px 24px;
  }

  .search-methods-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .database-list {
    grid-template-columns: 1fr;
  }

  .results-flow {
    gap: 16px;
  }

  .flow-step {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .step-circle {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .step-content {
    width: 100%;
  }

  .step-tags {
    justify-content: center;
  }

  .footer-content {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }

  .card-title {
    font-size: 1.25rem;
  }

  .guide-card {
    padding: 20px;
    margin: 0 4px;
  }

  .card-header {
    margin: -20px -20px 16px -20px;
    padding: 16px 20px 12px 20px;
  }

  .header-icon {
    width: 40px;
    height: 40px;
  }

  .method-item {
    padding: 20px;
  }

  .step-circle {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}
</style>
