window.sheetComment = {
	"name": "Comment",
	"color": "",
	"config": {
		"columnlen": {
			"2": 102
		}
	},
	"index": "5",
	"chart": [],
	"status": 0,
	"order": "5",
	"column": 18,
	"row": 36,
	"celldata": [{
		"r": 2,
		"c": 2,
		"v": {
			"m": "HoverShown",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "HoverShown",
			"bl": 1,
			"ps": {
				"left": null,
				"top": null,
				"width": null,
				"height": null,
				"value": "Hello world!",
				"isshow": false
			}
		}
	}, {
		"r": 7,
		"c": 2,
		"v": {
			"m": "Size",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Size",
			"bl": 1,
			"ps": {
				"left": null,
				"top": null,
				"width": null,
				"height": null,
				"value": "Hello,world!",
				"isshow": true
			}
		}
	}],
	"ch_width": 4748,
	"rh_height": 1790,
	"luckysheet_select_save": [{
		"row": [0, 0],
		"column": [0, 0]
	}],
	"luckysheet_selection_range": [],
	"scrollLeft": 0,
	"scrollTop": 0
}

// export default sheetComment;