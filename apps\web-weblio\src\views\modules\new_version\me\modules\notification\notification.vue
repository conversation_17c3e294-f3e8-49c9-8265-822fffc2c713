<script lang="ts" setup>
import type { NotificationItem } from './types';

import { $t } from '@vben/locales';
import { Button } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';

defineOptions({ name: 'NotificationPopup' });
withDefaults(defineProps<Props>(), {
  dot: false,
  notifications: () => [],
});
const emit = defineEmits<{
  clear: [];
  makeAll: [];
  read: [NotificationItem];
  viewAll: [];
}>();
const router = useRouter();
interface Props {
  /**
   * 显示圆点
   */
  dot?: boolean;
  /**
   * 消息列表
   */
  notifications?: NotificationItem[];
}

function handleViewAll() {
  emit('viewAll');
  router.push('/MyMessage');
}

function handleClick(item: NotificationItem) {
  emit('read', item);
}

async function handleClear() {
  emit('clear');
  // await deleteAll();
}

// async function handleMakeAll() {
//   await updateAllRead({});

//   // notifications.value.forEach((item) => (item.isRead = true));
// }
</script>
<template>
  <div class="relative">
    <div class="msgBox" v-if="notifications.length > 0">
      <ul class="!flex max-h-[360px] w-full flex-col">
        <template v-for="item in notifications" :key="item.title">
          <li
            class="hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3"
            @click="handleClick(item)"
          >
            <span
              v-if="!item.isRead"
              class="bg-primary absolute right-2 top-2 h-2 w-2 rounded"
            ></span>

            <span
              class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"
            >
              <img
                :src="item.avatar"
                class="aspect-square h-full w-full object-cover"
                role="img"
              />
            </span>
            <div class="flex flex-col gap-1 leading-none">
              <p class="font-semibold">{{ item.title }}</p>
              <p class="text-muted-foreground my-1 line-clamp-2 text-xs">
                {{ item.message }}
              </p>
              <p class="text-muted-foreground line-clamp-2 text-xs">
                {{ item.date }}
              </p>
            </div>
          </li>
        </template>
      </ul>
    </div>

    <template v-else>
      <div class="flex-center text-muted-foreground min-h-[150px] w-full">
        {{ $t('common.noData') }}
      </div>
    </template>

    <div
      class="border-border flex items-center justify-between border-t px-4 py-3"
    >
      <Button
        :disabled="notifications.length <= 0"
        size="sm"
        variant="ghost"
        @click="handleClear"
      >
        {{ $t('ui.widgets.clearNotifications') }}
      </Button>
      <Button size="sm" @click="handleViewAll">
        {{ $t('ui.widgets.viewAll') }}
      </Button>
    </div>
  </div>
</template>

<style scoped>
.msgBox {
  overflow-y: auto;
}
:deep(.bell-button) {
  &:hover {
    svg {
      animation: bell-ring 1s both;
    }
  }
}
.bg-header button span {
  background: white !important;
}
@keyframes bell-ring {
  0%,
  100% {
    transform-origin: top;
  }

  15% {
    transform: rotateZ(10deg);
  }

  30% {
    transform: rotateZ(-10deg);
  }

  45% {
    transform: rotateZ(5deg);
  }

  60% {
    transform: rotateZ(-5deg);
  }

  75% {
    transform: rotateZ(2deg);
  }
}
</style>
