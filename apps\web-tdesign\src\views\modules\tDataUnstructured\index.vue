<script setup lang="tsx">
import {ref} from 'vue';

import {useVbenModal} from '@vben/common-ui';

import EditForm from './components/EditForm.vue';
import IndexTable from './components/IndexTable.vue';
import EditItemTable from './components/EditItemTable.vue';
import Relevance from './components/Relevance.vue';

const editFormRef = ref();
const editItemTableRef = ref();
const tableRef = ref();
const [EditItemTableModel, editItemModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: EditItemTable,
});
const [RelevanceModel, RelevanceModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: Relevance,
});
const showEditItem = (record: any) => {
  editItemModalApi.setData({
    tagObj: record,
  });
  editItemModalApi.open();
};
const showRelevance = (record: any) => {
  RelevanceModalApi.setData({
    tagObj: record,
  });
  RelevanceModalApi.open();
};

</script>

<template>
  <Page title="非结构化数据">
      <EditForm ref="editFormRef" :out-ref="tableRef"/>
      <IndexTable ref="tableRef" :edit-form-ref="editFormRef" :editItemTableRef="editItemTableRef"
                  @show-item="showEditItem"/>
      <EditItemTableModel :is-search-form="false" @show-relevance="showRelevance"/>
      <RelevanceModel :is-search-form="false"/>
  </Page>
</template>
<style scoped>
</style>
