<script lang="ts">
import { EventBus, offEvent, onEvent } from '#/utils/eventBus.ts';
import { defineComponent, ref } from 'vue';
import draggable from 'vuedraggable';

import { WidgetConfig } from '../config/WidgetConfig';

export default defineComponent({
  name: 'WidgetList',
  components: {
    Draggable: draggable,
  },
  props: {
    eventPrefix: {
      type: String,
      default: '',
    },
    dragGroup: {
      type: String,
      default: () => 'dbox',
    },
  },
  emits: ['itemClick', 'reset'],
  setup(props, { emit }) {
    const defHandler = {};
    const eventHandler = {} as any;
    const makeEventHandler = () => {
      Object.keys(defHandler).forEach((item) => {
        eventHandler[`${props.eventPrefix}${item}`] = defHandler[item];
      });
    };
    const makeId = () => {
      return Math.floor(Math.random() * 100_000_000_000) + 100_000_000_000;
    };
    const widgetList = ref(WidgetConfig || []);
    const cloneItem = (item) => {
      const tmpTime = makeId();
      const tmpData = {
        ...item,
        id: tmpTime,
        name: `item_${makeId()}`,
        schema: {
          ...item.schema,
          name: `item_${tmpTime}`,
          syncOptions: item.syncOptions || {},
          id: makeId(),
        },
      };
      setTimeout(() => {
        emit('reset', tmpData);
      }, 50);
      return tmpData;
    };
    const reactNameAndId = (item: any) => {
      item.id = makeId();
      item.name = `item_${makeId()}`;
      item.schema.name = `item_${makeId()}`;
      item.schema.id = makeId();
      if (item.schema?.children?.length >= 0) {
        item.schema.children.forEach((ii: any) => {
          reactNameAndId(ii);
        });
      }
    };
    // ${props.eventPrefix}
    const itemClick = (item) => {
      const tmpTime = Date.now();
      const tmpData = {
        ...item,
        id: tmpTime,
        schema: {
          ...item.schema,
          name: `${item.component || 'item'}_${tmpTime}`,
          syncOptions: item.syncOptions || {},
        },
      };
      reactNameAndId(tmpData);
      EventBus.emit(
        `widgetList_itemClick`,
        JSON.parse(JSON.stringify(tmpData)),
      );
      // emit('itemClick', { ...item, id: Date.now() });
    };
    const tagMoveEnd = (item: any) => {
      console.info('tagMoveEnd', item);
      EventBus.emit(`widgetList_itemClick`);
    };
    return {
      widgetList,
      itemClick,
      cloneItem,
      eventHandler,
      makeEventHandler,
      tagMoveEnd,
    };
  },
  mounted() {
    this.makeEventHandler();
    onEvent(this.eventHandler);
  },
  unmounted() {
    this.makeEventHandler();
    offEvent(this.eventHandler);
  },
});
</script>

<template>
  <Draggable
    v-model="widgetList"
    :clone="cloneItem"
    :group="{ name: 'dbox', pull: 'clone', put: false }"
    class="grid w-full grid-cols-2 items-center justify-center gap-2"
    item-key="id"
    @end="tagMoveEnd"
  >
    <template #item="{ element }">
      <div
        class="hover:bg-accent flex h-[32px] cursor-pointer items-center justify-center rounded border border-dashed border-gray-400 text-xs"
        @click="itemClick(element)"
      >
        {{ element.name }}
      </div>
    </template>
  </Draggable>
</template>

<style scoped></style>
