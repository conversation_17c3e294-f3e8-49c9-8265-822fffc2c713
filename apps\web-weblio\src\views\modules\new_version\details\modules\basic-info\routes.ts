import type { RouteRecordRaw } from 'vue-router';

// 基础信息模块路由配置
export const basicInfoRoutes: RouteRecordRaw[] = [
  {
    path: '/details/basic-info',
    name: 'BasicInfo',
    component: () => import('./index.vue'),
    meta: {
      title: '基础信息',
      icon: 'info-circle',
      keepAlive: true,
      requiresAuth: false
    }
  },
  {
    path: '/details/basic-info/:id',
    name: 'BasicInfoDetail',
    component: () => import('./index.vue'),
    meta: {
      title: '详细信息',
      icon: 'file-text',
      keepAlive: true,
      requiresAuth: false
    },
    props: true
  }
];

// 子路由配置 (如果有components目录下的子组件需要独立路由)
export const basicInfoChildRoutes: RouteRecordRaw[] = [
  // 预留给components目录下的子组件路由
  // {
  //   path: '/details/basic-info/chemical-structure',
  //   name: 'ChemicalStructure',
  //   component: () => import('./components/ChemicalStructure.vue'),
  //   meta: {
  //     title: '化学结构',
  //     parent: 'BasicInfo'
  //   }
  // }
];

// 合并所有路由
export const routes: RouteRecordRaw[] = [
  ...basicInfoRoutes,
  ...basicInfoChildRoutes
];

// 导出默认路由配置
export default routes;

// 路由守卫配置
export const routeGuards = {
  // 路由前置守卫
  beforeEach: (to: any, from: any, next: any) => {
    // 这里可以添加权限验证、页面标题设置等逻辑
    if (to.meta?.title) {
      document.title = `${to.meta.title} - 化学数据库`;
    }
    next();
  },
  
  // 路由后置守卫
  afterEach: (to: any, from: any) => {
    // 这里可以添加页面访问统计、埋点等逻辑
    console.log(`导航到: ${to.path}`);
  }
};

// 面包屑导航配置
export const breadcrumbConfig = {
  '/details/basic-info': [
    { name: '首页', path: '/' },
    { name: '详情', path: '/details' },
    { name: '基础信息', path: '/details/basic-info' }
  ],
  '/details/basic-info/:id': [
    { name: '首页', path: '/' },
    { name: '详情', path: '/details' },
    { name: '基础信息', path: '/details/basic-info' },
    { name: '详细信息', path: '' }
  ]
}; 
