<script setup lang="tsx">
import {defineProps, reactive, ref} from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {Button, Cascader, Input, MessagePlugin, Select, Textarea, Upload,Switch} from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import { save,getOneByDataseCode } from '../api.ts';
import {useAccessStore} from "@vben/stores";
import {AddCircleIcon,DeleteIcon} from "tdesign-icons-vue-next";
import {getDictItems} from "#/api";
import {getCategorys, getDatasetList, getLabelList} from "#/views/modules/tData/api.ts";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
  classes: [
    {
      required: true,
      message: '请选择分级',
    },
  ],
  category: [
    {
      required: true,
      message: '请选择分类',
    },
  ],
  dataAuth: [
    {
      required: true,
      message: '请选择数据权限',
    },
  ],
  title: [
    {
      required: true,
      message: '必填',
    },
  ],
  dataCreateTime: [
    {
      required: true,
      message: '必填',
    },
  ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      props.outRef?.reload();
      initStatus();
      modalApi.close();
    },
  }),
  getOne: useRequest(getOneByDataseCode, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: async (res: any) => {
      formData.value = res
      if(formData.value.img){
        imgfile.value = JSON.parse(formData.value.img)
      }
      if(formData.value.describeFile){
        desfile.value = JSON.parse(formData.value.describeFile)
      }
      datasetFileList.value = []
      if(formData.value.datasetFileList){
        datasetFileList.value = formData.value.datasetFileList
        for(const item of datasetFileList.value){
          if(item.fileSwitch){
            item.fileList = JSON.parse(item.filePath)
          }
          if(item.originalFileSwitch){
            item.originalFileList = JSON.parse(item.originalFilePath)
          }
        }
      }else{
        datasetFileList.value.push(dataset.value)
      }
      if(formData.value.category){
        formData.value.category = formData.value.category.map((item) => Number(item));
      }
      if(formData.value.classes){
        formData.value.classes = Number(formData.value.classes)
        categoryList.value = await getCategorys(formData.value.classes);
      }
      modalApi.open();
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  formData.value = {};
  desfile.value = []
  imgfile.value = []
  datasetFileList.value = []
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    if(formData.value.publishStatus == '3'){
      MessagePlugin.warning("该数据已发布，不能修改，请下架后再进行修改")
      return
    }
    const vali = await form.value.validate();
    if (vali === true) {
      if(imgfile.value){
        formData.value.img = JSON.stringify(imgfile.value)
      }
      if(desfile.value){
        formData.value.describeFile = JSON.stringify(desfile.value)
      }
      if(datasetFileList.value.length > 0){
        for(const item of datasetFileList.value){
          if(!item.subName){
            MessagePlugin.warning("请输入数据集子集名称")
            return
          }
          if(!item.purpose){
            MessagePlugin.warning("请输入数据集子集用途")
            return
          }
          if(item.fileSwitch){
            if(item.fileList.length == 0){
              MessagePlugin.warning("请上传数据集解析文件")
              return
            }else{
              item.filePath = JSON.stringify(item.fileList)
            }
          }else{
            if(!item.filePath){
              MessagePlugin.warning("请输入数据集解析文件路径")
              return
            }
          }
          if(item.originalFileSwitch){
            if(item.originalFileList.length == 0){
              MessagePlugin.warning("请上传数据集原始文件")
              return
            }else{
              item.originalFilePath = JSON.stringify(item.originalFileList)
            }
          }else{
            if(!item.originalFilePath){
              MessagePlugin.warning("请输入数据集原始文件路径")
              return
            }
          }
        }
      }
      formData.value.datasetFileList = datasetFileList.value
      reqRunner.save.run({ ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = async (data?: any) => {
  dataAuthList.value = await getDictItems('DATA_AUTH');
  classList.value = await getDictItems('DATA_LEVEL');
  labelList.value = await getLabelList();
  classList.value.forEach((op) => {
    if(op.value == 0 || op.value == 1){
      op["disabled"] = true;
    }
  })
  if (data && data.datasetCode) {
    reqRunner.getOne.run({datasetCode:data.datasetCode,management:'1'});
  }else{
    datasetFileList.value.push(dataset.value)
    modalApi.open();
  }
};
const imgsuffix = ref('.png,.jpg,.svg')
const imgfile = ref([])
const imgbeforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!imgsuffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+imgsuffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+imgsuffix.value+"的文件")
    return false;
  }
  return true;
};
const imguploadSuccess = (context: { fileList: any[] }) => {
  imgfile.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const dessuffix = ref('.md')
const desfile = ref([])
const desbeforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!dessuffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+dessuffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+dessuffix.value+"的文件")
    return false;
  }
  return true;
};
const desuploadSuccess = (context: { fileList: any[] }) => {
  desfile.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const accessStore = useAccessStore();
const dataset = ref({
  subName:'',
  purpose:'',
  filePath:'',fileSwitch:false, fileList:[],
  originalFilePath:'',originalFileSwitch:false,originalFileList:[],
})
const datasetFileList = ref([])
const addDatasetFileList = () => {
  datasetFileList.value.push(dataset.value)
}
const delDatasetFileList = (index) => {
  datasetFileList.value.splice(index,1)
}
const dataAuthList = ref([]);
const classList = ref([]);
const categoryList = ref([]);
const labelList = ref([]);
const changeClass = async () => {
  formData.value.category = []
  categoryList.value = await getCategorys(formData.value.classes);
}

const subHandleSuccess = (context: { fileList: any[] },item) => {
  item.fileList = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const subHandleSuccess2 = (context: { fileList: any[] },item) => {
  item.originalFileList = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const changeFileSwitch = (item) => {
  item.fileList = []
  item.filePath = ''
};
const changeOriginalFileSwitch = (item) => {
  item.originalFileList = []
  item.originalFilePath = ''
};

defineExpose({
  open,
});
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[80%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES" class="w-full" label-align="top" style="padding: 20px">
      <div class="grid w-full grid-cols-2 gap-10 mb-10">
        <FormItem label="分级" name="classes">
          <Select v-model="formData.classes" :options="classList" clearable placeholder="请选择"
                  :on-change="changeClass"/>
        </FormItem>
        <FormItem label="分类" name="category">
          <Cascader v-model="formData.category" :options="categoryList" clearable placeholder="请选择"
                    multiple check-strictly value-mode="onlyLeaf" :show-all-levels="false" :min-collapsed-num="1"
                    :disabled="formData.classes != 0 && (formData.classes == '' || formData.classes == null)"/>
        </FormItem>
        <FormItem label="标签">
          <Select v-model="formData.labelses" :options="labelList" multiple clearable placeholder="请选择"/>
        </FormItem>
        <FormItem label="数据权限" name="dataAuth">
          <Select v-model="formData.dataAuth" :options="dataAuthList" clearable placeholder="请选择"/>
        </FormItem>
        <FormItem label="数据集标题" name="title">
          <Input v-model="formData.title" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="作者" name="author">
           <Input v-model="formData.author" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据创建日期" name="dataCreateTime">
           <Input v-model="formData.dataCreateTime" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="关键词" name="keywords">
          <Input v-model="formData.keywords" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="CSTR" name="cstr">
           <Input v-model="formData.cstr" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="DOI" name="doi">
           <Input v-model="formData.doi" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据格式" name="formats">
           <Input v-model="formData.formats" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="语言" name="languages">
          <Input v-model="formData.languages" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="开源协议" name="license">
           <Input v-model="formData.license" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="形式" name="modalities">
          <Input v-model="formData.modalities" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据来源" name="source">
           <Input v-model="formData.source" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="适用任务" name="tasks">
           <Input v-model="formData.tasks" clearable placeholder="请输入内容" />
        </FormItem>
      </div>
      <div class="grid w-full grid-cols-1 gap-10">
        <FormItem label="描述" name="dataDescribe">
          <Textarea v-model="formData.dataDescribe" clearable placeholder="请输入内容"  :maxlength="1000"
                    :autosize="{ minRows: 4, maxRows: 8 }"/>
        </FormItem>
      </div>
      <div class="grid w-full grid-cols-2 gap-10">
        <FormItem label="数据集封面图片" name="img">
          <Upload ref="img" v-model="imgfile" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="imgbeforeUpload" @success="imguploadSuccess"/>
        </FormItem>
        <FormItem label="描述文件" name="describeFile">
          <Upload ref="des" v-model="desfile" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" :beforeUpload="desbeforeUpload" @success="desuploadSuccess"/>
        </FormItem>
      </div>
      <div class="grid w-full grid-cols-5 gap-10" v-for="(item,index) in datasetFileList">
        <FormItem label="数据集子集名称">
          <Input v-model="item.subName" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据集用途">
          <Input v-model="item.purpose" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据集解析文件路径">
          <Switch v-model="item.fileSwitch" size="large" @change="changeFileSwitch(item)"></Switch>
          <div v-if="!item.fileSwitch">
            <Input v-model="item.filePath" clearable placeholder="请输入内容" />
          </div>
          <div v-else>
            <Upload v-model="item.fileList" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                    action="/rgdc-sys/file/upload" @success="(context) => subHandleSuccess(context,item)" />
          </div>
        </FormItem>
        <FormItem label="数据集原始文件路径">
          <Switch v-model="item.originalFileSwitch" size="large" @change="changeOriginalFileSwitch(item)"></Switch>
          <div v-if="!item.originalFileSwitch">
            <Input v-model="item.originalFilePath" clearable placeholder="请输入内容" />
          </div>
          <div v-else>
            <Upload v-model="item.originalFileList" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                    action="/rgdc-sys/file/upload" @success="(context) => subHandleSuccess2(context,item)" />
          </div>
        </FormItem>
        <FormItem>
          <div style="padding-top: 32px;">
            <Button theme="success" @click="addDatasetFileList">
              <template #icon>
                <AddCircleIcon />
              </template>
            </Button>
            <Button v-if="index != 0" style="margin-left: 10px" theme="danger" @click="delDatasetFileList(index)">
              <template #icon>
                <DeleteIcon />
              </template>
            </Button>
          </div>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
