import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '门户收藏夹',
    },
    name: 'tPortalFavorites',
    path: '/tPortalFavorites',
    children: [
      {
        meta: {
          title: '门户收藏夹编辑',
        },
        name: 'tPortalFavoritesIndex',
        path: '/tPortalFavorites/index',
        component: () =>
          import('#/views/modules/tPortalFavorites/index.vue'),
      },
    ],
  },
];

export default routes;
