
# 是否开启压缩，可以设置为 none, brotli, gzip
VITE_COMPRESS=none

# 是否开启 PWA
VITE_PWA=false

# vue-router 的模式
# VITE_ROUTER_HISTORY=hash

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# 打包后是否生成dist.zip
VITE_ARCHIVER=true

# 是否为认证主服务
VITE_AUTH_SERVER=true

# 钉钉相关配置 Start
# 服务域名，需要与钉钉应用配置的域名一致
VITE_APP_API_BASE_URL = 'http://localhost:5888/'

# 钉钉开发者后台的应用ID（CorpId或SuiteId）,Client ID (原 AppKey 和 SuiteKey)
VITE_APP_ID = 'ding7joesot9ogtehn1n'

# 钉钉corpId
VITE_APP_CORP_ID = 'ding5ccf8c7171ff38e835c2f4657eb6378f'
# 钉钉相关配置 End
