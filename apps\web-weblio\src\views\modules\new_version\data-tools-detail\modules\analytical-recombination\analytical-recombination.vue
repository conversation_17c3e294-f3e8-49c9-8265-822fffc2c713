<script setup lang="ts">
import { UploadIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Input,
  Loading,
  MessagePlugin,
  Radio,
  RadioGroup,
  Space,
  Table,
  Upload,
} from 'tdesign-vue-next';
import { h, ref } from 'vue';

import { downloadFile, getUploadList, uploadPdfs } from './api';

// 响应式数据
const loading = ref(false);
const literatureType = ref('论文');
const docType = ref('PDF');
const isScanned = ref('否');
const taskName = ref('');
const fileList = ref<File[]>([]);
const dialogVisible = ref(false);
const uploadLists = ref<any[]>([]);
const btnStart = ref('开始解构');

// 表单引用
const formRef = ref();

// 文件上传前处理
const beforeUpload = (file: File) => {
  const isLt500M = file.size / 1024 / 1024 < 500;
  if (!isLt500M) {
    MessagePlugin.error('上传文件大小不能超过 500MB!');
    return false;
  }

  // 检查文件类型
  if (!file.name.toLowerCase().endsWith('.pdf')) {
    MessagePlugin.error('仅支持PDF文件上传!');
    return false;
  }

  fileList.value.push(file);
  MessagePlugin.success('文件上传成功！');
  return false; // 阻止自动上传
};

// 移除文件
const handleRemove = (context: any) => {
  const index = fileList.value.findIndex((f) => f.name === context.file.name);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

// 删除文件
const removeFile = (index: number) => {
  fileList.value.splice(index, 1);
  MessagePlugin.success('文件已移除');
};

// 开始解构
const handleUpload = async () => {
  if (fileList.value.length === 0) {
    MessagePlugin.warning('请先选择文件');
    return;
  }

  if (!taskName.value.trim()) {
    MessagePlugin.warning('请填写任务名称再进行上传');
    return;
  }

  loading.value = true;
  btnStart.value = '正在解构...';

  try {
    const formData = new FormData();

    // 添加文件
    fileList.value.forEach((file) => {
      formData.append('pdf_files', file);
    });

    // 添加参数
    const params = {
      literature_type: literatureType.value,
      doc_type: docType.value,
      is_scanned: isScanned.value,
    };
    formData.append('params', JSON.stringify(params));

    const response: any = await uploadPdfs(formData, taskName.value);

    if (response.code === 0) {
      MessagePlugin.success('上传成功');
      // 跳转到演示页面
      window.location.href = `./demonstration?session_id=${response.data}`;
    } else {
      MessagePlugin.error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    MessagePlugin.error('上传失败，请重试');
  } finally {
    loading.value = false;
    btnStart.value = '开始解构';
  }
};

// 获取上传记录
const getTableData = async () => {
  try {
    const response: any = await getUploadList();
    if (response.code === 0) {
      uploadLists.value = response.data;
    } else {
      MessagePlugin.error('获取上传记录失败');
    }
  } catch (error) {
    console.error('获取上传记录失败:', error);
    MessagePlugin.error('获取上传记录失败');
  }
};

// 打开任务列表
const openTaskList = async () => {
  dialogVisible.value = true;
  await getTableData();
};

// 预览功能
const handlePreview = (row: any) => {
  window.location.href = `./demonstration?session_id=${row.session_id}`;
};

// 下载功能
const handleDownload = async (row: any) => {
  try {
    const response = await downloadFile(row.session_id);
    const blob = new Blob([response]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `下载文件_${row.session_id || 'unknown'}.zip`;
    document.body.append(link);
    link.click();
    window.URL.revokeObjectURL(downloadUrl);
    link.remove();
    MessagePlugin.success('下载成功');
  } catch (error) {
    console.error('文件下载失败：', error);
    MessagePlugin.error('文件下载失败');
  }
};

// 重置表单
const resetForm = () => {
  taskName.value = '';
  fileList.value = [];
  literatureType.value = '论文';
  docType.value = 'PDF';
  isScanned.value = '否';
};

// 表格列定义
const columns = [
  {
    colKey: 'created_at',
    title: '上传时间',
    width: 180,
  },
  {
    colKey: 'task_name',
    title: '任务名称',
  },
  {
    title: '操作',
    width: 180,
    cell: ({ row }: any) => {
      return h('div', [
        h(
          Button,
          {
            size: 'small',
            onClick: () => handlePreview(row),
          },
          '预览',
        ),
        h(
          Button,
          {
            size: 'small',
            theme: 'primary',
            onClick: () => handleDownload(row),
          },
          '下载',
        ),
      ]);
    },
  },
];
</script>

<template>
  <div class="analytical-recombination-container">
    <Card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">全文多模态解析重组工具</h2>
          <Button class="task-list-btn" @click="openTaskList" size="medium">
            <svg
              class="btn-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
              />
            </svg>
            任务列表
          </Button>
        </div>
      </template>

      <div class="description">
        <p class="description-text">
          综合深度神经网络技术，PDF 乱码解析技术和基于 PDF 元素块建模识别技术，
          基于 PYMuPDF、PaddlePaddle、CNSTD
          等多种开源工具，实现对科技文献全文中"文本、图片、表格、公式"等多模态数据对象的抽取和组织，
          重新组合为计算机可利用的结构化数据。在工程层面，本工具将支持分布式部署，多进程运行，保证充分利用计算资源实现最大效率的语义分割重组语料的建设。
        </p>
      </div>

      <Loading :loading="loading" show-overlay prevent-scroll-through>
        <div class="content-area">
          <div class="left-panel">
            <Form ref="formRef" label-align="top" class="upload-form">
              <FormItem label="文献类型：">
                <RadioGroup v-model="literatureType">
                  <Radio value="论文">论文</Radio>
                  <Radio value="专利">专利</Radio>
                  <Radio value="图书">图书</Radio>
                </RadioGroup>
              </FormItem>

              <FormItem label="文档类型：">
                <RadioGroup v-model="docType">
                  <Radio value="PDF">PDF</Radio>
                  <Radio value="DOC">DOC</Radio>
                </RadioGroup>
              </FormItem>

              <FormItem label="是否是扫描版：">
                <RadioGroup v-model="isScanned">
                  <Radio value="否">否</Radio>
                  <Radio value="是">是</Radio>
                </RadioGroup>
              </FormItem>

              <FormItem label="任务名称：" required>
                <Input
                  v-model="taskName"
                  placeholder="请输入任务名称"
                  class="task-name-input"
                />
              </FormItem>
            </Form>
          </div>

          <div class="right-panel">
            <Upload
              theme="custom"
              draggable
              :before-upload="beforeUpload"
              :value="fileList"
              @remove="handleRemove"
              multiple
              class="upload-area"
            >
              <template #dragContent>
                <div class="upload-trigger">
                  <UploadIcon size="48" />
                  <span class="upload-text">将文件拖到此处，或点击上传</span>
                  <span class="upload-tip">仅支持PDF文件上传</span>
                </div>
              </template>
            </Upload>

            <!-- 自定义文件列表显示 -->
            <div v-if="fileList.length > 0" class="custom-file-list">
              <h4 class="file-list-title">
                已上传文件 ({{ fileList.length }})
              </h4>
              <div class="file-list-container">
                <div
                  v-for="(file, index) in fileList"
                  :key="index"
                  class="file-item"
                >
                  <div class="file-info">
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{
                      formatFileSize(file.size)
                    }}</span>
                  </div>
                  <Button
                    size="small"
                    theme="danger"
                    variant="text"
                    @click="removeFile(index)"
                  >
                    删除
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="action-area">
          <Space>
            <Button
              theme="primary"
              @click="handleUpload"
              :loading="loading"
              size="large"
            >
              {{ btnStart }}
            </Button>
            <Button theme="default" @click="resetForm" size="large">
              重置
            </Button>
          </Space>
        </div>

        <div class="notice-area">
          <div class="download-notice">
            <span class="notice-icon">⚠</span>
            解构过程中请勿刷新页面
          </div>
        </div>
      </Loading>
    </Card>

    <!-- 上传记录对话框 -->
    <Dialog
      v-model:visible="dialogVisible"
      title="上传记录"
      width="60%"
      :footer="false"
    >
      <Table :data="uploadLists" :columns="columns" :border="true" stripe />
    </Dialog>
  </div>
</template>

<style scoped lang="less">
.analytical-recombination-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 48px);
  margin-top: 0;
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 0;
}

.card-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.task-list-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(49, 130, 206, 0.25);
  line-height: 1;

  &:hover {
    background: linear-gradient(135deg, #2c5aa0 0%, #2a4d96 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.35);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(49, 130, 206, 0.25);
  }

  .btn-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    display: inline-block;
    vertical-align: middle;
  }
}

.description {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #0052d9;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #333;
  font-size: 16px;
}

.content-area {
  display: flex;
  gap: 40px;
  margin-bottom: 32px;
  min-height: 350px;
  align-items: flex-start;
}

.left-panel {
  flex: 1;
  min-width: 320px;
  max-width: 500px;
}

.right-panel {
  flex: 1;
  min-width: 400px;
  display: flex;
  flex-direction: column;
}

.upload-form {
  .t-form__item {
    margin-bottom: 24px;
  }
}

.task-name-input {
  width: 100%;
}

.upload-area {
  width: 100%;
  position: relative;
  z-index: 1;

  // 隐藏文件列表区域，只保留拖拽区域
  :deep(.t-upload__flow-list) {
    display: none;
  }

  // 确保拖拽区域占满整个空间
  :deep(.t-upload__dragger) {
    width: 100%;
    height: 100%;
    border: none;
    background: transparent;
    padding: 0;
  }
}

.custom-file-list {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e7e7e7;
  position: relative;
  z-index: 1;

  .file-list-title {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .file-list-container {
    max-height: 200px;
    overflow-y: auto;
  }

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #fff;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #0052d9;
      background: #f0f7ff;
      box-shadow: 0 1px 4px rgba(0, 82, 217, 0.1);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .file-info {
      flex: 1;
      min-width: 0;

      .file-name {
        display: block;
        color: #333;
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-size {
        display: block;
        color: #666;
        font-size: 12px;
      }
    }
  }
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #dcdcdc;
  border-radius: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  text-align: center;

  &:hover {
    border-color: #0052d9;
    background: #f0f7ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 82, 217, 0.1);
  }

  .upload-text {
    margin-top: 20px;
    color: #333;
    font-size: 16px;
    font-weight: 500;
  }

  .upload-tip {
    margin-top: 12px;
    color: #666;
    font-size: 14px;
  }
}

.action-area {
  text-align: right;
  margin-bottom: 24px;
}

.notice-area {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.download-notice {
  font-size: 14px;
  color: #666;
  display: inline-block;
}

.notice-icon {
  color: #ff9800;
  margin-right: 5px;
}

// 响应式设计
@media (max-width: 1200px) {
  .content-area {
    flex-direction: column;
    gap: 24px;
  }

  .left-panel,
  .right-panel {
    flex: none;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .analytical-recombination-container {
    padding: 16px;
  }

  .content-area {
    gap: 20px;
  }

  .upload-trigger {
    padding: 24px 16px;
    min-height: 200px;
  }

  .card-header {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>
