<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

interface Props {
  modelValue?: string;
  width?: number | string;
  height?: number | string;
  clickable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  width: '200px',
  height: '200px',
  clickable: true,
});

const emit = defineEmits<{
  click: [smiles: string];
  error: [error: string];
}>();

// 全局RDKit变量
declare global {
  interface Window {
    initRDKitModule: () => Promise<any>;
    RDKitModule: any;
  }
}

let RDKit: any = null;

// 响应式数据
const loading = ref(false);
const error = ref('');
const svgContent = ref('');

// DOM引用
const thumbnailRef = ref<HTMLElement>();

// 加载RDKit
const loadRDKit = async () => {
  if (!RDKit) {
    try {
      if (!window.initRDKitModule) {
        await new Promise<void>((resolve, reject) => {
          const script = document.createElement('script');
          script.src = '/rdkit/RDKit_minimal.js';
          script.addEventListener('load', () => resolve());
          script.addEventListener('error', () =>
            reject(new Error('无法加载RDKit脚本文件')),
          );
          document.head.append(script);
        });
      }
      RDKit = await window.initRDKitModule();
    } catch (error) {
      console.error('Failed to load RDKit:', error);
      throw new Error('无法加载RDKit库');
    }
  }
  return RDKit;
};

// 生成2D结构
const generate2D = async () => {
  if (!props.modelValue.trim()) {
    svgContent.value = '';
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    const rdkit = await loadRDKit();
    const mol = rdkit.get_mol(props.modelValue);

    if (!mol || mol.is_valid() === 0) {
      throw new Error('无效的SMILES字符串');
    }

    const svg = mol.get_svg(300, 300);
    mol.delete();

    svgContent.value = svg;
  } catch (error_) {
    const errorMsg =
      error_ instanceof Error ? error_.message : '生成分子结构失败';
    error.value = errorMsg;
    emit('error', errorMsg);
  } finally {
    loading.value = false;
  }
};

// 点击处理
const handleClick = () => {
  if (props.clickable && props.modelValue) {
    emit('click', props.modelValue);
  }
};

// 监听SMILES变化
watch(() => props.modelValue, generate2D, { immediate: true });

onMounted(() => {
  if (props.modelValue) {
    generate2D();
  }
});
</script>

<template>
  <div
    ref="thumbnailRef"
    class="molecule-thumbnail"
    :class="{ clickable: clickable && modelValue, loading }"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <span>生成中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <span>{{ error }}</span>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!modelValue" class="empty-state">
      <span>无分子数据</span>
    </div>

    <!-- 分子结构显示 -->
    <div
      v-else-if="svgContent"
      class="structure-display"
      v-html="svgContent"
    ></div>

    <!-- 默认占位 -->
    <div v-else class="placeholder">
      <span>分子结构</span>
    </div>

    <!-- 点击提示 -->
    <div v-if="clickable && modelValue && !loading" class="click-hint">
      <span>点击查看详情</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.molecule-thumbnail {
  position: relative;
  width: v-bind(width);
  height: v-bind(height);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.2s ease;

  &.clickable {
    cursor: pointer;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      transform: translateY(-1px);
    }
  }

  &.loading {
    opacity: 0.7;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-state,
.empty-state,
.placeholder {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.error-state {
  color: #ff4d4f;
}

.structure-display {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(svg) {
    max-width: 100%;
    max-height: 100%;
  }
}

.click-hint {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.molecule-thumbnail.clickable:hover .click-hint {
  opacity: 1;
}
</style>
