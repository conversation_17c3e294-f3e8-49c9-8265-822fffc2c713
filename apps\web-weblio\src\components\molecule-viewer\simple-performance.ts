// 简化的3D性能优化工具

export interface SimplePerformanceConfig {
  maxFPS: number;
  enableCache: boolean;
  simplifyComplex: boolean;
}

// 分子渲染缓存
const renderCache = new Map<string, any>();
const MAX_CACHE_SIZE = 30;

// 帧率控制
let lastRenderTime = 0;

// 检查是否应该渲染（帧率限制）
export const shouldRender = (maxFPS: number = 30): boolean => {
  const now = Date.now();
  const frameInterval = 1000 / maxFPS;

  if (now - lastRenderTime >= frameInterval) {
    lastRenderTime = now;
    return true;
  }
  return false;
};

// 分子复杂度检测（简化版）
export const getMoleculeComplexity = (
  molBlock: string,
): 'complex' | 'simple' => {
  const lines = molBlock.split('\n');
  const atomCountMatch = lines[0]?.match(/^\s*(\d+)/);
  const atomCount = atomCountMatch
    ? Number.parseInt(atomCountMatch[1] || '0', 10)
    : 0;

  return atomCount > 50 ? 'complex' : 'simple';
};

// 缓存管理
export const getCachedMolecule = (smiles: string) => {
  return renderCache.get(smiles);
};

export const cacheMolecule = (smiles: string, data: any) => {
  if (renderCache.size >= MAX_CACHE_SIZE) {
    // 删除最旧的缓存项
    const firstKey = renderCache.keys().next().value;
    renderCache.delete(firstKey);
  }
  renderCache.set(smiles, data);
};

// 应用简化的3D优化
export const applySimple3DOptimization = (viewer: any, molBlock: string) => {
  if (!viewer) return;

  const complexity = getMoleculeComplexity(molBlock);

  try {
    // 根据复杂度调整设置
    if (complexity === 'complex') {
      // 复杂分子：使用线框模式，减少细节
      viewer.setStyle(
        {},
        {
          line: {
            linewidth: 1.5,
            colorscheme: 'default',
          },
        },
      );

      // 禁用一些功能以提升性能
      if (viewer.enableFog) viewer.enableFog(false);
      if (viewer.setClickable) viewer.setClickable(false);
    } else {
      // 简单分子：使用球棍模型
      viewer.setStyle(
        {},
        {
          stick: {
            radius: 0.12,
            colorscheme: 'default',
          },
          sphere: {
            scale: 0.22,
          },
        },
      );
    }

    // 通用优化设置
    viewer.setBackgroundColor('white');

    // 优化光照
    if (viewer.setAmbientLight) viewer.setAmbientLight(0.7);
    if (viewer.setDirectionalLight) viewer.setDirectionalLight(0.3);

    // 控制渲染频率
    const originalRender = viewer.render;
    viewer.render = () => {
      if (shouldRender(complexity === 'complex' ? 20 : 30)) {
        originalRender.call(viewer);
      }
    };
  } catch (error) {
    console.warn('应用3D优化时出错:', error);
  }
};

// 清理缓存
export const clearCache = () => {
  renderCache.clear();
};

// 获取缓存状态
export const getCacheInfo = () => {
  return {
    size: renderCache.size,
    maxSize: MAX_CACHE_SIZE,
    keys: [...renderCache.keys()],
  };
};
