<script setup lang="ts">
import {
  <PERSON><PERSON>, <PERSON>,
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref } from 'vue';
import {BulletpointIcon} from 'tdesign-icons-vue-next';
import {getNotesData} from '../api';
import { useSearchStore } from '#/store/search';

interface Notes {
  id: string;
  createdBy: string;
  createTime: string;
  updatedBy: string;
  updateTime: string;
  isDeleted: string;
  iupacStandardInchiKey: string;
  casRegistryNumber: string;
  desiredUnits: string;
  detail: string;
  refId: string;
  refUrl: string;
  title: string;
  isMore: Boolean;
  // 添加缺失的属性
  symbol?: string;
  comment?: string;
}

const state: any = reactive({
  detailItem: {},
  loading: true,
});
// 响应式数据
const notesList = ref<Notes[]>([]);

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const initNotes = async () => {
  try {
    // 调用说明信息查询API
    const response = await getNotesData({ iupacStandardInchiKey: state.detailItem.baseCode });
    notesList.value = response;
  } catch (error) {
    console.error('获取说明信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
};

onMounted(async () => {
  await initNotes();
});
</script>

<template>
  <div class="substance-react-info">
    <h1 class="title-style"><BulletpointIcon style="padding-right: 10px" size="1.4em"/>Notes</h1>
    <li class="describe-style">Symbols used in this document:</li>
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载说明信息中...</div>
    </div>
    <template v-else>
      <div class="react-section">
        <table class="notes-table">
          <tbody>
            <tr v-for="(note, index) in notesList" :key="note.id || index" >
              <td class="symbol-cell" v-html="note.symbol"></td>
              <td class="comment-cell" v-html="note.comment"></td>
            </tr>
          </tbody>
        </table>
        <div v-if="notesList.length === 0" class="empty-state">
          <div class="empty-content">
            <p>暂无说明信息</p>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.substance-react-info {
  padding: 20px;

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @keyframes spin {
      0% {
        transform: translateY(-50%) rotate(0deg);
      }

      100% {
        transform: translateY(-50%) rotate(360deg);
      }
    }
  }

  .title-style {
    font-size: 30px;
    font-weight: bold;
    padding-bottom: 40px;
  }
  .describe-style {
    font-size: 20px;
    padding-bottom: 10px;
    padding-left: 20px;
  }

  .font-style {
    font-size: 20px;
  }

  .font-style2 {
    font-size: 20px;
    padding: 10px 0 5px 0;
  }

  .react-section {
    padding: 0px 0px 0px 50px;
  }

  .notes-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 30px;
  }


  .symbol-cell {
    font-weight: 600;
    color: #333;
    font-size: 16px;
    padding: 12px 0;
    vertical-align: top;
    width: 200px;
  }

  .comment-cell {
    padding: 12px 0 12px 30px;
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    vertical-align: top;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    
    .empty-content {
      text-align: center;
      color: #999;
      font-size: 16px;
      
      p {
        margin: 0;
        padding: 20px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px dashed #d9d9d9;
      }
    }
  }



  .show-more-container {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
}

.dialog-content-scrollable {
  min-height: 50vh;
  max-height: 50vh;
  overflow-y: auto; // 垂直滚动
  padding-right: 8px; // 避免滚动条遮挡内容

  .step-item,
  .reference-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    p {
      margin: 8px 0;
      /* 调整段落间距 */
      line-height: 1.6;
      /* 增加行高 */
    }

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  .title-text {
    font-weight: 600;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #b8b8b8;
    border-radius: 10px;
    border: 1px solid #f8f8f8;

    &:hover {
      background-color: #909090;
    }
  }
}
</style>
