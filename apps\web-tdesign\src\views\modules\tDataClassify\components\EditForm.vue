<script setup lang="tsx">
import {defineProps, reactive, ref, watch} from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';
import {MessagePlugin, TreeSelect, Upload} from "tdesign-vue-next";

import { Input } from 'tdesign-vue-next';
import { DatePicker } from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import { Select } from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import {getByCode, listByTree, save} from '../api.ts';
import {baseUploadFile, getDictItems} from "#/api";
import {useAccessStore} from "@vben/stores";
/**
 * 树形数据
 */
const treeSelectData: any = ref([]);
/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});

const levels = ref([]);
const isShow = ref([]);

/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
        parentId: [
          {
            required: true,
            message: '必填',
          },
        ],
        classifyName: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 150,
             message: '最大长度为150',
           },
         ],
        isDisplay: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 1,
             message: '最大长度为1',
           },
         ],
        parentCode: [
           {
             max: 9999,
             message: '最大长度为64',
           },
         ],
        secretLevel: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 32,
             message: '最大长度为32',
           },
         ],
        isHomeDisplay: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 32,
             message: '最大长度为32',
           },
         ],
        classifyNameEng: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 32,
             message: '最大长度为32',
           },
         ],
        sortOrder: [
         ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      props.outRef?.reload();
      initStatus();
      modalApi.close();
    },
  }),
  listByTree: useRequest(listByTree, {
      manual: true,
      debounceWait: 300,
      onError: () => {},
      onSuccess: (res: any) => {
        res.push({
          classifyCode: -1,
          classifyName: '顶级节点',
          children: [],
        });
        treeSelectData.value = res;
      },
    }),
  getByCode: useRequest(getByCode, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      levels.value = res;
      levels.value.forEach((op) => {
        if(op.value == 0 || op.value == 1){
          op["disabled"] = true;
        }
      })
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    const ls = []
    levels.value.forEach((key) => {
      ls.push(key['value']);
    });
    if(ls.includes(formData.value.secretLevel)) {
      const vali = await form.value.validate();
      if (vali === true) {
        if(formData.value.isHomeDisplay == '1'){
          if(!file.value){
            MessagePlugin.error('请上传首页展示图片');
            return
          }
          formData.value.imageFile = JSON.stringify(file.value)
        }
        reqRunner.save.run({...state.tagObj, ...formData.value});
      }
    }else{
      MessagePlugin.error('数据敏感级别不正确，请检查！');
    }
  },
  onCancel: () => {
    initStatus();
    modalApi.close();
  },
});
const loadTreeData = () => {
  reqRunner.listByTree.run({});
};
const open = async (data?: any) => {
  levels.value = await getDictItems('DATA_LEVEL');
  isShow.value = await getDictItems('NO_YES');
  if (data) {
    state.tagObj = data;
    formData.value = data;
    file.value = []
    if(data.isHomeDisplay == '1'){
      file.value = JSON.parse(data.imageFile)
    }
  }
  loadTreeData();
  modalApi.open();
};
defineExpose({
  open,
});
watch(
  () => formData.value.parentCode,
  (val) => {
    if(val) {
      console.log(val);
      reqRunner.getByCode.run(val);
    }
  },
  {
    deep: true,
  },
);
const accessStore = useAccessStore();
const suffix = ref('.png,.jpg,.svg')
const file = ref([])
const img = ref()
const beforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!suffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
    return false;
  }
  return true;
};
const uploadSuccess = (context: { fileList: any[] }) => {
  file.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES" class="w-full" label-align="top">
      <div class="grid w-full grid-cols-2 gap-1">
       <FormItem label="上级分类" name="parentCode">
          <TreeSelect v-model="formData.parentCode" :data="treeSelectData"
            :keys="{
              value: 'classifyCode',
              label: 'classifyName',
              children: 'children'
            }"
            placeholder="请选择上级分类"
          />
        </FormItem>
        <FormItem label="类型名称" name="classifyName">
           <Input v-model="formData.classifyName" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="学科代码">
           <Input v-model="formData.subjectCode" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="是否显示" name="isDisplay">
           <Select v-model="formData.isDisplay" clearable placeholder="请输入内容" :options="isShow"/>
        </FormItem>
        <FormItem label="敏感级别" name="secretLevel">
           <Select v-model="formData.secretLevel" clearable placeholder="请输入内容" :options="levels" />
        </FormItem>
        <FormItem label="是否在导航页展示" name="isHomeDisplay">
          <Select v-model="formData.isHomeDisplay" clearable placeholder="请输入内容" :options="isShow"/>
        </FormItem>
        <FormItem label="排序" name="sortOrder">
           <InputNumber style="width:100%" :step="1" v-model="formData.sortOrder" clearable placeholder="请输入内容"  />
        </FormItem>
        <FormItem label="英文名" v-if="formData.isHomeDisplay == 1" name="classifyNameEng">
          <Input v-model="formData.classifyNameEng" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="描述" v-if="formData.isHomeDisplay == 1" name="classifyDescribe">
          <Input v-model="formData.classifyDescribe" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="展示图片" v-if="formData.isHomeDisplay == 1" label-align="left">
          <Upload ref="img" v-model="file" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="uploadSuccess"/>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
