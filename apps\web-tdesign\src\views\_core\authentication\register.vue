<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, h, ref, onMounted, nextTick, watch } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationRegister, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { MessagePlugin } from 'tdesign-vue-next';

import { registerApi } from '#/api';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Register' });

const loading = ref(false);
const router = useRouter();

// 验证码相关
const smsCodeSending = ref(false);
const smsCodeCountdown = ref(0);
const smsCodeText = ref('发送验证码');
const smsCodeValue = ref('');
const authStore = useAuthStore();

// 图形验证码相关
const captchaCode = ref('');
const captchaImage = ref('');
const captchaKey = ref('');
const captchaLoading = ref(false);
const captchaError = ref('');
const showCaptchaError = ref(false);

// 表单引用
const registerFormRef = ref();
const smsButtonRef = ref();
const captchaButtonRef = ref();

// 获取图形验证码
async function getCaptcha() {
  try {
    // 开始加载
    captchaLoading.value = true;

    // 清空当前输入的验证码和图片
    captchaCode.value = '';
    captchaImage.value = '';

    // 清除验证码错误状态
    clearCaptchaError();

    // 生成验证码key
    captchaKey.value = `${new Date().getTime()}-${Math.random().toString(36).substr(2, 9)}`;

    // 调用后端API获取验证码图片
    const response = await authStore.getAuthCaptchaImage(captchaKey.value);

    // 如果返回的是base64图片数据
    captchaImage.value = response;
    captchaLoading.value = false;
  } catch (error) {
    console.error('获取验证码失败:', error);
    captchaLoading.value = false;
    MessagePlugin.error('获取验证码失败，请重试');
  }
}

// 供父组件调用的设置验证码方法
function setCaptchaData(imageData: string, key: string) {
  captchaImage.value = imageData;
  captchaKey.value = key;
  // 停止加载状态
  captchaLoading.value = false;
  // 清除验证码错误状态
  clearCaptchaError();
  // 清空用户输入的验证码
  captchaCode.value = '';
  // 同步到表单
  const formApi = registerFormRef.value?.getFormApi?.();
  if (formApi) {
    formApi.setFieldValue('captcha', '');
  }
}

// 验证图形验证码
function validateCaptcha() {
  showCaptchaError.value = false;
  captchaError.value = '';

  if (!captchaCode.value) {
    captchaError.value = '请输入图形验证码';
    showCaptchaError.value = true;
    return false;
  }

  if (captchaCode.value.length < 4) {
    captchaError.value = '图形验证码长度不正确';
    showCaptchaError.value = true;
    return false;
  }

  return true;
}

// 清除验证码错误状态
function clearCaptchaError() {
  if (showCaptchaError.value) {
    showCaptchaError.value = false;
    captchaError.value = '';
  }
}

// 发送短信验证码
async function sendSmsCode() {

  try {
    // 获取表单API
    const formApi = registerFormRef.value?.getFormApi?.();
    
    if (!formApi) {
      MessagePlugin.error('表单未初始化');
      return;
    }
    
    const values = await formApi.getValues();
    // 手机号
    const phoneNumber = values.phoneNumber;
    // 图形验证码
    const captchaCode = values.captcha;
    
    
    if (!phoneNumber) {
      MessagePlugin.error('请先输入手机号');
      return;
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phoneNumber)) {
      MessagePlugin.error('请输入正确的手机号格式');
      return;
    }

    // 验证图形验证码
    if (!captchaCode) {
      MessagePlugin.error('请输入图形验证码');
      return;
    }

    smsCodeSending.value = true;

    // 调用发送验证码，传递图形验证码参数
    let res = await authStore.sendSmsCode(phoneNumber, captchaCode, captchaKey.value, 'register');
    if (res != null && res != undefined && res != '' && res['result'] == 'success'){
      MessagePlugin.success('验证码已发送，请注意查收');
      // 发送成功,开始倒计时
      startCountdown();
    } else if (res != null && res != undefined && res != '' && res['result'] == 'test'){
      MessagePlugin.success('验证码已发送，请注意查收');
      smsCodeValue.value = res['smsCode'];
      
      // 填充验证码到表单
      const formApi = registerFormRef.value?.getFormApi?.();
      if (formApi) {
        await formApi.setFieldValue('smsCode', res['smsCode']);
      }
      
      // 发送成功,开始倒计时
      startCountdown();
    } else{
      MessagePlugin.error('发送验证码失败，请重试');
    }
    
  } catch (error) {
    console.error('发送验证码失败:', error);
    if (error.code !== '9901'){
      MessagePlugin.error('发送验证码失败，请重试');
    }
  } finally {
    smsCodeSending.value = false;
  }
}

// 倒计时功能
function startCountdown() {
  smsCodeCountdown.value = 60;
  smsCodeText.value = `${smsCodeCountdown.value}秒后重发`;
  
  const timer = setInterval(() => {
    smsCodeCountdown.value--;
    if (smsCodeCountdown.value > 0) {
      smsCodeText.value = `${smsCodeCountdown.value}秒后重发`;
    } else {
      smsCodeText.value = '重新发送';
      clearInterval(timer);
    }
  }, 1000);
}

// 发送按钮
function positionSmsButton() {
  nextTick(() => {
    setTimeout(() => {
      // 短信验证码输入框 - 更精确的选择器
      let smsCodeInput: HTMLInputElement | null = null;
      const inputs = document.querySelectorAll('input');
      
      for (const input of inputs) {
        if (input.placeholder && input.placeholder.includes('验证码') && !input.placeholder.includes('图形验证码')) {
          smsCodeInput = input;
          break;
        }
      }
      
      const smsButton = smsButtonRef.value as HTMLElement;
      
      if (smsCodeInput && smsButton) {
        const inputRect = smsCodeInput.getBoundingClientRect();
        const wrapperElement = document.querySelector('.register-wrapper') as HTMLElement;
        
        if (wrapperElement) {
          const wrapperRect = wrapperElement.getBoundingClientRect();
          const buttonTop = inputRect.top - wrapperRect.top + inputRect.height / 2;
          
          // 发送按钮样式
          smsButton.style.visibility = 'hidden';
          smsButton.style.position = 'absolute';
          smsButton.style.top = `${buttonTop}px`;
          smsButton.style.right = '8px';
          smsButton.style.transform = 'translateY(-50%)';
          smsButton.style.zIndex = '10';
          smsButton.style.display = 'flex';
          smsButton.style.alignItems = 'center';
          smsButton.style.justifyContent = 'center';
          
          smsCodeInput.style.paddingRight = '110px';
          
          // 展示发送按钮
          setTimeout(() => {
            smsButton.style.visibility = 'visible';
          }, 50);
        }
      }
    }, 0);
  });
}

// 图形验证码按钮定位
function positionCaptchaButton() {
  nextTick(() => {
    setTimeout(() => {
      // 图形验证码输入框
      let captchaInput: HTMLInputElement | null = null;
      const inputs = document.querySelectorAll('input');
      
      for (const input of inputs) {
        if (input.placeholder && input.placeholder.includes('图形验证码')) {
          captchaInput = input;
          break;
        }
      }
      
      const captchaButton = captchaButtonRef.value as HTMLElement;
      
      if (captchaInput && captchaButton) {
        const inputRect = captchaInput.getBoundingClientRect();
        const wrapperElement = document.querySelector('.register-wrapper') as HTMLElement;
        
        if (wrapperElement) {
          const wrapperRect = wrapperElement.getBoundingClientRect();
          const buttonTop = inputRect.top - wrapperRect.top + inputRect.height / 2;
          
          // 图形验证码按钮样式
          captchaButton.style.visibility = 'hidden';
          captchaButton.style.position = 'absolute';
          captchaButton.style.top = `${buttonTop}px`;
          captchaButton.style.right = '8px';
          captchaButton.style.transform = 'translateY(-50%)';
          captchaButton.style.zIndex = '10';
          captchaButton.style.display = 'flex';
          captchaButton.style.alignItems = 'center';
          captchaButton.style.justifyContent = 'center';
          captchaButton.style.width = '80px';
          captchaButton.style.height = '32px';
          
          captchaInput.style.paddingRight = '90px';
          
          // 展示图形验证码按钮
          setTimeout(() => {
            captchaButton.style.visibility = 'visible';
          }, 50);
        }
      }
    }, 0);
  });
}

// 组件挂载后定位按钮
onMounted(() => {
  setTimeout(() => {
    positionSmsButton();
    positionCaptchaButton();
  }, 500);
  window.addEventListener('resize', () => {
    positionSmsButton();
    positionCaptchaButton();
  });
  // 自动获取图形验证码
  getCaptcha();
});

async function handleSubmit(value: Recordable<any>) {
  try {
    // 验证图形验证码
    // const captchaValid = validateCaptcha();
    // if (!captchaValid) {
    //   return;
    // }

    loading.value = true;
    
    const submitData = {
      ...value,
      captcha: captchaCode.value,
      checkKey: captchaKey.value
    };
    
    await registerApi(submitData);
    MessagePlugin.success("注册成功，即将跳转登录页面");
    
    setTimeout(() => {
      router.push({
        path: '/auth/login',
        query: {
          username: value.accountNumber
        }
      });
    }, 100);
  } catch (error) {
    console.error('注册失败:', error);
    if (error.code !== '9901'){
      MessagePlugin.error('注册失败，请重试');
    }
  } finally {
    loading.value = false;
  }
}

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入账号',
        autocomplete: 'new-username',
      },
      fieldName: 'accountNumber',
      label: $t('authentication.username'),
      rules: z.string()
        .min(1, { message: $t('authentication.usernameTip') })
        .max(50, { message: '账号最大长度50位' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: $t('authentication.password'),
        autocomplete: 'new-password',
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      renderComponentContent() {
        return {
          strengthText: () => $t('authentication.passwordStrength'),
        };
      },
      rules: z.string()
        .min(8, { message: '密码至少8位' })
        .regex(/^(?=.*[a-zA-Z])(?=.*\d).+$/, { message: '密码必须包含数字和英文' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.confirmPassword'),
        autocomplete: 'new-password',
      },
      dependencies: {
        rules(values) {
          const { password } = values;
          return z
            .string({ required_error: '请输入确认密码' })
            .min(8, { message: '密码至少8位' })
            .regex(/^(?=.*[a-zA-Z])(?=.*\d).+$/, { message: '密码必须包含数字和英文' })
            .refine((value) => value === password, {
              message: '两次输入的密码不一致',
            });
        },
        triggerFields: ['password'],
      },
      fieldName: 'confirmPassword',
      label: $t('authentication.confirmPassword'),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入姓名',
        autocomplete: 'name',
      },
      fieldName: 'realName',
      label: '姓名',
      rules: z.string()
        .min(1, { message: '请输入姓名' })
        .max(50, { message: '姓名最大长度50位' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入手机号',
        autocomplete: 'tel',
      },
      fieldName: 'phoneNumber',
      label: '手机号',
      rules: z.string()
        .min(1, { message: '请输入手机号' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号格式' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入图形验证码',
        autocomplete: 'off',
      },
      fieldName: 'captcha',
      label: '图形验证码',
      rules: z.string()
        .min(1, { message: '请输入图形验证码' }),
        // .refine((value) => {
        //   // 验证图形验证码是否与用户输入匹配
        //   return captchaCode.value === value;
        // }, { message: '图形验证码不正确' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入验证码',
        autocomplete: 'off',
      },
      fieldName: 'smsCode',
      label: '短信验证码',
      rules: z.string().min(1, { message: '请输入短信验证码' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入邮箱',
        autocomplete: 'email',
      },
      fieldName: 'email',
      label: '邮箱',
      rules: z.string()
        .min(1, { message: '请输入邮箱' })
        .max(50, { message: '邮箱最大长度50位' })
        .email({ message: '请输入正确的邮箱格式' }),
    },
  ];
});

// 监听图形验证码输入，同步到表单
watch(captchaCode, (newValue) => {
  const formApi = registerFormRef.value?.getFormApi?.();
  if (formApi) {
    formApi.setFieldValue('captcha', newValue);
  }
});
</script>

<template>
  <div class="register-wrapper">
    <AuthenticationRegister
      ref="registerFormRef"
      :form-schema="formSchema"
      :loading="loading"
      @submit="handleSubmit"
    />
    
    <!-- 验证码发送按钮 -->
    <div 
      ref="smsButtonRef"
      class="sms-code-button"
      :class="[
        'px-3 py-1 text-sm rounded border transition-colors duration-200 whitespace-nowrap',
        smsCodeCountdown > 0 || smsCodeSending
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
          : 'sendSmsCode-button-500 text-white hover:bg-blue-600 border-blue-500 cursor-pointer'
      ]"
      @click="sendSmsCode"
    >
      {{ smsCodeSending ? '发送中...' : smsCodeText }}
    </div>
    
    <!-- 图形验证码图片按钮 -->
    <div 
      ref="captchaButtonRef"
      class="captcha-button"
      :class="[
        'text-sm rounded border transition-colors duration-200 whitespace-nowrap',
        captchaLoading
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
          : 'border-gray-300 cursor-pointer hover:border-gray-400'
      ]"
      @click="getCaptcha"
    >
      <img
        v-if="captchaImage && !captchaLoading"
        :src="captchaImage"
        alt="验证码"
        class="captcha-image-button"
        title="点击刷新验证码"
      />
      <div v-else-if="captchaLoading" class="captcha-loading-button">
        <div class="spinner"></div>
      </div>
      <div v-else class="captcha-error-button">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </div>
    </div>
  </div>
</template>

<style scoped>
.register-wrapper {
  position: relative;
}

.sms-code-button {
  position: absolute;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  visibility: hidden;
}

.register-wrapper :deep(input[placeholder*="验证码"]:not([placeholder*="图形验证码"])) {
  padding-right: 110px !important;
}

.register-wrapper :deep(input[placeholder*="图形验证码"]) {
  padding-right: 90px !important;
}

.sendSmsCode-button-500 {
  background-color: hsl(var(--primary));
}

/* 图形验证码按钮样式 */
.captcha-button {
  position: absolute;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  visibility: hidden;
  width: 97px !important;
}

.captcha-image-button {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.captcha-loading-button {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-error-button {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
