<script setup lang="ts">
import { Input, Radio, RadioGroup } from 'tdesign-vue-next';
import { defineEmits, defineProps, ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  isReadOnly: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:modelValue']);
const data = ref(props.modelValue);
watch(
  () => props.modelValue,
  (val) => {
    data.value = val;
  },
  {
    deep: true,
  },
);
</script>

<template>
  <div class="w-full bg-white">
    <div class="grid grid-cols-1 rounded border">
      <div class="overflow-auto">
        <div
          v-for="item in data"
          class="flex items-center justify-center overflow-hidden border-b last:border-none"
        >
          <div
            class="w-full overflow-hidden border-r p-1"
            v-if="Object.keys(item).includes('required')"
          >
            <h4>必输项</h4>
            <div
              class="flex w-full flex-row items-center justify-center overflow-hidden"
            >
              <RadioGroup :disabled="isReadOnly"
                v-if="Object.keys(item).includes('required')"
                class="flex-1"
                v-model="item.required"
              >
                <Radio
                  v-for="kItem in [
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]"
                  :key="kItem.value"
                  :value="kItem.value"
                >
                  {{ kItem.label }}
                </Radio>
              </RadioGroup>
              <Input v-model="item.message" clearable class="flex-1" />
            </div>
          </div>

          <div
            class="w-full border-r p-1"
            v-if="Object.keys(item).includes('unique')"
          >
            <h4>唯一校验</h4>
            <div
              class="flex w-full flex-row items-center justify-center overflow-hidden"
            >
              <RadioGroup
                v-if="Object.keys(item).includes('unique')"
                class="flex-1"
                v-model="item.unique"
              >
                <Radio
                  v-for="kItem in [
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]"
                  :key="kItem.value"
                  :value="kItem.value"
                >
                  {{ kItem.label }}
                </Radio>
              </RadioGroup>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
