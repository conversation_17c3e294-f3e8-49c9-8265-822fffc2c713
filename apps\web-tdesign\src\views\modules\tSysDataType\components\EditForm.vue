<script setup lang="tsx">
import { defineProps, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {InputNumber, MessagePlugin, Select, Upload} from 'tdesign-vue-next';
import { Input } from 'tdesign-vue-next';
import { RadioGroup } from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import { save } from '../api.ts';
import {getDictItems} from "#/api";
import {useAccessStore} from "@vben/stores";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    const vali = await form.value.validate();
    if (vali === true) {
      formData.value.typeCode = formData.value.value
      formData.value.typeName = formData.value.text
      formData.value.isDeleted = '0'
      if(imgfile.value && imgfile.value.length > 0){
        formData.value.typeImg = JSON.stringify(imgfile.value)
      }
      reqRunner.save.run({ ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
const isShow = ref([])
const open = async (data?: any) => {
  isShow.value = await getDictItems('NO_YES');
  if (data) {
    formData.value = data;
    imgfile.value = []
    if(formData.value.typeImg){
      imgfile.value = JSON.parse(formData.value.typeImg)
    }
  }
  modalApi.open();
};
const accessStore = useAccessStore();
const imgfile = ref([])
const suffix = ref('.png,.jpg,.svg')
const img = ref()
const beforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!suffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
    return false;
  }
  return true;
};
const uploadSuccess = (context: { fileList: any[] }) => {
  imgfile.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
defineExpose({
  open,
});
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES" class="w-full" label-align="top">
      <div class="grid w-full grid-cols-2 gap-1">
        <FormItem label="是否展示" name="isDisplay">
          <Select v-model="formData.isDisplay" clearable placeholder="请输入内容" :options="isShow"/>
        </FormItem>
        <FormItem label="展示顺序" name="typeOrder">
          <InputNumber style="width:100%" :step="1" v-model="formData.typeOrder" clearable placeholder="请输入内容"  />
        </FormItem>
        <FormItem label="展示图片" v-if="formData.isDisplay == 1" label-align="left">
          <Upload ref="img" v-model="imgfile" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="uploadSuccess"/>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
