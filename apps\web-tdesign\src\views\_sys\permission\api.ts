import { requestClient } from '#/api/request';

export async function permissionListByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-user/permission/listByPage', data);
}

export async function permissionSave(data: any) {
  return requestClient.post<any>('/rgdc-user/permission/save', data);
}

// 批量删除
export async function permissionDeleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-user/permission/deleteBatch/${data}`);
}

// 列表标签
export async function permissionTags() {
  return requestClient.get<any>('/rgdc-user/permission/tagList');
}
