import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-insights',
      keepAlive: true,
      title: '我的流程',
    },
    name: 'WorkFlow',
    path: '/workflow',
    children: [
      {
        meta: {
          title: '流程定义',
          // icon: 'ic:baseline-add-card',
        },
        name: 'ProcessDefinition',
        path: '/process-definition',
        component: () =>
          import('#/views/_workflow/process-definition/index.vue'),
      },

      {
        meta: {
          title: '我的流程',
          // icon: 'ic:baseline-add-card',
        },
        name: 'MyProcess',
        path: '/MyProcess',
        component: () => import('#/views/_workflow/my-process/index.vue'),
      },

      {
        meta: {
          title: '我发布的流程',
          // icon: 'ic:baseline-add-card',
        },
        name: 'MyInstance',
        path: '/MyInstance',
        component: () => import('#/views/_workflow/my-instance/index.vue'),
      },

      {
        meta: {
          title: '我的审批',
          // icon: 'ic:baseline-add-card',
        },
        name: 'MyTask',
        path: '/MyTask',
        component: () => import('#/views/_workflow/my-task/index.vue'),
      },

      {
        meta: {
          title: '历史流程',
          // icon: 'ic:baseline-add-card',
        },
        name: 'MyHisInstance',
        path: '/MyHisInstance',
        component: () => import('#/views/_workflow/my-his-instance/index.vue'),
      },
      // {
      //   meta: {
      //     title: '流程设计器',
      //     icon: 'ic:baseline-add-card',
      //   },
      //   name: 'BpmnEditerIndex',
      //   path: '/BpmnEditer/index',
      //   component: () => import('#/views/_workflow/flowlong/UserPopSelect.vue'),
      // },
    ],
  },
];

export default routes;
