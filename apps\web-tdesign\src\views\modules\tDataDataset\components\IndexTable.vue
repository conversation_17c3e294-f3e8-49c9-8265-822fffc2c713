<script setup lang="ts">
import { baseDownloadFile, baseUploadFile } from '#/api';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import {
  AddCircleIcon,
  DeleteIcon,
  RefreshIcon,
  SearchIcon,
  UploadIcon,
  DownloadIcon,
} from 'tdesign-icons-vue-next';

import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Link, MessagePlugin,
  type PageInfo,
  Popconfirm,
  Space,
  Table,
  Upload,
} from 'tdesign-vue-next';

import { Input } from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import PutAway from "#/components/put-away/index.vue";
import { deleteBatch, listByPage } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
const initStatus = () => {
  /**
   * 默认选中全部
   */
  state.selectedRowKeys = [];
  /**
   * 默认不显示删除按钮
   */
  state.delDailogShow = false;
  /**
   * 默认不显示查询
   */
  state.hideQuery = false;
  /**
   * 默认不显示加载
   */
  state.loading = false;
};
/**
 * table 排序字段
 */
const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
 {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',

    width: 64,
  },

{ colKey: 'datasetName', ellipsis: true, sorter: false, title: '数据集名称' },
{ colKey: 'datasetDescribe', ellipsis: true, sorter: false, title: '数据集描述' },
{ colKey: 'createdBy', ellipsis: true, sorter: false, title: '创建人' },
{ colKey: 'createTime', ellipsis: true, sorter: true, title: '创建时间' },
{colKey: 'op', width: 100, title: '操作', align: 'center',  fixed: 'right',},
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };

      if (records.length === 0 && total !== 0) {
        pagination.value.current = 1;
        reload();
      }
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      if(res == '删除成功'){
        MessagePlugin.success('删除成功');
        state.selectedRowKeys=[];
        reload();
        initStatus();
      }else{
        MessagePlugin.warning(res);
      }
    },
  }),
  excelExport: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  excelImport: useRequest(baseUploadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      // 导入完成后刷新列表
      reload();
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run({
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};
/**
 * 新建按钮相应
 */
const edit = (record?: any) => {
  /**
   * 通知Form组件打开编辑窗口
   */
  props.editFormRef?.open(record ? { ...record } : {});
};
/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};
/**
 * 单条删除
 */
const remove = (record: any) => {
  reqRunner.deleteBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  // 重置分页
  pagination.value={
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);

};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  formData.value={}
  searchForm.value.reset();
  pagination.value={
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * table 行点击响应
 * @param record
 */
const handleRowClick = (record: any) => {};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowKeys = value;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};

const excelExport = async () => {
  // 下载Excel
  reqRunner.excelExport.run(`/tDataDataset/excelExport/${state.selectedRowKeys}`);
};

const excelImport = async (...args: any) => {
  reqRunner.excelImport.run('/tDataDataset/excelImport', { file: args[0].raw });
};

onMounted(() => {
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Space :size="8" class="w-full tiny-tdesign-style-patch" direction="vertical">
    <!--    查询表单定义区域-->
    <Card v-if="isSearchForm">
      <Form
        ref="searchForm"
        :data="formData"
        class="w-full"
        @reset="resetSearch"
        @submit="searchFormSubmit"
      >

         <div class="grid w-full grid-cols-3 gap-1">
                    <FormItem  label="数据集名称" name="datasetName">
                       <Input v-model="formData.datasetName" clearable placeholder="请输入内容" />
                    </FormItem>
                    <FormItem  label="数据集描述" name="describe">
                       <Input v-model="formData.datasetDescribe" clearable placeholder="请输入内容" />
                    </FormItem>
                    <FormItem  label="创建人" name="createdBy">
                       <Input v-model="formData.createdBy" clearable placeholder="请输入内容" />
                    </FormItem>
                </div>

        <div class="flex items-center justify-end mt-2 space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置
          </Button>

        </div>
      </Form>
    </Card>
    <Card>
      <!-- 表格定义区域 -->
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :bordered="true"
        :columns="columns"
        :data="state.dataSource"
        :hover="true"
        :loading="state.loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="state.selectedRowKeys"
        :sort="state.sort"
        :stripe="true"
        cell-empty-content="-"
        lazy-load
        resizable
        row-key="id"
        table-layout="fixed"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
        v-bind="tableConfig"
      >
        <!--        表格顶部按钮区域-->
        <template #topContent>
          <div class="flex justify-start w-full mb-2">
            <div class="flex items-center justify-start w-full pl-2">
              <div class="mr-2 t-card__title">数据集管理表</div>
              <div
                v-if="state.selectedRowKeys?.length > 0"
                class="text-blue-600/80"
              >
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </div>
            </div>
            <div class="flex justify-end w-full space-x-2">
<!--              <Upload-->
<!--                  :trigger-button-props="{ theme: 'primary', variant: 'base' }"-->
<!--                  :request-method="excelImport"-->
<!--                >-->
<!--                  <Button theme="primary">-->
<!--                    <template #icon>-->
<!--                      <UploadIcon />-->
<!--                    </template>-->
<!--                    导入Excel-->
<!--                  </Button>-->
<!--              </Upload>-->
<!--              <Button theme="primary" @click="excelExport">-->
<!--                <template #icon>-->
<!--                  <DownloadIcon />-->
<!--                </template>-->
<!--                导出Excel-->
<!--              </Button>-->
              <Button
                v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                theme="danger"
                @click="del"
              >
                <template #icon>
                  <DeleteIcon/>
                </template>
                删除
              </Button>
              <Button
                theme="primary"
                @click="edit"
              >
                <template #icon>
                  <AddCircleIcon/>
                </template>
                新增
              </Button>
              <Button variant="text" @click="reload">
                <RefreshIcon/>
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
            </div>
          </div>
        </template>
        <!--        空数据显示定义-->
        <template #empty>
          <div class="flex p-1 flex-col-center">
            <div class="mb-2 text-sx">暂无数据</div>
            <Button
              class="w-[100%]"
              theme="primary"
              variant="text"
              @click="edit"
            >
              <template #icon>
                <AddCircleIcon/>
              </template>
              点击创建新数据
            </Button>
          </div>
        </template>
        <!--        编辑按钮-->
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)">编辑</Link>
            <Popconfirm content="确定删除？" theme="warning" @confirm="remove(slotProps.row)">
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
