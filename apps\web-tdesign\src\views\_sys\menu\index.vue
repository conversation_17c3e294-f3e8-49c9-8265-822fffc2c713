<script lang="ts" setup>
import { getDictItems } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import { Page, useVbenModal } from '@vben/common-ui';
import { useI18n } from '@vben/locales';
import { MoveIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Col,
  EnhancedTable,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Row,
  Space,
  Tag,
} from 'tdesign-vue-next';
import { computed, h, onMounted, ref } from 'vue';

import { allMenu, deleteMenu, saveMenu } from './api';
import MenuModal from './components/modal/index.vue';

const { t } = useI18n();

// 表单控制属性
const formData: any = ref({});
const form = ref();

// 数据表控制属性
const data: any = ref([]);
const loading = ref(false);
const sort = ref([]);

const expandedTreeNodes = ref([]);
const stripe = ref(true);
const bordered = ref(true);
const hover = ref(true);
const tableLayout = ref(false);
const size: any = ref('medium');
const showHeader = ref(true);
const selectedRowKeys = ref([]);
const columnControllerVisible = ref(false);
const columns: any = ref([
  {
    // 列拖拽排序必要参数
    colKey: 'drag',
    title: '排序',
    cell: (row: any) => {
      return h(MoveIcon);
    },
    width: 46,
  },
  {
    colKey: 'meta.title',
    title: '菜单显示名称',
    ellipsis: true,
    cell: (e: any, { row }: any) => {
      return t(`${row.meta.title}`);
    },
  },
  {
    colKey: 'description',
    title: '描述',
    ellipsis: true,
  },

  {
    colKey: 'code',
    title: '菜单编码',
    ellipsis: true,
  },

  {
    colKey: 'name',
    title: '路由名称',
    ellipsis: true,
  },
  {
    colKey: 'path',
    title: '路由Path',
    ellipsis: true,
  },

  {
    colKey: 'status',
    title: '状态',
    ellipsis: true,
    width: 80,
    align: 'center',
    cell: (e: any, { row }: any) => {
      return row.status === 1
        ? h(
            Tag,
            {
              theme: 'primary',
            },
            '启用',
          )
        : h(
            Tag,
            {
              theme: 'danger',
            },
            '禁用',
          );
    },
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    ellipsis: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: '180',
    fixed: 'right',
  },
]);

const status = ref([]);
const groupColumn = ref(true);
const placement = ref<any['placement']>('top-right');
const customText = ref(true);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item: any) => item.colKey),
);
const expandAll = ref(false);
const tableRef = ref();

// 列配置
const columnControllerConfig = computed<any['columnController']>(() => ({
  // 列配置按钮位置
  placement: placement.value,
  hideTriggerButton: true,
  // 用于设置允许用户对哪些列进行显示或隐藏的控制，默认为全部字段
  fields: [
    'drag',
    'code',
    'name',
    'path',
    'description',
    'createTime',
    'updateTime',
    'op',
  ],
  // 弹框组件属性透传
  dialogProps: {
    preventScrollThrough: true,
  },
  // 列配置按钮组件属性透传
  buttonProps: customText.value
    ? {
        content: '',
        theme: '',
        variant: 'text',
      }
    : undefined,
  // 数据字段分组显示
  groupColumns: groupColumn.value
    ? [
        {
          label: '业务字段',
          value: 'a',
          columns: ['code', 'name', 'path', 'description'],
        },
        {
          label: '系统字段',
          value: 'b',
          columns: ['drag', 'row-select'],
        },
        {
          label: '记录字段',
          value: 'c',
          columns: ['createTime', 'updateTime'],
        },
      ]
    : undefined,
}));

const fetchData = async (params: any) => {
  loading.value = true;
  try {
    const res = await allMenu(params);

    data.value = res;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const loadData = async () => {
  const params = {
    ...formData.value,
  };

  await fetchData(params);
};

const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: MenuModal,
});

const add = () => {
  modalApi.setData({ refresh: loadData });
  modalApi.open();
};

const edit = (record: any) => {
  modalApi.setState({ title: '编辑' });
  modalApi.setData({ record, refresh: loadData });
  modalApi.open();
};
const onReset = () => {
  form.value.reset();
  loadData();
};
const onSubmit = async () => {
  loadData();
};

const showAll = () => {
  expandAll.value = !expandAll.value;
  expandAll.value ? tableRef.value.expandAll() : tableRef.value.foldAll();
};

const del = async (record: any) => {
  // Request: 发起远程请求进行删除
  await deleteMenu(record.id);
  loadData();
};

const onDragSort = async (data: any) => {
  // 如果拖拽的父节点为同一个
  if (data.target.parentCode === data.current.parentCode) {
    // 交换顺序字段
    let temp = null;
    temp = data.target.meta.order;
    data.target.meta.order = data.current.meta.order;
    data.current.meta.order = temp;
    await saveMenu(data.target);
    await saveMenu(data.current);
  } else {
    await saveMenu({
      ...data.current,
      parentCode:
        data.target.parentCode === '-1' ? '-1' : data.target.parentCode,
    });
  }

  await loadData();
};

onMounted(async () => {
  loadData();
  status.value = await getDictItems('BASE_STATUS');
});
</script>

<template>
  <Page description="用于配置系统动态菜单" title="系统菜单列表">
    <Space class="w-full" direction="vertical">
      <Card>
        <Form
          ref="form"
          :data="formData"
          :label-width="80"
          colon
          @reset="onReset"
          @submit="onSubmit"
        >
          <Row :gutter="[24, 24]">
            <Col :span="4">
              <FormItem label="菜单编码" name="code">
                <Input
                  v-model="formData.code"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入菜单编码"
                  type="search"
                />
              </FormItem>
            </Col>
            <Col :span="4">
              <FormItem label="路由名称" name="name">
                <Input
                  v-model="formData.path"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入路由名称"
                  type="search"
                />
              </FormItem>
            </Col>

            <Col :span="4">
              <FormItem label="描述" name="description">
                <Input
                  v-model="formData.description"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入描述"
                />
              </FormItem>
            </Col>
          </Row>
          <Row justify="end">
            <Col :span="24" class="mt-4">
              <Space size="small">
                <Button
                  :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
                  theme="primary"
                  type="submit"
                >
                  搜索
                </Button>
                <Button theme="default" type="reset" variant="base">
                  重置
                </Button>
                <!-- <PutAway v-model="hideQuery" variant="text" /> -->
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card>
        <div class="t-row--space-between mb-2 flex items-center justify-center">
          <div class="flex flex-wrap items-center justify-center gap-1">
            <div class="t-card__title ml-2">菜单列表</div>
            <div
              v-if="selectedRowKeys && selectedRowKeys.length > 0"
              class="text-[gray]"
            >
              已选择 {{ selectedRowKeys?.length || 0 }} 条数据
            </div>
          </div>
          <div class="flex flex-wrap items-center justify-center gap-2">
            <Button theme="default" @click="showAll">
              {{ expandAll ? `收起全部` : `展开全部` }}
            </Button>
            <Button @click="add"> 新增</Button>
            <ColumnDisplay v-model="columnControllerVisible" />
          </div>
        </div>
        <EnhancedTable
          ref="tableRef"
          v-model:column-controller-visible="columnControllerVisible"
          v-model:display-columns="displayColumns"
          v-model:expanded-tree-nodes="expandedTreeNodes"
          :bordered="bordered"
          :column-controller="columnControllerConfig"
          :columns="columns"
          :data="data"
          :hover="hover"
          :loading="loading"
          :selected-row-keys="selectedRowKeys"
          :show-header="showHeader"
          :size="size"
          :sort="sort"
          :stripe="stripe"
          :table-layout="tableLayout ? 'auto' : 'fixed'"
          :tree="{
            childrenKey: 'children',
            treeNodeColumnIndex: 1,
            indent: 32,
            expandTreeNodeOnClick: true,
          }"
          cell-empty-content="-"
          drag-sort="row-handler"
          lazy-load
          resizable
          row-key="code"
          @drag-sort="onDragSort"
        >
          <template #op="slotProps">
            <Space>
              <Link theme="primary" @click="edit(slotProps.row)"> 编辑</Link>
              <Popconfirm
                content="确定删除？"
                theme="danger"
                @confirm="del(slotProps.row)"
              >
                <Link theme="danger"> 删除</Link>
              </Popconfirm>
            </Space>
          </template>
        </EnhancedTable>
      </Card>
    </Space>
    <Modal />
  </Page>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
