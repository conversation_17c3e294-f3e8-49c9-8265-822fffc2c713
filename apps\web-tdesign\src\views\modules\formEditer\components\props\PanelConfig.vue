<script setup lang="ts">
import { defineEmits, defineProps, ref, watch } from 'vue';

import { Icon } from 'tdesign-icons-vue-next';
import { Button, Input } from 'tdesign-vue-next';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['update:modelValue']);
const data = ref(props.modelValue);
const removeItem = (item) => {
  data.value = data.value.filter((i) => i.id !== item.id);
  emits('update:modelValue', data.value);
};
const addItem = () => {
  data.value.push({
    id: Math.ceil(Math.random() * 100_000),
    component: 'CollapsePanel',
    unFormItem: true,
    isGroup: true,
    schema: {
      id: Math.ceil(Math.random() * 100_000),
      title: `面板${data.value.length + 1}`,
      name: `item_${Math.ceil(Math.random() * 100_000)}`,
      columns: 1,
      children: [],
    },
  });
  emits('update:modelValue', data.value);
};
watch(
  () => props.modelValue,
  (val) => {
    data.value = val;
  },
  {
    deep: true,
  },
);
</script>

<template>
  <div class="w-full bg-white">
    <div class="grid grid-cols-1 rounded border">
      <div class="flex overflow-hidden border-b">
        <div class="w-full border-r p-1">标题</div>
        <div class="flex w-[55px] items-center justify-center p-1"></div>
      </div>
      <div class="m-1 flex items-center justify-center">
        <Button
          style="width: 100%"
          theme="primary"
          variant="dashed"
          @click="addItem"
        >
          增加分组
        </Button>
      </div>
      <div class="max-h-[200px] overflow-auto">
        <div
          v-for="item in data"
          class="flex items-center justify-center overflow-hidden border-b last:border-none"
        >
          <div class="w-full overflow-hidden border-r p-1">
            <Input v-model="item.schema.title" clearable style="width: 100%"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
