<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';

import { getRecommendedConfig } from './performance-config';

interface Props {
  smiles?: string;
  viewMode?: '2d' | '3d';
  width?: number | string;
  height?: number | string;
  auto?: boolean; // 是否自动渲染
}

const props = withDefaults(defineProps<Props>(), {
  smiles: '',
  viewMode: '2d',
  width: '100%',
  height: '300px',
  auto: true,
});

const emit = defineEmits<{
  error: [error: string];
  loaded: [data: { smiles: string; viewMode: '2d' | '3d' }];
}>();

// 全局变量声明
declare global {
  interface Window {
    initRDKitModule: () => Promise<any>;
    RDKitModule: any;
    $3Dmol: any;
  }
}

let RDKit: any = null;
let $3Dmol: any = null;

// 响应式数据
const loading = ref(false);
const error = ref('');

// DOM引用
const viewer2d = ref<HTMLElement>();
const viewer3d = ref<HTMLElement>();

// 3DMol实例
let viewer3dInstance: any = null;

// 获取性能配置
const performanceConfig = getRecommendedConfig();

// 性能优化：渲染节流
let renderThrottleTimer: null | number = null;

// 优化3DMol性能的工具函数
const optimize3DPerformance = (viewer: any) => {
  if (!viewer) return;

  // 设置渲染节流
  const originalRender = viewer.render;
  viewer.render = () => {
    if (renderThrottleTimer) {
      cancelAnimationFrame(renderThrottleTimer);
    }
    renderThrottleTimer = requestAnimationFrame(() => {
      originalRender.call(viewer);
      renderThrottleTimer = null;
    });
  };

  // 优化交互响应
  viewer.enableMouseEvents(true);

  // 设置LOD（细节层次）
  viewer.setLOD(true);

  // 优化材质设置
  viewer.setAmbientLight(0.6);
  viewer.setDirectionalLight(0.4);
};

// 加载RDKit
const loadRDKit = async () => {
  if (!RDKit) {
    try {
      if (!window.initRDKitModule) {
        await new Promise<void>((resolve, reject) => {
          const script = document.createElement('script');
          script.src = '/rdkit/RDKit_minimal.js';
          script.addEventListener('load', () => resolve());
          script.addEventListener('error', () =>
            reject(new Error('无法加载RDKit脚本文件')),
          );
          document.head.append(script);
        });
      }
      RDKit = await window.initRDKitModule();
    } catch (error_) {
      console.error('Failed to load RDKit:', error_);
      throw new Error('无法加载RDKit库');
    }
  }
  return RDKit;
};

// 加载3DMol库
const load3DMol = async () => {
  if (!$3Dmol) {
    try {
      const molModule = await import('3dmol');
      $3Dmol = molModule.default || molModule;
    } catch (error_) {
      console.error('Failed to load 3DMol:', error_);
      throw new Error('无法加载3DMol库');
    }
  }
  return $3Dmol;
};

// 生成2D结构
const generate2D = async () => {
  try {
    const rdkit = await loadRDKit();
    const mol = rdkit.get_mol(props.smiles);

    if (!mol || mol.is_valid() === 0) {
      throw new Error('无效的SMILES字符串');
    }

    const svg = mol.get_svg(400, 300);
    mol.delete();

    if (viewer2d.value) {
      viewer2d.value.innerHTML = svg;
    }

    emit('loaded', {
      smiles: props.smiles,
      viewMode: '2d',
    });
  } catch (error_) {
    console.error('RDKit 2D generation error:', error_);
    throw error_;
  }
};

// 生成3D结构
const generate3D = async () => {
  try {
    const ThreeDMol = await load3DMol();
    const rdkit = await loadRDKit();

    const mol = rdkit.get_mol(props.smiles);
    if (!mol || mol.is_valid() === 0) {
      throw new Error('无效的SMILES字符串');
    }

    const molBlock = mol.get_molblock();
    mol.delete();

    if (viewer3dInstance) {
      viewer3dInstance.clear();
    }

    if (viewer3d.value) {
      viewer3dInstance = ThreeDMol.createViewer(viewer3d.value, {
        defaultcolors: ThreeDMol.elementColors.CPK,
        backgroundColor: 'white',
        // 使用动态性能配置
        antialias: performanceConfig.antialias,
        alpha: performanceConfig.alpha,
        premultipliedAlpha: performanceConfig.premultipliedAlpha,
        preserveDrawingBuffer: performanceConfig.preserveDrawingBuffer,
        powerPreference: performanceConfig.powerPreference,
        failIfMajorPerformanceCaveat:
          performanceConfig.failIfMajorPerformanceCaveat,
      });

      viewer3dInstance.addModel(molBlock, 'mol');

      // 使用配置的样式设置
      viewer3dInstance.setStyle(
        {},
        {
          stick: {
            radius: performanceConfig.stickRadius,
            colorscheme: 'default',
          },
          sphere: {
            scale: performanceConfig.sphereScale,
          },
        },
      );

      // 使用配置的动画时间
      viewer3dInstance.zoomTo({}, performanceConfig.zoomDuration, 1);

      // 应用性能优化配置
      apply3DPerformanceConfig(viewer3dInstance, performanceConfig);

      viewer3dInstance.render();
    }

    emit('loaded', {
      smiles: props.smiles,
      viewMode: '3d',
    });
  } catch (error_) {
    console.error('3DMol generation error:', error_);
    throw error_;
  }
};

// 渲染分子结构
const render = async () => {
  if (!props.smiles.trim()) {
    clearViewer();
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    await (props.viewMode === '2d' ? generate2D() : generate3D());
  } catch (error_) {
    const errorMsg =
      error_ instanceof Error ? error_.message : '渲染分子结构失败';
    error.value = errorMsg;
    emit('error', errorMsg);
  } finally {
    loading.value = false;
  }
};

// 清理显示器
const clearViewer = () => {
  if (viewer2d.value) {
    viewer2d.value.innerHTML = '';
  }

  if (viewer3dInstance) {
    try {
      viewer3dInstance.clear();
      viewer3dInstance.render();
    } catch (error_) {
      console.warn('Error clearing 3D viewer:', error_);
    }
  }

  error.value = '';
};

// 监听变化
watch([() => props.smiles, () => props.viewMode], () => {
  if (props.auto) {
    render();
  }
});

onMounted(() => {
  if (props.auto && props.smiles) {
    render();
  }
});

onUnmounted(() => {
  if (viewer3dInstance) {
    try {
      viewer3dInstance.clear();
    } catch (error_) {
      console.warn('Error during cleanup:', error_);
    }
    viewer3dInstance = null;
  }
});

// 暴露方法
defineExpose({
  render,
  clearViewer,
});
</script>

<template>
  <div class="pure-viewer">
    <!-- 2D显示 -->
    <div
      v-show="viewMode === '2d'"
      ref="viewer2d"
      class="viewer-2d"
      :class="{ loading }"
    >
      <div v-if="!smiles" class="placeholder">无分子数据</div>
    </div>

    <!-- 3D显示 -->
    <div
      v-show="viewMode === '3d'"
      ref="viewer3d"
      class="viewer-3d"
      :class="{ loading }"
    >
      <div v-if="!smiles" class="placeholder">无分子数据</div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      {{ error }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.pure-viewer {
  position: relative;
  width: v-bind(width);
  height: v-bind(height);
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.viewer-2d,
.viewer-3d {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.loading {
    opacity: 0.5;
  }
}

.placeholder {
  color: #999;
  font-size: 14px;
  text-align: center;
}

.viewer-2d :deep(svg) {
  max-width: 100%;
  max-height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff5f5;
  color: #d73a49;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
  max-width: 90%;
  text-align: center;
}
</style>
