<script setup lang="ts">
import {ref} from 'vue';

import {Page} from '@vben/common-ui';

import {claimTask, executeTask, rejectTask} from './api';
import IndexTable from './components/IndexTable.vue';
import DataDetailsView from './components/DataDetailsView.vue';
import CfgEditForm from "#/views/modules/tDataBase/components/CfgEditForm.vue";
import {MessagePlugin} from "tdesign-vue-next";
// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();

const add: any = async () => {
};

const edit: any = async (record: any) => {
  await executeTask({
    id: record.taskId,
    args: record.arg
  });
  MessagePlugin.success('审批通过！');
  indexTable.value.refresh();
};

const claim: any = async (record: any) => {
  // modalApi.setState({ title: '编辑' });
  await claimTask({
    id: record.taskId,
  });
  MessagePlugin.success('认领成功！');
  indexTable.value.refresh();
};

const remove: any = async (record: any) => {
  await rejectTask({
    id: record.taskId,
    args: record.arg
  });
  MessagePlugin.success('驳回成功！');
  indexTable.value.refresh();
};

const removeAndCancel: any = async (record: any) => {
  await rejectTask({
    id: record.taskId,
    args: record.arg
  });
  MessagePlugin.success('驳回成功！');
  indexTable.value.refresh();
};
const tableRef = ref();
const cfgEditFormRef = ref();
const dataDetailsViewRef = ref();
</script>
<template>
  <Page description="我的审批列表" title="我的审批">
    <CfgEditForm ref="cfgEditFormRef" :out-ref="tableRef" :isTask="true" @add="add"
                 @claim-task="claim" @edit="edit" @remove="removeAndCancel"/>
    <IndexTable ref="indexTable" @add="add" @claim-task="claim" @edit="edit" @remove="remove"
                :cfg-edit-form-ref="cfgEditFormRef" :data-details-view-ref="dataDetailsViewRef"/>
    <DataDetailsView ref="dataDetailsViewRef" :out-ref="tableRef"/>
  </Page>
</template>
