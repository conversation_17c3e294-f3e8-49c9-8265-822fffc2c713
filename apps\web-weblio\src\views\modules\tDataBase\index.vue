<script setup lang="tsx">
import { ref } from 'vue';

import CfgEditForm from './components/CfgEditForm.vue';
import IndexTable from './components/IndexTable.vue';

// const editFormRef = ref();
const tableRef = ref();
const cfgEditFormRef = ref();
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <CfgEditForm ref="cfgEditFormRef" :out-ref="tableRef" />
      <IndexTable ref="tableRef" :cfg-edit-form-ref="cfgEditFormRef" />
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
</style>
