<script setup lang="tsx">
import { uploadApi } from '#/api';
import { useAppConfig } from '@vben/hooks';
import { defineEmits, defineProps, ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: () => '',
  },
});

const emits = defineEmits(['update:modelValue', 'cumtEvent']);

const buildUrl = (url) => {
  return import.meta.env.BASE_URL === '/'
    ? `${url}`
    : `${import.meta.env.BASE_URL}${url}`;
};

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
const content = ref(props.modelValue);
const editorConfig = ref({
  toolbars: [
    [
      // 'fullscreen', // 全屏
      'source', // 源代码
      '|',
      'undo', // 撤销
      'redo', // 重做
      '|',
      'bold', // 加粗
      'italic', // 斜体
      'underline', // 下划线
      'fontborder', // 字符边框
      'strikethrough', // 删除线
      'superscript', // 上标
      'subscript', // 下标
      'removeformat', // 清除格式
      'formatmatch', // 格式刷
      'autotypeset', // 自动排版
      'blockquote', // 引用
      'pasteplain', // 纯文本粘贴模式
      '|',
      'forecolor', // 字体颜色
      'backcolor', // 背景色
      'insertorderedlist', // 有序列表
      'insertunorderedlist', // 无序列表
      'selectall', // 全选
      'cleardoc', // 清空文档
      '|',
      'rowspacingtop', // 段前距
      'rowspacingbottom', // 段后距
      'lineheight', // 行间距
      '|',
      'customstyle', // 自定义标题
      'paragraph', // 段落格式
      'fontfamily', // 字体
      'fontsize', // 字号
      '|',
      'directionalityltr', // 从左向右输入
      'directionalityrtl', // 从右向左输入
      'indent', // 首行缩进
      '|',
      'justifyleft', // 居左对齐
      'justifycenter', // 居中对齐
      'justifyright',
      'justifyjustify', // 两端对齐
      '|',
      'touppercase', // 字母大写
      'tolowercase', // 字母小写
      '|',
      'link', // 超链接
      'unlink', // 取消链接
      'anchor', // 锚点
      '|',
      // 'imagenone', // 图片默认
      // 'imageleft', // 图片左浮动
      // 'imageright', // 图片右浮动
      // 'imagecenter', // 图片居中
      // '|',
      // 'simpleupload', // 单图上传
      // 'insertimage', // 多图上传
      'emotion', // 表情
      // 'scrawl', // 涂鸦
      // 'insertvideo', // 视频
      // 'attachment', // 附件
      'insertframe', // 插入Iframe
      'insertcode', // 插入代码
      'pagebreak', // 分页
      'template', // 模板
      'background', // 背景
      'formula', // 公式
      '|',
      'horizontal', // 分隔线
      'date', // 日期
      'time', // 时间
      'spechars', // 特殊字符
      'wordimage', // Word图片转存
      '|',
      'inserttable', // 插入表格
      'deletetable', // 删除表格
      'insertparagraphbeforetable', // 表格前插入行
      'insertrow', // 前插入行
      'deleterow', // 删除行
      'insertcol', // 前插入列
      'deletecol', // 删除列
      'mergecells', // 合并多个单元格
      'mergeright', // 右合并单元格
      'mergedown', // 下合并单元格
      'splittocells', // 完全拆分单元格
      'splittorows', // 拆分成行
      'splittocols', // 拆分成列
      'contentimport', // 内容导入（支持Word、Markdown）
      '|',
      'print', // 打印
      // 'preview', // 预览
      'searchreplace', // 查询替换
      'help',
    ],
  ],
  // 后端服务地址，后端处理参考
  // https://open-doc.modstart.com/ueditor-plus/backend.html
  serverUrl: '/eel-boot/ueditor/config',
  UEDITOR_HOME_URL: buildUrl(`/static/UEditorPlus/`),
  UEDITOR_CORS_URL: buildUrl(`/static/UEditorPlus/`),
  initialFrameWidth: '100%', // 设置
  initialFrameHeight: 400,
  maxInputCount: 1,
  autoHeightEnabled: false,
  zIndex: 100,
  uploadServiceEnable: true,
  uploadServiceUpload(type, file, callback, option) {
    const uploadFile = {};
    if (file.chunks) {
      uploadFile.fileName = file.file?.name;
      uploadFile.file = file?.blob?.source;
    } else {
      uploadFile.file = file;
      uploadFile.fileName = file?.name;
    }
    const call = async function () {
      const aw = await uploadApi(uploadFile, (progressEvent) => {
        callback.progress(progressEvent.progress);
      });
      callback.success({
        state: 'SUCCESS',
        url: `${apiURL}/file/download?path=${encodeURI(aw?.data?.result?.url || '')}`,
      });
    };
    call();
  },
});

const beforeInit = (editorId) => {
  editorConfig.value.toolbars[0].push(`cum_test_btn${editorId}`);
  /**
   * 注意这里的 uiName不允许有大写字母 建议下划线分割
   */
  window.UE.registerUI(`cum_test_btn${editorId}`, (editor, uiName) => {
    // 注册按钮执行时的 command 命令，使用命令默认就会带有回退操作
    editor.registerCommand(uiName, {
      execCommand() {
        editor.execCommand('inserthtml', `<span></span>`);
      },
    });
    console.info(window.UE.ui.Button);
    // 创建一个 button
    /*const btn = new window.UE.ui.Button({
      // 按钮的名字
      name: uiName,
      // 提示
      title: '单图上传',
      // 需要添加的额外样式，可指定 icon 图标，图标路径参考常见问题 2
      cssRules:
        "background-image: url('favicon.ico') !important;background-size: cover;",
      // 点击时执行的命令
      onclick() {
        /!**
         * 在光标处添加内容
         *!/
        editor.execCommand(
          'inserthtml',
          `<p>这里是自定义button   ${editorId}</p>`,
        );
        emits('cumtEvent', { tag: 'cum_test_btn', editor });
        /!**
         * return true 防止事件传递
         *!/
        return true;
      },
    });

    // 因为你是添加 button，所以需要返回这个 button
    return btn;*/
    /* 指定添加到工具栏上的哪个位置，默认时追加到最后 */
    /* 指定这个 UI 是哪个编辑器实例上的，默认是页面上所有的编辑器都会添加这个按钮 */
  });
};
const ready = (editor) => {
  editor.setOpt(editorConfig.value);
  // console.info(editor.setOpt);
};

watch(
  () => props.modelValue,
  (val) => {
    content.value = val;
  },
  { deep: true },
);
watch(
  () => content,
  (val) => {
    emits('update:modelValue', val.value);
  },
  { deep: true },
);
</script>

<template>
  <vue-ueditor-wrap
    v-model="content"
    :config="editorConfig"
    :editor-dependencies="['ueditor.config.js', 'ueditor.all.js']"
    @before-init="beforeInit"
    @ready="ready"
  />
</template>

<style scoped></style>
