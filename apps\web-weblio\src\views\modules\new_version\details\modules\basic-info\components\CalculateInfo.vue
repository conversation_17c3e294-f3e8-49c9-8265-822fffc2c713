<script setup lang="ts">
import {
  Loading,
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref, watch } from 'vue';

import {
  getCalculateInfoData
} from '../api';
import { useSearchStore } from '#/store/search';

// 定义 props
const props = defineProps({
  calculateData: {
    type: Object,
    default: {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

interface CalculateInfoData {
  molecularWeight: string,
  xlogp3: string,
  hydrogenBondDonorCount: string,
  hydrogenBondAcceptorCount: string,
  rotatableBondCount: string,
  exactMass: string,
  monoisotopicMass: string,
  topologicalPolarSurfaceArea: string,
  heavyAtomCount: string,
  formalCharge: string,
  complexity: string,
  numberOfRings: string,
  carbonBondSaturationFsp3: string,
  isotopeAtomCount: string,
  definedAtomStereocenterCount: string,
  undefinedAtomStereocenterCount: string,
  definedBondStereocenterCount: string,
  undefinedBondStereocenterCount: string,
  covalentlyBondedUnitCount: string,
  mdl: string
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: false,

});

// 定义加载状态，用于控制页面加载动画显示
const loading = ref(props.loading);

// 初始化数据对象，字段与接口定义保持一致，确保类型安全
const data = ref<CalculateInfoData>({
  molecularWeight: '',
  xlogp3: '',
  hydrogenBondDonorCount: '',
  hydrogenBondAcceptorCount: '',
  rotatableBondCount: '',
  exactMass: '',
  monoisotopicMass: '',
  topologicalPolarSurfaceArea: '',
  heavyAtomCount: '',
  formalCharge: '',
  complexity: '',
  numberOfRings: '',
  carbonBondSaturationFsp3: '',
  isotopeAtomCount: '',
  definedAtomStereocenterCount: '',
  undefinedAtomStereocenterCount: '',
  definedBondStereocenterCount: '',
  undefinedBondStereocenterCount: '',
  covalentlyBondedUnitCount: '',
  mdl: ''
});

// 监听 props 变化，更新数据
watch(() => props.calculateData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    data.value = {
      molecularWeight: newData.molecularWeight || '',
      xlogp3: newData.xlogp3 || newData.logP || '',
      hydrogenBondDonorCount: newData.hydrogenBondDonorCount || '',
      hydrogenBondAcceptorCount: newData.hydrogenBondAcceptorCount || '',
      rotatableBondCount: newData.rotatableBondCount || '',
      exactMass: newData.exactMass || '',
      monoisotopicMass: newData.monoisotopicMass || '',
      topologicalPolarSurfaceArea: newData.topologicalPolarSurfaceArea || '',
      heavyAtomCount: newData.heavyAtomCount || '',
      formalCharge: newData.formalCharge || '',
      complexity: newData.complexity || '',
      numberOfRings: newData.numberOfRings || '',
      carbonBondSaturationFsp3: newData.carbonBondSaturationFsp3 || '',
      isotopeAtomCount: newData.isotopeAtomCount || '',
      definedAtomStereocenterCount: newData.definedAtomStereocenterCount || '',
      undefinedAtomStereocenterCount: newData.undefinedAtomStereocenterCount || '',
      definedBondStereocenterCount: newData.definedBondStereocenterCount || '',
      undefinedBondStereocenterCount: newData.undefinedBondStereocenterCount || '',
      covalentlyBondedUnitCount: newData.covalentlyBondedUnitCount || '',
      mdl: newData.mdl || ''
    };
  }
}, { immediate: true, deep: true });

// 监听 loading 状态变化
watch(() => props.loading, (newLoading) => {
  loading.value = newLoading;
  console.log("loading", loading.value, 2222);
});

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

onMounted(async () => {
  // 如果父组件没有传递数据，则使用原有的逻辑
  // if (!props.calculateData || Object.keys(props.calculateData).length === 0) {
  //   try {
  //     // 调用计算性质信息查询API
  //     const response = await getCalculateInfoData({ inchikey: state.detailItem.baseCode });
  //     data.value = response;
  //   } catch (error) {
  //     console.error('获取计算性质信息失败:', error);
  //   } finally {
  //     // 关闭加载状态
  //     loading.value = false;
  //   }
  // }
});

const fieldLabels = {
  molecularWeight: '分子量：',
  xlogp3: '水分配系数：',
  hydrogenBondDonorCount: '氢给体数：',
  hydrogenBondAcceptorCount: '氢受体数：',
  rotatableBondCount: '可旋转键数：',
  exactMass: '精确质量：',
  monoisotopicMass: '单同位素质量：',
  topologicalPolarSurfaceArea: '拓扑面积：',
  heavyAtomCount: '重原子数：',
  formalCharge: '形式电荷：',
  complexity: '复杂：',
  numberOfRings: '环数：',
  carbonBondSaturationFsp3: 'sp3杂化的碳原子比例：',
  isotopeAtomCount: '同位素原子计数：',
  definedAtomStereocenterCount: '定义原子立体中心计数：',
  undefinedAtomStereocenterCount: '未定义原子立体中心计数：',
  definedBondStereocenterCount: '定义键立构中心计数：',
  undefinedBondStereocenterCount: '未定义键立体中心计数：',
  covalentlyBondedUnitCount: '共价键合单元数：',
  mdl: '方法检测最低限值：'
};
</script>

<template>
  <div class="calculate-info">
    <!-- 加载状态提示 -->
    <div v-if="loading" class="loading-container">
      <Loading />
    </div>
    <!-- 主体内容展示 -->
    <div v-else class="content">
      <div class="info-items">
        <div v-for="(label, key) in fieldLabels" :key="key">
          <!-- 如果 data[key] 存在，则显示对应信息 -->
          <div v-if="data[key]" class="info-item">
            <!-- 字段标签 -->
            <span class="label">{{ label }}</span>
            <!-- 字段值 -->
            <span class="value">
              {{ data[key] }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.calculate-info {
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  font-size: 24px;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.structure-image {
  width: 100%;
  max-width: 400px;
  margin-bottom: 20px;
}

.structure-image img {
  width: 100%;
  height: auto;
}

.info-items {
  width: 80%;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 1px dashed #ddd;
}

.label {
  font-weight: bold;
  min-width: 240px;
  color: #555;
}

.value {
  flex-grow: 1;
  color: #333;
  word-break: break-all;
}

.smiles-link {
  color: #3498db;
  text-decoration: underline;
  cursor: pointer;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
