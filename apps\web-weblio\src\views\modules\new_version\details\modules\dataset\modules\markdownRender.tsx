import type { PropType, Ref } from 'vue';

import { mavonEditor } from 'mavon-editor';
import { defineComponent } from 'vue';

import 'mavon-editor/dist/css/index.css';

export default defineComponent({
  name: 'MarkdownRender',
  props: {
    content: {
      type: [String, Object] as PropType<Ref<string> | string>,
      required: true,
    },
    className: {
      type: String as PropType<string>,
      default: '',
    },
  },
  components: {
    mavonEditor,
  },
  setup(props) {
    return () => (
      <div class={props.className}>
        <mavon-editor
          defaultOpen="preview"
          editable={false}
          style="min-height: 200px; border: none"
          subfield={false}
          toolbarsFlag={false}
          v-model={props.content}
        />
      </div>
    );
  },
});
