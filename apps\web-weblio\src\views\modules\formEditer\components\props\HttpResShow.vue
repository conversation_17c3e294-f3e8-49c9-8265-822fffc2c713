<script setup lang="tsx">
import { defineProps, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import CodeShow from '../editer/CodeShow.vue';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});

const data = ref({});

const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {},
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});
</script>

<template>
  <Modal
    :show-cancel-button="false"
    :show-confirm-button="false"
    class="w-[40%]"
    title="网络请求结果"
  >
    <div class="flex w-full flex-col">
      <div>接口返回数据:</div>
      <CodeShow :code="JSON.stringify(state.tagObj || {}, null, 2)" />
    </div>
  </Modal>
</template>

<style scoped></style>
