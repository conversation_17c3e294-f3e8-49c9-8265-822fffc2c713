<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Form,
  FormItem,
  type FormProps,
  Input,
  Select,
  Textarea,
} from 'tdesign-vue-next';


import { generationEntitySave } from '../api';

const formData: any = ref({});
const form = ref();

const status = ref([]);
const delFlag = ref([]);

const FORM_RULES: FormProps['rules'] = {
  name: [
    {
      required: true,
      message: '必填',
    },
  ],
  tag: [
    {
      required: true,
      message: '必填',
    },
  ],
  tableName: [
    {
      required: true,
      message: '必填',
    },
  ],
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.setData({ record: formData.value });
    modalApi.close();
  },
  async onConfirm() {
    const vali = await form.value.validate();

    if (vali === true) {
      // 验证通过提交请求 并且关闭窗口
      await generationEntitySave(formData.value);
      modalApi.getData().refresh();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      console.log('打开时触发');
      formData.value = isOpen
        ? { ...modalApi.getData<Record<string, any>>()?.record }
        : {};
    }
  },
  title: '新增',
});
const isEdit = computed(() => {
  return modalApi.useStore().value.title === '新增';
});

onMounted(async () => {
});
</script>
<template>
  <Modal class="w-[40%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES">
      <div class="grid grid-cols-2 gap-2">
        <FormItem label="实体名称" name="name">
          <Input
            v-model="formData.name"
            :disabled="!isEdit"
            class="form-item-content"
            placeholder="请输入实体名称"
          />
        </FormItem>

        <FormItem v-if="isEdit" label="标签" name="tag">
          <Input
            v-model="formData.tag"
            class="form-item-content"
            placeholder="请输入标签"
          />
        </FormItem>

        <FormItem label="表名称" name="tableName">
          <Input
            v-model="formData.tableName"
            class="form-item-content"
            placeholder="请输入表名称"
          />
        </FormItem>

        <FormItem label="备注" name="remark">
          <Textarea
            v-model="formData.remark"
            class="form-item-content"
            placeholder="请输入备注"
          />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>
<style lang="less" scoped>
.form-item-content {
  width: 100%;
}
</style>
