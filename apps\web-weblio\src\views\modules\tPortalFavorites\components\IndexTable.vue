<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';

import { baseDownloadFile, baseUploadFile, getDictItems } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import { useSearchStore } from '#/store';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { useUserStore } from '@vben/stores';
import { DeleteIcon, RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Input,
  Link,
  MessagePlugin,
  Popconfirm,
  Space,
  Table,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useRouter } from 'vue-router';

import { deleteBatch, listByPage } from '../api';
/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});
const userStore = useUserStore();
const searchStore = useSearchStore();
/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
const initStatus = () => {
  /**
   * 默认选中全部
   */
  state.selectedRowKeys = [];
  /**
   * 默认不显示删除按钮
   */
  state.delDailogShow = false;
  /**
   * 默认不显示查询
   */
  state.hideQuery = false;
  /**
   * 默认不显示加载
   */
  state.loading = false;
};
/**
 * table 排序字段
 */
const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
  { colKey: 'row-select', width: 64, title: '选中标志', type: 'multiple' },
  { colKey: 'dataName', title: '数据名称', align: 'center', fixed: 'right' },
  {
    colKey: 'createTime',
    ellipsis: true,
    width: 300,
    sorter: true,
    title: '创建时间',
  },
  { colKey: 'op', width: 200, title: '操作', align: 'center', fixed: 'right' },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      const { records, total } = res;
      console.log(res);

      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };

      if (records.length === 0 && total !== 0) {
        pagination.value.current = 1;
        reload();
      }
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      state.selectedRowKeys = [];
      reload();
      initStatus();
    },
  }),
  excelExport: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  excelImport: useRequest(baseUploadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      // 导入完成后刷新列表
      reload();
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run({
    param: { ...formData.value },
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};
const router = useRouter();

/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};
/**
 * 单条删除
 */
const remove = (record: any) => {
  reqRunner.deleteBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  // 重置分页
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  formData.value = {};
  searchForm.value.reset();
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * table 行点击响应
 * @param record
 */
const handleRowClick = (_record: any) => {};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, _ctx: any) => {
  state.selectedRowKeys = value;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};
const resourcesType1 = ref();
const resourcesType2 = ref();
const editFormRef = ref();
// 跳转到详情页
function navigateToDetail(item: any) {
  // 存储 item 到 pinia
  searchStore.setCurrentDetailItem({
    ...item,
    baseCode: item?.operationCode || item.baseCode,
  });
  router.push({ path: '/details' });
}

// 显示审批弹窗
function showApprovalModal(item: any) {
  const newItem = {
    ...item,
    dataInformation: JSON.parse(item.dataInformation),
    baseCode: item?.operationCode || item.baseCode,
  };
  editFormRef.value.open(newItem);
}
const view = (item?: any) => {
  const { dataInformation } = item;
  if (dataInformation) {
    const data = JSON.parse(dataInformation);
    item.infomation = data;
  }
  const targetItem = item?.row || item;
  const sourceSystemCode = (userStore.userInfo as any)?.sourceSystemCode;
  const { dataAuth, isVisible } = targetItem;
  const authCode = dataAuth && String(dataAuth);
  const visibleStatus = Number(isVisible);
  if (!authCode) {
    MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
    return;
  }
  if (sourceSystemCode === '01' || sourceSystemCode === '02') {
    if (authCode === '0') {
      navigateToDetail(targetItem);
    } else if (visibleStatus === 1) {
      navigateToDetail(targetItem);
    } else if (visibleStatus === 0) {
      if (authCode === '1') {
        showApprovalModal(targetItem);
      } else if (authCode === '2') {
        MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
      }
    } else if (authCode === '2') {
      MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
    }
  } else {
    if (authCode === '2') {
      MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
      return;
    }
    if (visibleStatus === 1) {
      navigateToDetail(targetItem);
    } else if (visibleStatus === 0) {
      showApprovalModal(targetItem);
    }
  }
};
const init = () => {
  reload();
};
onMounted(async () => {
  resourcesType1.value = await getDictItems('DATA_TYPE');
  resourcesType2.value = await getDictItems('UNSTRUCTURED_DATA_TYPE');
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <EditForm ref="editFormRef" @success="init" />
  <Space :size="8" class="tiny-tdesign-style-patch w-full" direction="vertical">
    <Card v-if="isSearchForm">
      <Form
        ref="searchForm"
        :data="formData"
        class="w-full"
        @reset="resetSearch"
        @submit="searchFormSubmit"
      >
        <div class="grid w-full grid-cols-3 gap-1 p-3">
          <FormItem label="数据名称" name="dataType">
            <Input
              v-model="formData.dataName"
              clearable
              placeholder="请输入内容"
            />
          </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置
          </Button>
        </div>
      </Form>
    </Card>
    <Card>
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :bordered="true"
        :columns="columns"
        :data="state.dataSource"
        :hover="true"
        :loading="state.loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="state.selectedRowKeys"
        :sort="state.sort"
        :stripe="true"
        cell-empty-content="-"
        lazy-load
        resizable
        row-key="id"
        table-layout="fixed"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
        v-bind="tableConfig"
      >
        <template #topContent>
          <div class="mb-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">收藏列表</div>
              <div
                v-if="state.selectedRowKeys?.length > 0"
                class="text-blue-600/80"
              >
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <Button
                v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                theme="danger"
                @click="del"
              >
                <template #icon>
                  <DeleteIcon />
                </template>
                删除
              </Button>
              <Button variant="text" @click="reload">
                <RefreshIcon />
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
            </div>
          </div>
        </template>
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
          </div>
        </template>
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="view(slotProps.row)">查看</Link>
            <Popconfirm
              content="确定删除？"
              theme="warning"
              @confirm="remove(slotProps.row)"
            >
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
