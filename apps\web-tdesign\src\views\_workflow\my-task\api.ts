import { requestClient } from '#/api/request';

export async function listByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-workflow/queryTask/myTaskByPage2', data);
}

export async function executeTask(data: any) {
  return requestClient.post<any>('/rgdc-workflow/task/executeTask', data);
}

export async function claimTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/claimTask`, data);
}

export async function rejectTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/rejectTask`, data);
}
export async function myTaskProcssList() {
  return requestClient.get<any>(`/rgdc-workflow/queryTask/myTaskProcssList`);
}

export async function claimTasks(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/claimTasks`, data);
}
// 批量认领任务
export async function batchClaimTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/batchClaimTask`, data);
}

export async function myTaskByPage2(data: any) {
  return requestClient.post<any>(
    '/rgdc-workflow/queryTask/myTaskByPage2',
    data,
  );
}
