<script setup lang="ts">
import { baseDownloadFile, baseUploadFile, getDictItems } from '#/api';
import FormRender from '#/views/modules/formEditer/components/render/FormRender.vue';
import {
  AddCircleIcon,
  DownloadIcon,
  UploadIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Cascader,
  Form,
  FormItem,
  Link,
  Loading,
  MessagePlugin,
  Select,
  Space,
  Upload,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useRouter } from 'vue-router';

import {
  getByCode,
  getCategorys,
  getDatasetList,
  getLabelList,
  getTemplateListByDataType,
  isWithLuckySheet,
  saveOneData,
} from './api';

const props = defineProps({
  editVisible: {
    type: Boolean,
  },
});
const router = useRouter();
/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});

const reqRunner = {
  excelImport: useRequest(baseUploadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: (res: any) => {
      loading.value = false;
      MessagePlugin.error(res);
    },
    onSuccess: (res: any) => {
      loading.value = false;
      if (res == '导入成功') {
        MessagePlugin.success('数据批量填报成功！');
      } else {
        MessagePlugin.warning(res);
      }
      reload();
    },
  }),
  saveOneData: useRequest(saveOneData, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      if (res == '保存成功') {
        MessagePlugin.success('数据填报成功！');
        router.push({ path: '/tDataBaseIndex' });
      } else {
        MessagePlugin.warning(res);
      }
    },
  }),
  expExcelImport: useRequest(baseUploadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: (res: any) => {
      MessagePlugin.error(res);
    },
    onSuccess: (res: any) => {
      if (res == '导入成功') {
        MessagePlugin.success('数据批量填报成功！');
      } else {
        MessagePlugin.warning(res);
      }
      reload();
    },
  }),
  excelExport: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      MessagePlugin.success('导出成功，请查看下载任务');
    },
  }),
};

const disabled = ref(true);
const code = ref();
const codes = ref([]);
const dataType = ref();
const dataTypes = ref([]);
const data = ref();
const datacfg = ref();
const record = ref();
const luck_sheet = ref();
const withLuckySheet = ref();

const reload = async () => {
  withLuckySheet.value = await isWithLuckySheet(code.value);
  disabled.value = !(
    classes.value != null &&
    category.value.length > 0 &&
    dataset.value &&
    dataType.value != null &&
    code.value != null
  );
  data.value = {};
  datacfg.value = [];
  record.value = await getByCode(code.value);
  const list = JSON.parse(record.value?.cfg);
  setFormData(list);
  datacfg.value = list;
};
const setFormData = (list) => {
  list.forEach((item) => {
    data.value[item.schema?.name] = item.schema.value;
    if (item?.schema?.children?.length >= 0) {
      setFormData(item.schema.children);
    }
  });
};
const form: any = ref();
const dataForm = ref();
const saveData = async () => {
  if (
    classes.value === null ||
    classes.value === undefined ||
    isNaN(classes.value)
  ) {
    MessagePlugin.warning('请选择分级');
    return;
  }
  if (category.value && category.value.length === 0) {
    MessagePlugin.warning('请选择分类');
    return;
  }
  if (!dataset.value) {
    MessagePlugin.warning('请选择数据集');
    return;
  }
  form.value.clearValidate();
  const vali = await form.value.validate();
  if (vali === true) {
    state.tagObj.classCode = classes.value;
    state.tagObj.categoryList = category.value;
    state.tagObj.labelCodeList = labels.value;
    state.tagObj.datasetCode = dataset.value;
    reqRunner.saveOneData.run({
      ...state.tagObj,
      ...record.value,
      dataValue: data.value,
    });
  } else {
    MessagePlugin.warning('有必填项没有填写');
  }
};
onMounted(async () => {
  disabled.value = true;
  // 数据类型
  dataTypes.value = await getDictItems('DATA_TYPE');
  // 数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  classess.value.forEach((op) => {
    if (op.value == 0 || op.value == 1) {
      op.disabled = true;
    }
  });
  // 标签库
  labelses.value = await getLabelList();
});

const disabled_act = () => {
  disabled.value = true;
};

const changeDataType = async () => {
  if (dataType.value) {
    codes.value = await getTemplateListByDataType(dataType.value);
  }
  // disabled.value = true;
  data.value = [];
  code.value = null;
  disabled.value = !(
    classes.value != null &&
    category.value.length > 0 &&
    dataset.value &&
    dataType.value != null &&
    code.value != null
  );
};

const excelImport = async (...args: any) => {
  loading.value = true;
  reqRunner.excelImport.run('/tData/excelImport', {
    code: code.value,
    classes: classes.value,
    category: category.value,
    labelCode: labels.value,
    datasetCode: dataset.value,
    dataType: dataType.value,
    file: args[0].raw,
  });
};

const withLuckySheetExcelImport = async (...args: any) => {
  // LuckyExcel.transformExcelToLucky(
  //   args[0].raw,
  //   function (exportJson, luckysheetfile) {
  //     if (exportJson.sheets == null || exportJson.sheets.length == 0) {
  //       console.error(
  //         'Failed to read the content of the excel file, currently does not support xls files!'
  //       );
  //       return;
  //     }
  //     luck_sheet.value = exportJson;
  //     console.log("lucksheet", luck_sheet.value);
  //     // await createLuckSheet(args[0].raw);
  //     reqRunner.expExcelImport.run({
  //       code: code.value,
  //       classes: classes.value,
  //       categoryList: category.value,
  //       labelCodeList: labels.value,
  //       datasetCode: dataset.value,
  //       dataType: dataType.value,
  //       luckSheet: luck_sheet.value,
  //     });
  //   }
  // );
  reqRunner.expExcelImport.run('/tData/expExcelImport', {
    code: code.value,
    classes: classes.value,
    category: category.value,
    labelCode: labels.value,
    datasetCode: dataset.value,
    dataType: dataType.value,
    file: args[0].raw,
  });
};

const excelExport = async () => {
  // 下载Excel
  reqRunner.excelExport.run(
    `/tDataTemplate/excelExport/${code.value}/${dataType.value}`,
  );
};
const labels = ref();
const labelses = ref();
const dataset = ref();
const datasets = ref();
const classes = ref();
const category = ref();
const classess = ref();
const categorys = ref();
const changeClass = async () => {
  category.value = [];
  // 获取分类列表
  categorys.value = await getCategorys(classes.value);
  disabled.value = !(
    classes.value != null &&
    category.value.length > 0 &&
    dataset.value &&
    dataType.value != null &&
    code.value != null
  );
};
const changeCategory = async () => {
  dataset.value = '';
  datasets.value = [];
  datasets.value = await getDatasetList({
    categoryList: category.value.map(String),
  });
  disabled.value = !(
    classes.value != null &&
    category.value.length > 0 &&
    dataset.value &&
    dataType.value != null &&
    code.value != null
  );
};
const changeDataset = async () => {
  disabled.value = !(
    classes.value != null &&
    category.value.length > 0 &&
    dataset.value &&
    dataType.value != null &&
    code.value != null
  );
};
const toTDataTemplate = () => {
  router.push({ name: 'tDataTemplateIndex' });
};
const loading = ref(false);
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <Loading :loading="loading" show-overlay prevent-scroll-through indicator>
        <div
          class="flex h-full w-full flex-col gap-2 overflow-hidden pl-[6%] pr-[6%]"
        >
          <Space size="large">
            <Link theme="primary" size="large" @click="toTDataTemplate">
              维护数据模板>>
            </Link>
          </Space>
          <Card class="top-card">
            <Form ref="dataForm" class="w-full" label-align="top">
              <div class="mt-5 grid w-full grid-cols-3 gap-10">
                <FormItem label="分级" name="classes" :required-mark="true">
                  <Select
                    v-model="classes"
                    :options="classess"
                    clearable
                    placeholder="请选择"
                    :on-change="changeClass"
                  />
                </FormItem>
                <FormItem label="分类" name="category" :required-mark="true">
                  <Cascader
                    v-model="category"
                    :options="categorys"
                    clearable
                    placeholder="请选择"
                    multiple
                    check-strictly
                    value-mode="onlyLeaf"
                    :show-all-levels="false"
                    :min-collapsed-num="1"
                    :on-change="changeCategory"
                    :disabled="
                      classes != 0 && (classes == '' || classes == null)
                    "
                  />
                </FormItem>
                <FormItem label="数据集" name="dataset" :required-mark="true">
                  <Select
                    v-model="dataset"
                    :options="datasets"
                    clearable
                    placeholder="请选择"
                    :on-change="changeDataset"
                    :disabled="category.length === 0"
                  />
                </FormItem>
              </div>
              <div class="mt-2 grid w-full grid-cols-3 gap-10">
                <FormItem label="标签" name="labels">
                  <Select
                    v-model="labels"
                    :options="labelses"
                    multiple
                    clearable
                    placeholder="请选择"
                  />
                </FormItem>
                <FormItem
                  label="数据类别"
                  name="dataType"
                  :required-mark="true"
                >
                  <Select
                    v-model="dataType"
                    :options="dataTypes"
                    clearable
                    placeholder="请选择"
                    :on-change="changeDataType"
                    :on-clear="disabled_act"
                  />
                </FormItem>
                <FormItem label="模板" name="code" :required-mark="true">
                  <Select
                    v-model="code"
                    :options="codes"
                    clearable
                    placeholder="请选择"
                    :disabled="dataType == '' || dataType == null"
                    :on-change="reload"
                    :on-clear="disabled_act"
                  />
                </FormItem>
              </div>
              <div class="mt-2 grid w-full grid-cols-1 gap-1">
                <div v-if="!withLuckySheet" class="flex justify-end">
                  <div class="mr-2">
                    <Button
                      theme="primary"
                      :disabled="disabled"
                      @click="excelExport()"
                    >
                      <template #icon>
                        <DownloadIcon />
                      </template>
                      模板下载
                    </Button>
                  </div>
                  <div class="mr-2">
                    <Upload
                      :trigger-button-props="{
                        theme: 'primary',
                        variant: 'base',
                      }"
                      :request-method="excelImport"
                    >
                      <Button theme="primary" :disabled="disabled">
                        <template #icon>
                          <UploadIcon />
                        </template>
                        批量导入
                      </Button>
                    </Upload>
                  </div>
                </div>
                <div v-if="withLuckySheet" class="flex justify-end">
                  <div class="mr-2">
                    <Button
                      theme="primary"
                      :disabled="disabled"
                      @click="excelExport()"
                    >
                      <template #icon>
                        <DownloadIcon />
                      </template>
                      模板下载
                    </Button>
                  </div>
                  <div class="mr-2">
                    <Upload
                      :trigger-button-props="{
                        theme: 'primary',
                        variant: 'base',
                      }"
                      :request-method="withLuckySheetExcelImport"
                    >
                      <Button theme="primary" :disabled="disabled">
                        <template #icon>
                          <UploadIcon />
                        </template>
                        批量导入
                      </Button>
                    </Upload>
                  </div>
                </div>
              </div>
            </Form>
          </Card>
          <Card v-if="!disabled">
            <div
              class="border-1 mt-2 flex h-full w-full flex-col overflow-auto rounded-lg p-2"
            >
              <Form ref="form" :data="data">
                <div
                  class="bg-background flex w-full flex-col rounded-lg shadow"
                  style="position: relative"
                >
                  <FormRender
                    v-model="data"
                    :form-config="datacfg"
                    class="bg-background w-full p-2"
                  />
                </div>
                <div class="flex justify-center rounded-lg p-10">
                  <Button
                    theme="primary"
                    :disabled="disabled"
                    @click="saveData"
                  >
                    <template #icon>
                      <AddCircleIcon />
                    </template>
                    保存
                  </Button>
                </div>
              </Form>
            </div>
          </Card>
        </div>
      </Loading>
    </div>
  </div>
</template>
<style scoped>
.top-card {
  background-image: url('/static/images/top-card.jpeg');
  background-size: cover;
}
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
</style>
