<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" src="../internal.js?04dbe7f0"></script>
    <link rel="stylesheet" type="text/css" href="audio.css?622512f6"/>
</head>
<body>
<div class="wrapper">
    <div id="audioTab">
        <div id="tabHeads" class="tabhead">
            <span tabSrc="audio" class="focus" data-content-id="audio"><var id="lang_tab_insertV"></var></span>
            <span tabSrc="upload" style="display:none;" data-content-id="upload"><var
                id="lang_tab_uploadV"></var></span>
        </div>
        <div id="tabBodys" class="tabbody">
            <div id="audio" class="panel focus">
                <table>
                    <tr>
                        <td><label for="audioUrl" class="url"><var id="lang_audio_url"></var></label></td>
                        <td><input id="audioUrl" type="text"><a href="javascript:;" id="audioSelect"
                                                                style="display:none;">选择音频</a></td>
                    </tr>
                </table>
                <div style="padding:0 5px 5px 5px;color:#999;">
                    外链音频支持MP3格式
                </div>
                <div id="preview"></div>
                <div id="audioInfo">
                    <fieldset>
                        <legend><var id="lang_alignment"></var></legend>
                        <div id="audioFloat"></div>
                    </fieldset>
                </div>
            </div>
            <div id="upload" class="panel">
                <div id="upload_left">
                    <div id="queueList" class="queueList">
                        <div class="statusBar element-invisible">
                            <div class="progress">
                                <span class="text">0%</span>
                                <span class="percentage"></span>
                            </div>
                            <div class="info"></div>
                            <div class="btns">
                                <div id="filePickerBtn"></div>
                                <div class="uploadBtn"><var id="lang_start_upload"></var></div>
                            </div>
                        </div>
                        <div id="dndArea" class="placeholder">
                            <div class="filePickerContainer">
                                <div id="filePickerReady"></div>
                            </div>
                        </div>
                        <ul class="filelist element-invisible">
                            <li id="filePickerBlock" class="filePickerBlock"></li>
                        </ul>
                    </div>
                </div>
                <div id="uploadaudioInfo">
                    <fieldset>
                        <legend><var id="lang_upload_alignment"></var></legend>
                        <div id="upload_alignment"></div>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- jquery -->
<script type="text/javascript" src="../../third-party/jquery-1.10.2.js?25f4b625"></script>

<!-- webuploader -->
<script type="text/javascript" src="../../third-party/webuploader/webuploader.js?386fb72a"></script>
<link rel="stylesheet" type="text/css" href="../../third-party/webuploader/webuploader.css?b8f06036">

<!-- audio -->
<script type="text/javascript" src="audio.js?93d36899"></script>
</body>
</html>
