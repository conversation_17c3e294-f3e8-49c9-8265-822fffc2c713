<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';

import { computed, markRaw, ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';

import { AuthenticationLogin, SliderCaptcha, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { useAuthStore } from '#/store';

import * as dd from 'dingtalk-jsapi';

defineOptions({ name: 'Login' });

const authStore = useAuthStore();
const route = useRoute();

// AuthenticationLogin 组件引用
const authLoginRef = ref();

// 验证码相关响应式数据
const captchaImage = ref('');
const captchaKey = ref('');
const captchaLoading = ref(false);

// UUID生成所需的16进制字符
const hexList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'];

// 生成UUID的辅助函数
function buildUUID(): string {
  let uuid = '';
  for (let i = 1; i <= 36; i++) {
    if (i === 9 || i === 14 || i === 19 || i === 24) {
      uuid += '-';
    } else if (i === 15) {
      uuid += 4;
    } else if (i === 20) {
      uuid += hexList[(Math.random() * 4) | 8];
    } else {
      uuid += hexList[(Math.random() * 16) | 0];
    }
  }
  return uuid.replace(/-/g, '');
}

// 获取图片验证码的方法
async function getCaptchaImage () {
  try {
    captchaLoading.value = true;

    // 生成验证码key
    captchaKey.value = `${new Date().getTime()}-${buildUUID()}`;

    // 调用后端API获取验证码图片
    const response = await authStore.getAuthCaptchaImage(captchaKey.value);

    // 如果返回的是base64图片数据
    captchaImage.value = response;

    return {
      image: captchaImage.value,
      key: captchaKey.value
    };
      } catch (error) {
      console.error('获取验证码失败:', error);


  } finally {
    captchaLoading.value = false;
  }
}

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      fieldName: 'accountNumber',
      label: $t('authentication.username'),
      // 如果从注册页面跳转过来，自动填充用户名
      defaultValue: route.query.username as string || '',
      rules: z
        .string()
        .min(1, { message: $t('authentication.accountNumberTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
  ];
});

// 钉钉三方登录
let search = window.location.search
// 使用 URLSearchParams 来解析查询参数
let params = new URLSearchParams(search);
// 初始化一个空对象，用于存放参数
let paramObject = {};

// 遍历所有参数并将它们放入对象中
params.forEach((value, key) => {
    paramObject[key] = value;
});

if(paramObject['code']){

  const codeJson = {
      code: paramObject['code'],
    };

  authStore.authLoginDing(codeJson);


}

if (typeof dd !== 'undefined' && dd.version) {

  dd.ready(async () => {
    try {
      // corpId 需要在钉钉后台的配置中获取
      const result = await dd.runtime.permission.requestAuthCode({
        corpId: import.meta.env.VITE_APP_CORP_ID
      });
      const code = result.code;

      const authCodeJson = {
        authCode: code,
      };

      authStore.authLoginDing(authCodeJson);
      console.log('获取到的授权码:', code);
      // 在这里可以将获取到的code传递给后端
    } catch (err) {
      console.log('获取免登授权码失败：', JSON.stringify(err));
    }
  });
} else {
    console.log("不在钉钉环境中");
}

// 处理子组件的获取验证码事件
async function handleGetCaptcha() {
  try {
    const result = await getCaptchaImage();

    // 将获取到的验证码数据设置到子组件中
    if (authLoginRef.value && result) {
      await nextTick();
      authLoginRef.value.setCaptchaData(result.image, result.key);
    }
  } catch (error) {
    console.error('处理获取验证码事件失败:', error);
  }
}

// 处理手机号登录获取图形验证码事件
async function handleGetMobileCaptcha() {
  try {
    const result = await getCaptchaImage();

    // 将获取到的验证码数据设置到子组件的手机号登录验证码中
    if (authLoginRef.value && result) {
      await nextTick();
      authLoginRef.value.setMobileCaptchaData(result.image, result.key);
    }
  } catch (error) {
    console.error('处理手机号登录获取图形验证码事件失败:', error);
  }
}

// 处理手机号登录发送验证码事件
async function handleSendSmsCode(phoneNumber: string, captcha?: string, checkKey?: string) {
  try {
    // 调用store中的发送验证码方法，传递图形验证码参数
    const result = await authStore.sendSmsCode(phoneNumber, captcha, checkKey, 'login');
    
    // 将结果传递回子组件处理
    if (authLoginRef.value) {
      await nextTick();
      authLoginRef.value.handleSmsCodeResult(result);
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    
    // 发送失败时也要通知子组件重置状态
    if (authLoginRef.value) {
      await nextTick();
      authLoginRef.value.handleSmsCodeResult({ result: 'error' });
    }
  }
}

// 处理登录提交（区分登录类型）
async function handleSubmit(submitData: any) {
  try {
    if (submitData.loginType === 'mobile') {
      // 手机号登录，调用手机号登录接口
      await authStore.authPhoneNumberLogin({
        phoneNumber: submitData.phoneNumber,
        smsCode: submitData.smsCode,
        captcha: submitData.captcha,
        checkKey: submitData.checkKey
      });
    } else {
      // 账号密码登录，调用原有接口
      await authStore.authLogin(submitData);
    }
  } catch (error) {
    console.error('登录失败:', error);
  }
}

// 向外暴露验证码相关方法和数据
defineExpose({
  // 获取图片验证码
  getCaptchaImage,
  // 验证码相关数据
  captchaImage,
  captchaKey,
  captchaLoading
});

</script>

<template>
  <AuthenticationLogin
    ref="authLoginRef"
    :form-schema="formSchema"
    :loading="authStore.loginLoading"
    @submit="handleSubmit"
    @get-captcha="handleGetCaptcha"
    @get-mobile-captcha="handleGetMobileCaptcha"
    @send-sms-code="handleSendSmsCode"
  />
</template>
