<script setup lang="ts">
defineProps<{
  list: Array<{
    count: number;
    icon: string;
    imgUrl?: string;
    key: string;
    name: string;
    text?: string;
  }>;
  onCardClick: (item: any) => void;
  onMore: () => void;
  title: string;
}>();
</script>

<template>
  <div class="data-category">
    <div class="header">
      <span class="title">{{ title }}</span>
      <button class="more-btn" @click="onMore">更多</button>
    </div>
    <div class="card-list">
      <div
        v-for="item in list"
        :key="item.key"
        class="category-card"
        @click="() => onCardClick(item)"
      >
        <!-- item.imageFile -->
        <img :src="item.imgUrl" class="icon" />
        <div class="name">{{ item.text }}</div>
        <div class="count">{{ item.count }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.data-category {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
  padding: 24px 24px 16px 24px;
  margin-bottom: 24px;
  border: 1px solid #f0f0f0;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}
.title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  letter-spacing: 0.5px;
}
.more-btn {
  background: none;
  border: none;
  color: var(--td-brand-color, #0052d9);
  cursor: pointer;
  font-size: 14px;
  padding: 2px 10px;
  border-radius: 6px;
  transition: background 0.2s;
}
.more-btn:hover {
  background: #f5f7fa;
}
.card-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 8px;
}
.category-card {
  width: 100%;
  height: 200px;
  background: #f7f9fb;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #e5ecf5;
  transition:
    box-shadow 0.2s,
    border 0.2s,
    background 0.2s;
  position: relative;
}

/* 蓝色背景：第一行的第2,4个 + 第二行的第1,3个，以8个为循环 */
.category-card:nth-child(8n + 2),
.category-card:nth-child(8n + 4),
.category-card:nth-child(8n + 5),
.category-card:nth-child(8n + 7) {
  background: #e6f4ff;
  border: 1px solid #91caff;
}

/* 蓝色背景卡片中的数字使用深蓝色 */
.category-card:nth-child(8n + 2) .count,
.category-card:nth-child(8n + 4) .count,
.category-card:nth-child(8n + 5) .count,
.category-card:nth-child(8n + 7) .count {
  color: #0052d9;
  font-weight: 600;
}

.category-card:hover {
  box-shadow: 0 4px 16px rgba(0, 82, 217, 0.1);
  border: 1.5px solid var(--td-brand-color, #0052d9);
  background: #f0f6ff;
}
.icon {
  width: 56px;
  height: 56px;
  margin-bottom: 16px;
}
.name {
  font-size: 17px;
  color: #222;
  font-weight: 500;
  margin-bottom: 8px;
  text-align: center;
  letter-spacing: 0.2px;
}
.count {
  font-size: 15px;
  color: #409eff;
  font-weight: 500;
  text-align: center;
}
@media (max-width: 900px) {
  .card-list {
    grid-template-columns: repeat(3, 1fr);
  }
  .title {
    font-size: 17px;
  }
  .name {
    font-size: 15px;
  }
  .count {
    font-size: 13px;
  }
  .more-btn {
    font-size: 12px;
  }
  .category-card {
    height: 160px;
  }
  .icon {
    width: 48px;
    height: 48px;
    margin-bottom: 14px;
  }
}
@media (max-width: 480px) {
  .card-list {
    grid-template-columns: repeat(2, 1fr);
  }
  .title {
    font-size: 15px;
  }
  .name {
    font-size: 13px;
  }
  .count {
    font-size: 11px;
  }
  .more-btn {
    font-size: 10px;
  }
  .category-card {
    height: 130px;
  }
  .icon {
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
  }
}
</style>
