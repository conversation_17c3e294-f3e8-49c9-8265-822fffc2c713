import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '资源类型管理',
    },
    name: 'tSysDataType',
    path: '/tSysDataType',
    children: [
      {
        meta: {
          title: '资源类型管理编辑',
        },
        name: 'tSysDataTypeIndex',
        path: '/tSysDataType/index',
        component: () =>
          import('#/views/modules/tSysDataType/index.vue'),
      },
    ],
  },
];

export default routes;
