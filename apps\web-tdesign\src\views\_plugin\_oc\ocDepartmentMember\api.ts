// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/ocDepartmentMember/listByPage', data);
}

export async function saveBranch(data: any) {
  return requestClient.post<any[]>(
    `/ocDepartmentMember/saveBranch/${data.depId}`,
    data.users,
  );
}

export async function save(data: any) {
  return requestClient.post<any>('/ocDepartmentMember/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/ocDepartmentMember/deleteBatch/${data}`);
}

export async function getOne(data: any) {
  return requestClient.get<any>(`/ocDepartmentMember/getOne/${data}`);
}

export async function getByIds(data: any) {
  return requestClient.get<any>(`/ocDepartmentMember/getByIds/${data}`);
}
