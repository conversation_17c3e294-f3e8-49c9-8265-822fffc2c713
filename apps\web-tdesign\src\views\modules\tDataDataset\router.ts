import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据集管理',
    },
    name: 'tDataDataset',
    path: '/tDataDataset',
    children: [
      {
        meta: {
          title: '数据集管理编辑',
        },
        name: 'tDataDatasetIndex',
        path: '/tDataDataset/index',
        component: () =>
          import('#/views/modules/tDataDataset/index.vue'),
      },
    ],
  },
];

export default routes;
