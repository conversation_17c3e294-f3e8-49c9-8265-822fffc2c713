<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';
import type { PropType } from 'vue';

import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { useVbenModal } from '@vben/common-ui';
import { Icon } from 'tdesign-icons-vue-next';
import { Button, Form, FormItem, Input, Table } from 'tdesign-vue-next';
import { onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { listByPage } from './api';

const props = defineProps({
  /**
   * 行选择类型 multiple多选，single单选
   */
  rowSelectionType: {
    type: String,
    default: 'multiple', // 默认单选
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  sendApi: {
    type: Object as PropType<any>,
    default: () => {},
  },
  columns: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
});
const emit = defineEmits(['selectChange', 'valueSelected']);
/**
 * 内部静态数据定义
 */
const state = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],

  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
  /**
   * 选中的行数据
   */
  selectedRowData: [],
});
/**
 * table 排序字段
 */

const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const searchParam: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref(props.columns);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: (e) => {
      console.info('error', e);
    },
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run(
    {
      param: searchParam.value,
      current: pagination.value.current,
      pageSize: pagination.value.pageSize,
      sorts: removalUnderline(state.sort),
    },
    props.sendApi,
  );
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  searchForm.value.reset();
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowData = ctx.selectedRowData;
  state.selectedRowKeys = value;
  emit('selectChange', value, ctx);
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};

onMounted(() => {
  reload();
});

const [Modal, modalApi] = useVbenModal({
  onConfirm: () => {
    emit('valueSelected', state.selectedRowData);
    modalApi.close();
  },
  onOpenChange: (visible) => {
    if (visible) {
      reload();
    } else {
      state.selectedRowKeys = [];
      state.selectedRowData = [];
    }
  },
});
const show = (data?: any) => {
  if (data) {
    state.selectedRowData = data;
    state.selectedRowKeys = data.map((item) => item[props.rowKey]);
  }
  modalApi.open();
};
const handelClean = () => {
  state.selectedRowData = [];
  state.selectedRowKeys = [];
};
/**
 * 导出资源
 */
defineExpose({
  show,
});
</script>

<template>
  <Modal class="w-[60%]" title="数据选择">
    <Table
      v-bind="tableConfig"
      v-model:column-controller-visible="tableConfig.columnControllerVisible"
      v-model:display-columns="displayColumns"
      :columns="columns"
      :data="state.dataSource"
      :loading="state.loading"
      :multiple-sort="true"
      :pagination="pagination"
      :pagination-affixed-bottom="false"
      :row-key="rowKey"
      :row-selection-allow-uncheck="true"
      :selected-row-keys="state.selectedRowKeys"
      :sort="state.sort"
      lazy-load
      resizable
      size="small"
      @page-change="rehandlePageChange"
      @select-change="rehandleSelectChange"
      @sort-change="sortChange"
    >
      <!--        表格顶部按钮区域-->
      <template #topContent>
        <Form
          ref="searchForm"
          :data="searchParam"
          :label-width="40"
          class="w-full"
          label-align="right"
          @reset="resetSearch"
          @submit="searchFormSubmit"
        >
          <!--一列表单布局-->
          <div class="grid w-full grid-cols-1">
            <FormItem label="账号" name="accountNumber">
              <Input
                v-model="searchParam.accountNumber"
                clearable
                placeholder="请输入内容"
              />
            </FormItem>
          </div>
          <div class="mb-2 mt-2 flex items-center justify-between space-x-2">
            <div class="pl-1 text-blue-600/80">
              <span v-if="state.selectedRowKeys?.length > 0">
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </span>
            </div>
            <div class="flex space-x-2">
              <Button theme="primary" type="submit">
                <template #icon>
                  <Icon name="search" />
                </template>
                查询
              </Button>
              <Button theme="default" type="reset">
                <template #icon>
                  <Icon name="refresh" />
                </template>
                重置
              </Button>
            </div>
            <!--          <PutAway v-model="state.hideQuery" variant="text" />-->
          </div>
        </Form>
      </template>
    </Table>
    <template v-if="state.selectedRowKeys?.length > 0" #prepend-footer>
      <Button theme="danger" @click="handelClean"> 清空选择</Button>
    </template>
  </Modal>
</template>
