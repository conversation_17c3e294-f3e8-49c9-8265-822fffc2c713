import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout, PageLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    // 微前端接入
    component: window?.__MICRO_APP_ENVIRONMENT__ ? PageLayout : BasicLayout,
    meta: {
      icon: 'ic:baseline-miscellaneous-services',
      keepAlive: true,
      order: 1000,
      title: $t('sys.title'),
    },
    name: 'Sys',
    path: '/sys',
    children: [
      {
        meta: {
          title: $t('sys.account.title'),
        },
        name: 'Account',
        path: '/sys/account',
        component: () => import('#/views/_sys/account/index.vue'),
      },

      {
        meta: {
          title: $t('sys.permission.title'),
        },
        name: 'Permission',
        path: '/sys/permission',
        component: () => import('#/views/_sys/permission/index.vue'),
      },

      {
        meta: {
          title: $t('sys.role.title'),
        },
        name: 'Role',
        path: '/sys/role',
        component: () => import('#/views/_sys/role/index.vue'),
      },
      {
        meta: {
          title: '字典',
        },
        name: 'Dict',
        path: '/sys/dict',
        component: () => import('#/views/_sys/base-data/dict/index.vue'),
      },

      {
        meta: {
          title: '菜单',
        },
        name: 'Menu',
        path: '/sys/menu',
        component: () => import('#/views/_sys/menu/index.vue'),
      },
    ],
  },
];

export default routes;
