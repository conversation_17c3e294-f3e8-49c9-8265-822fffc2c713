import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '关于我们',
    },
    name: 'tSysAbout',
    path: '/tSysAbout',
    children: [
      {
        meta: {
          title: '关于我们编辑',
        },
        name: 'tSysAboutIndex',
        path: '/tSysAbout/index',
        component: () =>
          import('#/views/modules/tSysAbout/index.vue'),
      },
    ],
  },
];

export default routes;
