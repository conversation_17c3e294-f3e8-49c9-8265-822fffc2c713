<script setup lang="ts">
import { EventBus, offEvent, onEvent } from '#/utils/eventBus.ts';
import OptionConfig from '#/views/modules/formEditer/components/props/OptionConfig.vue';
import PanelConfig from '#/views/modules/formEditer/components/props/PanelConfig.vue';
import RulesConfig from '#/views/modules/formEditer/components/props/RulesConfig.vue';
import SyncOptionConfig from '#/views/modules/formEditer/components/props/SyncOptionConfig.vue';
import {
  Checkbox,
  CheckboxGroup,
  Collapse,
  CollapsePanel,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  Option,
  Radio,
  RadioGroup,
  Select,
  Textarea,
  TimePicker,
  Tooltip,
} from 'tdesign-vue-next';
import { onMounted, onUnmounted, ref, watch } from 'vue';

import { doHttp } from '../util';

defineOptions({ name: 'Props' });
const props = defineProps({
  eventPrefix: {
    type: String,
    default: '',
  },
});
const formData = ref({});
const tagData = ref({});
const asyncOptions = ref([]);
const defHandler = {
  widgetList_itemClick: (data) => {
    if (data && data.schema) {
      formData.value = data.schema; // { ...data.schema };
      tagData.value = data; // { ...data };
      asyncOptions.value = [];
    }
  },
  formEditer_itemClick: (data) => {
    if (data && data.schema) {
      formData.value = data.schema; // { ...data.schema };
      tagData.value = data; // { ...data };
      asyncOptions.value = [];
    }
  },
  formEditer_itemDelete: () => {
    formData.value = {};
    tagData.value = {};
    asyncOptions.value = [];
  },
};
const eventHandler = {} as any;
const makeEventHandler = () => {
  Object.keys(defHandler).forEach((item) => {
    eventHandler[`${props.eventPrefix}${item}`] = defHandler[item];
  });
};
onMounted(() => {
  makeEventHandler();
  onEvent(eventHandler);
});
onUnmounted(() => {
  makeEventHandler();
  offEvent(eventHandler);
});

watch(
  () => formData,
  (nv) => {
    doHttp(nv.value?.syncOptions || {}, asyncOptions);
    EventBus.emit(`${props.eventPrefix}formEditer_itemChange`, {
      ...tagData.value,
      schema: nv.value,
    });
  },
  {
    deep: true,
  },
);
</script>

<template>
  <div class="h-full w-full overflow-auto">
    <Form v-model="formData" :label-width="70" label-align="left">
      <Collapse :default-expand-all="true">
        <CollapsePanel>
          <template #header>
            <div>
              <span class="t-icon t-icon-edit"></span>
              <span>基础属性</span>
            </div>
          </template>
          <FormItem
            v-if="formData.name !== undefined"
            label="字段标识"
            name="name"
          >
            <Input v-model="formData.name" class="w-full" :disabled="formData.name == 'data_name' || formData.name == 'describe'"/>
          </FormItem>

          <FormItem
            v-if="formData.columns !== undefined"
            label="列"
            name="columns"
          >
            <Input v-model="formData.columns" class="w-full" />
          </FormItem>
          <FormItem
            v-if="formData.title !== undefined"
            label="标题"
            name="title"
          >
            <Input v-model="formData.title" class="w-full" :disabled="formData.name == 'data_name' || formData.name == 'describe'"/>
          </FormItem>
          <FormItem
            v-if="
              formData.placeholder !== undefined &&
              ![
                'UploadImage',
                'LuckySheet',
                'Ketcher',
                'UEditor',
                'Comment',
              ].includes(tagData.component)
            "
            label="提示说明"
            name="placeholder"
          >
            <Input v-model="formData.placeholder" class="w-full" />
          </FormItem>
<!--          <FormItem
            v-if="formData.action !== undefined"
            label="上传地址"
            name="action"
          >
            <Input v-model="formData.action" class="w-full" />
          </FormItem>-->
          <FormItem
            v-if="formData.theme !== undefined"
            label="展示方式"
            name="theme"
          >
            <!--            accept="image/png,image/jpeg,image/jpg"-->
            <Select v-model="formData.theme" style="width: 100%">
              <Option
                v-for="item in [
                  { label: '简单文件', value: 'file' },
                  { label: '简单图片', value: 'image' },
                  { label: '文件输入', value: 'file-input' },
                  { label: '图片拖拽大方格', value: 'image-flow' },
                  { label: '文件拖拽大方格', value: 'file-flow' },
                ]"
                :key="item.value"
                :label="`${item.label} [${item.value}]`"
                :value="item.value"
              />
            </Select>
          </FormItem>
          <FormItem
            v-if="formData.accept !== undefined"
            label="文件类型"
            name="theme"
          >
            <Select v-model="formData.accept" style="width: 100%" multiple>
              <Option
                v-for="item in [
                  { label: '任意文件', value: '*/*' },
                  { label: '仅图片', value: 'image/*' },
                  { label: '仅音频', value: 'audio/*' },
                  { label: '仅视频', value: 'video/*' },
                  { label: 'PNG', value: 'image/png' },
                  { label: 'JPEG', value: 'image/jpeg' },
                  { label: 'JPG', value: 'image/jpg' },
                  { label: 'PDF', value: '.pdf' },
                  { label: 'Word文档', value: '.doc,.docx' },
                  { label: 'Excel文档', value: '.xlx,.xlsx' },
                  { label: '压缩文件', value: '.zip,.rar,.7z' },
                ]"
                :key="item.value"
                :label="`${item.label} [${item.value}]`"
                :value="item.value"
              />
            </Select>
          </FormItem>
          <FormItem
            v-if="formData.max !== undefined"
            label="最大数量"
            name="max"
          >
            <Input v-model="formData.max" class="w-full" />
          </FormItem>
          <FormItem
            v-if="formData.multiple !== undefined"
            label="多文件"
            name="multiple"
          >
            <RadioGroup
              v-model="formData.multiple"
              placeholder="请输入"
              style="width: 100%"
            >
              <div
                class="flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border bg-white p-1 pl-2 pr-2"
              >
                <Radio :key="1" :value="true"> 是</Radio>
                <Radio :key="1" :value="false"> 否</Radio>
              </div>
            </RadioGroup>
          </FormItem>
          <FormItem
            v-if="formData.autoUpload !== undefined"
            label="自动上传"
            name="autoUpload"
          >
            <RadioGroup
              v-model="formData.autoUpload"
              placeholder="请输入"
              style="width: 100%" :disabled="true"
            >
              <div
                class="flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border bg-white p-1 pl-2 pr-2"
              >
                <Radio :key="1" :value="true"> 是</Radio>
                <Radio :key="1" :value="false"> 否</Radio>
              </div>
            </RadioGroup>
          </FormItem>
          <FormItem
            v-if="formData.showImageFileName !== undefined"
            label="显示文件名"
            name="showImageFileName"
          >
            <RadioGroup
              v-model="formData.showImageFileName"
              placeholder="请输入"
              style="width: 100%"
            >
              <div
                class="flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border bg-white p-1 pl-2 pr-2"
              >
                <Radio :key="1" :value="true"> 是</Radio>
                <Radio :key="1" :value="false"> 否</Radio>
              </div>
            </RadioGroup>
          </FormItem>

          <FormItem
            v-if="
              ![
                'UploadImage',
                'LuckySheet',
                'Ketcher',
                'UEditor',
                'Comment',
              ].includes(tagData.component) && formData.value !== undefined
            "
            label="是否只读"
            name="isReadonly"
          >
            <RadioGroup
              v-model="formData.isReadonly"
              placeholder="请输入"
              style="width: 100%"
            >
              <div
                class="flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border bg-white p-1 pl-2 pr-2"
              >
                <Radio :key="1" :value="true"> 是</Radio>
                <Radio :key="1" :value="false"> 否</Radio>
              </div>
            </RadioGroup>
          </FormItem>

          <FormItem
            v-if="
              ['Select'].includes(tagData.component) &&
              formData.value !== undefined
            "
            label="是否多选"
            name="multiple"
          >
            <RadioGroup
              v-model="formData.multiple"
              placeholder="请输入"
              style="width: 100%"
            >
              <div
                class="flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border bg-white p-1 pl-2 pr-2"
              >
                <Radio :key="1" :value="true"> 是</Radio>
                <Radio :key="1" :value="false"> 否</Radio>
              </div>
            </RadioGroup>
          </FormItem>

          <FormItem
            v-if="['Comment'].includes(tagData.component)"
            label="头像"
            name="avatar"
          >
            <Input v-model="formData.avatar" class="w-full" />
          </FormItem>
          <FormItem
            v-if="['Comment'].includes(tagData.component)"
            label="评论人"
            name="author"
          >
            <Input v-model="formData.author" class="w-full" />
          </FormItem>
          <FormItem
            v-if="['Comment'].includes(tagData.component)"
            label="时间"
            name="datetime"
          >
            <Input v-model="formData.datetime" class="w-full" />
          </FormItem>
          <FormItem
            v-if="['Comment'].includes(tagData.component)"
            label="内容"
            name="content"
          >
            <Input v-model="formData.content" class="w-full" />
          </FormItem>

          <FormItem
            v-if="
              ![
                'UploadImage',
                'LuckySheet',
                'Ketcher',
                'UEditor',
                'DynamicTable',
                'Comment',
              ].includes(tagData.component) && formData.value !== undefined
            "
            label="默认值"
            name="value"
          >
            <Input
              v-if="tagData.component === 'Input'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            />
            <InputNumber
              v-if="tagData.component === 'InputNumber'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            />
            <Textarea
              v-if="tagData.component === 'Textarea'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            />
            <RadioGroup
              v-if="tagData.component === 'Radio'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            >
              <div
                class="flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border bg-white p-1 pl-2 pr-2"
              >
                <Tooltip
                  v-for="item in [...formData.options, ...asyncOptions]"
                  :content="item.value"
                >
                  <Radio :key="item.value" :value="item.value">
                    {{ item.label }}
                  </Radio>
                </Tooltip>
              </div>
            </RadioGroup>
            <CheckboxGroup
              v-if="tagData.component === 'Checkbox'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            >
              <div
                class="flex max-h-[100px] w-full flex-wrap gap-2 overflow-auto rounded border bg-white p-1 pl-2 pr-2"
              >
                <Tooltip
                  v-for="item in [
                    ...(formData?.options || []),
                    ...asyncOptions,
                  ]"
                  :content="item.value"
                >
                  <Checkbox :key="item.value" :value="item.value">
                    {{ item.label }}
                  </Checkbox>
                </Tooltip>
              </div>
            </CheckboxGroup>
            <Select
              v-if="tagData.component === 'Select'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            >
              <Option
                v-for="item in [...(formData.options || []), ...asyncOptions]"
                :key="item.value"
                :label="`${item.label} [${item.value}]`"
                :value="item.value"
              />
            </Select>
            <DatePicker
              v-if="tagData.component === 'DatePicker'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            />
            <DatePicker
              v-if="tagData.component === 'DateAndTimePicker'"
              v-model="formData.value"
              :enable-time-picker="true"
              placeholder="请输入"
              style="width: 100%"
            />
            <TimePicker
              v-if="tagData.component === 'TimePicker'"
              v-model="formData.value"
              placeholder="请输入"
              style="width: 100%"
            />
          </FormItem>

          <!--        <FormItem-->
          <!--          v-if="formData.maxLength != undefined"-->
          <!--          label="最大位数"-->
          <!--          name="maxLength"-->
          <!--        >-->
          <!--          <Input v-model="formData.maxLength" class="w-full" />-->
          <!--        </FormItem>-->
        </CollapsePanel>
        <CollapsePanel v-if="formData.rules && formData.rules.length > 0">
          <template #header>
            <div>
              <span class="t-icon t-icon-edit"></span>
              <span>表单校验</span>
            </div>
          </template>
          <FormItem
            v-if="formData.rules && formData.rules.length > 0"
            name="rules"
            label-align="top"
          >
            <RulesConfig v-model="formData.rules" class="w-full" :isReadOnly="formData.name == 'data_name'"/>
          </FormItem>
        </CollapsePanel>

        <CollapsePanel v-if="formData.needPanel">
          <template #header>
            <div>
              <span class="t-icon t-icon-edit"></span>
              <span>分组配置</span>
            </div>
          </template>
          <FormItem label-align="top" name="options">
            <PanelConfig v-model="formData.children" />
          </FormItem>
        </CollapsePanel>
        <CollapsePanel v-if="formData.needOptions">
          <template #header>
            <div>
              <span class="t-icon t-icon-edit"></span>
              <span>静态数据</span>
            </div>
          </template>
          <FormItem label-align="top" name="options">
            <OptionConfig v-model="formData.options" />
          </FormItem>
        </CollapsePanel>
        <CollapsePanel v-if="formData.needOptions">
          <template #header>
            <div>
              <span class="t-icon t-icon-edit"></span>
              <span>动态数据</span>
            </div>
          </template>
          <FormItem label-align="top" name="syncOptions">
            <SyncOptionConfig v-model="formData.syncOptions" />
          </FormItem>
        </CollapsePanel>
      </Collapse>
    </Form>
  </div>
</template>

<style scoped>
:deep(.t-collapse-panel__content) {
  padding: 6px;
}

:deep(.t-form__item) {
  margin-right: 0;
  margin-bottom: 6px;
}

:deep(.t-collapse-panel__header) {
  padding: 6px;
}

:deep(.t-collapse-panel__body) {
  background: transparent !important;
}
</style>
