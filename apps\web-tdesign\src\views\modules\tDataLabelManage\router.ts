import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '标签管理',
    },
    name: 'tDataLabelManage',
    path: '/tDataLabelManage',
    children: [
      {
        meta: {
          title: '标签管理编辑',
        },
        name: 'tDataLabelManageIndex',
        path: '/tDataLabelManage/index',
        component: () =>
          import('#/views/modules/tDataLabelManage/index.vue'),
      },
    ],
  },
];

export default routes;
