<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Card,
  Form,
  FormItem,
  type FormProps,
  RadioButton,
  RadioGroup,
} from 'tdesign-vue-next';

import { getDictItems } from '#/api';

import { updateRead } from '../api';

const formData: any = ref({});
const form = ref();

const status: any = ref([]);

const FORM_RULES: FormProps['rules'] = {
  status: [
    {
      required: true,
      message: '必填',
    },
  ],
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.setData({ record: formData.value });
    modalApi.close();
  },
  async onConfirm() {
    const vali = await form.value.validate();

    if (vali === true) {
      // 验证通过提交请求 并且关闭窗口
      await updateRead(formData.value.readStatus, formData.value);
      modalApi.getData().refresh();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      console.log('打开时触发');
      formData.value = isOpen
        ? { ...modalApi.getData<Record<string, any>>()?.record }
        : {};
    }
  },
  title: '新增',
});
const isEdit = computed(() => {
  return modalApi.useStore().value.title === '新增';
});

onMounted(async () => {
  status.value = await getDictItems('MSG_READ');
});
</script>
<template>
  <Modal class="w-[40%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES">
      <div class="grid grid-cols-1 gap-2">
        <FormItem label="消息内容" name="content">
          {{ formData.content }}
        </FormItem>

        <FormItem label="状态" name="read">
          <!-- <Select
            v-model="formData.read"
            :options="status"
            clearable
            placeholder="选择状态"
          /> -->

          <RadioGroup v-model="formData.readStatus">
            <RadioButton
              v-for="item in status"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </RadioButton>
          </RadioGroup>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>
<style lang="less" scoped>
.form-item-content {
  width: 100%;
}
</style>
