<template>
  <Header class="web-lio-header">
    <div style="  display: flex; justify-content: center;">
    <Row justify="space-between" align="middle" style="padding-top: 15px;
    padding-bottom: 15px;">
      <Col class="flex items-center" style="padding: 0 2rem; margin-right: 450px">
        <!-- Logo -->
        <div @click="goHome">
          <img src="/static/images/02-Vector.png" :alt="$t('webLio.header.logoAlt')"
               class="h-8 w-auto mr-8" style="width: 70px;height: 70px;"/>
        </div>
        <Space :size="10" style="display: inline-block" @click="goHome">
          <div class="title1">{{ $t('webLio.header.title.title1') }}</div>
          <div class="title2">{{ $t('webLio.header.title.title2') }}</div>
        </Space>
      </Col>
      <Col style="padding: 0 2rem;">
        <Space :size="8">
          <template v-if="!accessStore.accessToken">
            <div ref="loginButtonRef" style="display: inline-block;">
              <Button theme="default" variant="text" @click="toggleLoginModal">{{ $t('webLio.header.loginRegister') }}</Button>
            </div>
          </template>
          <template v-else>
            <div class="user-info" ref="userDropdownRef" @click="toggleUserDropdown" :aria-expanded="showUserDropdown">
              <span style="color: #0052d9;">{{ userStore.userInfo?.accountNumber || userStore.userInfo?.realName || '用户' }}</span>
              <ChevronDownIcon class="user-arrow" :class="{ 'is-active': showUserDropdown }" />
            </div>
          </template>
          <span class="header-divider">|</span>
          <div class="language-switch" ref="langDropdownRef" @click="toggleLangDropdown">
            <img src="/static/images/language-icon.png" alt="Language" class="lang-icon" />
            <span class="current-lang">{{ preferences.app.locale === 'zh-CN' ? '中文' : 'English' }}</span>
            <ChevronDownIcon class="lang-arrow" :class="{ 'is-active': showLangDropdown }" />
          </div>
        </Space>
      </Col>
    </Row>
    </div>
    <div style="background-color: #063E8B; display: flex; justify-content: center;">
      <Row style="background-color: #063E8B">
        <Col class="flex items-center" style="padding: 0 2rem;">
          <Space :size="10" style="display: inline-block;">
            <HeadMenu expandType="popup" v-model="menu" style="background-color: #063E8B;">
              <MenuItem value="1" @click="clickMenu('1','1')">{{ $t('webLio.header.menu.menu1') }}</MenuItem>
              <Submenu value="2" @click="clickMenu('2','2')">
                <template #title>
                  <span>{{ $t('webLio.header.menu.menu2') }}</span>
                </template>
                <MenuItem value="21" @click="clickMenu('2','21')">{{ $t('webLio.header.menu.menu21') }}</MenuItem>
                <MenuItem value="22" @click="clickMenu('2','22')">{{ $t('webLio.header.menu.menu22') }}</MenuItem>
                <MenuItem value="23" @click="clickMenu('2','23')">{{ $t('webLio.header.menu.menu23') }}</MenuItem>
                <MenuItem value="24" @click="clickMenu('2','24')">{{ $t('webLio.header.menu.menu24') }}</MenuItem>
              </Submenu>
<!--              <Submenu value="3" @click="clickMenu('3','3')">-->
<!--                <template #title>-->
<!--                  <span>{{ $t('webLio.header.menu.menu3') }}</span>-->
<!--                </template>-->
<!--                <MenuItem value="31" @click="clickMenu('3','31')">{{ $t('webLio.header.menu.menu31') }}</MenuItem>-->
<!--                <MenuItem value="32" @click="clickMenu('3','32')">{{ $t('webLio.header.menu.menu32') }}</MenuItem>-->
<!--                <MenuItem value="33" @click="clickMenu('3','33')">{{ $t('webLio.header.menu.menu33') }}</MenuItem>-->
<!--                <MenuItem value="34" @click="clickMenu('3','34')">{{ $t('webLio.header.menu.menu34') }}</MenuItem>-->
<!--              </Submenu>-->
              <MenuItem value="3" @click="clickMenu('3','3')">{{ $t('webLio.header.menu.menu3') }}</MenuItem>
              <Submenu value="4" @click="clickMenu('4','4')">
                <template #title>
                  <span>{{ $t('webLio.header.menu.menu4') }}</span>
                </template>
                <MenuItem value="41" @click="clickMenu('4','41')">{{ $t('webLio.header.menu.menu41') }}</MenuItem>
                <MenuItem value="42" @click="clickMenu('4','42')">{{ $t('webLio.header.menu.menu42') }}</MenuItem>
              </Submenu>
              <Submenu value="5" @click="clickMenu('5','5')">
                <template #title>
                  <span>{{ $t('webLio.header.menu.menu5') }}</span>
                </template>
                <MenuItem value="51" @click="clickMenu('5','51')">{{ $t('webLio.header.menu.menu51') }}</MenuItem>
                <MenuItem value="52" @click="clickMenu('5','52')">{{ $t('webLio.header.menu.menu52') }}</MenuItem>
              </Submenu>
              <MenuItem value="6" @click="clickMenu('6','6')">{{ $t('webLio.header.menu.menu6') }}</MenuItem>
              <MenuItem value="7" @click="clickMenu('7','7')">{{ $t('webLio.header.menu.menu7') }}</MenuItem>
            </HeadMenu>
          </Space>
        </Col>
      </Row>
    </div>
    <!-- 登录弹窗 -->
    <teleport to="body">
      <div v-if="showLoginModal">
        <div class="login-modal-overlay" @click="showLoginModal = false"></div>
        <div class="login-modal" :style="{ top: loginModalPosition.top + 'px', left: loginModalPosition.left + 'px' }">
          <div class="modal-arrow"></div>
          <form class="login-form" @submit.prevent="handleLogin">
            <input v-model="accountNumber" class="form-input" :placeholder="$t('webLio.login.username')" />
            <div class="password-input-wrapper">
              <input v-model="password" :type="showPassword ? 'text' : 'password'" class="form-input" :placeholder="$t('webLio.login.password')" />
              <button type="button" class="password-toggle" @click="togglePasswordVisibility">
                <EyeOpenIcon v-if="showPassword" class="eye-icon" />
                <EyeCloseIcon v-else class="eye-icon" />
              </button>
            </div>
            <button class="login-button" :disabled="isLoading">
              <Loading v-if="isLoading" class="login-loading" size="small" />
              <span v-else>{{ $t('webLio.login.loginBtn') }}</span>
            </button>
            <div class="login-footer">
              <a class="register-link" href="#" @click="register">{{ $t('webLio.login.register') }}</a>
              <a class="forgot-password" href="#">{{ $t('webLio.login.forgotPassword') }}</a>
            </div>
          </form>
        </div>
      </div>
    </teleport>

    <!-- 用户下拉悬浮窗 -->
    <teleport to="body">
      <div v-if="showUserDropdown">
        <div class="user-dropdown-overlay" @click="showUserDropdown = false"></div>
        <div class="user-dropdown-panel" :style="{ top: userDropdownPosition.top + 'px', left: userDropdownPosition.left + 'px' }">
          <div class="dropdown-arrow" style="right: 30px; left: auto; top: -8px;"></div>
          <div class="user-info-content">
            <div class="user-header">
              <span class="greeting" style="font-size: 15px; color: #333; display: block; margin-bottom: 0;">{{ $t('webLio.header.user.greeting') }}</span>
              <span class="user-name" style="font-size: 17px; color: #0052d9; display: block; margin-bottom: 14px; font-weight: normal;">{{ userStore.userInfo?.realName }}</span>
            </div>
            <div style="border-top: 1px solid #eee; margin-bottom: 0;"> </div>
            <div class="user-details" style="padding: 0px 0 0 0; margin-bottom: 12px;">
              <div class="detail-item" style="margin-bottom: 10px; display: flex; flex-direction: column; align-items: flex-start;">
                <span class="label" style="font-size: 13px; color: #888; margin-bottom: 2px;">{{ $t('webLio.header.user.department') }}</span>
                <span class="value" style="font-size: 14px; color: #333;">{{ userStore.userInfo?.department || '--' }}</span>
              </div>
              <div class="detail-item" style="margin-bottom: 10px; display: flex; flex-direction: column; align-items: flex-start;">
                <span class="label" style="font-size: 13px; color: #888; margin-bottom: 2px;">{{ $t('webLio.header.user.researcher') }}</span>
                <span class="value" style="font-size: 14px; color: #333;">{{ userStore.userInfo?.realName || '--' }}</span>
              </div>
              <div class="detail-item" style="margin-bottom: 10px; display: flex; flex-direction: column; align-items: flex-start;">
                <span class="label" style="font-size: 13px; color: #888; margin-bottom: 2px;">{{ $t('webLio.header.user.account') }}</span>
                <span class="value" style="font-size: 14px; color: #333;">{{ userStore.userInfo?.username || '--' }}</span>
              </div>
              <div class="detail-item" style="display: flex; flex-direction: column; align-items: flex-start;">
                <span class="label" style="font-size: 13px; color: #888; margin-bottom: 2px;">{{ $t('webLio.header.user.email') }}</span>
                <span class="value" style="font-size: 14px; color: #333;">{{ userStore.userInfo?.email || '--' }}</span>
              </div>
            </div>
            <div class="user-button-details">
              <div class="detail-button-item">
                <Button class="label" variant="outline" theme="primary" @click="toTask">待办</Button>
                <Button class="label" variant="outline" theme="primary" @click="toHisInstance">已办</Button>
              </div>
              <div class="detail-button-item">
                <Button class="label" variant="outline" theme="primary" @click="toMyInstance">审批查询</Button>
                <Button class="label" variant="outline" theme="primary" @click="toFavorites">收藏夹</Button>
              </div>
              <div class="detail-button-item">
                <Button class="label" variant="outline" theme="primary" @click="toDataBase">我的数据</Button>
                <Button class="label" variant="outline" theme="primary" @click="toUnstructured">我的文件</Button>
              </div>
            </div>
            <button class="logout-btn" style="width: 100%; height: 36px; background: #001F5C; border: none; border-radius: 6px; color: white; font-size: 14px; font-weight: normal; cursor: pointer;" @click="handleLogout">退出</button>
          </div>
        </div>
      </div>
    </teleport>


    <!-- 语言切换下拉悬浮窗 -->
    <teleport to="body">
      <div v-if="showLangDropdown">
        <div class="lang-dropdown-overlay" @click="showLangDropdown = false"></div>
        <div class="lang-dropdown-panel" :style="{ top: langDropdownPosition.top + 'px', left: langDropdownPosition.left + 'px', padding: '8px 12px' }">
          <div class="dropdown-arrow" style="right: 60px; left: auto; top: -8px;"></div>
          <div class="lang-options">
            <div class="lang-option" :class="{ active: preferences.app.locale === 'zh-CN' }" @click="handleUpdate('zh-CN'); showLangDropdown = false;" style="display: flex; align-items: center; justify-content: space-between; padding: 6px 16px; border-radius: 4px; font-size: 16px; color: #0052d9; background: preferences.app.locale === 'zh-CN' ? '#e6f4ff' : '#fff'; font-weight: normal; cursor: pointer; margin-bottom: 2px;">
              <span :style="{ color: preferences.app.locale === 'zh-CN' ? '#0052d9' : '#333' }">中文</span>
              <span v-if="preferences.app.locale === 'zh-CN'" class="check-icon" style="display: flex; align-items: center; margin-left: 12px;">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4 8.5L7 11.5L12 5.5" stroke="#0052d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </span>
            </div>
            <div class="lang-option" :class="{ active: preferences.app.locale === 'en-US' }" @click="handleUpdate('en-US'); showLangDropdown = false;" style="display: flex; align-items: center; justify-content: space-between; padding: 6px 16px; border-radius: 8px; font-size: 16px; color: #333; background: preferences.app.locale === 'en-US' ? '#e6f4ff' : '#fff'; font-weight: normal; cursor: pointer;">
              <span :style="{ color: preferences.app.locale === 'en-US' ? '#0052d9' : '#333' }">English</span>
              <span v-if="preferences.app.locale === 'en-US'" class="check-icon" style="display: flex; align-items: center; margin-left: 12px;">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4 8.5L7 11.5L12 5.5" stroke="#0052d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </span>
            </div>
          </div>
        </div>
      </div>
    </teleport>
  </Header>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue';
import { Header, Button, Space, Row, Col, MessagePlugin, Loading ,HeadMenu,MenuItem,Submenu} from 'tdesign-vue-next';
import {
  ChevronDownIcon,
  BrowseIcon as EyeOpenIcon,
  BrowseOffIcon as EyeCloseIcon,
} from 'tdesign-icons-vue-next';
import { useUserStore, useAccessStore } from '@vben/stores';
import { preferences, updatePreferences } from '@vben/preferences';
import { useRouter, useRoute } from 'vue-router';
import { loadLocaleMessages } from '@vben/locales';
import type { SupportedLanguagesType } from '@vben/locales';
import { useAuthStore } from '#/store';
import { $t } from '#/locales';

const userStore = useUserStore();
const accessStore = useAccessStore();
const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
const activeNav = ref('');

// 登录弹窗相关
const showLoginModal = ref(false);
const loginButtonRef = ref<HTMLElement | null>(null);
const loginModalPosition = ref({ top: 0, left: 0 });
const isLoading = ref(false);
const accountNumber = ref('');
const password = ref('');
const showPassword = ref(false);

const toggleLoginModal = async () => {
  showLoginModal.value = !showLoginModal.value;
  if (showLoginModal.value) {
    await nextTick();
    if (loginButtonRef.value) {
      const buttonRect = loginButtonRef.value.getBoundingClientRect();
      const buttonWidth = buttonRect.width;
      const modalWidth = 380;
      loginModalPosition.value = {
        top: buttonRect.bottom + 8,
        left: buttonRect.right - modalWidth,
      };
      const arrowOffset = buttonWidth / 2;
      document.documentElement.style.setProperty('--arrow-right', `${arrowOffset}px`);
    }
  }
};
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};
const handleLogin = async () => {
  if (!accountNumber.value) {
    MessagePlugin.warning($t('webLio.login.validation.usernameRequired'));
    return;
  }
  if (!password.value) {
    MessagePlugin.warning($t('webLio.login.validation.passwordRequired'));
    return;
  }
  isLoading.value = true;
  try {
    const loginResult = await authStore.authLogin({
      accountNumber: accountNumber.value,
      password: password.value
    });
    if (accessStore.accessToken && loginResult.userInfo?.realName) {
      showLoginModal.value = false;
      accountNumber.value = '';
      password.value = '';
    }
    // 刷新页面 (整体刷新效果不好)
    // window.location.reload();
  } catch (error) {
    console.error('Login failed:', error);
  } finally {
    isLoading.value = false;
  }
};

// 用户下拉相关
const showUserDropdown = ref(false);
const userDropdownRef = ref<HTMLElement | null>(null);
const userDropdownPosition = ref({ top: 0, left: 0 });
const handleLogout = async () => {
  await authStore.logoutWebLio();
  showUserDropdown.value = false
};
const toggleUserDropdown = () => {
  showLangDropdown.value = false
  showUserDropdown.value = !showUserDropdown.value;
  if (showUserDropdown.value && userDropdownRef.value) {
    const buttonRect = userDropdownRef.value.getBoundingClientRect();
    const dropdownWidth = 280;
    userDropdownPosition.value = {
      top: buttonRect.bottom + 8,
      left: buttonRect.right - dropdownWidth
    };
  }
};

// 语言切换下拉相关
const showLangDropdown = ref(false);
const langDropdownRef = ref<HTMLElement | null>(null);
const langDropdownPosition = ref({ top: 0, left: 0 });
async function handleUpdate(value: string) {
  // 只允许 'zh-CN' 和 'en-US'
  const locale = value as SupportedLanguagesType;
  updatePreferences({ app: { locale } });
  await loadLocaleMessages(locale);
}
const toggleLangDropdown = () => {
  showUserDropdown.value = false
  showLangDropdown.value = !showLangDropdown.value;
  if (showLangDropdown.value) {
    nextTick(() => {
      const button = document.querySelector('.language-switch');
      if (button) {
        const buttonRect = button.getBoundingClientRect();
        const dropdownWidth = 120;
        langDropdownPosition.value = {
          top: buttonRect.bottom + 8,
          left: Math.max(0, buttonRect.right - dropdownWidth)
        };
      }
    });
  }
};

if (route.path === '/aaa') {
  activeNav.value = 'webLio.header.nav.nav1';
}else if(route.path === '/aaa'){
  activeNav.value = 'webLio.header.nav.nav2';
}else if(route.path === '/tDataBaseIndex'){
  activeNav.value = 'webLio.header.nav.nav3';
}else if(route.path === '/aaa'){
  activeNav.value = 'webLio.header.nav.nav4';
}  else {
  activeNav.value = '';
}

const handleNavClick = (navKey: string) => {
  if (navKey == 'webLio.header.nav.nav3'){
    if(userStore.userInfo){
      activeNav.value = navKey;
      router.push({name:'tDataBaseIndex'});
    }else{
      activeNav.value = ''
      MessagePlugin.warning("请先进行登录")
    }
  } else{
    MessagePlugin.warning($t(navKey)+'功能正在开发中，敬请期待！');
  }
};
const toTask = () => {
  showUserDropdown.value = false
  router.push({name: 'MyTask'})
}
const toHisInstance = () => {
  showUserDropdown.value = false
  router.push({name: 'MyHisInstance'})
}
const toFavorites = () => {
  showUserDropdown.value = false
  router.push({name: 'tPortalFavoritesIndex'})
}
const toUnstructured = () => {
  showUserDropdown.value = false
  router.push({name: 'tDataUnstructuredIndex'})
}
const toMyInstance = () => {
  showUserDropdown.value = false
  router.push({name: 'MyInstance'})
}
const toDataBase = () => {
  showUserDropdown.value = false
  router.push({name: 'tDataBaseIndex'})
}
const goHome = () => {
  showUserDropdown.value = false
  activeNav.value = ''
  router.push({name: 'home'})
}
const clickMenu = (pid,value) => {
  menu.value = pid
  if(value == '1'){
    router.push({name: 'home'})
  }else if(value == '2'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 0}})
  }else if(value == '3'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 22}})
  }else if(value == '21'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 1}})
  }else if(value == '22'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 23}})
  }else if(value == '23'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 24}})
  }else if(value == '24'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 25}})
  }else if(value == '31'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 26}})
  }else if(value == '32'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 27}})
  }else if(value == '33'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 28}})
  }else if(value == '34'){
    router.push({name: 'tSearchIndex', query: {CategoryCode: 29}})
  }else if(value == '4'){
    router.push({name: 'tDataServiceIndex'})
  }else if(value == '41'){
    /*const newWindow = window.open('https://www.dicp.ac.cn/', '_blank');
    if (newWindow) {
      newWindow.opener = null; // 增强安全性，防止 opener 攻击
    } else {
      console.warn('请允许弹出窗口以继续');
    }*/
    MessagePlugin.warning('功能正在开发中，敬请期待！');
  }else if(value == '42'){
    /*const newWindow = window.open('https://www.dicp.ac.cn/', '_blank');
    if (newWindow) {
      newWindow.opener = null; // 增强安全性，防止 opener 攻击
    } else {
      console.warn('请允许弹出窗口以继续');
    }*/
    MessagePlugin.warning('功能正在开发中，敬请期待！');
  }else if(value == '6'){
    if(userStore.userInfo){
      router.push({name: 'tDataIndex'})
    }else{
      MessagePlugin.warning("请先进行登录")
    }
  }else if(value == '7'){
    router.push({name: 'aboutUs'})
  }else{
    MessagePlugin.warning('功能正在开发中，敬请期待！');
  }
}
const menu = ref('1')
onMounted(async () => {
});
const register = () => {
  router.push('/auth/register')
}
</script>

<style scoped>
.web-lio-header {
  background-color: #ffffff;
  color: #333333;
  height: 156px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid #e5e7eb;
}
.title1 {
  font-size: 29px;
  font-weight: bold;
  padding-top: 13px;
}
.title2 {
  padding-top: 6px;
}
:deep(.t-menu__item.t-is-active) {
  color: #ffffff;
  background-color: #1760C2;
  height: 56px;
  width: 160px;
}
:deep(.t-menu__item:hover:not(.t-is-active):not(.t-is-opened):not(.t-is-disabled)) {
  color: #ffffff;
  background-color: #1760C2;
  height: 56px;
  width: 160px;
}
:deep(.t-head-menu .t-menu__item) {
  color: #ffffff;
  font-size: 20px;
  font-weight: 700;
  height: 56px;
  width: 160px;
  font-family: "MicrosoftYaHei";
  line-height: 1.6;
}
.header-divider {
  color: var(--td-text-color-primary);
  font-size: 12px;
  margin: 0 12px;
  opacity: 1;
  font-weight: 300;
  display: flex;
  align-items: center;
  height: 32px;
  vertical-align: middle;
}
.language-switch {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 90px;
}
.language-switch:hover {
  background-color: #edf2f7;
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.lang-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}
.current-lang {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}
.lang-arrow {
  width: 16px;
  height: 16px;
  color: #6b7280;
  transition: transform 0.2s ease;
}
.lang-arrow.is-active {
  transform: rotate(180deg);
}
.user-info {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  height: 32px;
  gap: 8px;
}
.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 999;
}
.login-modal {
  position: fixed;
  background: white;
  width: 380px;
  padding: 20px 24px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: modalSlideDown 0.2s ease;
}
.modal-arrow {
  position: absolute;
  top: -8px;
  right: var(--arrow-right, 62px);
  width: 16px;
  height: 8px;
  overflow: hidden;
}
.modal-arrow::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background: white;
  transform: translateY(50%) rotate(45deg);
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
  border-radius: 2px;
}
@keyframes modalSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.form-input {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  padding-right: 40px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  background-color: #fff;
}
.form-input:focus {
  border-color: #0052d9;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 82, 217, 0.1);
}
.form-input::placeholder {
  color: #999;
}
.password-input-wrapper {
  position: relative;
  width: 100%;
}
.password-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 2;
  padding: 0;
}
.password-toggle:hover {
  color: #0052d9;
}
.eye-icon {
  width: 18px;
  height: 18px;
  color: #999;
  transition: color 0.3s ease;
}
.eye-icon:hover {
  color: #666;
}
.login-button {
  width: 100%;
  height: 40px;
  background-color: #001F5C;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-button:hover {
  opacity: 0.9;
}
.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
.login-loading {
  color: white;
}
.login-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  margin-top: 4px;
}
.register-link, .forgot-password {
  color: #0052d9;
  text-decoration: none;
  transition: color 0.3s ease;
}
.register-link:hover, .forgot-password:hover {
  color: #003eb3;
}
.user-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 999;
}
.user-dropdown-panel {
  position: fixed;
  background: white;
  width: 280px;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  top: 0;
  left: 0;
}
.dropdown-arrow {
  position: absolute;
  top: -8px;
  left: 12px;
  width: 16px;
  height: 8px;
  overflow: hidden;
}
.dropdown-arrow::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background: white;
  transform: translateY(50%) rotate(45deg);
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
  border-radius: 2px;
}
.user-info-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.greeting {
  font-size: 14px;
  font-weight: 500;
}
.user-name {
  font-size: 14px;
  font-weight: 500;
}
.user-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-bottom: 1px solid #eee;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.label {
  font-size: 12px;
  font-weight: 500;
}
.value {
  font-size: 14px;
  font-weight: 500;
}
.logout-btn {
  width: 100%;
  height: 40px;
  background-color: #001F5C;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}
.logout-btn:hover {
  opacity: 0.9;
}
.lang-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 999;
}
.lang-dropdown-panel {
  position: fixed;
  background: white;
  width: 140px;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  top: 0;
  left: 0;
}
.dropdown-arrow {
  position: absolute;
  top: -8px;
  left: 12px;
  width: 16px;
  height: 8px;
  overflow: hidden;
}
.dropdown-arrow::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background: white;
  transform: translateY(50%) rotate(45deg);
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
  border-radius: 2px;
}
.lang-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.lang-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}
.lang-option:hover {
  background-color: #edf2f7;
}
.lang-option.active {
  background-color: #edf2f7;
}
.check-icon {
  margin-left: 12px;
  display: flex;
  align-items: center;
}
.user-button-details {
  border-bottom: 1px solid #eee;
  margin-bottom: 12px;
}
.detail-button-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.detail-button-item .label {
  width: 120px;
}
:deep(.t-head-menu .t-menu__item) {
  width: auto;
  min-width: 160px;
}
</style>
