<script setup lang="ts">
import {
  Button,
  Form,
  FormItem,
  Input,
  Link,
  MessagePlugin,
  Pagination,
  Popconfirm,
  Space,
  Table,
} from 'tdesign-vue-next';
import { onMounted, reactive, ref } from 'vue';

import { claimTask, claimTasks, myTaskByPage2 } from '../api';
import PermissionApprovalDialog from './permission-approval/index.vue';

const permissionApprovalRef = ref();
// 查询表单
const searchForm = reactive({
  dataName: '',
  workflowName: '',
  status: '',
});

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 批量选择相关
const selectedRowKeys = ref<any[]>([]);
const batchClaimLoading = ref(false);

// 表格列
const columns: any[] = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 50,
    align: 'center' as const,
  },
  {
    title: '序号',
    colKey: 'serial-number',
    width: 80,
    align: 'center' as const,
  },
  {
    colKey: 'arg.operation_name',
    title: '数据名称',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'arg.operation_version',
    title: '数据版本',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'processName',
    title: '流程名称',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'currentNodeName',
    title: '当前任务',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'createTime',
    title: '提交时间',
    sorter: true,
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'op',
    title: '操作',
    align: 'center' as const,
  },
];

const handleApproval = (record: any) => {
  permissionApprovalRef.value?.openDialog(record);
};

// 查询
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.pageNum,
      pageSize: pagination.pageSize,
      param: {
        ...searchForm,
      },
      sorts: [],
    };
    const res = await myTaskByPage2(params);
    tableData.value = res?.records.map((item: any) => {
      return {
        ...item,
        arg: JSON.parse(item.variable),
      };
    });
    total.value = res?.total || 0;
    // 清空选择
    selectedRowKeys.value = [];
  } finally {
    loading.value = false;
  }
};

// 查询按钮
const onSearch = () => {
  pagination.pageNum = 1;
  fetchData();
};

// 重置按钮
const onReset = () => {
  searchForm.dataName = '';
  searchForm.workflowName = '';
  searchForm.status = '';
  pagination.pageNum = 1;
  fetchData();
};

// 分页切换
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.pageNum = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchData();
};

// 单个认领
const handleClaimTask = async (record: any) => {
  await claimTask({
    id: record.taskId,
  });
  MessagePlugin.success('认领成功！');
  fetchData();
};

// 批量认领
const handleBatchClaim = async () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请选择要认领的任务！');
    return;
  }

  try {
    batchClaimLoading.value = true;
    await claimTasks([...selectedRowKeys.value]);

    MessagePlugin.success(
      `批量认领成功！共认领 ${selectedRowKeys.value.length} 个任务`,
    );
    fetchData();
  } catch {
    MessagePlugin.error('批量认领失败，请重试');
  } finally {
    batchClaimLoading.value = false;
  }
};

// 表格选择变化 - 只允许选择可认领的任务
const onSelectChange = (value: string[]) => {
  // 过滤出只有可认领的任务
  const validSelectedKeys = value.filter((key) => {
    const row = tableData.value.find((item) => item.taskId === key);
    return row && row.actorType !== 0;
  });
  selectedRowKeys.value = validSelectedKeys;
};

// 判断行是否可选择
const getSelectableRowKeys = () => {
  return tableData.value
    .filter((item) => item.actorType !== 0)
    .map((item) => item.taskId);
};

// 首次加载
onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="layout-main">
    <div class="inquiry-under-approval">
      <!-- 查询表单 -->
      <div class="search-section">
        <Form layout="inline" @submit.prevent="onSearch" class="search-form">
          <FormItem label="数据名称">
            <Input
              v-model="searchForm.dataName"
              placeholder="请输入数据名称"
              style="width: 200px"
            />
          </FormItem>
          <FormItem label="流程名称">
            <Input
              v-model="searchForm.workflowName"
              placeholder="请输入流程名称"
              style="width: 200px"
            />
          </FormItem>
          <FormItem>
            <Space size="small">
              <Button theme="primary" type="submit" @click="onSearch">
                查询
              </Button>
              <Button @click="onReset" variant="outline"> 重置 </Button>
            </Space>
          </FormItem>
        </Form>
      </div>

      <!-- 批量操作区域 -->
      <div class="table-header" v-if="selectedRowKeys.length > 0">
        <div class="batch-actions">
          <div class="selected-info">
            <span class="info-text">
              已选择 {{ selectedRowKeys.length }} 个可认领的任务
            </span>
          </div>
          <div class="action-buttons">
            <Popconfirm
              :content="`确定要批量认领选中的 ${selectedRowKeys.length} 个任务吗？`"
              theme="default"
              @confirm="handleBatchClaim"
            >
              <Button theme="primary" :loading="batchClaimLoading" size="small">
                批量认领
              </Button>
            </Popconfirm>
          </div>
        </div>
      </div>

      <!-- 列表表格 -->
      <div class="table-section">
        <Table
          :data="tableData"
          :columns="columns"
          :loading="loading"
          row-key="taskId"
          class="custom-table"
          bordered
          :selected-row-keys="selectedRowKeys"
          :selectable-row-keys="getSelectableRowKeys()"
          @select-change="onSelectChange"
        >
          <template #serial-number="{ rowIndex }">
            {{ (pagination.pageNum - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
          <template #op="{ row }">
            <Space size="small">
              <Popconfirm
                v-if="row.actorType !== 0"
                content="是否认领该任务？"
                theme="default"
                @confirm="handleClaimTask(row)"
              >
                <Link theme="primary">认领</Link>
              </Popconfirm>
              <Link
                v-if="row.actorType === 0"
                theme="primary"
                @click="handleApproval(row)"
              >
                审批
              </Link>
            </Space>
          </template>
        </Table>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <Pagination
          :total="total"
          :page-size="pagination.pageSize"
          :current="pagination.pageNum"
          @change="onPageChange"
          show-size-changer
          show-total
        />
      </div>
    </div>
  </div>
  <div class="footer-bar"></div>
  <PermissionApprovalDialog ref="permissionApprovalRef" @refresh="fetchData" />
</template>

<style scoped>
.layout-main {
  display: flex;
  gap: 32px;
  margin-bottom: 32px;
  justify-content: center;
}

.inquiry-under-approval {
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  flex: 1 1 0%;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 16px;
}

.search-form .t-form__item {
  margin-bottom: 0 !important;
}

/* 表格头部区域 */
.table-header {
  margin-bottom: 16px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #bbdefb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.selected-info {
  display: flex;
  align-items: center;
}

.info-text {
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  margin-right: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.custom-table :deep(.t-table-thead) th {
  background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
  color: #fff;
  font-weight: 600;
  border-bottom: none;
  padding: 16px 12px;
}

.custom-table :deep(.t-table-tbody) td {
  text-align: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.custom-table :deep(.t-table-tbody) tr:hover {
  background-color: #f8f9fa;
}

.custom-table :deep(.t-table-tbody) tr:nth-child(even) {
  background-color: #fafafa;
}

/* 分页区域 */
.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

.footer-bar {
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .inquiry-under-approval {
    min-width: 800px;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .batch-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }
}
</style>
