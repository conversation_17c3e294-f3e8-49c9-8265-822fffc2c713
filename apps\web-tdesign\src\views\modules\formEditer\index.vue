<script setup lang="ts">
import { Page } from '@vben/common-ui';
import { Button, Form } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';

import CodeShow from './components/editer/CodeShow.vue';
import FormEditer from './components/editer/FormEdit.vue';
import FormRender from './components/render/FormRender.vue';

const data = ref([
  {
    name: 'item_131428808766',
    component: 'Collapse',
    unFormItem: true,
    schema: {
      needPanel: true,
      children: [
        {
          component: 'CollapsePanel',
          unFormItem: true,
          isGroup: true,
          schema: {
            title: '面板1',
            columns: 1,
            children: [
              {
                name: 'item_103431071461',
                component: 'Select',
                props: {
                  clearable: true,
                },
                schema: {
                  rules: [
                    {
                      required: false,
                      message: '必填项',
                    },
                  ],
                  needOptions: true,
                  title: '下拉框',
                  name: 'item_140288438015',
                  value: '',
                  icon: '',
                  placeholder: '请输入',
                  maxLength: 100,
                  isDisabled: false,
                  isShow: true,
                  syncOptions: {},
                  options: [
                    {
                      label: '选项1',
                      value: '1',
                      id: '1',
                    },
                    {
                      label: '选项2',
                      value: '2',
                      id: '2',
                    },
                  ],
                  id: 192_337_685_988,
                },
                id: 130_274_007_346,
              },
              {
                name: 'item_143794218421',
                component: 'Select',
                props: {
                  clearable: true,
                },
                schema: {
                  rules: [
                    {
                      required: false,
                      message: '必填项',
                    },
                  ],
                  needOptions: true,
                  title: '下拉框',
                  name: 'item_176121215609',
                  value: '',
                  icon: '',
                  placeholder: '请输入',
                  maxLength: 100,
                  isDisabled: false,
                  isShow: true,
                  syncOptions: {},
                  options: [
                    {
                      label: '选项1',
                      value: '1',
                      id: '1',
                    },
                    {
                      label: '选项2',
                      value: '2',
                      id: '2',
                    },
                  ],
                  id: 155_558_171_727,
                },
                id: 182_469_336_333,
              },
              {
                name: 'item_109015447502',
                component: 'Select',
                props: {
                  clearable: true,
                },
                schema: {
                  rules: [
                    {
                      required: false,
                      message: '必填项',
                    },
                  ],
                  needOptions: true,
                  title: '下拉框',
                  name: 'item_136142124983',
                  value: '',
                  icon: '',
                  placeholder: '请输入',
                  maxLength: 100,
                  isDisabled: false,
                  isShow: true,
                  syncOptions: {},
                  options: [
                    {
                      label: '选项1',
                      value: '1',
                      id: '1',
                    },
                    {
                      label: '选项2',
                      value: '2',
                      id: '2',
                    },
                  ],
                  id: 115_829_686_963,
                },
                id: 148_287_301_896,
              },
              {
                name: 'item_132682551183',
                component: 'Select',
                props: {
                  clearable: true,
                },
                schema: {
                  rules: [
                    {
                      required: false,
                      message: '必填项',
                    },
                  ],
                  needOptions: true,
                  title: '下拉框',
                  name: 'item_135201380808',
                  value: '',
                  icon: '',
                  placeholder: '请输入',
                  maxLength: 100,
                  isDisabled: false,
                  isShow: true,
                  syncOptions: {},
                  options: [
                    {
                      label: '选项1',
                      value: '1',
                      id: '1',
                    },
                    {
                      label: '选项2',
                      value: '2',
                      id: '2',
                    },
                  ],
                  id: 112_246_621_722,
                },
                id: 181_657_534_183,
              },
              {
                name: 'item_147112271200',
                component: 'Select',
                props: {
                  clearable: true,
                },
                schema: {
                  rules: [
                    {
                      required: false,
                      message: '必填项',
                    },
                  ],
                  needOptions: true,
                  title: '下拉框',
                  name: 'item_175214369555',
                  value: '',
                  icon: '',
                  placeholder: '请输入',
                  maxLength: 100,
                  isDisabled: false,
                  isShow: true,
                  syncOptions: {},
                  options: [
                    {
                      label: '选项1',
                      value: '1',
                      id: '1',
                    },
                    {
                      label: '选项2',
                      value: '2',
                      id: '2',
                    },
                  ],
                  id: 123_256_319_929,
                },
                id: 115_330_800_896,
              },
            ],
            name: 'item_190243974690',
            id: 140_570_339_791,
          },
          id: 189_409_337_476,
          name: 'item_126634593870',
        },
      ],
      name: 'item_124288541944',
      syncOptions: {},
      id: 105_018_143_472,
    },
    id: 198_732_333_771,
    syncOptions: {},
  },
]);
const formData: any = ref({});
const form: any = ref();
const formConfig: any = ref();
const clickHd = async () => {
  form.value.clearValidate();
  console.info('FormData:', formData.value);

  const vali = await form.value.validate();
  console.info('表单验证结果:', vali);
  // form.value.validator((error) => {
  //   if (vali) {
  //     console.info(JSON.stringify(formData.value));
  //   } else {
  //     console.log('校验不通过', error);
  //   }
  // });
};
const setFormData = (list) => {
  list.forEach((item) => {
    formData.value[item.schema.name] = item.schema.value;
    if (item?.schema?.children?.length >= 0) {
      setFormData(item.schema.children);
    }
  });
};
const clickxr = () => {
  setFormData(data.value);
  // setTimeout(() => {
  formConfig.value = data.value;
  // }, 0);
};

onMounted(() => {});
</script>

<template>
  <Page class="overflow-y-hidden" style="height: calc(100vh - 90px)">
    <div class="h-min-[50%] flex h-full flex-col">
      <FormEditer
        v-model="data"
        class="bg-background h-full overflow-y-hidden"
      />
      <div class="flex h-full w-full overflow-y-hidden border">
        <div
          class="flex h-full w-full flex-col overflow-hidden border-r bg-white"
        >
          <Button class="w-full" @click="clickxr">渲染</Button>
          <div class="h-full w-full overflow-auto">
            <CodeShow :code="JSON.stringify(data, null, 2)" />
          </div>
        </div>
        <div class="h-full w-full overflow-auto bg-white p-2">
          <Form ref="form" :data="formData">
            <FormRender
              v-model="formData"
              :form-config="formConfig"
              class="bg-background w-full p-2"
            />
          </Form>
          <Button @click="clickHd">提交</Button>
          <CodeShow :code="JSON.stringify(formData, null, 2)" />
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped></style>
