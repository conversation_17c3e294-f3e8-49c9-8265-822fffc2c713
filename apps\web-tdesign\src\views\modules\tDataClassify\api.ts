// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataClassify/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataClassify/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tDataClassify/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataClassify/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataClassify/getByIds/${data}`);
}
export async function listByTree(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataClassify/listByTree', data);
}
export async function collectByTree(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataClassify/collectByTree', data);
}
export async function searchlistByTree(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataClassify/searchlistByTree', data);
}
export async function getByCode(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataClassify/getByCode/${data}`);
}
export function uploadBuild() {
  return `${import.meta.env.VITE_GLOB_API_URL}/rgdc-sys/file/upload`;
}
