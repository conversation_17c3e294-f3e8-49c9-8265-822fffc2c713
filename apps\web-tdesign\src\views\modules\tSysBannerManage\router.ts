import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: 'banner管理',
    },
    name: 'tSysBannerManage',
    path: '/tSysBannerManage',
    children: [
      {
        meta: {
          title: 'banner管理编辑',
        },
        name: 'tSysBannerManageIndex',
        path: '/tSysBannerManage/index',
        component: () =>
          import('#/views/modules/tSysBannerManage/index.vue'),
      },
    ],
  },
];

export default routes;
