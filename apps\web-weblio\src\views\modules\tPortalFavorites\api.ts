// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  // return requestClient.post<any[]>('/rgdc-submit/tPortalFavorites/listByPage', data);
  return requestClient.post<any[]>(
    '/rgdc-submit/tPortalFavorites/listByPage',
    data,
  );
}

export async function saveFavorites(data: any) {
  return requestClient.post<any>('/rgdc-submit/tPortalFavorites/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(
    `/rgdc-submit/tPortalFavorites/deleteBatch/${data}`,
  );
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tPortalFavorites/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(
    `/rgdc-submit/tPortalFavorites/getByIds/${data}`,
  );
}

export async function getOneByOperationCode(data: any) {
  return requestClient.get<any>(
    `/rgdc-submit/tPortalFavorites/getOneByOperationCode/${data}`,
  );
}
