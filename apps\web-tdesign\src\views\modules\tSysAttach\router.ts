import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '附件',
    },
    name: 'tSysAttach',
    path: '/tSysAttach',
    children: [
      {
        meta: {
          title: '附件编辑',
        },
        name: 'tSysAttachIndex',
        path: '/tSysAttach/index',
        component: () =>
          import('#/views/modules/tSysAttach/index.vue'),
      },
    ],
  },
];

export default routes;
