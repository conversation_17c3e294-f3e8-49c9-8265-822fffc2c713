import type { PropType } from 'vue';

import { Image, ImageViewer, Table, TabPanel, Tabs } from 'tdesign-vue-next';
import { computed, defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'Preview',
  props: {
    jsonData: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  },
  setup(props) {
    const imageViewerVisible = ref(false);
    const currentImageSrc = ref('');

    const showImageViewer = (src: string) => {
      currentImageSrc.value = src;
      imageViewerVisible.value = true;
    };

    // 用 computed 代替 watch
    const columns = computed(() => {
      const val = props.jsonData;
      return val && val.length > 0
        ? Object.keys(val[0]).map((key) => {
            // 检查这一列是否包含图片
            const hasImages = val.some(
              (row) =>
                row[key] &&
                typeof row[key] === 'object' &&
                row[key].bytes &&
                row[key].path,
            );

            // 计算文本内容的平均长度来动态设置列宽
            let columnWidth = 120; // 最小宽度

            if (hasImages) {
              columnWidth = 150; // 图片列固定宽度
            } else {
              // 计算文本内容长度
              const textLengths = val
                .map((row) => {
                  const value = row[key];
                  if (typeof value === 'string') return value.length;
                  if (value !== null) return String(value).length;
                  return 0;
                })
                .filter((len) => len > 0);

              if (textLengths.length > 0) {
                const avgLength =
                  textLengths.reduce((a, b) => a + b, 0) / textLengths.length;
                const maxLength = Math.max(...textLengths);

                // 根据平均长度和最大长度智能设置宽度
                if (avgLength <= 20) {
                  columnWidth = 120;
                } else if (avgLength <= 50) {
                  columnWidth = 180;
                } else if (avgLength <= 100) {
                  columnWidth = 250;
                } else {
                  columnWidth = 300;
                }

                // 如果最大长度特别短，进一步减小宽度
                if (maxLength <= 10) {
                  columnWidth = Math.min(columnWidth, 100);
                }

                // 如果是数字类型居多，减小宽度
                const nonNullValues = val
                  .map((row) => row[key])
                  .filter(
                    (value) =>
                      value !== null && value !== undefined && value !== '',
                  );
                const isNumeric =
                  nonNullValues.length > 0 &&
                  nonNullValues.every((value) => !Number.isNaN(Number(value)));
                if (isNumeric) {
                  columnWidth = Math.min(columnWidth, 120);
                }
              }
            }

            return {
              title: key,
              colKey: key,
              width: columnWidth,
              ellipsis: !hasImages, // 只有非图片列才启用ellipsis功能
              align: hasImages ? ('center' as const) : ('left' as const),
              resizable: true,
              cell: (h, context: any) => {
                const value = context?.row?.[key];

                // 判断是否为图片base64对象
                if (
                  value &&
                  typeof value === 'object' &&
                  value.bytes &&
                  value.path
                ) {
                  const imageSrc = `data:image/png;base64,${value.bytes}`;
                  return (
                    <div
                      onClick={() => showImageViewer(imageSrc)}
                      style={{
                        textAlign: 'center',
                        cursor: 'pointer',
                      }}
                    >
                      <Image
                        fit="contain"
                        src={imageSrc}
                        style={{
                          width: '120px',
                          height: '80px',
                          backgroundColor: 'transparent',
                        }}
                      />
                      <div
                        style={{
                          fontSize: '12px',
                          color: '#666',
                          marginTop: '4px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '120px',
                        }}
                        title={value.path}
                      >
                        {value.path}
                      </div>
                    </div>
                  );
                }

                // 对于普通字符串，使用两行限制显示
                return value;
              },
            };
          })
        : [];
    });

    return () => (
      <>
        <style>
          {`
            .t-table td, .t-table th {
              border-right: 1px solid #e6e8eb !important;
            }
            .t-table td:last-child, .t-table th:last-child {
              border-right: none !important;
            }
            .t-table td {
              max-height: 2.8em !important;
              line-height: 1.4 !important;
              padding: 8px !important;
              overflow: hidden !important;
              word-break: break-word !important;
            }
          `}
        </style>
        <Tabs defaultValue="table" style={{ marginTop: '16px' }}>
          <TabPanel label="表格视图" value="table">
            <Table
              bordered
              columns={columns.value}
              data={props.jsonData}
              empty="暂无数据"
              resizable
              rowKey={columns.value[0]?.colKey}
              scroll={{ x: 1000 } as any}
              size="medium"
              stripe
              style={{
                marginTop: '16px',
                border: '1px solid #e6e8eb',
              }}
              tableLayout="fixed"
              verticalAlign="middle"
            />
          </TabPanel>
          <TabPanel label="JSON视图" value="json">
            <pre
              style={{
                background: '#f6f8fa',
                padding: '16px',
                borderRadius: '8px',
                maxHeight: '500px',
                overflow: 'auto',
                marginTop: '16px',
              }}
            >
              {props.jsonData.length > 0
                ? JSON.stringify(props.jsonData, null, 2)
                : '暂无数据'}
            </pre>
          </TabPanel>
        </Tabs>

        <ImageViewer
          images={[currentImageSrc.value]}
          onClose={() => (imageViewerVisible.value = false)}
          visible={imageViewerVisible.value}
        />
      </>
    );
  },
});
