<script setup lang="tsx">
import type { PropType } from 'vue';

import { SearchIcon } from 'tdesign-icons-vue-next';
import { Button } from 'tdesign-vue-next';
import { computed, defineModel, defineProps, onMounted, ref, watch } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { getByIds } from './api';
import PopTable from './pop-table.vue';

defineOptions({ name: 'SiliconDataTypePopSelect' });
const props = defineProps({
  title: {
    type: String,
    default: '选择',
  },
  rowSelectionType: {
    type: Object as PropType<'multiple' | 'single'>,
    default: 'single',
  },
  optLabel: {
    type: String,
    default: 'value',
  },
  optValue: {
    type: String,
    default: 'id',
  },
  columns: {
    type: Array,
    default: () => [],
  },
  sendApi: {
    type: Object as PropType<any>,
    default: () => {},
  },
});
const emit = defineEmits(['valueChange']);
const loading = ref(false);
const popTableRef = ref();
const modelValue = defineModel('modelValue');
const selectedRowData = ref([]);
const reqRunner = {
  getByIds: useRequest(getByIds, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    onError: (e) => {
      loading.value = false;
    },
    onSuccess: (res: any) => {
      loading.value = false;
      selectedRowData.value = res;
    },
  }),
};
const listeners = {
  click: () => {
    /**
     * 点击打开table
     */
    popTableRef.value.show(selectedRowData.value);
  },
};
const emitHanler = {
  // selectChange: (ctx: any) => {
  //   selectedRowData.value = ctx.selectedRowData;
  //
  // },
  valueSelected: (data?: any) => {
    emit('valueChange', data);
    selectedRowData.value = data || [];
    modelValue.value = (data || [])
      .map((item) => item[props.optValue])
      .join(',');
  },
};
const value = computed<string[]>({
  get() {
    return selectedRowData.value.map((item) => item[props.optLabel]);
  },
  // set(val) {
  // modelValue.value = val;
  // }
});
watch(
  modelValue,
  (value, oldValue) => {
    if (value && oldValue != value) {
      loading.value = true;
      reqRunner.getByIds.run(modelValue.value, props.sendApi.getByIds);
    }
  },
  { deep: true },
);
onMounted(() => {
  reqRunner.getByIds.run(modelValue.value, props.sendApi.getByIds);
  loading.value = true;
});
</script>
<template>
  <Button v-on="listeners">
    <template #icon>
      <SearchIcon />
    </template>
    {{ title }}
  </Button>
  <PopTable
    :columns="[
      {
        title: '选中标志',
        colKey: 'row-select',
        type: rowSelectionType,

        width: 64,
      },
      ...(columns || {}),
    ]"
    ref="popTableRef"
    :send-api="sendApi?.listByPage"
    :row-key="optValue"
    :row-selection-type="rowSelectionType"
    v-on="emitHanler"
  />
</template>

<style scoped></style>
