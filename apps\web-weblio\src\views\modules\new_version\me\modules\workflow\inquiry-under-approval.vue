<script setup lang="ts">
import {
  Button,
  Form,
  FormItem,
  Input,
  Pagination,
  Table,
} from 'tdesign-vue-next';
import { onMounted, reactive, ref } from 'vue';

import { listByPageApi } from '../../api';

// 查询表单
const searchForm = reactive({
  dataName: '',
  processName: '',
  status: '',
});

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 表格列
const columns = [
  {
    title: '序号',
    colKey: 'serial-number',
    width: 80,
    align: 'center' as const,
  },
  {
    colKey: 'arg.operation_name',
    title: '数据名称',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'arg.operation_version',
    title: '数据版本',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'processName',
    title: '流程名称',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'currentNodeName',
    title: '当前任务',
    ellipsis: true,
    align: 'center' as const,
  },
  {
    colKey: 'createTime',
    title: '提交时间',
    sorter: true,
    ellipsis: true,
    align: 'center' as const,
  },
];

// 查询
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.pageNum,
      pageSize: pagination.pageSize,
      param: {
        ...searchForm,
      },
      sorts: [],
    };
    const res = await listByPageApi(params);
    tableData.value = res?.records.map((item: any) => {
      return {
        ...item,
        arg: JSON.parse(item.variable),
      };
    });
    total.value = res?.total || 0;
  } finally {
    loading.value = false;
  }
};

// 查询按钮
const onSearch = () => {
  pagination.pageNum = 1;
  fetchData();
};

// 重置按钮
const onReset = () => {
  searchForm.dataName = '';
  searchForm.processName = '';
  searchForm.status = '';
  pagination.pageNum = 1;
  fetchData();
};

// 分页切换
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.pageNum = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchData();
};

// 首次加载
onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="layout-main">
    <div class="inquiry-under-approval">
      <!-- 查询表单 -->
      <Form layout="inline" @submit.prevent="onSearch" class="search-form">
        <FormItem label="数据名称">
          <Input
            v-model="searchForm.dataName"
            placeholder="请输入数据名称"
            style="width: 200px"
          />
        </FormItem>
        <FormItem label="流程名称">
          <Input
            v-model="searchForm.processName"
            placeholder="请输入流程名称"
            style="width: 200px"
          />
        </FormItem>
        <FormItem>
          <Button
            theme="primary"
            type="submit"
            style="margin-right: 8px"
            @click="onSearch"
          >
            查询
          </Button>
          <Button @click="onReset" variant="outline"> 重置 </Button>
        </FormItem>
      </Form>

      <!-- 列表表格 -->
      <Table
        :data="tableData"
        :columns="columns"
        :loading="loading"
        row-key="id"
        class="custom-table"
        style="margin-top: 20px"
        bordered
      >
        <template #serial-number="{ rowIndex }">
          {{ (pagination.pageNum - 1) * pagination.pageSize + rowIndex + 1 }}
        </template>
      </Table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <Pagination
          :total="total"
          :page-size="pagination.pageSize"
          :current="pagination.pageNum"
          @change="onPageChange"
          show-size-changer
          show-total
        />
      </div>
    </div>
    <!-- 右侧栏预留，如有右侧内容可放这里 -->
    <!-- <div class="layout-side">右侧内容</div> -->
  </div>
  <div class="footer-bar"></div>
</template>

<style scoped>
.layout-main {
  display: flex;
  gap: 32px;
  margin-bottom: 32px;
  justify-content: center; /* 主内容居中 */
}
.inquiry-under-approval {
  background: #fff;
  padding: 32px 32px 24px 32px; /* 减小内边距 */
  border-radius: 16px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  flex: 1 1 0%;
  min-width: 900px;
  max-width: 1100px;
}
.search-form {
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
}
.search-form .t-form__item {
  margin-bottom: 0 !important; /* 去除多余下边距 */
}
.custom-table {
  margin-bottom: 16px;
}
.custom-table :deep(.t-table-thead) th {
  background: #155bd4;
  color: #fff;
  font-weight: 500;
}
.custom-table :deep(.t-table-tbody) td {
  text-align: center;
}
.pagination-wrapper {
  margin-top: 16px;
  text-align: right;
}
.footer-bar {
  margin-top: 32px;
}
/* 右侧栏预留样式 */
.layout-side {
  min-width: 320px;
  max-width: 400px;
  background: #fff;
  border-radius: 16px;
  padding: 32px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  height: fit-content;
}

/* 右下角消息通知区域整体 */
.notice-panel {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px 20px 16px 20px;
  min-width: 320px;
  max-width: 400px;
  margin-bottom: 24px;
}

/* 消息列表 */
.notice-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 每条消息间距 */
  margin-bottom: 16px;
}

/* 单条消息卡片 */
.notice-item {
  background: #f7f9fa;
  border-radius: 10px;
  padding: 14px 16px 10px 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 消息标题 */
.notice-title {
  font-weight: 500;
  color: #155bd4;
  margin-bottom: 2px;
}

/* 消息内容 */
.notice-content {
  color: #333;
  font-size: 14px;
  margin-bottom: 2px;
}

/* 消息时间 */
.notice-time {
  color: #999;
  font-size: 12px;
  align-self: flex-end;
}

/* 按钮区域 */
.notice-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
}
</style>
