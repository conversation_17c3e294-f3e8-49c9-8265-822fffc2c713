<script setup lang="tsx">
import FormEditer from '#/views/modules/formEditer/components/editer/FormEdit.vue';
import { useVbenDrawer } from '@vben/common-ui';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import {checkMetaData, save, saveMetaData} from '../api.ts';
import {Dialog,Alert} from "tdesign-vue-next";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});

const cfgData = ref([]);

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});

/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
const updateConfirm = () => {
  reqRunner.save.run({
    ...state.tagObj,
    cfg: JSON.stringify(cfgData.value),
  });
};

const reqRunner = {
  save: useRequest(save, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: () => {
      reqRunner.saveMetaData.run({
        ...state.tagObj,
        cfg: JSON.stringify(cfgData.value),
      });
    },
  }),
  saveMetaData: useRequest(saveMetaData, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: () => {
      initStatus();
      modalApi.close();
      isCheck.value = false
      props.outRef?.reload();
    },
  }),
  checkMetaData: useRequest(checkMetaData, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      if(res == '校验成功'){
        updateConfirm()
      }else{
        isCheck.value = true
        checkBody.value = res
        checkBodyList.value = checkBody.value.split('<br>')
      }
    },
  }),
};
const [Modal, modalApi] = useVbenDrawer({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      initStatus();
    }
  },
  onConfirm: async () => {
    reqRunner.checkMetaData.run({
      ...state.tagObj,
      cfg: JSON.stringify(cfgData.value),
    });
  },
  onCancel: () => {
    initStatus();
    modalApi.close();
  },
});

/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const dataType = ref()
const isTemplateEdit = ref(false)
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    isTemplateEdit.value = data.isTemplateEdit
    dataType.value = data.dataType
    state.tagObj = data;
    cfgData.value = [];
    if(data.cfg && data.cfg != '[]'){
      cfgData.value = JSON.parse(data.cfg || '[]');
    }
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});
onMounted(async () => {
});
const isCheck = ref(false);
const checkBody = ref();
const checkBodyList = ref([]);
</script>

<template>
  <Dialog v-model:visible="isCheck" :close-btn="false" header="更新确认"
    theme="warning" @confirm="updateConfirm">
    <span v-for="item in checkBodyList">
      <div style="text-indent: 20px;">{{item}}</div>
    </span>
  </Dialog>
  <Modal
    :header="false"
    :title="state.tagObj?.id ? `编辑` : `新建`"
    class="w-[90%]"
  >
    <Alert v-if="isTemplateEdit" theme="info" title="模板配置提示">
      <div>1.字段标识为每个字段的唯一标识，<span style="color:red">不可重复</span>；</div>
      <div>2.标题为字段的展示名称。</div>
    </Alert>
    <FormEditer v-model="cfgData" :dataType="dataType"/>
  </Modal>
</template>

<style scoped></style>
