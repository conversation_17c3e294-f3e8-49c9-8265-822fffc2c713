<script setup lang="ts">
import { Icon } from 'tdesign-icons-vue-next';
import { Button, Input } from 'tdesign-vue-next';
import { defineEmits, defineProps } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['update:modelValue']);
// const data = ref(props.modelValue);

const data = defineModel();
const removeItem = (item) => {
  console.log('removeItem', data.value);
  const temp = data.value.filter((i) => i.id !== item.id);
  data.value = temp;
  // emits('update:modelValue', data.value);
};
const addItem = () => {
  const tmp = Math.ceil(Math.random() * 100_000);
  data.value.push({
    id: `${tmp}${Date.now()}`,
    label: `标题${tmp}`,
    value: tmp,
  });
  // emits('update:modelValue', data.value);
};
// watch(
//   () => props.modelValue,
//   (val) => {
//     data.value = val.map((item) => {
//       return {
//         id: `${Math.ceil(Math.random() * 100_000)}${Date.now()}`,
//         label: item.label,
//         value: item.value,
//       };
//     });
//   },
//   {
//     deep: true,
//   },
// );

// watch(
//   () => data.value,
//   (val) => {
//     emits('update:modelValue', val);
//   },
//   {
//     deep: true,
//   },
// );
</script>

<template>
  <div class="w-full bg-white">
    <div class="grid grid-cols-1 rounded border">
      <div class="flex overflow-hidden border-b">
        <div class="w-full border-r p-1">标题</div>
        <div class="w-full p-1">数据值</div>
        <div class="flex w-[55px] items-center justify-center p-1"></div>
      </div>
      <div class="m-1 flex items-center justify-center">
        <Button
          style="width: 100%"
          theme="primary"
          variant="dashed"
          @click="addItem"
        >
          增加一条
        </Button>
      </div>
      <div class="max-h-[200px] overflow-auto">
        <div
          v-for="item in data"
          class="flex items-center justify-center overflow-hidden border-b last:border-none"
        >
          <div class="w-full overflow-hidden border-r p-1">
            <Input v-model="item.label" clearable style="width: 100%" />
          </div>
          <div class="w-full overflow-hidden p-1">
            <Input v-model="item.value" clearable style="width: 100%" />
          </div>
          <div
            class="flex h-full w-[40px] items-center justify-center gap-1 p-2"
          >
            <Icon
              class="cursor-pointer text-red-600"
              name="delete"
              @click="removeItem(item)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
