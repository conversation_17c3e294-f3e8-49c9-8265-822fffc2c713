// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-sys/tSysLog/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-sys/tSysLog/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-sys/tSysLog/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-sys/tSysLog/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-sys/tSysLog/getByIds/${data}`);
}
