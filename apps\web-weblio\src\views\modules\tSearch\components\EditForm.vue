<script setup lang="tsx">
import type { FormProps } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import {
  dataAuthApply,
  getAllPCodes,
} from '#/views/modules/tDataPermission/api.ts';
import { useVbenModal } from '@vben/common-ui';
import {
  DatePicker,
  Form,
  FormItem,
  MessagePlugin,
  Select,
  Textarea,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
const emit = defineEmits(['success']);
const status = ref([]);
const dataTypes = ref([]);
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});

const resourceTypeList = ref();
const permissionTypeList = ref();
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
  // resourceType: [
  //   {
  //     required: true,
  //     message: '请选择数据权限申请范围',
  //   },
  // ],
  permissionType: [
    {
      required: true,
      message: '请选择权限类型',
    },
  ],
  startTime: [
    {
      required: true,
      message: '请选择权限生效时间',
    },
  ],
  endTime: [
    {
      required: true,
      message: '请选择权限失效时间',
    },
  ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  dataAuthApply: useRequest(dataAuthApply, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      // props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
      MessagePlugin.success('申请成功，请等待审批');
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      /**
       * 保存数据
       */
      reqRunner.dataAuthApply.run({
        ...state.tagObj,
        ...formData.value,
        // resourceType: formData.value.resourceType.toString(),
      });
      // props.outRef?.reload();
      emit('success');
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});

const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const resourcesType2 = ref();
const open = async (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  console.log('data', data);
  if (data) {
    resourcesType2.value = await getDictItems('UNSTRUCTURED_DATA_TYPE');
    state.tagObj = data;
    formData.value = data;
    resourceTypeList.value = resourcesType2.value.some(
      (item) => item.value === formData.value.dataType,
    )
      ? [{ label: '文件', value: '**unstructured_file**' }]
      : await getAllPCodes(formData.value.templateCode);
    const currentDate = new Date();
    formData.value.startTime = formatDate(currentDate);
    const currentDate_7d = new Date();
    currentDate_7d.setDate(currentDate_7d.getDate() + 7);
    formData.value.endTime = formatDate(currentDate_7d);
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});

onMounted(async () => {
  permissionTypeList.value = await getDictItems('DATA_PERMISSION_TYPE');
});
</script>

<template>
  <Modal
    title="数据权限申请"
    class="custom-edit-modal"
    style="max-width: 700px; width: 90vw"
  >
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="w-full"
      label-align="right"
    >
      <div class="grid w-full grid-cols-1 gap-3">
        <!-- <FormItem class="mt-3" label="申请范围" name="resourceType">
          <Select
            v-model="formData.resourceType"
            :options="resourceTypeList"
            multiple
            filterable
            clearable
            placeholder="请选择"
          />
        </FormItem> -->
        <FormItem label="权限类型" name="permissionType">
          <Select
            v-model="formData.permissionType"
            :options="permissionTypeList"
            clearable
            placeholder="请选择"
          />
        </FormItem>
        <FormItem label="权限生效时间" name="startTime">
          <DatePicker
            v-model="formData.startTime"
            enable-time-picker
            allow-input
            clearable
          />
        </FormItem>
        <FormItem label="权限失效时间" name="endTime">
          <DatePicker
            v-model="formData.endTime"
            enable-time-picker
            allow-input
            clearable
          />
        </FormItem>
        <FormItem label="备注" name="remark">
          <Textarea
            v-model="formData.remark"
            placeholder="若有其他说明，请填写备注"
          />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped>
.custom-edit-modal .t-dialog__body {
  padding: 32px 32px 24px 32px;
  font-size: 16px;
  background: #fafbfc;
}
.custom-edit-modal .t-form__item {
  margin-bottom: 20px;
}
.custom-edit-modal .t-dialog__footer {
  padding: 16px 32px 24px 32px;
}
.custom-edit-modal .t-form {
  width: 100%;
}
.custom-edit-modal .t-dialog__footer .t-button {
  margin-left: 16px;
  min-width: 88px;
}
</style>
