<script setup lang="ts">
import IndexTable from '#/views/_generation/entity/components/IndexTable.vue';
import EditForm from '#/views/_generation/entity/components/EditForm.vue';
import { Page, useVbenModal } from '@vben/common-ui';
import { ref } from 'vue';

// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();
const [EditFormModle, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: EditForm,

  onOpenChange(isOpen: boolean) {
    if (isOpen == false) {
      console.log('关闭时触发获取数据', modalApi.getData());
    }
  },
});
const add: any = () => {
  modalApi.setState({ title: '新增' });
  modalApi.setData({ refresh: indexTable.value.loadData });
  modalApi.open();
};

const edit: any = (record: any) => {
  modalApi.setState({ title: '编辑' });
  modalApi.setData({ record, refresh: indexTable.value.loadData });
  modalApi.open();
};
</script>

<template>
  <Page description="实体对象列表" title="代码生成器实体定义">
    <IndexTable @add="add" @edit="edit" ref="indexTable" />
    <EditFormModle />
  </Page>
</template>
