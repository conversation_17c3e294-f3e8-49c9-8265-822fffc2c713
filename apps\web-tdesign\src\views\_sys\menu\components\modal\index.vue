<script lang="ts" setup>
import { onMounted, ref, h } from 'vue';

import { useVbenModal, IconPicker } from '@vben/common-ui';

import {
  Divider,
  Form,
  FormItem,
  type FormProps,
  Input,
  InputNumber,
  Select,
  Switch,
  TreeSelect,
  InputAdornment,
  Button,
} from 'tdesign-vue-next';

import { getDictItems } from '#/api';

import { allMenu, saveMenu } from '../../api';

const inputComponent: any = h(Input);
const formData: any = ref({});
const form = ref();

const metaFormData: any = ref({});
const metaForm = ref();

const status = ref([]);
const menuTree: any = ref([]);

const FORM_RULES: FormProps['rules'] = {
  parentCode: [
    {
      required: true,
      message: '必填',
    },
  ],
  code: [
    {
      required: true,
      message: '必填',
    },
  ],
  name: [
    {
      required: true,
      message: '必填',
    },
  ],
  path: [
    {
      required: true,
      message: '必填',
    },
  ],

  status: [
    {
      required: true,
      message: '必填',
    },
  ],
  description: [
    {
      required: true,
      message: '必填',
    },
  ],
};

const META_FORM_RULES: FormProps['rules'] = {
  title: [
    {
      required: true,
      message: '必填',
    },
  ],
  order: [
    {
      required: true,
      message: '必填',
    },
  ],
};

const loadMenu = async () => {
  const res = await allMenu({});
  res.push({
    code: '-1',
    description: '顶级菜单',
    children: [],
  });
  menuTree.value = res;
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    const vali = await form.value.validate();
    const metaVali = await metaForm.value.validate();

    if (vali === true && metaVali === true) {
      // 验证通过提交请求 并且关闭窗口
      await saveMenu({
        ...formData.value,
        meta: metaFormData.value,
      });
      // 刷新表格
      modalApi.getData().refresh();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    loadMenu();
    formData.value = isOpen
      ? { ...modalApi.getData<Record<string, any>>()?.record }
      : {};
    metaFormData.value = isOpen
      ? { ...modalApi.getData<Record<string, any>>()?.record?.meta }
      : {};
  },
  title: '新增',
});

onMounted(async () => {
  status.value = await getDictItems('BASE_STATUS');
  await loadMenu();
});
</script>
<template>
  <Modal class="w-4/12">
    <Form
      ref="form"
      :data="formData"
      :label-width="120"
      :rules="FORM_RULES"
      class="grid grid-cols-2 gap-2"
      label-align="top"
    >
      <FormItem class="col-span-2" label="上级菜单" name="parentCode">
        <TreeSelect
          v-model="formData.parentCode"
          :data="menuTree"
          :keys="{ value: 'code', label: 'description', children: 'children' }"
          placeholder="请选择上级菜单"
        />
      </FormItem>

      <FormItem label="菜单编码" name="code">
        <Input
          v-model="formData.code"
          :style="{ minWidth: '134px' }"
          placeholder="请输入菜单编码"
        />
      </FormItem>
      <FormItem help="前端路由跳转Name" label="路由Name" name="name">
        <Input v-model="formData.name" placeholder="请输入路由Name" />
      </FormItem>

      <FormItem help="前端路由跳转Path" label="路由Path" name="path">
        <Input v-model="formData.path" placeholder="请输入路由Path" />
      </FormItem>

      <FormItem
        help="如果是非页面菜单需要配置BasicLayout"
        label="路由组件路径"
        name="component"
      >
        <Input v-model="formData.component" placeholder="请输入路由组件路径" />
      </FormItem>

      <FormItem label="路由重定向" name="redirect">
        <Input v-model="formData.redirect" placeholder="请输入路由重定向" />
      </FormItem>

      <FormItem label="状态" name="status">
        <Select
          v-model="formData.status"
          :options="status"
          :style="{ minWidth: '134px' }"
          placeholder="请选择状态"
        />
      </FormItem>

      <FormItem
        class="col-span-2"
        help="请填写用户简单易懂的页面信息"
        label="描述"
        name="description"
      >
        <Input
          v-model="formData.description"
          :style="{ minWidth: '134px' }"
          class="form-item-content"
          placeholder="请输入描述"
        />
      </FormItem>
    </Form>

    <Divider> 菜单元数据配置项</Divider>

    <Form
      ref="metaForm"
      :data="metaFormData"
      :label-width="120"
      :rules="META_FORM_RULES"
      class="grid grid-cols-2 gap-4"
      label-align="top"
    >
      <FormItem help="支持填写前端i18n" label="菜单显示名称" name="title">
        <Input v-model="metaFormData.title" placeholder="请输入菜单显示名称" />
      </FormItem>
      <FormItem
        help="请设置排列顺序，尽量不要重复否则会导致拖拽排序失效"
        label="菜单排列顺序"
        name="order"
      >
        <InputNumber
          v-model="metaFormData.order"
          placeholder="菜单排列顺序"
          style="width: 100%"
        />
      </FormItem>
      <FormItem label="外链-跳转路径" name="link">
        <Input
          v-model="metaFormData.link"
          :style="{ minWidth: '134px' }"
          placeholder="请输入外链-跳转路径"
        />
      </FormItem>

      <FormItem label="图标" name="icon">
        <!-- <Input v-model="metaFormData.icon" placeholder="请输入图标"> </Input> -->
        <IconPicker
          class="w-full"
          :input-component="inputComponent"
          v-model="metaFormData.icon"
          icon-slot="suffix"
          model-value-prop="value"
          prefix="lucide"
        />
      </FormItem>

      <FormItem class="col-span-2" label="嵌入页面URL" name="iframeSrc">
        <Input v-model="metaFormData.iframeSrc" placeholder="嵌入页面URL" />
      </FormItem>

      <FormItem label="开启KeepAlive缓存" name="keepAlive">
        <Switch
          v-model="metaFormData.keepAlive"
          placeholder="开启KeepAlive缓存"
        />
      </FormItem>
      <FormItem label="当前路由在标签页不展现" name="hideInTab">
        <Switch
          v-model="metaFormData.hideInTab"
          placeholder="当前路由在标签页不展现"
        />
      </FormItem>

      <FormItem label="当前路由在菜单中不展现" name="hideInMenu">
        <Switch
          v-model="metaFormData.hideInMenu"
          placeholder="当前路由在菜单中不展现"
        />
      </FormItem>

      <FormItem label="当前路由在面包屑中不展现" name="hideInBreadcrumb">
        <Switch
          v-model="metaFormData.hideInBreadcrumb"
          placeholder="当前路由在面包屑中不展现"
        />
      </FormItem>

      <FormItem label="当前路由的子级在菜单中不展现" name="hideChildrenInMenu">
        <Switch
          v-model="metaFormData.hideChildrenInMenu"
          placeholder="当前路由的子级在菜单中不展现"
        />
      </FormItem>

      <FormItem label="在新窗口打开" name="openInNewWindow">
        <Switch
          v-model="metaFormData.openInNewWindow"
          placeholder="在新窗口打开"
        />
      </FormItem>

      <FormItem label="是否固定在标签页" name="affixTab">
        <Switch
          v-model="metaFormData.affixTab"
          placeholder="是否固定在标签页"
        />
      </FormItem>
    </Form>
  </Modal>
</template>
