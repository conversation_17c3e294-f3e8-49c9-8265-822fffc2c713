<script setup lang="ts">
import {getDictItems} from '#/api';
import { defineProps, onMounted, reactive, ref } from 'vue';
import {
  Button,
  Form,
  FormItem,
  MessagePlugin,
  Select,
  Upload,
  Input,
  Textarea,
  type FormProps
} from "tdesign-vue-next";
import {useAccessStore} from "@vben/stores";
import {useRequest} from "vue-hooks-plus";
import {save} from "#/views/modules/tSysFeedback/api.ts";
import {useRouter} from "vue-router";

/**
 * 属性定义
 */
const props = defineProps({

});
const FORM_RULES: FormProps['rules'] = {
  feedbackType: [
    {
      required: true,
      message: '请选择反馈类型',
    },
  ],
  description: [
    {
      required: true,
      message: '请输入问题描述',
    },
  ],
  contactWay: [
    {
      required: true,
      message: '请输入联系方式',
    },
  ],
};

const formData: any = ref({});

const router = useRouter()
const reqRunner = {
  save: useRequest(save, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      if (res == '保存成功') {
        MessagePlugin.success('提交成功！');
        router.push({path: '/home'})
      } else {
        MessagePlugin.warning(res);
      }
    },
  }),
};


const statusList = ref()
const feedbackTypeList = ref()
const imageFile = ref([])
const accessStore = useAccessStore();
const suffix = ref('.jpg,.png,.svg,.gif')
const beforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!suffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
    return false;
  }
  return true;
};
const uploadSuccess = (context: { fileList: any[] }) => {
  imageFile.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const dataForm = ref()
const saveData = async () => {
  dataForm.value.clearValidate();
  const vali = await dataForm.value.validate();
  if (vali === true) {
    if(imageFile.value.length > 0){
      formData.value.imageFile = JSON.stringify(imageFile.value)
    }
    formData.value.status = '0'
    formData.value.isDeleted = '0'
    reqRunner.save.run({...formData.value});
  } else {
    MessagePlugin.warning('有必填项没有填写');
    return
  }
};
onMounted(async () => {
  imageFile.value = []
  statusList.value = await getDictItems('FEEDBACK_STATUS');
  feedbackTypeList.value = await getDictItems('FEEDBACK_TYPE');
});

/**
 * 导出资源
 */
defineExpose({
});
</script>

<template>
  <div class="background-feedback">
    <div class="title-data">用户反馈</div>
    <div class="describe-data">您的建议，让我们更好</div>
  </div>
  <div class="feedback-flex">
    <div class="feedback-item">
      <Form ref="dataForm" :data="formData" :rules="FORM_RULES" label-align="top">
        <FormItem label="反馈类型" name="feedbackType" style="width:500px">
          <Select v-model="formData.feedbackType" :options="feedbackTypeList" clearable placeholder="请选择反馈类型"/>
        </FormItem>
        <FormItem label="问题描述" name="description" style="width:500px">
          <Textarea v-model="formData.description" clearable placeholder="请说明问题描述与建议，我们将为你不断改进～" :maxlength="200"
                    style="width:500px;" :autosize="{ minRows: 5, maxRows: 8 }" />
        </FormItem>
        <FormItem>
          <Upload theme="image" v-model="imageFile" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" :beforeUpload="beforeUpload" @success="uploadSuccess"
                  tips="支持格式：.jpg .png .svg .gif ，单个文件不能超过5MB" accept="image/*"/>
        </FormItem>
        <FormItem label="联系方式" name="contactWay" style="width:500px">
          <Input v-model="formData.contactWay" clearable placeholder="请输入手机号/邮箱/微信" style="width:500px"/>
        </FormItem>
      </Form>
      <div style="padding: 20px 0 20px 210px;"><Button theme="primary" @click="saveData">提交反馈</Button></div>
    </div>
    <div class="feedback-background"></div>
  </div>
</template>
<style scoped>
.background-feedback{
  background-color: #004ea2;
  min-height: 300px;
}
.title-data{
  font-size: 36px;
  font-weight: 700;
  letter-spacing: 0;
  color: #ffffff;
  position: relative;
  margin-bottom: 20px;
  margin-left: 80px;
  padding-top: 50px;
}
.describe-data{
  font-size: 26px;
  font-weight: 700;
  letter-spacing: 0;
  color: #ffffff;
  position: relative;
  margin-bottom: 20px;
  margin-top: 50px;
  margin-left: 80px;
  font-style: italic;
}
.feedback-flex {
  display: flex;
  flex-wrap: wrap;
  padding: 20px 50px 30px 100px;
}
.feedback-item{
  flex: 1 1 50%;
}
.feedback-background{
  flex: 1 1 50%;
  background-image: url('/static/images/feedback-background.svg');
  background-size: cover;
}
</style>
