<script setup>
import {
  defineExpose,
  defineModel,
  onMounted,
  onUpdated,
  ref,
  watch,
} from 'vue';

const idKetcher = ref();

const modelValue = defineModel();

watch(
  () => modelValue.value,
  (newVal) => {
    console.log(newVal);
  },
);

const buildUrl = (url) => {
  return import.meta.env.BASE_URL === '/'
    ? `${url}`
    : `${import.meta.env.BASE_URL}${url}`;
};

const initKetcher = () => {
  setTimeout(() => {
    // idKetcher.value.contentWindow.ketcher;
    setMolecule(JSON.stringify(modelValue.value));
    const observer = new MutationObserver(handleMutation);

    observer.observe(idKetcher.value.contentDocument, {
      childList: true,
      subtree: true,
    });

    async function handleMutation(mutationsList, observer) {
      // iframe内容变化时的处理逻辑
      modelValue.value = JSON.parse(
        await idKetcher.value?.contentWindow?.ketcher?.getKet(),
      );
    }

    // idKetcher.value?.contentWindow?.ketcher?.setMolfile(
    //   JSON.stringify(modelValue.value),
    // );
  }, 500);
};

const getSmiles = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getSmiles();
};

const getMolfile = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getMolfile();
};

function setMolecule(e) {
  return idKetcher.value?.contentWindow?.ketcher?.setMolecule(e);
}

const getKet = () => {
  return idKetcher.value?.contentWindow?.ketcher?.getKet();
};

onMounted(() => {
  console.log('onMounted.value', modelValue.value);
  setMolecule(JSON.stringify(modelValue.value));
});

onUpdated(() => {
  console.log('onUpdated.value', modelValue.value);
  // setMolecule(modelValue.value);
});

defineExpose({
  getSmiles,
  getMolfile,
  setMolecule,
  getKet,
});
</script>

<template>
  <iframe
    ref="idKetcher"
    class="frame h-full w-full"
    :src="buildUrl('/static/standalone/index.html')"
    @load="initKetcher"
  ></iframe>
</template>

<style scoped>
.molecule {
  padding: 30px;
}

.input-search {
  width: 800px;
}

.molecule .left_content {
  width: 800px;
}

.molecule .right_content {
  width: calc(100% - 800px);
  margin-top: 50px;
  margin-left: 50px;
}

.el-login-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 40px;
  font-family: Arial;
  font-size: 12px;
  line-height: 40px;
  text-align: center;
  letter-spacing: 1px;
}

.radio-group {
  display: grid;
}

.ant-radio-wrapper {
  padding: 10px 0 !important;
}
</style>
