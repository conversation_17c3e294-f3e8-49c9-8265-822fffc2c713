<script setup lang="ts">
import {ref, onMounted} from 'vue';
import { $t } from '#/locales';
import {
  Button,
  Input,
  Space,
  MessagePlugin,
  List,
  ListItem,
  ListItemMeta,
  Typography,
  Card, Tooltip
} from 'tdesign-vue-next';
import {
  ChevronDownIcon,
} from 'tdesign-icons-vue-next';

import {useRouter} from "vue-router";
import ChemFormula from '../tsearch/components/ChemFormula.vue';
import {statistics} from '#/views/modules/tDataBase/api.ts';
import {useUserStore} from "@vben/stores";
import {getHomeDisplayList} from "#/views/modules/tDataClassify/api.ts";
import {getServiceHome} from "#/views/modules/tDataService/api.ts";
import { preferences } from '@vben/preferences';
const searchInput = ref('');
const searchTab = ref('all');

const router = useRouter()
const userStore = useUserStore();

const tabs = [
  { value: 'all', labelKey: 'webLio.tabs.all' }, // 全部
  { value: 'molecule', labelKey: 'webLio.tabs.molecule' }, // 分子
  // { value: 'reaction', labelKey: 'webLio.tabs.reaction' }, // 反应
  { value: 'experiment', labelKey: 'webLio.tabs.experiment' }, // 实验
  // { value: 'project', labelKey: 'webLio.tabs.project' }, // 项目
  { value: 'literature', labelKey: 'webLio.tabs.literature' }, // 文献
];

const ellipsisState = {
  row: 2,
  expandable: false,
  collapsible: false,
};

// 添加快链鼠标悬停状态
const hoveredLinks = ref({});

// 添加搜索建议数据
const searchSuggestions = [
  'C2H4O2',
  '异戊醇',
  'Glycerol',
  '62-53-3',
  'PUPZLCDOIYMWBV-UHFFFAOYSA-N'
];

// 处理建议点击
const handleSuggestionClick = (suggestion: string) => {
  searchInput.value = suggestion;
  toSearch(1, 'molecule', searchInput.value);
};

// --- Methods ---
const handleSearch = () => {
  if(searchInput.value == ''){
    MessagePlugin.warning('请输入查询内容！');
  }else {
    if (searchTab.value == 'all' || searchTab.value == 'molecule' || searchTab.value == 'experiment' || searchTab.value == 'literature') {
      toSearch(1, searchTab.value, searchInput.value);
    } else {
      MessagePlugin.warning('功能正在开发中，敬请期待！');
    }
    console.log('Searching for:', searchInput.value, 'in tab:', searchTab.value);
  }
};

const toSearch = (isAdvanced: any, queryType: any, queryText: any) => {
  router.push({name:'tSearchIndex', query: {isAdvanced: isAdvanced, queryType:queryType, queryText:queryText}});
};

const categoryMore = () => {
  router.push({name: 'tDataClassDetail'})
}

const dialogVisible = ref(false)
const childModalRef = ref<InstanceType<typeof ChemFormula> | null>(null)
const openDialog = () => {
  dialogVisible.value = true;
  childModalRef.value?.open();
}
const moth = (data) => {
  searchInput.value = data.smiles
  dialogVisible.value = false
  toSearch(1, 'molecule', searchInput.value);
}
const statistics_res = ref([]);
const categorys = ref([]);
const services = ref([]);
onMounted(async () => {
  statistics_res.value = await statistics();
  services.value = await getServiceHome();
  categorys.value = await getHomeDisplayList()
});
const toSearchByCategoryCode = (value) => {
  router.push({name: 'tSearchIndex', query: {CategoryCode: value}})
};
const toLink1 = () => {
  const newWindow = window.open('https://www.dicp.ac.cn/', '_blank');
  if (newWindow) {
    newWindow.opener = null; // 增强安全性，防止 opener 攻击
  } else {
    console.warn('请允许弹出窗口以继续');
  }
};
const toLink2 = () => {
  const newWindow = window.open('http://www.las.cas.cn/', '_blank');
  if (newWindow) {
    newWindow.opener = null; // 增强安全性，防止 opener 攻击
  } else {
    console.warn('请允许弹出窗口以继续');
  }
};

const statsClickHandle = (item: any) => {
  if(item == 'classify'){
    router.push({name: 'tDataClassDetail'})
  }
  if(item == 'dataset'){
    router.push({name: 'tSearchIndex', query: {isDataset: true}})
  }
  if(item == 'dataService'){
    router.push({name: 'tDataServiceIndex'})
  }
  if(item == 'dataCount'){
    toSearch(1,'all','')
  }
}

const toLinkService = (link : any) => {
  /*const newWindow = window.open(link, '_blank');
  if (newWindow) {
    newWindow.opener = null; // 增强安全性，防止 opener 攻击
  } else {
    console.warn('请允许弹出窗口以继续');
  }*/
  MessagePlugin.warning('功能正在开发中，敬请期待！');
};

const toServiceDetails = () => {
  router.push({name: 'tDataServiceIndex'})
};
const hero = ref(1)
const changeHero = () => {
  if(hero.value == 1){
    hero.value = 2
    return
  }else if(hero.value == 2){
    hero.value = 3
    return
  }else if(hero.value == 3){
    hero.value = 4
    return
  }else if(hero.value == 4){
    hero.value = 5
    return
  }else if(hero.value == 5){
    hero.value = 6
    return
  }else if(hero.value == 6){
    hero.value = 7
    return
  }else if(hero.value == 7){
    hero.value = 8
    return
  }else{
    hero.value = 1
  }
};
</script>

<template>
  <ChemFormula v-if="dialogVisible" ref="childModalRef" @moth="moth"/>
  <div class="web-lio-page" @keydown.enter="handleSearch">
    <div class="web-lio-content">
      <!-- Hero Section -->
      <div v-if="hero == 1" class="hero-section text-white flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div v-else-if="hero == 2" class="hero-section2 text-black flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div v-else-if="hero == 3" class="hero-section3 text-black flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div v-else-if="hero == 4" class="hero-section4 text-black flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div v-else-if="hero == 5" class="hero-section5 text-black flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div v-else-if="hero == 6" class="hero-section6 text-black flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div v-else-if="hero == 7" class="hero-section7 text-white flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div v-else class="hero-section8 text-white flex flex-col items-center justify-center" @click="changeHero">
        <h1 class="text-5xl font-bold mb-4">{{ $t('webLio.hero.title') }}</h1> <!-- 科研数据管理平台 -->
        <p class="text-lg mb-8">{{ $t('webLio.hero.subtitle') }}</p> <!-- 助力科研，提升效率 -->
      </div>
      <div class="hero-section-nobg text-white flex flex-col items-center justify-center">
        <div class="p-2 rounded-md w-full max-w-3xl">
          <!-- 自定义标签组件替代原有的 Tabs -->
          <div class="custom-tabs">
            <div class="tabs-list">
              <div v-for="tab in tabs" :key="tab.value" @click="searchTab = tab.value"
                   :class="['tab-item', { active: searchTab === tab.value }]">
                {{ $t(tab.labelKey) }}
              </div>
              <div class="advanced-search">
                <Button theme="default" variant="text" class="advanced-search-btn" style="color: #FFFFFF;" @click="toSearch(2,'all','')">{{ $t('webLio.tabs.advancedSearch') }} >></Button>
              </div>
            </div>
          </div>

          <div class="flex items-center mt-2">
            <div class="search-container flex-grow relative">
              <Input v-model="searchInput" :placeholder="$t('webLio.search.placeholder')" size="large"
                     class="search-input" clearable/>
              <div class="search-buttons">
                <Button theme="primary" @click="handleSearch" class="search-btn" :title="$t('webLio.search.alt.search')">
                  <img src="/static/images/05.png" :alt="$t('webLio.search.alt.search')" style="width: 24px;height: 24px;"/>
                </Button>
              </div>
            </div>
            <Button theme="success" class="green-btn" :title="$t('webLio.search.alt.structure')" @click="openDialog">
              <img src="/static/images/04.png" :alt="$t('webLio.search.alt.structure')" style="width: 24px;height: 24px;"/>
            </Button>
          </div>
          <Space class="mt-2 text-gray-500 text-sm search-suggestions" style="color: #00B9EF;margin-top: 12px;">
            <span v-for="(suggestion, index) in searchSuggestions" :key="index" class="search-suggestion"
                  @click="handleSuggestionClick(suggestion)">
              {{ suggestion }}
            </span>
          </Space>
        </div>
      </div>

      <!-- Stats Section -->
      <div class="stats">
        <div class="stats-section">
          <div class="stats-wrapper" style="display: flex;flex-direction: column;">
            <div style="display: flex;gap: 120px;">
              <div class="stat-item" @click="statsClickHandle('classify')">
                <img :src="'/static/images/06-1.png'" class="stats-img"/>
                <div class="stat-value-label">
                  <div class="stat-value">{{ statistics_res.find(item => item.value === 'classify')?.count ?? 0 }}</div>
                  <div class="stat-label">{{ $t('webLio.stats.classify') }}</div>
                </div>
              </div>
              <div class="stat-item" @click="statsClickHandle('dataset')">
                <img :src="'/static/images/06-2.png'" class="stats-img"/>
                <div class="stat-value-label">
                  <div class="stat-value">{{ statistics_res.find(item => item.value === 'dataset')?.count ?? 0 }}</div>
                  <div class="stat-label">{{ $t('webLio.stats.dataset') }}</div>
                </div>
              </div>
              <div class="stat-item" @click="statsClickHandle('dataService')">
                <img :src="'/static/images/06-3.png'" class="stats-img"/>
                <div class="stat-value-label">
                  <div class="stat-value">{{ statistics_res.find(item => item.value === 'dataService')?.count ?? 0 }}</div>
                  <div class="stat-label">{{ $t('webLio.stats.dataService') }}</div>
                </div>
              </div>
              <div class="stat-item" @click="statsClickHandle('dataCount')">
                <img :src="'/static/images/06-4.png'" class="stats-img"/>
                <div class="stat-value-label">
                  <div class="stat-value">{{ statistics_res.find(item => item.value === 'dataCount')?.count ?? 0 }}</div>
                  <div class="stat-label">{{ $t('webLio.stats.dataCount') }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Category Section -->
      <div class="category-service-section-full">
        <div class="category-service-section">
          <div class="category-section">
            <div class="category-head">
              <div class="blue-block"></div>
              <h2 class="category-title">{{ $t('webLio.categories.title') }}</h2>
              <h2 class="category-subtitle" v-if="preferences.app.locale === 'zh-CN'">{{ '/ DATA RESOURCES' }}</h2>
              <h2 class="category-subtitle" v-else></h2>
              <div class="category-more">
                <Button class="view-more-btn" @click="categoryMore" variant="text">
                  {{ '+' + $t('webLio.categories.viewMore') }}
                </Button>
              </div>
            </div>
            <div class="category-wrapper">
              <div class="category-grid category-grid">
                <div v-for="category in categorys.slice(0, 9)" :key="category.classifyCode"
                     class="category-card">
                  <div class="category-card-inner" @click="toSearchByCategoryCode(category.classifyCode)">
                    <div class="category-card-inner-left">
                      <div class="category-name">{{ preferences.app.locale === 'zh-CN' ? $t(category.classifyName) : $t(category.classifyNameEng) }}</div>
                      <div class="category-count">{{ category.num }}</div>
                    </div>
                    <div class="category-icon">
                      <img :src="category.imageFile" referrerpolicy="no-referrer" class="quick-link-icon"/>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="service-section">
            <div class="service-head">
              <div class="blue-block"></div>
              <h2 class="service-title">{{ $t('webLio.services.title') }}</h2>
              <h2 class="service-subtitle" v-if="preferences.app.locale === 'zh-CN'">{{ '/ DATA SERVICES' }}</h2>
              <h2 class="service-subtitle" v-else></h2>
              <div class="service-more">
                <Button class="view-more-btn" variant="text" @click="toServiceDetails">
                  {{ '+' + $t('webLio.categories.viewMore') }}
                </Button>
              </div>
            </div>
            <div class="service-wrapper">
              <Space direction="vertical" size="large">
                <List :split="true">
                  <ListItem class="service-item" v-for="service in services">
                    <ListItemMeta :image="service.imgPath" :title="service.serviceTitle" @click="toLinkService(service.serviceLink)" style="cursor: pointer">
                      <template #description>
                        <Tooltip :content="service.serviceDescribe" :destroyOnClose="false" :showArrow="false" theme="light" placement="bottom">
                          <Typography :ellipsis="ellipsisState">
                            <div>{{service.serviceDescribe}}</div>
                          </Typography>
                        </Tooltip>
                      </template>
                    </ListItemMeta>
                  </ListItem>
                </List>
              </Space>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-bg-img">
        <div class="construction-section">
          <div class="construction-head">
            <div class="blue-block"></div>
            <h2 class="construction-title">{{ $t('webLio.constructions.title') }}</h2>
            <h2 class="construction-subtitle" v-if="preferences.app.locale === 'zh-CN'">{{ '/ CONSTRUCTION ENTITY' }}</h2>
            <h2 class="construction-subtitle" v-else></h2>
          </div>
          <div class="construction-wrapper">
            <Card class="construction-card">
              <div class="construction-item" @click="toLink1">
                <img :src="'/static/images/02-Vector.png'" class="construction-icon"/>
                <img :src="'/static/images/08-1.png'" class="construction-img1"/>
              </div>
              <div class="construction-item1" @click="toLink2">
                <img :src="'/static/images/08-2.png'" class="construction-img2"/>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.t-card__body) {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.construction-icon {
  width: 70px;
  height: 70px;
}

.construction-img1 {
  width: 400px;
  height: 70px;
}

.construction-img2 {
  width: 500px;
  height: 70px;
}

.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
}

.web-lio-content {
  flex-grow: 1;
}

.hero-section {
  background-image: url('/static/images/01-background01.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}

.hero-section2 {
  background-image: url('/static/images/01-background02.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}
.hero-section2 h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.hero-section2 p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}

.hero-section3 {
  background-image: url('/static/images/01-background03.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}
.hero-section3 h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.hero-section3 p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}

.hero-section4 {
  background-image: url('/static/images/01-background04.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}
.hero-section4 h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.hero-section4 p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}

.hero-section5 {
  background-image: url('/static/images/01-background05.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}
.hero-section5 h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.hero-section5 p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}

.hero-section6 {
  background-image: url('/static/images/01-background06.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}
.hero-section6 h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.hero-section6 p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}
.hero-section7 {
  background-image: url('/static/images/01-background07.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}
.hero-section7 h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.hero-section7 p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}
.hero-section8 {
  background-image: url('/static/images/01-background08.png');
  background-size: cover;
  background-position: center;
  min-height: 200px;
  padding: 120px 2rem 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 0;
}
.hero-section8 h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.hero-section8 p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}

.bottom-bg-img {
  background-image: url('/static/images/constructions-background.png');
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 0;
}

.hero-section h1 {
  font-size: 60px;
  font-weight: 400;
  margin-bottom: 16px;
  letter-spacing: 1px;
}

.hero-section p {
  font-size: 22px;
  margin-bottom: 99px;
  opacity: 0.9;
}
.hero-section-nobg {
  padding: 0 2rem 0;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 1; /* 创建新的层叠上下文 */
  margin-top: -60px;
  margin-bottom: 20px;
}
.p-2.rounded-md.w-full.max-w-3xl {
  padding: 0;
  max-width: 1150px;
}

.custom-tabs {
  width: 100%;
  background-color: transparent;
  margin-bottom: 0px;
  position: relative;
  padding-right: 60px;
}

.tabs-list {
  display: flex;
  padding: 0;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
  backdrop-filter: blur(4px);
  box-shadow: none;
}

.tab-item {
  padding: 2px 9px;
  margin: 0;
  margin-right: 10px;
  font-size: 14px;
  color: white;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-weight: 400;
  opacity: 0.85;
  background-color: transparent;
  position: relative;
}

.tab-item:hover {
  opacity: 1;
  background-color: rgba(54, 168, 255, 0.2);
}

.tab-item:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #00B9EF;
  transition: width 0.3s ease;
}

.tab-item:hover:after {
  width: 100%;
}

.tab-item.active {
  background-color: #00B9EF;
  color: white;
  opacity: 1;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tab-item.active:after {
  display: none;
}

.advanced-search {
  margin-left: auto;
  padding-left: 20px;
}

.advanced-search-btn {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  padding-right: 0;
}

.advanced-search-btn:hover {
  color: white;
  background-color: transparent;
}

.search-container {
  position: relative;
  width: 100%;
  margin-right: 1px;
}

.search-input {
  width: 100%;
  background-color: white;
  border-radius: 4px 4px 4px 4px !important;
  border: none !important;
  height: 46px;
}

:deep(.search-input .t-input__suffix) {
  right: 46px;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}

:deep(.search-input .t-input__suffix-clear) {
  color: #999;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 16px;
  height: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  border-radius: 50%;
}

:deep(.search-input .t-input__suffix-clear:hover) {
  color: #666;
  background: transparent;
}

:deep(.search-input .t-input__inner) {
  height: 46px;
  font-size: 15px;
  padding-left: 6px;
  padding-right: 65px;
}

:deep(.search-input .t-icon.t-icon-close) {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

.search-buttons {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 10;
}

:deep(.search-input) {
  position: relative;
  z-index: 1;
}

:deep(.search-input .t-input) {
  position: relative;
  z-index: 1;
}

.search-btn {
  border: none;
  background: transparent;
  padding: 0 !important;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  background: transparent;
}

.green-btn {
  border: none;
  background: #00B42A !important;
  padding: 0 !important;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 4px 4px 0 !important;
  transition: background-color 0.3s ease;
}

.green-btn:hover {
  background: #009A29 !important;
}

.search-btn img,
.green-btn img {
  width: 24px;
  height: 24px;
}

.quick-links-section {
  padding: 20px 0 40px;
  position: relative;
  z-index: 2;
  margin-top: 30px;
}

.quick-links-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 110px;
  flex-wrap: wrap;
}

.quick-link-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px;
  border-radius: 8px;
}

.quick-link-item:hover {
  transform: translateY(-8px);
  background-color: #004EA2;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.quick-link-icon {
  width: 50px;
  height: 50px;
}

.quick-link-label {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  margin-top: 8px;
}

.stats {
  display: flex;
  width: 100%;
  height: 150px;
  z-index: 10;
  justify-content: center;
  margin-bottom: 30px;
}

.stats-section {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #F7F9FB;
  border: 1px solid #e5e7eb;
  width: 1150px;
  height: 100%;
  position: relative;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.stats-container {
  padding: 18px 35px 12px;
}

.stats-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 5px;
  align-items: center
}

.stat-item {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
  padding: 0;
  position: relative;
  text-align: left;
}

.stats-img {
  width: 90px;
  height: 90px;
}

.stat-value-label{
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 10px;
}

.stat-value {
  font-size: 30px;
  color: #FF7E00;
  line-height: 1;
  transition: all 0.2s ease;
  font-weight: 600;
}

.stat-label {
  font-size: 16px;
  color: #333333;
  margin-top: 5px;
  line-height: 1;
  font-weight: 600;
}

.stats-more {
  align-self: flex-start;
  margin-top: 4px;
  padding-left: 0;
}

.stats-more-link {
  color: #004EA2;
  font-size: 14px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  font-weight: 400;
}

.stats-more-link:hover {
  text-decoration: underline;
}

.stats-more-link .arrow {
  margin-left: 4px;
  font-weight: bold;
}

.category-service-section-full{
  display: flex;
  justify-content: center;
  width: 100%;
}

.category-service-section{
  display: flex;
  flex-direction: row;
  width: 1150px;
}

.category-section {
  background-color: #FFFFFF;
  position: relative;
  overflow: visible;
  width: 765px;
}

.service-section {
  background-color: #FFFFFF;
  position: relative;
  overflow: visible;
  width: 375px;
  margin-left: 10px;
}

.construction-section {
  position: relative;
  overflow: visible;
  width: 1150px;
}

.category-head {
  display: inline-flex;
  align-items: center;
  width: 100%;
  height: 40px;
}

.service-head {
  display: inline-flex;
  align-items: center;
  width: 100%;
  height: 40px;
}

.construction-head {
  margin-top: 20px;
  display: inline-flex;
  align-items: center;
  width: 100%;
  height: 40px;
}

.blue-block {
  background-color: #125CC1;
  width: 5px;
  height: 60%;
}

.category-title {
  font-size: 22px;
  font-weight: 600;
  color: #333333;
  text-align: left;
  margin-left: 10px;
  width: 98px;
}

.service-title {
  font-size: 22px;
  font-weight: 600;
  color: #333333;
  text-align: left;
  margin-left: 10px;
  width: 98px;
}

.construction-title {
  font-size: 22px;
  font-weight: 600;
  color: #333333;
  text-align: left;
  margin-left: 10px;
  width: 98px;
}

.category-subtitle {
  font-size: 16px;
  text-align: left;
  color: #DED8D7;
  margin-left: 10px;
  width: 580px;
}

.service-subtitle {
  font-size: 16px;
  text-align: left;
  color: #DED8D7;
  margin-left: 10px;
  width: 193px;
}

.construction-subtitle {
  font-size: 16px;
  text-align: left;
  color: #DED8D7;
  margin-left: 10px;
  width: 580px;
}

.category-wrapper {
  position: relative;
  overflow: visible;
  margin: 0 auto;
  max-width: 100%;
  padding: 15px 13px 0;
}

.service-wrapper {
  position: relative;
  overflow: visible;
  margin: 0 auto;
  max-width: 100%;
  padding: 15px 13px 0;
}

.construction-wrapper {
  position: relative;
  overflow: visible;
  margin: 0 auto;
  max-width: 100%;
  padding: 15px 13px 0;
}

.construction-card {
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: center;
  height: 150px;
  margin-bottom: 20px;
}

.construction-item {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-left: 30px;
}

.construction-item1 {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-left: 40px;
}

.service-item{
  height: 140px;
}

.category-grid {
  display: grid;
  gap: 20px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  transform: translateX(0);
  padding: 0;
  margin-bottom: 10px;
}

.category-grid:last-child {
  margin-bottom: 20px;
}

.category-grid {
  grid-template-columns: repeat(3, 1fr);
}

.category-card {
  border-radius: 2px;
  overflow: visible;
  transition: all 0.3s ease;
  height: 80px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.category-card:hover {
  transform: translateY(-5px);
  background-color: #2f7ac6 !important;
  border-color: rgba(255, 255, 255, 0.2);
  z-index: 2;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.category-card:hover .category-name,
.category-card:hover .category-count {
  color: #ffffff;
  font-weight: bold;
}

.category-card:hover .category-icon {
  color: #ffffff;
  filter: brightness(0) invert(1);
}

.category-card:nth-child(3n+1) {
  background-color: #F7F9FB;
}
.category-card:nth-child(3n+2) {
  background-color: #F7F9FB;
}
.category-card:nth-child(3n+3) {
  background-color: #F7F9FB;
}

.category-card-inner {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 15px;
  height: 100%;
}

.category-card-inner-left {
  display: flex;
  flex-direction: column;
  width: 120px;
}

.category-icon {
  color: #0d6efd;
  font-size: 44px;
  transition: all 0.3s ease;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: right;
  border-radius: 50%;
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

.category-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  text-align: left;
}

.category-count {
  font-size: 16px;
  font-weight: 600;
  color: #125CC1;
}

.category-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 56px;
}

.category-more {
  text-align: center;
}

.view-more-btn {
  color: #125CC1;
  border: none;
  padding: 12px 8px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
}

.animated-arrow {
  display: inline-block;
  animation: bounce 1.5s infinite;
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #fff;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(4px);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.t-button.w-\[40px\].h-\[40px\] .t-icon {
  font-size: 24px;
}

.quick-links img {
  transition: all 0.3s ease;
}

.quick-links-section {
  width: 100%;
  margin-top: 0px;
}

.search-btn {
  border-radius: 8px 8px 8px 8px !important;
  padding: 0 !important;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  background: linear-gradient(to bottom, #50A3FB, #004EA2);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  margin-right: 4px;
}

.search-btn:hover {
  background: linear-gradient(to bottom, #4095EA, #003E92);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.search-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.green-btn {
  border-radius: 8px 8px 8px 8px !important;
  padding: 0 !important;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  background: linear-gradient(to bottom, #02D059, #00903D);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  margin-left: 10px;
}

.green-btn:hover {
  background: linear-gradient(to bottom, #02C050, #007F30);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.green-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.search-btn img, .green-btn img {
  width: 22px;
  height: 22px;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
}

.search-input {
  background-color: white;
  border-radius: 4px 4px 4px 4px !important;
  border: none !important;
  height: 46px;
}

:deep(.search-input .t-input) {
  border: none !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  height: 100%;
}

:deep(.mt-2) {
  margin-top: 5px;
}

:deep(.search-input .t-input:focus-within) {
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.2);
}

.search-buttons {
  display: flex;
  height: 46px;
}

.search-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.search-suggestion {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding: 2px 8px;
  border-radius: 4px;
}

.search-suggestion:hover {
  color: #0035ff;
  background-color: rgba(0, 185, 239, 0.2);
  transform: translateY(-1px);
}

.search-suggestion:active {
  transform: translateY(0);
  background-color: rgba(0, 185, 239, 0.3);
}

.search-suggestion::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background-color: #00B9EF;
  transition: width 0.3s ease;
}

.search-suggestion:hover::after {
  width: 100%;
}

:deep(.search-input .t-input__suffix-clear) {
  color: #999;
  font-size: 16px;
  margin-right: 46px;
  cursor: pointer;
  transition: color 0.3s ease;
}

:deep(.search-input .t-input__suffix-clear:hover) {
  color: #666;
}

@keyframes modalSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
