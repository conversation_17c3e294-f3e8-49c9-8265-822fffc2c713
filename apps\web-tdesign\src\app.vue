<script lang="ts" setup>
import { computed } from 'vue';

import { useTDesignTokens } from '@vben/hooks';

import { ConfigProvider } from 'tdesign-vue-next';
// const { commonTokens } = useNaiveDesignTokens();
import { preferences } from '@vben/preferences';

import enConfig from 'tdesign-vue-next/es/locale/en_US';
import zhConfig from 'tdesign-vue-next/es/locale/zh_CN';

defineOptions({ name: 'App' });

const tokenLocale: any = computed(() =>
  preferences.app.locale === 'zh-CN' ? zhConfig : enConfig,
);

useTDesignTokens();
// 主应用跨域
</script>

<template>
  <ConfigProvider :global-config="tokenLocale" class="h-full">
    <RouterView />
  </ConfigProvider>
</template>
