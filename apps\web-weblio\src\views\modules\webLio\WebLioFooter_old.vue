<template>
  <!-- Bottom Footer (Blue Background) -->
  <Footer class="web-lio-footer">
    <div class="container mx-auto">
      <div class="bottom-footer-content">
        <!-- 页脚链接 -->
        <Space size="small" separator="|" class="policy-links">
          <Button theme="default" variant="text" size="small" @click="toExpectant('webLio.footer.bottom.disclaimer')">
            {{ $t('webLio.footer.bottom.disclaimer') }}
          </Button>
          <Button theme="default" variant="text" size="small" @click="toExpectant('webLio.footer.bottom.terms')">
            {{ $t('webLio.footer.bottom.terms') }}
          </Button>
        </Space>
        <Space size="small" separator=" ">
          <div class="copyright">{{ $t('webLio.footer.bottom.copyright') }}</div>
          <div class="copyright"><member-icon />{{' '+$t('webLio.footer.bottom.visits') + ' : ' + visit}}</div>
        </Space>
      </div>
    </div>
  </Footer>
</template>

<script setup lang="ts">
import {Button, Space, Footer, MessagePlugin} from 'tdesign-vue-next';
import {$t} from "@vben/locales";
import {onMounted, ref} from "vue";
import {visits} from '#/views/modules/tDataBase/api.ts';
import {MemberIcon} from "tdesign-icons-vue-next";
const toExpectant = (labelKey: string) => {
  MessagePlugin.warning($t(labelKey)+'功能开发中，敬请期待！');
}
const visit = ref(0);
onMounted(async () => {
  visit.value = await visits();
})
</script>

<style scoped>
.middle-footer {
  background-color: #ffffff;
  border-top: none;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px 0;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}
.middle-footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
}
.header-nav {
  display: flex;
  justify-content: center;
  align-items: center;
}
.header-logo {
  height: 40px;
  width: auto;
  margin-right: 24px;
}
.nav-links {
  display: flex;
  justify-content: center;
  align-items: center;
}
.nav-link {
  color: #374151;
  font-size: 15px;
  margin: 0 16px;
  padding: 8px 0;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}
.nav-link:hover {
  color: #0052d9;
}
.nav-link:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #0052d9;
  transition: width 0.3s ease;
}
.nav-link:hover:after {
  width: 100%;
}
.blue-divider {
  height: 4px;
  background-color: #0d3a85;
  width: 100%;
}
.web-lio-footer {
  background-color: #063E8B;
  color: #e5e7eb;
  padding: 48px 0;
}
.bottom-footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.policy-links {
  margin-bottom: 14px;
}
.web-lio-footer .t-button--variant-text {
  color: #e5e7eb;
  font-size: 13px;
  padding: 0 10px;
  height: auto;
  line-height: 1.2;
  margin: 0 4px;
  position: relative;
  transition: all 0.3s ease;
  background-color: transparent;
}
.web-lio-footer .t-button--variant-text:hover {
  color: #ffffff;
  transform: translateY(-2px);
  background-color: transparent;
}
.web-lio-footer .t-space__separator {
  color: #e5e7eb;
  pointer-events: none;
}
.copyright {
  color: #e5e7eb;
  font-size: 13px;
  opacity: 0.9;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
