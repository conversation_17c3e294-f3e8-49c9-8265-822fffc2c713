<script lang="ts" setup>
import {MessagePlugin, type PageInfo, type TableRowData} from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import PutAway from '#/components/put-away/index.vue';
import { removalUnderline } from '#/utils/sort';
import { permissionTags } from '#/views/_sys/permission/api';
import { useVbenModal } from '@vben/common-ui';
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  Link,
  Row,
  Select,
  Space,
  Table,
} from 'tdesign-vue-next';
import { computed, ref } from 'vue';

import {
  roleBindPermissionDelete,
  roleBindPermissionDeleteBatch,
  roleBindPermissionListByPageApi,
} from '../../api';
import SelectPermissionModelComponent from '../select-permission-modal/index.vue';

// const { t } = useI18n();

// 表单控制属性
const formData: any = ref({});
const record: any = ref({});
const form = ref();
// 是否隐藏
const hideQuery = ref(true);

// 数据表控制属性
const data: any = ref([]);
const loading = ref(false);
const queryItemsLoading = ref(false);
const sort = ref([]);
const pagination: any = ref({
  current: 1,
  pageSize: 20,
  total: 0,
});

const [SelectPermissionModal, selectPermissionModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: SelectPermissionModelComponent,
});

const stripe = ref(true);
const bordered = ref(false);
const hover = ref(true);
const tableLayout = ref(false);
const size: any = ref('medium');
const showHeader = ref(true);
const selectedRowKeys = ref([]);
const columnControllerVisible = ref(false);
const columns: any = ref([
  {
    colKey: 'row-select',
    type: 'multiple',
    title: '多选',
    width: 50,
  },
  {
    title: '序号',
    colKey: 'serial-number',
    width: '100',
  },
  {
    colKey: 'code',
    title: '权限标识',
    ellipsis: true,
    sorter: true,
  },

  {
    colKey: 'name',
    title: '权限名称',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'tag',
    title: '标签',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'type_text',
    title: '授权类型',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'power_text',
    title: '系统权限开关（授权类型为系统时生效）',
    ellipsis: true,
    ellipsisTitle: true,
    sorter: true,
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'createBy',
    title: '创建人',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'updateBy',
    title: '更新人',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'remark',
    title: '备注',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: '180',
    fixed: 'right',
  },
]);

const types = ref([]);
const tags = ref([]);
const groupColumn = ref(true);
const placement = ref<any['placement']>('top-right');
const customText = ref(true);
const displayColumns = ref<any['displayColumns']>([
  'row-select',
  'serial-number',
  'code',
  'name',
  'tag',
  'type_text',
  'power_text',
  'remark',
  'createTime',
  'op',
]);
// 列配置
const columnControllerConfig = computed<any['columnController']>(() => ({
  // 列配置按钮位置
  placement: placement.value,
  hideTriggerButton: true,
  // 用于设置允许用户对哪些列进行显示或隐藏的控制，默认为全部字段
  fields: [
    'row-select',
    'serial-number',
    'code',
    'name',
    'tag',
    'type_text',
    'power_text',
    'remark',
    'createTime',
    'updateTime',
    'createBy',
    'updateBy',
    'op',
  ],
  // 弹框组件属性透传
  dialogProps: {
    preventScrollThrough: true,
  },
  // 列配置按钮组件属性透传
  buttonProps: customText.value
    ? {
        content: '',
        theme: '',
        variant: 'text',
      }
    : undefined,
  // 数据字段分组显示
  groupColumns: groupColumn.value
    ? [
        {
          label: '业务字段',
          value: 'a',
          columns: ['code', 'name', 'type_text', 'power_text', 'remark'],
        },
        {
          label: '系统字段',
          value: 'b',
          columns: ['serial-number', 'row-select'],
        },
        {
          label: '记录字段',
          value: 'c',
          columns: ['createTime', 'updateTime', 'createBy', 'updateBy'],
        },
      ]
    : undefined,
}));

const fetchData = async (params: any) => {
  loading.value = true;
  try {
    const { records, total } = await roleBindPermissionListByPageApi(
      params,
      record.value.code,
    );

    data.value = records;
    pagination.value = {
      ...pagination.value,
      total,
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const loadData = async () => {
  const params = {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };

  await fetchData(params);
};
const rehandlePageChange = (
  pageInfo: PageInfo,
  newDataSource: TableRowData[],
) => {
  // eslint-disable-next-line no-console
  console.log('分页变化', pageInfo, newDataSource);
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};
const onReset = () => {
  form.value.reset();
  loadData();
};
const onSubmit = async () => {
  loadData();
};

const rehandleSelectChange = (value: any) => {
  selectedRowKeys.value = value;
};

const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

const loadQueryItems = async () => {
  queryItemsLoading.value = true;
  try {
    types.value = await getDictItems('PERMISSION_TYPE');
    formData.value.type = 1;
    const res = await permissionTags();
    tags.value = res.map((item: any) => {
      return { value: item, label: item };
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    queryItemsLoading.value = false;
  }
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      record.value = isOpen
        ? { ...modalApi.getData<Record<string, any>>()?.record }
        : {};
      await loadQueryItems();
      loadData();
    }
  },

  title: '分配权限',
});

const handleRowClick = () => {};

const roleBindPermissionDeleteBatchMethod = async (data1, data2) => {
  console.log(data2);
  if(data2 != '') {
    let res = await roleBindPermissionDeleteBatch(
      data1,
      data2,
    );
    if (res) {
      await loadData();
    } else {
      MessagePlugin.warning("请勾选要解除绑定的权限")
    }
  }else{
    MessagePlugin.warning("请勾选要解除绑定的权限")
  }
};
</script>

<template>
  <Modal :footer="false" class="w-8/12">
    <Space direction="vertical">
      <Card>
        <Form
          ref="form"
          :data="formData"
          :label-width="80"
          colon
          @reset="onReset"
          @submit="onSubmit"
        >
          <Row :gutter="[24, 24]">
            <Col :span="4">
              <FormItem label="权限标识" name="code">
                <Input
                  v-model="formData.code"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入权限标识"
                  type="search"
                />
              </FormItem>
            </Col>
            <Col :span="4">
              <FormItem label="权限名称" name="name">
                <Input
                  v-model="formData.name"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入权限名称"
                  type="search"
                />
              </FormItem>
            </Col>

            <Col :span="4">
              <FormItem label="标签" name="tag">
                <Select
                  v-model="formData.tag"
                  :options="tags"
                  clearable
                  placeholder="选择标签"
                />
              </FormItem>
            </Col>
            <Col v-show="hideQuery" :span="4">
              <FormItem label="授权类型" name="type">
                <Select
                  v-model="formData.type"
                  :disabled="true"
                  :options="types"
                  clearable
                  placeholder="选择授权类型"
                />
              </FormItem>
            </Col>
            <Col v-show="hideQuery" :span="4">
              <FormItem label="备注" name="remark">
                <Input
                  v-model="formData.remark"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入备注"
                />
              </FormItem>
            </Col>
          </Row>
          <Row justify="end">
            <Col :span="24" class="mt-4">
              <Space size="small">
                <Button
                  :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
                  theme="primary"
                  type="submit"
                >
                  搜索
                </Button>
                <Button theme="default" type="reset" variant="base">
                  重置
                </Button>
                <PutAway v-model="hideQuery" variant="text" />
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card title="已绑定权限列表">
        <template #actions>
          <Space>
            <Button
              @click="
                () => {
                  selectPermissionModalApi.setData({
                    record,
                    refresh: loadData,
                  });
                  selectPermissionModalApi.open();
                }
              "
            >
              添加权限授权
            </Button>
            <Button
              @click="roleBindPermissionDeleteBatchMethod(record.code,selectedRowKeys.join(','))"
            >
              批量解除绑定
            </Button>
            <Button
              @click="
                async () => {
                  await roleBindPermissionDelete(record.code);
                  loadData();
                }
              "
            >
              解除全部绑定
            </Button>
            <ColumnDisplay v-model="columnControllerVisible" />
          </Space>
        </template>
        <Table
          v-model:column-controller-visible="columnControllerVisible"
          v-model:display-columns="displayColumns"
          :bordered="bordered"
          :column-controller="columnControllerConfig"
          :columns="columns"
          :data="data"
          :hover="hover"
          :loading="loading"
          :pagination="pagination"
          :pagination-affixed-bottom="true"
          :selected-row-keys="selectedRowKeys"
          :show-header="showHeader"
          :size="size"
          :sort="sort"
          :stripe="stripe"
          :table-layout="tableLayout ? 'auto' : 'fixed'"
          cell-empty-content="-"
          drag-sort="col"
          lazy-load
          multiple-sort
          resizable
          row-key="code"
          @drag-sort="onDragSort"
          @page-change="rehandlePageChange"
          @row-click="handleRowClick"
          @select-change="rehandleSelectChange"
          @sort-change="sortChange"
        >
          <template #op="slotProps">
            <Space>
              <Link
                theme="primary"
                @click="
                  async () => {
                    await roleBindPermissionDeleteBatch(
                      record.code,
                      slotProps.row.code,
                    );
                    loadData();
                  }
                "
              >
                取消授权
              </Link>
            </Space>
          </template>
        </Table>
      </Card>
    </Space>
    <SelectPermissionModal />
  </Modal>
</template>
