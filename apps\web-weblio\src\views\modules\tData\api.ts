// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

// export async function listByPage(data: any) {
//   return requestClient.post<any[]>('/tData/listByPage', data);
// }

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tData/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tData/deleteBatch/${data}`);
}

export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tData/getOne/${data}`);
}

export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tData/getByIds/${data}`);
}

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataTemplate/listByPage', data);
}

export async function getByCode(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tData/getByCode/${data}`);
}
export async function getByCodeShare(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tData/getByCodeShare/${data}`);
}
export async function getTemplateListByDataType(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tData/getTemplateListByDataType/${data}`);
}

export async function getAllCodes() {
  return requestClient.get<any>(`/rgdc-submit/tData/getAllCodes`);
}

export async function saveOneData(data: any) {
  return requestClient.post<any>('/rgdc-submit/tData/saveOneData', data);
}

// export async function expExcelImport(data: any) {
//   return requestClient.post<any>('/tData/expExcelImport', data);
// }

export async function getLabelList() {
  return requestClient.get<any>(`/rgdc-submit/tDataLabelManage/getLabelList`);
}
export async function getLabelListShare() {
  return requestClient.get<any>(`/rgdc-submit/tDataLabelManage/getLabelListShare`);
}
export async function getDatasetList(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tDataDataset/getDatasetList`, data);
}
export async function getDatasetListShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tDataDataset/getDatasetListShare`, data);
}
export async function getCategorys(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tDataClassify/listByTree/${data}`);
}
export async function isWithLuckySheet(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tData/isWithLuckySheet/${data}`);
}
