import { computed, defineComponent, ref } from 'vue';

import styles from './index.module.less';

interface MenuItem {
  title: string;
  content?: () => any;
  disabled?: boolean;
  children?: MenuItem[];
  key?: string;
}

interface LayoutData {
  header?: () => any;
  menu: MenuItem[];
}

export default defineComponent({
  name: 'DetailLayoutTabbed',
  props: {
    data: {
      type: Object as () => LayoutData,
      required: true,
    },
  },
  setup(props) {
    const activeKey = ref<string>('');
    const expandedKeys = ref<Set<string>>(new Set());
    const menuCollapsed = ref<boolean>(true); // 小屏幕下菜单收起状态

    // 获取所有可点击的菜单项（扁平化处理）
    const getAllClickableItems = (
      items: MenuItem[],
      prefix = '',
    ): MenuItem[] => {
      const result: MenuItem[] = [];
      items.forEach((item, index) => {
        const key = item.key || `${prefix}${index}`;
        const menuItem = { ...item, key };

        // 如果有content，就是可点击的
        if (item.content) {
          result.push(menuItem);
        }

        // 递归处理子项
        if (item.children && item.children.length > 0) {
          result.push(...getAllClickableItems(item.children, `${key}-`));
        }
      });
      return result;
    };

    const flatMenuItems = computed(() => getAllClickableItems(props.data.menu));

    // 初始化激活的菜单项和展开状态
    if (flatMenuItems.value.length > 0) {
      const firstClickableItem = flatMenuItems.value.find(
        (item) => !item.disabled && item.content,
      );
      if (firstClickableItem && firstClickableItem.key) {
        activeKey.value = firstClickableItem.key;
      }

      // 默认展开第一级有子菜单的项目
      const defaultExpandedKeys = new Set<string>();
      props.data.menu.forEach((item, index) => {
        const key = item.key || `${index}`;
        if (item.children && item.children.length > 0) {
          defaultExpandedKeys.add(key);
        }
      });
      expandedKeys.value = defaultExpandedKeys;
    }

    // 获取当前激活的菜单项
    const activeMenuItem = computed(() =>
      flatMenuItems.value.find((item) => item.key === activeKey.value),
    );

    // 点击菜单项
    const handleMenuClick = (item: MenuItem) => {
      if (item.disabled || !item.content || !item.key) {
        return;
      }
      activeKey.value = item.key;
    };

    // 切换展开/收起状态
    const toggleExpand = (key: string, hasChildren: boolean) => {
      if (!hasChildren) return;

      const newExpandedKeys = new Set(expandedKeys.value);
      if (newExpandedKeys.has(key)) {
        newExpandedKeys.delete(key);
      } else {
        newExpandedKeys.add(key);
      }
      expandedKeys.value = newExpandedKeys;
    };

    // 切换小屏幕菜单的展开/收起状态
    const toggleMenuCollapse = () => {
      menuCollapsed.value = !menuCollapsed.value;
    };

    // 渲染菜单项
    const renderMenuItem = (item: MenuItem, depth = 0) => {
      const key = item.key;
      if (!key) return null;

      const isActive = activeKey.value === key;
      const hasContent = !!item.content;
      const hasChildren = item.children && item.children.length > 0;
      const isExpanded = expandedKeys.value.has(key);

      return (
        <div
          class={[
            styles['menu-item-wrapper'],
            hasChildren ? styles['has-children'] : '',
          ].filter(Boolean)}
          key={key}
        >
          <div
            class={[
              styles['menu-item'],
              isActive ? styles.active : '',
              item.disabled ? styles.disabled : '',
              hasContent ? styles.clickable : styles['no-content'],
              `${styles[`depth-${depth}`]}`,
            ].filter(Boolean)}
          >
            <div
              class={styles['menu-item-content']}
              onClick={(e) => {
                e.stopPropagation();
                handleMenuClick(item);
              }}
            >
              <span class={styles['menu-item-title']}>{item.title}</span>
            </div>

            {hasChildren && (
              <div
                class={[
                  styles['expand-button'],
                  isExpanded ? styles.expanded : '',
                ].filter(Boolean)}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleExpand(key, hasChildren);
                }}
              >
                <span class={styles['expand-icon']}>▼</span>
              </div>
            )}
          </div>

          {hasChildren && (
            <div
              class={[
                styles['menu-children'],
                isExpanded ? styles.expanded : styles.collapsed,
              ].filter(Boolean)}
            >
              {(item.children || []).map((child, index) => {
                const childKey = child.key || `${key}-${index}`;
                return renderMenuItem({ ...child, key: childKey }, depth + 1);
              })}
            </div>
          )}
        </div>
      );
    };

    return () => (
      <div class={styles['layout-container']}>
        {/* 头部区域 */}
        {props.data.header && (
          <div class={styles['layout-header']}>{props.data.header()}</div>
        )}

        {/* 主体区域 */}
        <div class={styles['layout-main']}>
          {/* 左侧导航区域 */}
          <div class={styles['layout-sidebar']}>
            {/* 大屏幕：原始树形菜单 */}
            <div class={styles['menu-container-desktop']}>
              {props.data.menu.map((item, index) => {
                const key = item.key || `${index}`;
                return renderMenuItem({ ...item, key }, 0);
              })}
            </div>

            {/* 小屏幕：扁平化可点击菜单 */}
            <div class={styles['menu-container-mobile']}>
              <div
                class={[
                  styles['mobile-menu-items'],
                  menuCollapsed.value
                    ? styles['menu-collapsed']
                    : styles['menu-expanded'],
                ].filter(Boolean)}
              >
                {flatMenuItems.value.map((item) => (
                  <div
                    class={[
                      styles['mobile-menu-item'],
                      activeKey.value === item.key ? styles.active : '',
                      item.disabled ? styles.disabled : '',
                    ].filter(Boolean)}
                    key={item.key}
                    onClick={() => handleMenuClick(item)}
                  >
                    {item.title}
                  </div>
                ))}
              </div>

              {/* 展开收起按钮 */}
              <button
                class={styles['mobile-toggle-button']}
                onClick={toggleMenuCollapse}
              >
                {menuCollapsed.value ? '展开' : '收起'}
              </button>
            </div>
          </div>

          {/* 右侧内容区域 */}
          <div class={styles['layout-content']}>
            {activeMenuItem.value && activeMenuItem.value.content ? (
              <div class={styles['content-wrapper']} key={activeKey.value}>
                {activeMenuItem.value.content()}
              </div>
            ) : (
              <div class={styles['content-placeholder']}>
                <div class={styles['placeholder-text']}>
                  请选择左侧菜单项查看内容
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  },
});
