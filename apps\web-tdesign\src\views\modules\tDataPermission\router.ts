import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据分块权限管理',
    },
    name: 'tDataPermission',
    path: '/tDataPermission',
    children: [
      {
        meta: {
          title: '数据分块权限管理编辑',
        },
        name: 'tDataPermissionIndex',
        path: '/tDataPermission/index',
        component: () =>
          import('#/views/modules/tDataPermission/index.vue'),
      },
    ],
  },
];

export default routes;
