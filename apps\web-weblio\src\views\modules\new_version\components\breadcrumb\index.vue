<script setup lang="ts">
import { CaretRightSmallIcon } from 'tdesign-icons-vue-next';
import { Breadcrumb } from 'tdesign-vue-next';

defineOptions({
  name: 'Breadcrumb',
});
</script>

<template>
  <div class="breadcrumb-container">
    <Breadcrumb v-bind="$attrs">
      <template #separator>
        <CaretRightSmallIcon style="color: #fff !important" />
      </template>
    </Breadcrumb>
  </div>
</template>
<style scoped lang="less">
.breadcrumb-container {
  padding: 20px 0 !important;
  background-color: #0f569f;
  :deep(.t-breadcrumb) {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }
  :deep(.t-breadcrumb__inner-text) {
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
  }
  :deep(.t-breadcrumb__separator) {
    color: #ffffff;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .breadcrumb-container {
    :deep(.t-breadcrumb) {
      padding: 0 16px;
    }
  }
}

@media (max-width: 480px) {
  .breadcrumb-container {
    :deep(.t-breadcrumb) {
      padding: 0 16px;
    }
  }
}
</style>
