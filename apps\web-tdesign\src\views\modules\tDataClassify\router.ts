import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '分级分类',
    },
    name: 'tDataClassify',
    path: '/tDataClassify',
    children: [
      {
        meta: {
          title: '分级分类编辑',
        },
        name: 'tDataClassifyIndex',
        path: '/tDataClassify/index',
        component: () =>
          import('#/views/modules/tDataClassify/index.vue'),
      },
      {
        meta: {
          title: '分级分类汇总',
        },
        name: 'tDataClassDetail',
        path: '/tDataClassify/classDetail',
        component: () =>
          import('#/views/modules/tDataClassify/classDetail.vue'),
      },
    ],
  },
];

export default routes;
