<script setup lang="tsx">
import {type FormProps, Loading} from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import { useVbenModal } from '@vben/common-ui';
import { Form, FormItem, Input, Select } from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { save } from '../api.ts';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
const status = ref([]);
const dataTypes = ref([]);
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
  name: [
    {
      required: true,
      message: '请输入模板名称',
    },
  ],
  dataType: [
    {
      required: true,
      message: '请选择数据类型',
    },
  ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {
      loading.value = false
    },
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      loading.value = false
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      loading.value = true
      reqRunner.save.run({ ...state.tagObj, ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});

onMounted(async () => {
  status.value = await getDictItems('IS_ONLINE');
  dataTypes.value = await getDictItems('DATA_TYPE');
});
const loading = ref(false)
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Loading :loading="loading" showOverlay preventScrollThrough indicator>
      <Form
        ref="form"
        :data="formData"
        :rules="FORM_RULES"
        class="w-full"
        label-align="right"
      >
        <div class="grid w-full grid-cols-1 gap-2">
          <FormItem label="模板名称" name="name">
            <Input v-model="formData.name" clearable placeholder="请输入内容" />
          </FormItem>
          <FormItem label="启用状态" name="status">
            <Select
              v-model="formData.isOnline"
              :options="status"
              clearable
              placeholder="请选择"
            />
          </FormItem>
          <FormItem label="模板类别" name="dataType">
            <Select
              v-model="formData.dataType"
              :options="dataTypes"
              clearable
              placeholder="请选择"
            />
          </FormItem>
        </div>
      </Form>
    </Loading>
  </Modal>
</template>

<style scoped></style>
