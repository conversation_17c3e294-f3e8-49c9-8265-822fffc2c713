// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/ocDepartment/listByPage', data);
}
export async function listByTree(data: any) {
  return requestClient.post<any[]>('/ocDepartment/listByTree', data);
}
export async function save(data: any) {
  return requestClient.post<any>('/ocDepartment/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/ocDepartment/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/ocDepartment/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/ocDepartment/getByIds/${data}`);
}
