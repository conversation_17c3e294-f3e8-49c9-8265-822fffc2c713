import type { Router } from 'vue-router';

import { mainAuthStoreConfig } from '#/preferences';
import { accessRoutes, coreRouteNames } from '#/router/routes';
import { useAuthStore } from '#/store';
import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';
import { startProgress, stopProgress } from '@vben/utils';
import { CrossStorageClient } from 'cross-storage';

import { generateAccess } from './access';

/**
 * 通用守卫配置
 * @param router
 */
function setupCommonGuard(router: Router) {
  // 记录已经加载的页面
  const loadedPaths = new Set<string>();

  router.beforeEach(async (to) => {
    to.meta.loaded = loadedPaths.has(to.path);

    // 页面加载进度条
    if (!to.meta.loaded && preferences.transition.progress) {
      startProgress();
    }
    return true;
  });

  router.afterEach((to) => {
    // 记录页面是否加载,如果已经加载，后续的页面切换动画等效果不在重复执行

    loadedPaths.add(to.path);

    // 关闭页面加载进度条
    if (preferences.transition.progress) {
      stopProgress();
    }
  });
}

/**
 * 其他接入守卫配置
 */
async function setupRouter(router: Router) {
  // 监听路由变化，如果路由发生变化，则重新生成路由
  router.beforeEach(async (to, from) => {
    const { accessibleMenus, accessibleRoutes } = await generateAccess({
      roles: [],
      router,
      // 则会在菜单中显示，但是访问会被重定向到403
      routes: accessRoutes,
    });

    const accessStore = useAccessStore();

    if (accessStore.isAccessChecked) {
      return true;
    }

    // 保存菜单信息和路由信息
    accessStore.setAccessMenus(accessibleMenus);
    accessStore.setAccessRoutes(accessibleRoutes);
    accessStore.setIsAccessChecked(true);

    const redirectPath = (from.query.redirect ?? to.fullPath) as string;

    return {
      ...router.resolve(decodeURIComponent(redirectPath)),
      replace: true,
    };
  });
}

/**
 * 权限访问守卫配置
 * @param router
 */
function setupAccessGuard(router: Router) {
  router.beforeEach(async (to, from) => {
    const accessStore = useAccessStore();
    const userStore = useUserStore();
    const authStore = useAuthStore();

    if (!import.meta.env.VITE_AUTH_SERVER) {
      console.log('当前服务为子服务，需要加载跨域存储');
      // 加载跨域存储
      const storage = new CrossStorageClient(
        mainAuthStoreConfig.url,
        mainAuthStoreConfig.config,
      );
      await storage.onConnect();
      const res = await storage.get('rgdc-web-5.5.3-dev-core-access');
      authStore.setCrossOriginStore(res);
      console.log('输出', authStore.getCrossOriginStore());
      // 如果子服务未获取到主服务的权限，则需要跳转到主服务进行登录
    }

    // 基本路由，这些路由不需要进入权限拦截
    if (coreRouteNames.includes(to.name as string)) {
      if (to.path === LOGIN_PATH && accessStore.accessToken) {
        return decodeURIComponent(
          (to.query?.redirect as string) || DEFAULT_HOME_PATH,
        );
      }
      return true;
    }

    // accessToken 检查
    if (!accessStore.accessToken) {
      // 明确声明忽略权限访问权限，则可以访问
      if (to.meta.ignoreAccess) {
        return true;
      }

      // 没有访问权限，跳转登录页面
      if (to.fullPath !== LOGIN_PATH) {
        return {
          path: LOGIN_PATH,
          // 如不需要，直接删除 query
          query: { redirect: encodeURIComponent(to.fullPath) },
          // 携带当前跳转的页面，登录后重新跳转该页面
          replace: true,
        };
      }
      return to;
    }

    // 是否已经生成过动态路由
    if (accessStore.isAccessChecked) {
      return true;
    }
    // 生成路由表
    // 当前登录用户拥有的角色标识列表
    const userInfo: any =
      userStore.userInfo || (await authStore.fetchUserInfo());
    const userRoles = userInfo.roles ?? [];

    // 生成菜单和路由
    const { accessibleMenus, accessibleRoutes } = await generateAccess({
      roles: userRoles,
      router,
      // 则会在菜单中显示，但是访问会被重定向到403
      routes: accessRoutes,
    });

    // 保存菜单信息和路由信息
    accessStore.setAccessMenus(accessibleMenus);
    accessStore.setAccessRoutes(accessibleRoutes);
    accessStore.setIsAccessChecked(true);
    const redirectPath = (from.query.redirect ?? to.fullPath) as string;

    return {
      ...router.resolve(decodeURIComponent(redirectPath)),
      replace: true,
    };
  });
}

/**
 * 项目守卫配置
 * @param router
 */
async function createRouterGuard(router: Router) {
  /** 通用 */
  setupCommonGuard(router);

  // 微前端接入
  // window?.__MICRO_APP_ENVIRONMENT__
  if (window?.__MICRO_APP_ENVIRONMENT__) {
    await setupRouter(router);
  } else {
    setupAccessGuard(router);
  }
  /** 权限访问 */
  // setupAccessGuard(router);
}

export { createRouterGuard };
