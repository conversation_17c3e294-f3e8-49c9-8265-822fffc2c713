<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>化工知识协同加工与管理平台</title>
    <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
    <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
    <link rel="stylesheet" href="../static/vue/theme/index.css">
    <script src="../static/vue/min/vue.min.js"></script>
    <script src="../static/vue/element-ui2.15.13_index.js"></script>
    <script src="../static/vue/axios0.26.0_axios.min.js"></script>
    <style>
        html,
        body {
            min-width: 100%;
        }

        .mon_warp {
            margin: 0px;
            width: 100%;
            background-size: cover;
        }

        .mon_body {
            display: none;
            width: 100%;
        }

        .el-menu-vertical-demo {
            height: 100%;
        }

        .el-card {
            margin-top: 20px;
        }

        .el-upload__tip {
            margin-top: 10px;
        }

        .clearfix:before,
        .clearfix:after {
            display: table;
            content: "";
        }

        .clearfix:after {
            clear: both;
        }

        .center {
            border: 1px solid #ccc;
            width: 100%;
            margin: 20px auto 20px;
            border-radius: 20px;
            padding: 12px;
            min-width: 1200px;
            display: flex;
            justify-content: space-between;
        }

        .upload-demo {
            width: 100%;
        }

        .el-upload-dragger {
            width: 660px;
            height: 250px;
            padding: 40px;
        }

        .el-upload__text {
            font-size: 16px;
            margin: 20px 0;
        }

        .el-icon-upload {
            font-size: 67px;
            margin: 20px 0;
        }

        .el-menu-item.is-active {
            background-color: #ecf5ff;
            color: #409EFF;
        }

        .el-menu-item {
            font-size: 14px;
            height: 56px;
            line-height: 56px;
        }

        .el-menu-item:hover {
            background-color: #ecf5ff;
        }

        .right {
            padding: 30px;
            border-radius: 20px;
            background: #f6f6f6;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }

        .form-row .el-form-item {
            flex: 1;
        }

        .ar_btn_upload_list {
            width: calc(100% - 210px) !important;
            float: right;
            margin-top: 10px;
        }

        .ar_btn_upload_list p {
            line-height: 20px;
        }

        .ar_btn_upload_list a {
            color: #012d74;
        }

        .ar_btn_upload_list a:hover {
            color: #9b46cc;
            text-decoration: revert;
        }

        .ar_btn_upload_list .el-progress {
            width: 200px;
            display: inline-block;
        }

        .ar_btn_upload_list .upload_error {
            color: #f44336;
        }

        .ar_btn_upload_list .upload_success {
            color: #67c23a;
        }

        .upload_files p {
            line-height: 30px;
        }

        .down a {
            cursor: pointer;
            color: #00bcd4;
        }
    </style>
</head>

<body>
    <div class="header_app" id="header_app"></div>

    <div class="mon_warp clearfix" id="app">
        <div class="mon_body clearfix">
            <el-row :gutter="20">
                <!-- 左侧菜单 -->
                <el-col :span="3">
                    <div style="padding-top:20px">
                        <el-menu :default-active="menuActive"  class="el-menu-vertical-demo" 
             @select="handleMenuSelect"
            style="padding-top:20px;"
              active-text-color="#409EFF">
                 <el-menu-item index="cleanTool">
                   <el-tooltip content="数据汇聚与清洗工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据汇聚与清洗工具
                  </span>
                </el-tooltip>
                  </el-menu-item>
              <el-menu-item index="classiFication">

                 <el-tooltip content="数据整编与分类工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据整编与分类工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="home">
                <el-tooltip content="全文多模态解析重组工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    全文多模态解析重组工具
                  </span>
                </el-tooltip>
              </el-menu-item><el-menu-item index="relationship">
                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    知识对象及关系挖掘工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>

            </el-menu>
                    </div>
                </el-col>

                <!-- 右侧内容 -->
                <el-col :span="21">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <h2>数据汇聚与清洗工具</h2>
                        </div>
                        <div>
                            <h2 style="font-size: 16px;">单条数据新增</h2>
                            <p style="font-size: 14px;color: #737373;">

                                <span
                                    style="color: #929db0;">任务名称：</span><em v-html="task_name"></em>
                               
                            </p>

                            <div class="center">
                                <div class="left" :style="{width:isShowPdf  && pdfUrl?'48%':0}" v-if="isShowPdf && pdfUrl">

                                    <iframe :src="pdfUrl"
                                        width="100%" height="600px" style="border: none;">

                                    </iframe>
                                </div>
                                <div class="right" :style="{width:isShowPdf && pdfUrl ?'48%':'100%'}">
                                    <el-form :model="form" label-width="100px" style="width: 100%;">
                                        <h3 style="color: #2b6cb0;">data</h3>

                                        <div class="form-row">
                                            <el-form-item label="article_title">
                                                <el-input v-model="form.article_title"></el-input>
                                            </el-form-item>
                                            <el-form-item label="doi">
                                                <el-input v-model="form.doi"></el-input>
                                            </el-form-item>
                                        </div>

                                        <div class="form-row">
                                            <el-form-item label="publication_year">
                                                <el-input v-model="form.publication_year"></el-input>
                                            </el-form-item>
                                            <el-form-item label="source_title">
                                                <el-input v-model="form.source_title"></el-input>
                                            </el-form-item>
                                        </div>

                                        <!-- <div class="form-row">
                                            <el-form-item label="file_name">
                                                <el-input v-model="form.file_name"></el-input>
                                            </el-form-item>
                                        </div> -->
                                        <h3 style="color: #2b6cb0;">其他</h3>
                                        <div class="form-row">
                                            <el-form-item label="pdf文件">
                                                <div class="upload_files">
                                                    <el-upload class="upload-demo" :show-file-list="false"
                                                        action="/query_file_json"
                                                        :before-upload="($event) =>{beforeAvatarUploadFile($event, 'need_publish_trailer')}"
                                                        multiple>
                                                        <el-button type="primary">点击上传</el-button>

                                                    </el-upload>

                                                    <p v-for="(item, index) in fileData" class="down">
                                                        <!-- <span v-html="item.uploadDate" title="上传时间"></span>&nbsp;&nbsp; -->
                                                        <span v-html="item.fileName" title="文件名"></span>
                                                        (<em v-html="item.fileSizeLabel"></em>)&nbsp;&nbsp;
                                                        <!-- <span>2024-12-16 15:48:16</span> -->
                                                        <span>
                                                            <em v-if="item.upload_status == -1"
                                                                class="upload_error">上传失败&nbsp;&nbsp;</em>
                                                            <em v-if="item.upload_status == 1"
                                                                class="upload_success">上传成功&nbsp;&nbsp;</em>
                                                            <em v-if="item.upload_status == 0"
                                                                v-text="i18n.new_33">正在上传：</em>
                                                            <el-progress v-if="item.upload_status == 0"
                                                                :percentage="item.upload_progress"
                                                                :stroke-width="10"></el-progress>
                                                        </span>

                                                        <a href="JavaScript: void(0)"
                                                            :title="'下载(' + item.fileName + ')'"><i
                                                                icon="el-icon-download"></i>下载</a>&nbsp;&nbsp;
                                                        <a href="JavaScript: void(0)"
                                                            @click="deleteFileList(index, item)"
                                                            :title="'删除(' + item.fileName + ')'"><i
                                                                icon="el-icon-delete"></i>删除</a>
                                                    </p>
                                                </div>
                                            </el-form-item>

                                        </div>
                                    </el-form>

                                </div>
                            </div>
                        </div>
                        <div style="text-align: right;width: 100%;">
                            <el-button>取消</el-button>
                            <el-button type="primary" @click="addSubmit" v-if="isEdit == 0">新增</el-button>
                            <el-button type="primary" v-else @click="submit">保存</el-button>
                        </div>

                    </el-card>
                </el-col>
            </el-row>


        </div>
    </div>

    <div id="dataid" data="{{session.user}}" style="display:none"></div>
    <div class="mon_footer"></div>

    <script src="../static/js/jquery.min.js"></script>
    <script src="../static/js/monitor.js"></script>

    <script>
        let _this = this;
        var $_GET = (function () {
            var url = window.document.location.href.toString(); //获取的完整url
            var u = url.split("?");
            if (typeof (u[1]) == "string") {
                u = u[1].split("&");
                var get = {};
                for (var i in u) {
                    var j = u[i].split("=");
                    get[j[0]] = j[1];
                }
                return get;
            } else {
                return {};
            }
        })();
        const vm = new Vue({
            el: '#app',
            data: {
                activeIndex: '3',
                isShowPdf: false,
                menuActive: 'cleanTool',
                form: {
                    article_title: '',
                    source_title: "",
                    publication_year: "",
                    doi: "",
                    // filepath:"",
                    pdf_path: "",
                    table_name:"",
                    // task_id:"",
                },
                isEdit: 0,
                fileData: [],
                task_name: $_GET["task_name"] || "",
                pdfUrl:''
            },
            mounted() {
                $('.mon_body').css({ 'display': 'revert' });
                if ($_GET["table_name"]) {
                    this.getTableData(); // 获取数据
                }
                if ($_GET["isEdit"]) {
                    this.isEdit = $_GET["isEdit"]
                } 
                 if ($_GET["table_name"]) {
                    this.form.table_name = $_GET["table_name"]
                }

                if ($_GET["isEdit"] == 1) {
                    this.isShowPdf = true
                } else {
                    this.isShowPdf = false
                }

            },
            methods: {
                //删除文件
                deleteFileList(index, row) {
                    let that = this;
                    this.$confirm('删除(' + row.fileName + '), 是否继续?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        that.fileData.splice(index, 1);
                    }).catch(() => {
                    });
                },
                beforeAvatarUploadFile(file, type = '') {
                    let that = this;
                    const isLt2M = file.size / 1024 / 1024 / 1024 < 1;
                    if (!isLt2M) {
                        this.$message.error('上传文件大小不能超过 1GB!');
                    }
                    let fileSizeBool = true;
                    let fileSize = this.fileData.length;
                    if (fileSize >= 100) {
                        fileSizeBool = false;
                        this.$message.error('上传文件数量不能超过 100个!');
                    }
                 
                    if (isLt2M && fileSizeBool) {

                        // 加载状态
                        // that.uploadFileLoading(type, true, file.name)

                        //阻止元素发生默认的行为
                        event.preventDefault();
                        let formData = new FormData();
                        formData.append('pdf_file', file);
                        console.log(file);
                       
                        axios.post(server_url + '/gather_api/upload_pdf',
                            formData, { 'Content-type': 'multipart/form-data' })
                            .then(function (response) {
                                // 一秒后关闭进度条
                                let data = response.data;
                                // that.uploadFileLoading(type, false, file.name)
                                if (data.code == 0) {
                                    that.$message.success('上传成功！')
                                    that.form.pdf_path = data.file_path
                                  
                                    let up_obj = {
                                      
                                        fileName: file.name,
                                       
                                        fileSizeLabel: _this.fileBytesToSize(file.size),
                                        fileInfo: '',
            
                                        upload_status: 1,


                                    }
                                    that.$set(that.fileData, that.fileData.length, up_obj);
                                    console.log(that.fileData)

                                } else {
                                    that.$message({
                                        message: '上传失败了',
                                        type: 'error',
                                        duration: 3000,
                                        center: true
                                    });
                                }
                            }).catch(function (error) {
                                that.$message({
                                    message: '上传失败了!!!',
                                    type: 'error',
                                    duration: 3000,
                                    center: true
                                });
                                console.log(error);
                            });
                    }
                    return isLt2M && fileSizeBool;
                },
                //新增保存
                addSubmit() { 
                    
                      let that = this
                    axios.post(server_url + '/gather_api/add_new_data',
                        JSON.stringify(that.form), {
                        headers: { 'Content-Type': "application/json;charset=utf-8" },
                        timeout: 60000,
                    }).then(function (res) {
                        console.log(res.data);
                        if(res.data.code == 0){
                            that.$message({
                                message: '新增成功',
                                type: 'success',
                                duration: 3000,
                                center: true
                            });
                             // 600ms 后跳转到列表页
                        setTimeout(() => {
                            window.location.href = `./cleanTask?table_name=${that.form.table_name}`;
                        }, 1000);
                       }
                    })
                },
                //修改保存
                submit() {
                    let that = this
                    that.form.data_id = that.form.id
                    that.form.table_name = $_GET["table_name"]
                    axios.post(server_url + '/gather_api/save_data',
                        JSON.stringify(that.form), {
                        headers: { 'Content-Type': "application/json;charset=utf-8" },
                        timeout: 60000,
                    }).then(function (res) {
                        console.log(res.data);
                       if(res.data.code == 0){
                            that.$message({
                                message: '修改成功',
                                type: 'success',
                                duration: 3000,
                                center: true
                            });
                             // 600ms 后跳转到列表页
                        setTimeout(() => {
                            window.location.href = `./cleanTask?table_name=${that.form.table_name}`;
                        }, 1000);
                       }
                    })
                },
                getTableData() {
                    let that = this
                    let param = {
                        "data_id": $_GET["data_id"],
                        table_name: $_GET["table_name"]
                    };
                    axios.post(server_url + '/gather_api/preview_data_pdf',
                        JSON.stringify(param), {
                        headers: { 'Content-Type': "application/json;charset=utf-8" },
                        timeout: 60000,
                    }).then(function (res) {
                        if (res.data.code == 0) {
                            that.$nextTick(() => {
                                that.form = res.data.data

                                
                                that.pdfUrl = _this.getPreviewFileUrl(res.data.data.orig_pdf_name)

                                console.log(url);
                            })
                        }
                    })
                },
  handleMenuSelect(index) {
    if (index === 'cleanTool') {
      window.location.href = '/cleanTool'; // 跳转到对应的页面
    } else if (index === 'classiFication') {
      window.location.href = '/classiFication';
    } else if (index === 'relationship') {
      window.location.href = '/relationship';
    } else if (index === 'home') {
      window.location.href = '/'; // 跳转到首页
    } else if (index === 'qualitycontrol') {
      window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
    } 
    this.menuActive = index; // 更新高亮状态
  },




            }
        });
    </script>
</body>

</html>