<script lang="ts" setup>
import {
  ComponentDividerHorizontalIcon,
  ComponentDividerVerticalIcon,
  DeleteIcon,
  PageFirstIcon,
  PageLastIcon,
  PlusIcon,
  RemoveIcon,
} from 'tdesign-icons-vue-next';
import { Input, Link, Space, Table } from 'tdesign-vue-next';
import { onMounted, ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {
        columns: [
          {
            colKey: 'col-1',
            title: '列1',
          },
          {
            colKey: 'col-2',
            title: '列2',
          },
          {
            colKey: 'op',
            title: '操作',
            fixed: 'right',
            width: 150,
          },
        ],
        data: [],
      };
    },
  },
});

const emits = defineEmits(['update:modelValue']);

const modelValue: any = ref({ ...props.modelValue });

watch(
  () => modelValue.value,
  (newVal) => {
    emits('update:modelValue', newVal);
  },
  { deep: true },
);

const editGrid: any = ref(new Map());
const overEdit = (col: any, row: any) => {
  // editGrid.value.set(getXYKey(col, row), false);
  editGrid.value.clear();
};
const getXYKey = (col: any, row: any) => {
  let xyKey = null;
  xyKey = row === undefined ? `${col}-col` : `${col}-${row}`;
  return xyKey;
};
const editClick = (col: any, row: any) => {
  editGrid.value.set(getXYKey(col, row), true);
};
const columnsEditOver = (col: any, row: any) => {
  editGrid.value.set(getXYKey(col, row), false);
  // 编辑后需要调整数据
};

const add = (row: any) => {
  // 在row下添加一行数据
  modelValue.value.data.splice(row, 0, {});
};

const del = (row: any) => {
  // 将此行数据在list中删除
  modelValue.value.data.splice(row, 1);
};

const delCol = (col: any) => {
  // 需要删除Data内全部数据
  const temp = modelValue.value.data.map((item: any) => {
    delete item[`${modelValue.value.columns[col].colKey}`];

    return item;
  });
  modelValue.value.data = temp;
  modelValue.value.columns.splice(col, 1);

  editGrid.value.clear();
};

const beforeAddCol = (col: any) => {
  let max: any = 0;
  for (let i = 0; i < modelValue.value.columns.length; i++) {
    const col = modelValue.value.columns[i];
    if (col.colKey) {
      const spl = col.colKey.split('-');
      if (spl.length === 2) {
        max =
          Number.parseInt(spl[1]) > Number.parseInt(max)
            ? Number.parseInt(spl[1])
            : max;
      }
    }
  }

  modelValue.value.columns.splice(col, 0, {
    colKey: `col-${max + 1}`,
    title: `列${max + 1}`,
  });
  editGrid.value.clear();
};

const afterAddCol = (col: any) => {
  let max: any = 0;
  for (let i = 0; i < modelValue.value.columns.length; i++) {
    const col = modelValue.value.columns[i];
    if (col.colKey) {
      const spl = col.colKey.split('-');
      if (spl.length === 2) {
        max =
          Number.parseInt(spl[1]) > Number.parseInt(max)
            ? Number.parseInt(spl[1])
            : max;
      }
    }
  }

  modelValue.value.columns.splice(col + 1, 0, {
    colKey: `col-${max + 1}`,
    title: `列${max + 1}`,
  });
  editGrid.value.clear();
};

const addCol = () => {
  // 查询columns中colKey最大数
  let max: any = 0;
  for (let i = 0; i < modelValue.value.columns.length; i++) {
    const col = modelValue.value.columns[i];
    if (col.colKey) {
      const spl = col.colKey.split('-');
      if (spl.length === 2) {
        max =
          Number.parseInt(spl[1]) > Number.parseInt(max)
            ? Number.parseInt(spl[1])
            : max;
      }
    }
  }

  modelValue.value.columns.splice(-1, 0, {
    colKey: `col-${max + 1}`,
    title: `列${max + 1}`,
  });
};

const addRow = () => {
  // 将colIndex传入
  console.log('addRow');
  modelValue.value.data.push({});
};

const isEdit = (col: any, row: any) => {
  return editGrid.value.get(getXYKey(col, row));
};

const get = () => {
  return modelValue.value;
};

onMounted(() => {
  // 默认添加一列
  emits('update:modelValue', modelValue.value);
});

defineExpose({
  get,
});
</script>

<template>
  <Table :columns="modelValue.columns" :data="modelValue.data">
    <!--    <template #topContent>-->
    <!--      <div class="flex min-h-[18px] w-full items-center justify-center p-4">-->
    <!--        <Link v-if="!editNameFlag" @click="editName">-->
    <!--          {{ headerName }}-->
    <!--        </Link>-->
    <!--        <Link v-if="!editNameFlag && !headerName" @click="editName">-->
    <!--          {{ `点击编辑标题` }}-->
    <!--        </Link>-->
    <!--        <Input-->
    <!--          v-if="editNameFlag"-->
    <!--          v-model="headerName"-->
    <!--          :auto-width="true"-->
    <!--          placeholder="请输入标题"-->
    <!--          @blur="editName"-->
    <!--          @enter="editName"-->
    <!--        />-->
    <!--      </div>-->
    <!--    </template>-->
    <template
      v-for="key in modelValue.columns"
      :key="key.colKey"
      #[key.title]="{ row, col, rowIndex, colIndex }"
    >
      <!-- 自定义列头 -->
      <div v-if="key.colKey && !row">
        <div v-if="key.colKey !== 'op'">
          <Link
            v-if="!isEdit(colIndex, rowIndex) && col.title"
            @click="editClick(colIndex, rowIndex)"
          >
            {{ col.title }}
          </Link>
          <div
            v-if="!isEdit(colIndex, rowIndex) && !col.title"
            class="min-h-[18px] w-full cursor-pointer"
            @click="editClick(colIndex, rowIndex)"
          ></div>
          <div class="flex items-center">
            <Input
              v-show="isEdit(colIndex, rowIndex)"
              v-model="modelValue.columns[colIndex].title"
              @blur="columnsEditOver(colIndex, rowIndex)"
              @enter="columnsEditOver(colIndex, rowIndex)"
            >
              <template #suffixIcon>
                <PageFirstIcon
                  v-if="isEdit(colIndex, rowIndex)"
                  @click="beforeAddCol(colIndex)"
                />
                <DeleteIcon
                  v-if="isEdit(colIndex, rowIndex)"
                  @click="delCol(colIndex)"
                />
                <PageLastIcon
                  v-if="isEdit(colIndex, rowIndex)"
                  @click="afterAddCol(colIndex)"
                />
              </template>
            </Input>
          </div>
        </div>
        <div v-if="key.colKey === 'op'">
          <Space size="small">
            <span @click="addCol">
              <ComponentDividerHorizontalIcon />
            </span>
            <span @click="addRow">
              <ComponentDividerVerticalIcon />
            </span>
          </Space>
          <!-- <div @click="get">获取当前列配置</div> -->
        </div>
      </div>
    </template>

    <!-- 自定义数据 -->
    <template
      v-for="key in modelValue.columns"
      :key="key.colKey"
      #[key.colKey]="{ row, col, rowIndex, colIndex }"
    >
      <div v-if="key.colKey !== 'op'" class="w-full cursor-pointer">
        <Input
          v-if="isEdit(colIndex, rowIndex)"
          v-model="row[col.colKey]"
          :autofocus="true"
          @blur="overEdit(colIndex, rowIndex)"
          @enter="overEdit(colIndex, rowIndex)"
        />
        <div
          v-if="!isEdit(colIndex, rowIndex)"
          class="item-center flex min-h-[18px] w-full cursor-pointer"
          @click="editClick(colIndex, rowIndex)"
        >
          <span class="height-auto inline">
            {{ row[col.colKey] }}
          </span>
        </div>
      </div>

      <div v-if="key.colKey === 'op'">
        <Space>
          <span @click="add(rowIndex)">
            <PlusIcon />
          </span>
          <span @click="del(rowIndex)">
            <RemoveIcon />
          </span>
        </Space>
      </div>
    </template>
  </Table>
</template>
