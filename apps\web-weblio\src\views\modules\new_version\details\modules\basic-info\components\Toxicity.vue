<script setup lang="ts">
import {
  Loading,
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref, watch } from 'vue';

import {
  getToxicityInfoData
} from '../api';
import { useSearchStore } from '#/store/search';

// 定义 props
const props = defineProps({
  toxicityData: {
    type: Object,
    default: {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

interface ToxicityInfoData {
  toxicitySummary: string,
  carcinogenClassification: string,
  healthEffects: string,
  exposureRoutes: string,
  symptoms: string,
  adverseEffects: string,
  acuteEffects: string,
  toxicityData: string,
  treatment: string,
  antidoteAndEmergencyTreatment: string
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: false,

});

// 定义加载状态，用于控制页面加载动画显示
const loading = ref(props.loading);

// 初始化毒性信息数据对象，字段命名与接口定义保持一致，确保类型安全
const data = ref<ToxicityInfoData>({
  toxicitySummary: '',
  carcinogenClassification: '',
  healthEffects: '',
  exposureRoutes: '',
  symptoms: '',
  adverseEffects: '',
  acuteEffects: '',
  toxicityData: '',
  treatment: '',
  antidoteAndEmergencyTreatment: ''
})

// 监听 props 变化，更新数据
watch(() => props.toxicityData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    data.value = {
      toxicitySummary: newData.toxicitySummary || newData.summary || '',
      carcinogenClassification: newData.carcinogenClassification || newData.classification || '',
      healthEffects: newData.healthEffects || newData.effects || '',
      exposureRoutes: newData.exposureRoutes || newData.routes || '',
      symptoms: newData.symptoms || '',
      adverseEffects: newData.adverseEffects || newData.adverse || '',
      acuteEffects: newData.acuteEffects || newData.acute || '',
      toxicityData: newData.toxicityData || newData.data || '',
      treatment: newData.treatment || '',
      antidoteAndEmergencyTreatment: newData.antidoteAndEmergencyTreatment || newData.antidote || ''
    };
  }
}, { immediate: true, deep: true });

// 监听 loading 状态变化
watch(() => props.loading, (newLoading) => {
  loading.value = newLoading;
  console.log("loading", loading.value, 2222);
});

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

onMounted(async () => {
  // 如果父组件没有传递数据，则使用原有的逻辑
  // if (!props.toxicityData || Object.keys(props.toxicityData).length === 0) {
  //   try {
  //     // 调用毒性信息查询API请求方法，传入 inchikey 参数
  //     const response = await getToxicityInfoData({ inchikey: state.detailItem.baseCode });
  //     data.value = response;
  //   } catch (error) {
  //     console.error('获取毒性信息失败:', error);
  //   } finally {
  //     // 关闭加载状态
  //     loading.value = false;
  //   }
  // }
});

const fieldLabels = {
  toxicitySummary: '毒性总结：',
  carcinogenClassification: '毒性分级：',
  healthEffects: '健康影响：',
  exposureRoutes: '接触途径：',
  symptoms: '症状：',
  adverseEffects: '不良反应：',
  acuteEffects: '急性效应：',
  toxicityData: '毒性数据：',
  treatment: '治疗：',
  antidoteAndEmergencyTreatment: '解毒剂及急救处理：'
};
</script>

<template>
  <div class="toxicity-info">
    <!-- 加载状态提示 -->
    <div v-if="loading" class="loading-container">
      <Loading />
    </div>
    <!-- 主体内容展示 -->
    <div v-else class="content">
      <div class="info-items">
        <div v-for="(label, key) in fieldLabels" :key="key">
          <!-- 如果 data[key] 存在，则显示对应信息 -->
          <div v-if="data[key]" class="info-item">
            <!-- 字段标签 -->
            <span class="label">{{ label }}</span>
            <!-- 字段值 -->
            <span class="value">
              {{ data[key] }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.toxicity-info {
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  font-size: 24px;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.structure-image {
  width: 100%;
  max-width: 400px;
  margin-bottom: 20px;
}

.structure-image img {
  width: 100%;
  height: auto;
}

.info-items {
  width: 80%;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 1px dashed #ddd;
}

.label {
  font-weight: bold;
  min-width: 240px;
  color: #555;
}

.value {
  flex-grow: 1;
  color: #333;
  word-break: break-all;
}

.smiles-link {
  color: #3498db;
  text-decoration: underline;
  cursor: pointer;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
