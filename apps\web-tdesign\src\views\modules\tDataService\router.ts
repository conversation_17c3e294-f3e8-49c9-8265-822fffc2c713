import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据工具管理',
    },
    name: 'tDataService',
    path: '/tDataService',
    children: [
      {
        meta: {
          title: '数据工具管理编辑',
        },
        name: 'tDataServiceIndex',
        path: '/tDataService/index',
        component: () =>
          import('#/views/modules/tDataService/index.vue'),
      },
    ],
  },
];

export default routes;
