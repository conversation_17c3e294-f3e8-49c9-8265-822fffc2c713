import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-add-chart',
      keepAlive: true,
      title: '组件示例',
    },
    name: 'Components',
    path: '/components',
    children: [
      {
        meta: {
          title: '自定义表格',
        },
        name: 'DynamicTable',
        path: '/dynamic-table',
        component: () => import('#/views/modules/components-demo/index.vue'),
      },
      {
        meta: {
          title: '在线表格',
        },
        name: 'LuckySheet',
        path: '/lucky-sheet',
        component: () =>
          import('#/views/modules/components-demo/LuckySheet.vue'),
      },
      {
        meta: {
          title: '富文本编辑器',
        },
        name: 'UEditor',
        path: '/ueditor-index',
        component: () => import('#/views/modules/components-demo/UEditor.vue'),
      },
      {
        meta: {
          title: 'Excel导入导出',
        },
        name: 'Excel',
        path: '/excel',
        component: () => import('#/views/modules/components-demo/Excel.vue'),
      },
      {
        meta: {
          title: '上传下载示例',
        },
        name: 'UploadDownload',
        path: '/uploadDownload',
        component: () =>
          import('#/views/modules/components-demo/UploadDownload.vue'),
      },
      {
        meta: {
          title: 'Ketcher示例',
        },
        name: 'Ketcher',
        path: '/Ketcher',
        component: () =>
          import('#/views/modules/components-demo/KetcherDemo.vue'),
      },
      {
        meta: {
          title: 'Form示例',
        },
        name: 'FormReanderDemo',
        path: '/FormReanderDemo',
        component: () =>
          import('#/views/modules/components-demo/FormReanderDemo.vue'),
      },
      {
        meta: {
          title: '表单设计器',
        },
        name: 'formEditer',
        path: '/formEditer/demo',
        component: () => import('#/views/modules/formEditer/index.vue'),
      },
    ],
  },
];

export default routes;
