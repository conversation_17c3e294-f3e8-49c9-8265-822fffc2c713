<script setup lang="tsx">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import EditForm from './components/EditForm.vue';
import ViewForm from './components/ViewForm.vue';
import IndexTable from './components/IndexTable.vue';

const viewFormRef = ref();
const editFormRef = ref();
const tableRef = ref();
</script>

<template>
  <Page title="反馈管理">
    <ViewForm ref="viewFormRef" :out-ref="tableRef" />
    <EditForm ref="editFormRef" :out-ref="tableRef" />
    <IndexTable ref="tableRef" :edit-form-ref="editFormRef" :view-form-ref="viewFormRef"/>
  </Page>
</template>

<style scoped></style>
