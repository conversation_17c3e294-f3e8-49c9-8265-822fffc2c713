<script setup>
import { CloseIcon, EditIcon, UserAddIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  Divider,
  Drawer,
  Form,
  FormItem,
  Input,
  InputNumber,
  Option,
  Radio,
  RadioGroup,
  Select,
  Space,
  Tag,
} from 'tdesign-vue-next';
</script>
<script>
import addNode from './addNode.vue';

export default {
  components: {
    AddNode: addNode,
  },
  inject: ['select'],
  props: {
    modelValue: { type: Object, default: () => {} },
  },
  data() {
    return {
      nodeConfig: {},
      drawer: false,
      isEditTitle: false,
      form: {},
    };
  },
  watch: {
    modelValue() {
      this.nodeConfig = this.modelValue;
    },
  },
  mounted() {
    this.nodeConfig = this.modelValue;
  },
  methods: {
    show() {
      this.form = {};
      this.form = JSON.parse(JSON.stringify(this.nodeConfig));
      this.drawer = true;
    },
    editTitle() {
      this.isEditTitle = true;
      this.$nextTick(() => {
        this.$refs.nodeTitle.focus();
      });
    },
    saveTitle() {
      this.isEditTitle = false;
    },
    save() {
      if (this.form.nodeAssigneeList === undefined) {
        this.form.nodeAssigneeList = [];
      }
      this.$emit('update:modelValue', this.form);
      this.drawer = false;
    },
    delNode() {
      this.$emit('update:modelValue', this.nodeConfig.childNode);
    },
    delUser(index) {
      this.form.nodeAssigneeList.splice(index, 1);
    },
    delRole(index) {
      this.form.nodeAssigneeList.splice(index, 1);
    },
    selectHandle(type, data) {
      this.select(type, data);
    },
    changeSetType() {
      this.form.nodeAssigneeList = [];
    },
    toText(nodeConfig) {
      switch (nodeConfig.setType) {
        case 1: {
          if (
            nodeConfig.nodeAssigneeList &&
            nodeConfig.nodeAssigneeList.length > 0
          ) {
            const users = nodeConfig.nodeAssigneeList
              .map((item) => item.name)
              .join('、');
            return users;
          } else {
            return false;
          }
        }
        case 3: {
          if (
            nodeConfig.nodeAssigneeList &&
            nodeConfig.nodeAssigneeList.length > 0
          ) {
            const roles = nodeConfig.nodeAssigneeList
              .map((item) => item.name)
              .join('、');
            return `角色-${roles}`;
          } else {
            return false;
          }
        }
        case 4: {
          return '发起人自选';
        }
        case 5: {
          return '发起人自己';
        }
        // No default
      }
    },
  },
};
</script>
<template>
  <div class="node-wrap">
    <div class="node-wrap-box" @click="show">
      <div class="title" style="background: #ff943e">
        <UserAddIcon class="icon" />
        <span>{{ nodeConfig.nodeName }}</span>
        <CloseIcon class="close" @click="delNode()" />
      </div>
      <div class="content">
        <span v-if="toText(nodeConfig)">{{ toText(nodeConfig) }}</span>
        <span v-else class="placeholder">请选择</span>
      </div>
    </div>
    <AddNode v-model="nodeConfig.childNode" />
    <Drawer
      v-model:visible="drawer"
      :size="500"
      attach="body"
      title="审批人设置"
    >
      <template #header>
        <div class="node-wrap-drawer__title">
          <label v-if="!isEditTitle" @click="editTitle"
            >{{ form.nodeName }}
            <EditIcon />
          </label>
          <Input
            v-if="isEditTitle"
            v-model="form.nodeName"
            clearable
            @blur="saveTitle"
            @enter="saveTitle"
          />
        </div>
      </template>

      <div style="padding: 0 20px 20px">
        <Form label-align="top">
          <FormItem label="审批人员类型">
            <Select v-model="form.setType" @change="changeSetType">
              <Option :value="1" label="指定成员" />
              <Option :value="3" label="角色" />
              <Option :value="4" label="发起人自选" />
              <Option :value="5" label="发起人自己" />
            </Select>
          </FormItem>

          <FormItem v-if="form.setType === 1" label="选择成员">
            <Button
              type="primary"
              @click="selectHandle(1, form.nodeAssigneeList)"
            >
              <template #icon>
                <UserAddIcon />
              </template>
              选择人员
            </Button>
          </FormItem>

          <div v-if="form.setType === 1">
            <div class="tags-list">
              <Space>
                <Tag
                  v-for="(user, index) in form.nodeAssigneeList"
                  :key="user.id"
                  closable
                  @close="delUser(index)"
                >
                  {{ user.name }}
                </Tag>
              </Space>
            </div>
          </div>

          <FormItem v-if="form.setType === 3" label="选择角色">
            <Button
              type="primary"
              @click="selectHandle(2, form.nodeAssigneeList)"
            >
              <template #icon>
                <UserAddIcon />
              </template>
              选择角色
            </Button>
          </FormItem>
          <div v-if="form.setType === 4">
            <div class="tags-list">
              <Space>
                <Tag
                  v-for="(role, index) in form.nodeAssigneeList"
                  :key="role.id"
                  closable
                  type="info"
                  @close="delRole(index)"
                >
                  {{ role.name }}
                </Tag>
              </Space>
            </div>
          </div>
          <FormItem v-if="form.setType === 5" label="发起人自选">
            <RadioGroup v-model="form.selectMode">
              <Radio :value="1">自选一个人</Radio>
              <Radio :value="2">自选多个人</Radio>
            </RadioGroup>
          </FormItem>

          <Divider />
          <FormItem label="">
            <Checkbox v-model="form.termAuto" label="超时自动审批" />
          </FormItem>
          <template v-if="form.termAuto">
            <FormItem label="审批期限（为 0 则不生效）">
              <InputNumber v-model="form.term" :min="0" /> 小时
            </FormItem>
            <FormItem label="审批期限超时后执行">
              <RadioGroup v-model="form.termMode">
                <Radio :value="0">自动通过</Radio>
                <Radio :value="1">自动拒绝</Radio>
              </RadioGroup>
            </FormItem>
          </template>
          <Divider />
          <FormItem label="多人审批时审批方式">
            <RadioGroup v-model="form.examineMode">
              <p style="width: 100%">
                <Radio :value="1">按顺序依次审批</Radio>
              </p>
              <p style="width: 100%">
                <Radio :value="2">会签 (可同时审批，每个人必须审批通过)</Radio>
              </p>
              <p style="width: 100%">
                <Radio :value="3">或签 (有一人审批通过即可)</Radio>
              </p>
            </RadioGroup>
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button type="primary" @click="save">保存</Button>
        <Button @click="drawer = false">取消</Button>
      </template>
    </Drawer>
  </div>
</template>
