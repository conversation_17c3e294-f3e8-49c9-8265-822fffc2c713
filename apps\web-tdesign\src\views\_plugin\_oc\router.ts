import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '组织机构',
    },
    name: 'oc',
    path: '/oc',
    children: [
      {
        meta: {
          title: '用户信息',
        },
        name: '/oc/user',
        path: '/oc/user',
        component: () => import('#/views/_plugin/_oc/user/index.vue'),
      },
      {
        meta: {
          title: '部门信息',
        },
        name: '/oc/ocDepartmentIndex',
        path: '/oc/ocDepartment/index',
        component: () => import('#/views/_plugin/_oc/ocDepartment/index.vue'),
      },
    ],
  },
];

export default routes;
