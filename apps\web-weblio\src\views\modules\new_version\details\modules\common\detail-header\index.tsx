import type { PropType } from 'vue';

import { ChevronLeftIcon } from 'tdesign-icons-vue-next';
import { defineComponent } from 'vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'DetailHeader',
  props: {
    mode: {
      type: String as PropType<'simple' | 'slot'>,
      default: 'simple',
    },
    // 移除background相关props
  },
  emits: ['back'],
  setup(props, { slots, emit }) {
    const router = useRouter();
    const onBack = () => {
      router.back();
      emit('back');
    };

    return () => (
      <div
        class="header-container"
        style={{
          background: '#ffffff',
          marginBottom: '16px',
          borderBottom: '1px solid #e8e8e8',
        }}
      >
        <div class="header-content">
          {/* 返回按钮 */}
          <button
            class="header-back-btn"
            onClick={onBack}
            style={{
              display: 'flex',
              alignItems: 'center',
              color: '#666',
              padding: '10px 16px',
            }}
          >
            <ChevronLeftIcon
              style={{ fontSize: '20px', color: '#666', marginRight: '4px' }}
            />
            <span style={{ fontSize: '16px', color: '#666', whiteSpace: 'nowrap' }}>返回</span>
          </button>
          {props.mode === 'slot' ? (
            <>
              {/* 左插槽 */}
              <div class="header-left">{slots.left?.()}</div>
              {/* 右插槽 */}
              <div class="header-right">{slots.right?.()}</div>
            </>
          ) : null}
        </div>
      </div>
    );
  },
});

// 建议配合如下样式（可放到同目录index.module.less或全局样式中）：
// .header-container { position: relative; width: 100%; height: 56px; background: #083786; margin-bottom: 16px; }
// .header-content { display: flex; align-items: center; height: 100%; }
// .header-back-btn { margin-right: 16px; background: none; border: none; cursor: pointer; padding: 10px 16px; }
// .header-left { flex: 1; }
// .header-right { flex: 1; text-align: right; }
