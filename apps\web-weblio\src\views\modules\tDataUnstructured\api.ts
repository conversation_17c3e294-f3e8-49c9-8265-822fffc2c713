// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataUnstructured/listByPage', data);
}
export async function editItemListByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tSysAttach/listByPage', data);
}
export async function editItemListByPageImage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tSysAttach/listByPageImage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataUnstructured/save', data);
}

export async function saveAttach(data: any) {
  return requestClient.post<any>('/rgdc-submit/tSysAttach/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tDataUnstructured/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/tDataUnstructured/getOne/${data}`);
}
export async function getOneByFileCode(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataUnstructured/getOneByFileCode/${data}`);
}
export async function getOneShare(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataUnstructured/getOneShare/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataUnstructured/getByIds/${data}`);
}
export async function deleteAttachBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tSysAttach/deleteBatch/${data}`);
}
export async function downAttachBatch(data: any) {
  return requestClient.post<any>('/rgdc-submit/tSysAttach/downAttachBatch', data);
}
export async function publish(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataUnstructured/publish', data);
}
export async function unPublish(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataUnstructured/unPublish', data);
}
