<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { MessagePlugin } from 'tdesign-vue-next';

import { revoke } from './api';
import IndexTable from './components/IndexTable.vue';
import DataDetailsView from '#/views/_workflow/my-task/components/DataDetailsView.vue';
import CfgEditForm from "#/views/modules/tDataBase/components/CfgEditForm.vue";
// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();
const cfgEditFormRef = ref();
const dataDetailsViewRef = ref();

const add: any = () => {
  MessagePlugin.success('部署成功');
};

const revokeClick: any = async (record: any) => {
  // modalApi.setState({ title: '编辑' });
  await revoke({ id: record.id });
  indexTable.value.refresh();
  MessagePlugin.success('撤销成功');
};
</script>

<template>
  <Page description="历史流程列表" title="历史流程">
    <IndexTable ref="indexTable" @add="add" @revoke="revokeClick" :cfg-edit-form-ref="cfgEditFormRef" :data-details-view-ref="dataDetailsViewRef"/>
    <CfgEditForm ref="cfgEditFormRef" :out-ref="indexTable" :isTask="true" :isHis="true"/>
    <DataDetailsView ref="dataDetailsViewRef" :out-ref="indexTable"/>
  </Page>
</template>
