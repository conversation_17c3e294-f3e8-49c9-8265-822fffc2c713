<script setup>
import { EditIcon, UserAddIcon } from 'tdesign-icons-vue-next';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer,
  Form,
  FormItem,
  Input,
  Space,
  Tag,
} from 'tdesign-vue-next';
</script>
<script>
import { cloneDeep } from '#/utils/copyObject';

import addNode from './addNode.vue';

export default {
  components: {
    AddNode: addNode,
  },
  inject: ['select'],
  props: {
    modelValue: { type: Object, default: () => {} },
  },
  data() {
    return {
      nodeConfig: {},
      drawer: false,
      isEditTitle: false,
      form: {},
    };
  },
  watch: {
    modelValue() {
      this.nodeConfig = this.modelValue;
    },
  },
  mounted() {
    this.nodeConfig = this.modelValue;
  },
  methods: {
    show() {
      this.form = {};
      if (this.nodeConfig.nodeAssigneeList === undefined) {
        this.nodeConfig.nodeAssigneeList = [];
      }
      this.form = cloneDeep(this.nodeConfig);
      this.isEditTitle = false;
      this.drawer = true;
    },
    editTitle() {
      this.isEditTitle = true;
      this.$nextTick(() => {
        this.$refs.nodeTitle.focus();
      });
    },
    saveTitle() {
      this.isEditTitle = false;
    },
    selectHandle(type, data) {
      this.select(type, data);
    },
    delRole(index) {
      this.form.nodeAssigneeList.splice(index, 1);
    },
    save() {
      // 生在人世间
      // if (this.form.nodeAssigneeList.length > 0) {
      //   this.form.useScope = 1;
      // }

      this.$emit('update:modelValue', this.form);
      this.drawer = false;
    },
    toText(nodeConfig) {
      return nodeConfig.nodeAssigneeList &&
        nodeConfig.nodeAssigneeList.length > 0
        ? nodeConfig.nodeAssigneeList.map((item) => item.name).join('、')
        : '所有人';
    },
  },
};
</script>
<template>
  <div class="node-wrap">
    <div class="node-wrap-box start-node" @click="show">
      <div class="title" style="background: #576a95">
        <UserAddIcon class="icon" />
        <span>
          {{ nodeConfig.nodeName }}
        </span>
      </div>
      <div class="content">
        <span>{{ toText(nodeConfig) }}</span>
      </div>
    </div>
    <AddNode v-model="nodeConfig.childNode" />
    <Drawer v-model:visible="drawer" :size="500" attach="body" title="发起人">
      <template #header>
        <div class="node-wrap-drawer__title">
          <label v-if="!isEditTitle" @click="editTitle"
            >{{ form.nodeName }}<EditIcon
          /></label>
          <Input
            v-if="isEditTitle"
            ref="nodeTitle"
            v-model="form.nodeName"
            clearable
            @blur="saveTitle"
            @enter="saveTitle"
          />
        </div>
      </template>

      <div style="padding: 0 20px 20px">
        <Form label-align="top">
          <FormItem label="谁可以发起此审批">
            <Button
              shape="round"
              type="primary"
              @click="selectHandle(2, form.nodeAssigneeList)"
            >
              选择角色
            </Button>
          </FormItem>
          <div class="tags-list">
            <Space>
              <Tag
                v-for="(role, index) in form.nodeAssigneeList"
                :key="role.id"
                closable
                @close="delRole(index)"
              >
                {{ role.name }}
              </Tag>
            </Space>
          </div>
          <Alert
            v-if="form.nodeAssigneeList && form.nodeAssigneeList.length === 0"
            message="不指定则默认所有人都可发起此审批"
            type="info"
          />
        </Form>
      </div>

      <template #footer>
        <Button type="primary" @click="save">保存</Button>
        <Button @click="drawer = false">取消</Button>
      </template>
    </Drawer>
  </div>
</template>
