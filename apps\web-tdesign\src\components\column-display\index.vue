<script lang="ts" setup>
import { Icon } from 'tdesign-icons-vue-next';
import { Button } from 'tdesign-vue-next';

const emit: any = defineEmits(['update:modelValue', 'onchange']);
const modelValue = defineModel<boolean>({ required: true });
const toggleAdvanced = () => {
  modelValue.value = !modelValue.value;
  emit('onchange', modelValue.value);
};
</script>

<template>
  <Button variant="text" v-bind="$attrs" @click="toggleAdvanced">
    <Icon name="setting-1" />
  </Button>
</template>
