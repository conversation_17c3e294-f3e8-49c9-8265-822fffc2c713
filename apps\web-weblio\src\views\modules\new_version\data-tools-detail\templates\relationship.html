<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>化工知识协同加工与管理平台</title>
  <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
  <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
  <link rel="stylesheet" href="../static/vue/theme/index.css">
  <script src="../static/vue/min/vue.min.js"></script>
  <script src="../static/vue/element-ui2.15.13_index.js"></script>
  <script src="../static/vue/axios0.26.0_axios.min.js"></script>
  <style>
    html,
    body {
      min-width: 100%;
    }

    .mon_warp {
      margin: 0px;
      width: 100%;
      background-size: cover;
    }

    .mon_body {
      display: none;
      width: 100%;
    }

    .el-menu-vertical-demo {
      height: 100%;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-upload__tip {
      margin-top: 10px;
    }

    .clearfix:before,
    .clearfix:after {
      display: table;
      content: "";
    }

    .clearfix:after {
      clear: both;
    }

    .center {
      border: 1px solid #ccc;
      width: 100%;
      margin: 20px auto 20px;
      border-radius: 20px;
      padding: 20px;
      min-width: 1200px;
      display: flex;
    }

    .upload-demo {
      width: 100%;
    }

    .el-upload__text {
      font-size: 16px;
      margin: 20px 0;
    }

    .el-icon-upload {
      font-size: 67px;
      margin: 20px 0;
    }

    .el-menu-item.is-active {
      background-color: #ecf5ff;
      color: #409EFF;
    }

    .el-menu-item {
      font-size: 14px;
      height: 56px;
      line-height: 56px;
    }

    .el-menu-item:hover {
      background-color: #ecf5ff;
    }

    .download-notice {
      font-size: 14px;
      color: #666;
      display: inline-block;
      margin-top: 10px;
    }

    .notice-icon {
      color: #ff9800;
      margin-right: 5px;
    }

    .download-all-container {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .el-upload-dragger {
      width: 200px;
      height: 200px;
      padding: 20px;
      /* Reduced padding to fit content comfortably */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .el-upload__text {
      font-size: 16px;
      margin: 10px 0;
      /* Adjusted margin for better spacing */
    }

    .el-icon-upload {
      font-size: 50px;
      /* Slightly smaller icon to fit the new height */
      margin: 10px 0;
    }

    .el-textarea__inner {
      min-height: 200px;
    }

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  </style>
</head>

<body>
  <div class="header_app" id="header_app"></div>

  <div class="mon_warp clearfix" id="app">
    <div class="mon_body clearfix">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="3">
          <div style="padding-top:20px">
             <el-menu :default-active="menuActive"  class="el-menu-vertical-demo" 
             @select="handleMenuSelect"
            style="padding-top:20px;"
              active-text-color="#409EFF">
               <el-menu-item index="cleanTool">
                   <el-tooltip content="数据汇聚与清洗工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据汇聚与清洗工具
                  </span>
                </el-tooltip>
                  </el-menu-item>
              <el-menu-item index="classiFication">

                 <el-tooltip content="数据整编与分类工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据整编与分类工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="home">
                <el-tooltip content="全文多模态解析重组工具" placement="right">
                  <span>
,                    全文多模态解析重组工具
                  </span>
                </el-tooltip>
              </el-menu-item><el-menu-item index="relationship">
                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    知识对象及关系挖掘工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>

            </el-menu>
          </div>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="21">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <h2>知识对象及关系挖掘工具</h2>
            </div>
            <div>
              <p style="font-size: 16px;">
                化工领域知识对象抽取是针对化工科技文献中的文本、图像、表格等多源信息，精准抽取实体及实体之间关系的关键技术。
                其功能设计需深度适配化工领域的专业性、复杂性与多模态特性，知识对象抽取功能可实现从非结构化数据到可复用知识资产的转化，为研发、生产、安全等环节提供智能化支持，推动化工行业向数据驱动的新型研发范式转型。

              </p>
              <!-- <div style="text-align: right;width: 100%;padding-top: 10px;">
              <el-button type="success" style="margin-right: 60px;" @click="startopen">任务列表</el-button>
            </div> -->
              <div class="center">
                <div style="width: 70%;">
                   <div style="width: 100%;">
                  <div class="left" v-loading="loading">
                    <el-form label-width="120px" label-position="left">
                      <el-form-item label="文献类型：">
                        <el-radio-group v-model="extract_type">
                          <el-radio label="text">text</el-radio>
                          <el-radio label="image">image</el-radio>

                        </el-radio-group>
                      </el-form-item>

                      <el-form-item label="选择模型：">
                        <el-radio-group v-model="model_type">
                          <el-radio label="1">本地模型</el-radio>
                          <el-radio label="2">deepseek</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <el-form-item label="选择语言类型：">
                        <el-radio-group v-model="lang">
                          <el-radio label="cn">cn</el-radio>
                          <el-radio label="en">en</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <el-form-item label="选择领域：">
                        <el-radio-group v-model="domain_type">
                          <el-radio label="common">common</el-radio>
                          <el-radio label="chemical">chemical</el-radio>
                          <el-radio label="gene">gene</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <el-form-item label="请输入文本：" required>
                        <div style="display: flex; gap: 20px;">
                          <el-input type="textarea" rows="9" v-model="content" :disabled="extract_type == 'image'"
                            style="width:200px; height: 200px;"></el-input>
                          <el-upload class="upload-demo" drag :disabled="extract_type == 'text'"
                            action="/query_file_json"
                            :before-upload="($event) =>{beforeAvatarUploadFile($event, 'need_publish_trailer')}"
                            multiple>
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                          </el-upload>
                        </div>
                      </el-form-item>

                    </el-form>
                    <div style="width: 90%;text-align: right;">
                      <el-button type="primary" @click="extract">抽取</el-button>
                    </div>
                  </div>
                </div>
                <div style="display: flex;margin-top: 10px;width: calc(96% - 52px);
    justify-content: space-between;" v-if="entities.length > 0">
                  <span>实体：</span>
                  <div
                    style="background-color: #ecf5ff; padding: 10px;text-align: center;width: calc(100% - 60px);line-height: 24px;font-size: 14px;height:190px;">
                    <el-scrollbar style="height: 100%;">
                      <p v-for="item in entities" v-html="item.value"></p>
                    </el-scrollbar>
                  </div>
                </div>

                <div style="display: flex;margin-top: 10px;width: calc(96% - 52px);justify-content: space-between;"
                  v-if="relations.length > 0">
                  <span>三元组：</span>
                  <div
                    style="background-color: #ecf5ff; padding: 10px;text-align: center;width: calc(100% - 60px);line-height: 24px;font-size: 14px;height:190px;">
                    <el-scrollbar style="height: 100%;">
                      <p v-for="item in relations" v-html="item"></p>
                    </el-scrollbar>
                  </div>

                </div>
                <div style="width: 100%;text-align: right;margin-top: 10px;display: flex;" v-if="isduiqi">
                  <span>对齐</span>
                  <div style="width: 20%;margin-left: 25px;">

                    <el-upload class="upload-demo" :show-file-list="false" action="/query_file_json"
                      :before-upload="($event) =>{UploadFile($event, 'need_publish_trailer')}" multiple>
                      <el-button type="success">上传文本json</el-button>
                    </el-upload>
                  </div>

                  <div style="width: 20%;">

                    <el-upload class="upload-demo" :show-file-list="false" action="/query_file_json"
                      :before-upload="($event) =>{UploadFileImg($event, 'need_publish_trailer')}" multiple>
                      <el-button type="success">上传图像json</el-button>
                    </el-upload>
                  </div>
                  <div style="float: right;margin-left: 37%;">
                      <el-button type="primary" @click="duiqi">对齐</el-button>
                  </div>
                </div>
                </div>
                <div style="width: 30%;">
                </div>
             
              </div>
            </div>
            <!-- <div style="text-align: right;width: 100%;">
              <el-button type="primary" style="margin-right: 60px;" @click="handleUpload" v-text="btnStart"></el-button>
            </div> -->
            <!-- <div class="download-all-container">
            <button class="download-all">全部下载</button>
            <div class="download-notice"> 
                <span class="notice-icon">⚠</span>解构过程中请勿刷新页面
            </div>
        </div> -->
          </el-card>
        </el-col>
      </el-row>

      <!-- 上传记录对话框 -->
      <el-dialog title="上传记录" :visible.sync="dialogVisible" width="60%">
        <el-table :data="uploadLists" style="width: 100%;" :border="true" stripe>
          <el-table-column prop="created_at" label="上传时间" width="180"></el-table-column>
          <el-table-column prop="task_name" label="任务名称"></el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button size="mini" @click="handlePreview(scope.row)">预览</el-button>
              <el-button size="mini" type="primary" @click="handleDownload(scope.row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
    </div>
  </div>

  <div id="dataid" data="{{session.user}}" style="display:none"></div>
  <div class="mon_footer"></div>

  <script src="../static/js/jquery.min.js"></script>
  <script src="../static/js/monitor.js"></script>

  <script>
    let _this = this;

    const vm = new Vue({
      el: '#app',
      data: {
        activeIndex: '3',
        lang: 'cn',
        domain_type: "common",
        extract_type: "text",
        model_type: '2',
        fileList: [],
        dialogVisible: false,
        btnStart: "开始解构",
        uploadLists: [],
        loading: false,
        fileName: '', // 新增：文件名称输入框
        menuActive: "relationship",
        content: "",
        files: null,
        entities: [],
        loading: false,
        relations: [],
        text_json:null,
        image_json:null,
        uploadedFiles:[],
        uploadedImgs:[],
        isduiqi:false,
      },
      mounted() {
        $('.mon_body').css({ 'display': 'revert' });
        this.getTableData(); // 获取上传记录

      },
      methods: {
        duiqi(){
           let that = this
         
         if(!that.uploadedFiles.length || !that.uploadedFiles.length){
          that.$message.error('文本和图像必须传json文件才可以对齐！')
         }
          // 创建 FormData 对象
          let formData = new FormData();
          // 如果存在文件，附加到 FormData
          if (that.text_json) {
            formData.append('text_json', that.text_json); // 附加原始文件
          }

           if (that.image_json) {
            formData.append('image_json', that.image_json); // 附加原始文件
          }
          axios.post(server_url + '/api/align', formData
            , {
              headers: { 'Content-Type': 'multipart/form-data' },
              timeout: 60000
            }).then(function (res) {
              console.log(res, 'res');

              const data = res.data
              if (data.code == 200) {
                that.loading = false
                that.$message.success('对齐成功！');
                // location.href = `${server_url}${data.data.download_url}`;
                that.imgDataJson = data.data.image_map || []
                if(that.imgDataJson){
                  that.imgDataJson.forEach((item)=>{
                    item.image_url = server_url + '/api/view/' + item.image_url 
                  })
                }
                console.log(that.imgDataJson);

                localStorage.setItem('jsonData',JSON.stringify(that.imgDataJson))
                location.href = './relationshipUpload'
                that.entities = data.data.result.entities || []
                that.relations = data.data.result.relations || []
              }
            }).catch((error) => {
              that.loading = false
            })
        },
        //抽取
        extract() {
          let that = this
          let param = {
            extract_type: that.extract_type,
            domain_type: that.domain_type,
            model_type: (that.model_type)-0 || '',
            lang: that.lang,
            content: that.content,
          }
          // let data = {}
          // return
          // 创建 FormData 对象
          let formData = new FormData();
          // 如果存在文件，附加到 FormData
          if (that.files) {
            formData.append('files', that.files); // 附加原始文件
          }

          // 附加 JSON 数据
          formData.append('data', JSON.stringify(param));
          that.loading = true
          axios.post(server_url + '/api/extract', formData
            , {
              headers: { 'Content-Type': 'multipart/form-data' },
              timeout: 60000
            }).then(function (res) {
              console.log(res, 'res');

              const data = res.data
              if (data.code == 200) {
                that.loading = false
                that.$message.success('抽取成功！');
                location.href = `${server_url}${data.data.download_url}`;
                that.$message.success('下载成功！');
                that.entities = data.data.result.entities || []
                that.relations = data.data.result.relations || []
                that.isduiqi = true
              }
            }).catch((error) => {
              that.loading = false
            })
        },
        handleMenuSelect(index) {
    if (index === 'cleanTool') {
      window.location.href = '/cleanTool'; // 跳转到对应的页面
    } else if (index === 'classiFication') {
      window.location.href = '/classiFication';
    } else if (index === 'relationship') {
      window.location.href = '/relationship';
    } else if (index === 'home') {
      window.location.href = '/'; // 跳转到首页
    } else if (index === 'qualitycontrol') {
      window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
    } 
    this.menuActive = index; // 更新高亮状态
  },
        startopen() {
          this.dialogVisible = true;
          this.getTableData(); // 打开对话框时获取最新的上传记录
        },
        handleAdd() { },
        handleRemove(file, fileList) {
          console.log('File removed:', file);
        },
        handlePreview(row) {
          // 预览功能可以根据需要实现
          location.href = `./demonstration?session_id=${row.session_id}`;
        },

        beforeAvatarUploadFile(file, type = '') {
          let that = this;
          const isLt100M = file.size / 1024 / 1024 < 500;
          if (!isLt100M) {
            this.$message.error('上传文件大小不能超过 500MB!');
            return false; // 阻止上传
          }
          // 直接存储原始文件对象
          that.files = file; // 存储 File 对象
          that.$message.success('上传成功！')
          return true; // 允许上传继续
        },

        UploadFile(file){
           let that = this;
          const isLt100M = file.size / 1024 / 1024 < 500;
          if (!isLt100M) {
            this.$message.error('上传文件大小不能超过 500MB!');
            return false; // 阻止上传
          }

          // 将文件添加到数组
          that.uploadedFiles.push(file);
         

          // 直接存储原始文件对象
          that.text_json = file; // 存储 File 对象
          that.$message.success('上传成功！')
          return true; // 允许上传继续
        },
         
        UploadFileImg(file){
            let that = this;
          const isLt100M = file.size / 1024 / 1024 < 500;
          if (!isLt100M) {
            this.$message.error('上传文件大小不能超过 500MB!');
            return false; // 阻止上传
          }
          that.uploadedImgs.push(file)
         

          // 直接存储原始文件对象
          that.image_json = file; // 存储 File 对象
          that.$message.success('上传成功！')
          return true; // 允许上传继续
        },
        // 新增：点击“开始解构”后统一上传
        handleUpload() {


          if (this.fileList.length === 0) {
            this.$message.warning("请先选择文件");
            return;
          }
          if (!this.fileName) {
            this.$message.warning("请填写文件名称再进行上传");
            return;
          }
          this.loading = true;
          const formData = new FormData();
          let overSize = false;

          this.fileList.forEach(file => {
            if (file.raw.size / 1024 / 1024 > 500) {
              overSize = true;
            } else {
              formData.append('pdf_files', file.raw);
            }
          });

          if (overSize) {
            this.$message.error("有文件大小超过 50MB，无法上传");
            return;
          }
          this.btnStart = "正在解构...";
          axios.post(`${server_url}/api/upload_pdfs?task_name=${encodeURIComponent(this.fileName)}`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          }).then(response => {
            this.btnStart = "开始解构";

            this.$message.success("上传成功");
            this.loading = false;
            location.href = `./demonstration?session_id=${response.data.data}`;
            this.response_handle(response.data, true);
          }).catch(error => {
            this.$message.error("上传失败");
            console.error(error);
          });
        },
        // 你的后端响应处理方法
        response_handle(data, success) {
          console.log("上传结果：", data);
          // TODO: 你可以在这里添加逻辑，比如跳转或展示解析结果
        },
        handleDownload(row) {
          const that = this;
          axios({
            url: server_url + '/api/get_zip_by_session_id',
            method: 'GET',
            params: { session_id: row.session_id },
            responseType: 'blob' // 告诉 axios 这是一个二进制文件
          }).then(function (response) {
            const blob = new Blob([response.data]);
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;

            // 可选：给文件命名
            const fileName = `下载文件_${row.session_id || 'unknown'}.zip`;
            link.download = fileName;

            document.body.appendChild(link);
            link.click();
            window.URL.revokeObjectURL(downloadUrl);
            document.body.removeChild(link);
          }).catch(function (error) {
            console.log('文件下载失败：', error);
          });
        },

        getTableData() {
          // 这里可以添加获取表格数据的逻辑
          let that = this;

          axios.get(server_url + '/api/upload_list').then(function (res) {
            if (res.data.code === 0) {

              that.$nextTick(() => {
                that.uploadLists = res.data.data;
                console.log(that.uploadLists);
              });

            }
          }).catch(function (error) {
            console.log(error);
            that.enterpriseLoading = false; // Ensure loading state is turned off in case of an error
          });
        },
      }
    });
  </script>
</body>

</html>