<script setup lang="ts">
import { getDictItems } from '#/api';
import { useSearchStore } from '#/store/search';
import {
  AddIcon,
  ArrowLeftIcon,
  DeleteIcon,
  InfoCircleIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  FormItem,
  Input,
  MessagePlugin,
  Select,
  Space,
} from 'tdesign-vue-next';
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

const searchStore = useSearchStore();
const router = useRouter();

// 主关键词
const searchContent = ref('');

// 选项数据
const logicOptions = ref<any[]>([]);
const dataTypeOptions = ref<any[]>([]);
const chemFieldList = ref([]);
const experimentFieldList = ref([]);
const paperFieldList = ref([]);
// 字段选项缓存
const searchFieldMap: Record<string, any[]> = reactive({
  chem: [],
  experiment: [],
  patent_s: [],
  book: [],
  paper: [],
  patent: [],
  standard: [],
  images: [],
  bio: [], // 兼容旧逻辑
});

// 条件列表
const logicList = reactive<any[]>([
  {
    logic: 'AND',
    dataType: 'chem',
    field: 'smiles',
    searchList: searchFieldMap.chem,
    query: '',
  },
]);

onMounted(async () => {
  logicOptions.value = await getDictItems('LOGIC');
  chemFieldList.value = await getDictItems('CHEM_SEARCH_FIELD');
  experimentFieldList.value = await getDictItems('EXPERIMENT_SEARCH_FIELD');
  paperFieldList.value = await getDictItems('PAPER_SEARCH_FIELD');
  searchFieldMap.chem = chemFieldList.value;
  searchFieldMap.experiment = experimentFieldList.value;
  searchFieldMap.patent_s = experimentFieldList.value;
  searchFieldMap.book = paperFieldList.value;
  searchFieldMap.paper = paperFieldList.value;
  searchFieldMap.patent = paperFieldList.value;
  searchFieldMap.standard = paperFieldList.value;
  searchFieldMap.images = paperFieldList.value;
  searchFieldMap.bio = experimentFieldList.value;

  // 获取数据类型选项
  const structTypes = await getDictItems('DATA_TYPE');
  const unstructTypes = await getDictItems('UNSTRUCTURED_DATA_TYPE');
  dataTypeOptions.value = [...(structTypes || []), ...(unstructTypes || [])];

  // 初始化第一个条件的searchList
  if (logicList.length > 0 && dataTypeOptions.value.length > 0) {
    logicList[0].dataType = dataTypeOptions.value[0].value;
    handleDataTypeChange(logicList[0]);
  }
});

// 监听 dataType 变化，自动切换 field 和 searchList
function handleDataTypeChange(item) {
  let list: any[] = [];
  switch (item.dataType) {
    case '0': {
      list = chemFieldList.value;

      break;
    }
    case '1':
    case '2': {
      list = experimentFieldList.value;

      break;
    }
    case 'book':
    case 'images':
    case 'paper':
    case 'patent':
    case 'standard': {
      list = paperFieldList.value;

      break;
    }
    default: {
      list = [];
    }
  }
  item.searchList = list;
  item.field = list.length > 0 ? list[0].value : '';
}

// 新增条件
function addCondition() {
  const type = dataTypeOptions.value[0]?.value || '';
  logicList.push({
    logic: 'AND',
    dataType: type,
    field: '',
    searchList: [],
    query: '',
  });
  handleDataTypeChange(logicList[logicList.length - 1]);
}
// 删除条件
function removeCondition(idx) {
  if (logicList.length > 1) logicList.splice(idx, 1);
}

// 提交
function handleSearch() {
  const _result = {
    searchQuery: searchContent.value,
    logicList: logicList.map((item) => ({
      logic: item.logic,
      dataType: item.dataType,
      field: item.field,
      searchList: item.searchList,
      query: item.query,
    })),
  };
  searchStore.setAll(_result);
  router.push({ path: '/search-page' });
}

function resetForm() {
  searchContent.value = '';
  const type = dataTypeOptions.value[0]?.value || '';
  logicList.splice(0, logicList.length, {
    logic: 'AND',
    dataType: type,
    field: '',
    searchList: [],
    query: '',
  });
  handleDataTypeChange(logicList[0]);
}

const handleBack = () => router.back();
const handleLearnMore = () =>
  MessagePlugin.info('了解更多关于布尔运算符的使用方法');
</script>

<template>
  <div class="advanced-search-page">
    <Card class="search-card">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">高级检索</h1>
          <div class="info-section">
            <InfoCircleIcon class="info-icon" />
            <span class="info-text">
              您可以使用运算符AND、OR、NOT来优化检索。
              <a
                href="#"
                class="learn-more-link"
                @click.prevent="handleLearnMore"
                >了解更多
              </a>
            </span>
          </div>
        </div>
        <button class="back-btn" @click="handleBack">
          <ArrowLeftIcon class="back-icon" />
          <span class="back-text">返回</span>
        </button>
      </div>

      <!-- 主关键词输入 -->
      <div class="main-keyword-section">
        <div class="section-header">
          <SearchIcon class="section-icon" />
          <span class="section-title">关键词</span>
        </div>
        <FormItem style="margin-left: 0" :label-width="0">
          <Input
            v-model="searchContent"
            placeholder="请输入关键词"
            size="large"
            class="main-keyword-input"
          />
        </FormItem>
      </div>

      <!-- 条件列表 -->
      <div class="boolean-conditions">
        <div v-for="(item, idx) in logicList" :key="idx" class="condition-row">
          <!-- 逻辑下拉 -->
          <Select
            v-model="item.logic"
            :options="logicOptions"
            class="operator-select"
            size="large"
          />
          <!-- 数据类型下拉 -->
          <Select
            v-model="item.dataType"
            :options="dataTypeOptions"
            class="type-select"
            size="large"
            @change="() => handleDataTypeChange(item)"
          />
          <!-- 字段下拉 -->
          <Select
            v-model="item.field"
            :options="item.searchList"
            class="condition-select"
            size="large"
          />
          <!-- 关键词输入 -->
          <Input
            v-model="item.query"
            placeholder="请输入关键词"
            class="keyword-input"
            size="large"
          />
          <!-- 删除按钮 -->
          <Button
            theme="danger"
            variant="text"
            @click="removeCondition(idx)"
            class="delete-btn"
            :disabled="logicList.length <= 1"
          >
            <template #icon>
              <DeleteIcon />
            </template>
          </Button>
        </div>
      </div>

      <!-- 新增条件按钮 -->
      <div class="add-condition-section">
        <Button theme="primary" @click="addCondition" class="add-condition-btn">
          <template #icon>
            <AddIcon />
          </template>
          新增
        </Button>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <Space size="medium">
          <Button theme="primary" @click="handleSearch" size="large">
            <template #icon>
              <SearchIcon />
            </template>
            开始检索
          </Button>
          <Button theme="default" @click="resetForm" size="large">
            重置
          </Button>
        </Space>
      </div>
    </Card>
  </div>
</template>

<style scoped lang="scss">
.advanced-search-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.search-card {
  max-width: 1200px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.info-section {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.info-icon {
  color: #1890ff;
  font-size: 16px;
}

.learn-more-link {
  color: #1890ff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.back-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px 16px;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background-color: #f0f9ff;
    transform: translateX(-2px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }

  &:active {
    transform: translateX(-1px);
  }
}

.back-icon {
  font-size: 20px;
  color: #666;
  margin-right: 4px;
  transition: color 0.3s ease;
}

.back-text {
  font-size: 16px;
  color: #666;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.back-btn:hover {
  .back-icon,
  .back-text {
    color: #1890ff;
  }
}

.search-form {
  padding: 0;
}

.main-keyword-section {
  margin-bottom: 32px;
  // 去除所有左边距
  margin-left: 0 !important;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-icon {
  color: #1890ff;
  font-size: 18px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.main-keyword-input {
  width: 100%;
}

.boolean-conditions {
  margin-bottom: 24px;
}

.condition-row {
  display: grid;
  grid-template-columns: 100px 200px 200px 1fr 60px;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background-color: #f8f9fa;
    border-color: #d9d9d9;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }
}

.operator-select {
  width: 100%;
}

.type-select {
  width: 100%;
}

.condition-select {
  width: 100%;
}

.keyword-input {
  width: 100%;
}

.delete-btn {
  width: 48px !important;
  height: 48px !important;
  min-width: 48px !important;
  min-height: 48px !important;
  max-width: 48px !important;
  max-height: 48px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  line-height: 1 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  // 彻底覆盖TDesign Button的伪类
  &:hover,
  &:focus,
  &:active {
    background: #fff2f0 !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.2) !important;
    outline: none !important;
  }

  .t-icon {
    font-size: 20px !important;
    color: #ff4d4f !important;
    transition: color 0.3s;
  }
}

.add-condition-section {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.add-condition-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.25);
  }

  &:active {
    transform: translateY(-1px);
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid #e8e8e8;
}

// 响应式设计
@media (max-width: 768px) {
  .advanced-search-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .condition-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .operator-select,
  .type-select,
  .condition-select,
  .keyword-input {
    width: 100%;
  }

  .delete-btn {
    justify-self: center;
  }
}
</style>
