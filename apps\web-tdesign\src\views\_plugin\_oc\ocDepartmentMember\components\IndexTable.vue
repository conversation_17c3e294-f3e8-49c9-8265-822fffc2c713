<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';

import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { Icon } from 'tdesign-icons-vue-next';
import {
  Button,
  Dialog,
  Divider,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Space,
  Table,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { UserPopSelect } from '../../user/components';
import { deleteBatch, listByPage, saveBranch } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },

  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
/**
 * table 排序字段
 */

const tableConfig = ref(BaseTableConfig);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);
/**
 * 列定义
 */
const columns = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',

    width: 64,
  },

  // { colKey: 'departmentCode', ellipsis: true, sorter: true, title: '部门code' },
  // {
  //   colKey: 'isPrincipal',
  //   ellipsis: true,
  //   sorter: true,
  //   title: '是否为负责人 0否 1是',
  // },
  // { colKey: 'status', ellipsis: true, sorter: true, title: '状态0禁用1启用' },
  {
    colKey: 'userName',
    ellipsis: true,
    sorter: true,
    title: '人员姓名',
    width: 300,
  },
  { colKey: 'remark', ellipsis: true, title: '备注' },
  { colKey: 'op', width: 80, title: '操作', align: 'center' },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      state.selectedRowKeys = [];
      reload();
    },
  }),
  saveBranch: useRequest(saveBranch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      reload();
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;

  run({
    param: { ...formData.value, departmentId: props.extendSearchObj?.id },
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};

/**
 * 删除按钮响应
 */
const del = () => {
  state.delDailogShow = true;
};
/**
 * 执行批量删除
 */
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};
/**
 * 单条删除
 */
const remove = (record: any) => {
  reqRunner.deleteBatch.run([record.id]);
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  /**
   * 返回第一页
   */
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  searchForm.value.reset();
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  state.selectedRowKeys = value;
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};
const addUserToDep = (data: any) => {
  reqRunner.saveBranch.run({
    depId: props.extendSearchObj?.id,
    users: data,
  });
  // console.log('addUserToDep', data);
};
onMounted(() => {
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Space :size="8" class="tiny-tdesign-style-patch" direction="vertical">
    <!--    查询表单定义区域-->
    <Form
      ref="searchForm"
      :data="formData"
      class="w-full"
      @reset="resetSearch"
      @submit="searchFormSubmit"
      label-align="right"
      label-width="60"
    >
      <div class="flex w-full flex-row items-center justify-center gap-2">
        <!--一列表单布局-->
        <div class="flex-1">
          <FormItem label="人员姓名" name="userName">
            <Input
              v-model="formData.userName"
              clearable
              placeholder="请输入内容"
            />
          </FormItem>
        </div>
        <div class="flex flex-row gap-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <Icon name="search" />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <Icon name="refresh" />
            </template>
            重置
          </Button>

          <!--          <PutAway v-model="state.hideQuery" variant="text" />-->
        </div>
      </div>
    </Form>
    <Divider style="padding: 0; margin: 0" />
    <!-- 表格定义区域 -->
    <Table
      v-model:column-controller-visible="tableConfig.columnControllerVisible"
      v-model:display-columns="displayColumns"
      :bordered="true"
      :columns="columns"
      :data="state.dataSource"
      :hover="true"
      :loading="state.loading"
      :pagination="pagination"
      :pagination-affixed-bottom="true"
      :selected-row-keys="state.selectedRowKeys"
      :sort="state.sort"
      :stripe="true"
      cell-empty-content="-"
      lazy-load
      resizable
      row-key="id"
      table-layout="fixed"
      @page-change="rehandlePageChange"
      @select-change="rehandleSelectChange"
      @sort-change="sortChange"
      v-bind="tableConfig"
    >
      <!--        表格顶部按钮区域-->
      <template #topContent>
        <div class="mb-2 flex w-full justify-start">
          <div class="flex w-full items-center justify-start pl-2">
            <div class="t-card__title mr-2">人员列表</div>
            <div
              v-if="state.selectedRowKeys?.length > 0"
              class="text-blue-600/80"
            >
              选中了[{{ state.selectedRowKeys?.length || 0 }}]行
            </div>
          </div>
          <div class="flex w-full justify-end space-x-2">
            <Button
              v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
              theme="danger"
              @click="del"
            >
              <template #icon>
                <Icon name="delete" />
              </template>
              删除
            </Button>
            <UserPopSelect
              row-selection-type="multiple"
              title="增加人员"
              @value-change="addUserToDep"
            />
            <Button variant="text" @click="reload">
              <Icon name="refresh" />
            </Button>
            <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
          </div>
        </div>
      </template>
      <!--        空数据显示定义-->
      <template #empty>
        <div class="flex-col-center flex p-1">
          <div class="text-sx mb-2">暂无数据</div>
          <UserPopSelect
            row-selection-type="multiple"
            title="增加人员"
            @value-change="addUserToDep"
          />
          <!--          <Button class="w-[100%]" theme="primary" variant="text" @click="edit">-->
          <!--            <template #icon>-->
          <!--              <Icon name="add-circle" />-->
          <!--            </template>-->
          <!--            点击创建新数据-->
          <!--          </Button>-->
        </div>
      </template>
      <!--        编辑按钮-->
      <template #op="slotProps">
        <Space size="small">
          <!--          <Link theme="primary" @click="edit(slotProps.row)">编辑</Link>-->
          <Popconfirm
            content="确定删除？"
            theme="warning"
            @confirm="remove(slotProps.row)"
          >
            <Link theme="danger">删除</Link>
          </Popconfirm>
        </Space>
      </template>
    </Table>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
