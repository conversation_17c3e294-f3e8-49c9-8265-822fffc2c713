import { defineOverridesPreferences } from '@vben/preferences';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 * !!! 更改配置后请清空缓存，否则可能不生效
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  // 如果使用后端路由配置，则在app 对象内添加  accessMode: 'backend',
  app: {
    name: import.meta.env.VITE_APP_TITLE,
    // 动态路由
    accessMode: 'backend',
  },
});

export const crossOriginStoreConfig: any = [
  {
    origin: /**************:5888$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
];

export const mainAuthStoreConfig: any = {
  url: 'http://**************:5888/hub.html',
  config: {},
};
