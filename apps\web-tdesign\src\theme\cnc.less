.t-table__header {
  th {
    background: var(--td-brand-color-hover) !important;
    color: #fff !important;
  }

  th:last-child {
    border-bottom-right-radius: 4px !important;
    border-top-right-radius: 4px !important;
  }

  th:first-child {
    border-bottom-left-radius: 4px !important;
    border-top-left-radius: 4px !important;
  }

  .t-table__sort-icon--active {
    color: #ffffff99 !important;
  }
}

.t-table--striped.t-table--hoverable
  > .t-table__content
  > table
  > tbody
  tr:hover {
  background-color: #e8f5ff !important;
}

.dark
  .t-table--striped.t-table--hoverable
  > .t-table__content
  > table
  > tbody
  tr:hover {
  background-color: hsl(var(--border)) !important;
}

.t-table td,
.t-table th {
  border-left: none !important;
  //padding: 15px var(--td-comp-paddingLR-l) !important;
}

.t-table__content {
  border-left: none !important;
  border-right: none !important;
  border-radius: 0px !important;
  border-top: 0px !important;
}

//
//.vben-menu-item {
//  background: transparent !important;
//}
//
//
//.vben-menu-item.is-active {
//  background: var(--td-brand-color-hover) !important;
//  color: #FFF !important;
//}
//
//.vben-sub-menu {
//  ul {
//    background-color: #E5ECF5;
//    margin: 0 5px;
//    border-radius: 4px;
//  }
//}
//
//.vben-sub-menu.is-opened {
//  background-color: #E5ECF588;
//  border-radius: 4px;
//}
//
//.vben-sub-menu-content {
//  background: transparent !important;
//}

.hover\:bg-accent:hover {
  background-color: #ffffff0e !important;
}

.bg-header {
  background: var(--td-brand-color-hover) !important;

  svg {
    color: #fff !important;
  }

  .theme-toggle__sun {
    fill: #fff !important;
  }

  button {
    color: #ffffff !important;

    &:hover {
      // background: #00000055 !important;
      // color: var(--td-brand-color-hover) !important;
    }

    span{
      background: var(--td-brand-color-hover) !important;
    }

    .dot {
      background: var(--td-success-color-6) !important;
    }
  }

  .items-center {
    color: var(--td-brand-color-hover) !important;
  }

  li {
    * {
      color: #ffffff !important;
    }
  }
}

aside {
  ul {
    padding: 5px;
  }

  .header-logo {
    z-index: 9999;
    margin-right: -1px;
    background: var(--td-brand-color-hover) !important;
    overflow: hidden;
    height: 49px !important;

    * {
      color: #ffffff !important;
    }
  }
}

.bg-background-deep {
  background-color: #e5ecf5 !important;
}

.dark .bg-background-deep {
  background-color: hsl(var(--background-deep)) !important;
}

.lucide-search-icon {
  path {
    color: var(--td-brand-color-hover) !important;
  }

  circle {
    color: var(--td-brand-color-hover) !important;
  }
}
