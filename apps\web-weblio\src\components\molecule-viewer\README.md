# 分子结构展示组件 (MoleculeViewer)

一个基于Vue 3的分子结构可视化组件套件，支持2D和3D分子结构的显示，使用SMILES字符串作为输入。

## 🎯 新功能特性

- ✅ **多种显示模式**: 缩略图模式、内联模式、弹窗模式
- ✅ **组件化设计**: 可拆分组合使用的子组件
- ✅ **点击弹窗**: 默认显示2D缩略图，点击弹出详细查看器
- ✅ **SMILES支持**: 接受SMILES字符串作为输入，支持大多数有机分子
- ✅ **2D/3D切换**: 在弹窗中可以在2D平面图和3D立体图之间自由切换
- ✅ **v-model支持**: 完整的双向数据绑定
- ✅ **纯前端实现**: 使用RDKit和3DMol.js本地库，无需远程API
- ✅ **浏览器兼容**: 自动检测浏览器能力，提供兼容性提示
- ✅ **错误处理**: 完善的SMILES验证和错误提示机制
- ✅ **响应式设计**: 支持自定义宽度和高度

## 🧩 组件架构

### 主组件
- **MoleculeViewer**: 主入口组件，支持多种模式

### 子组件
- **MoleculeThumbnail**: 缩略图组件（默认2D显示，可点击）
- **MoleculeModal**: 弹窗查看器（包含2D/3D切换功能）
- **MoleculePureViewer**: 纯分子渲染组件（只负责渲染，无UI控件）

## 📦 技术栈

- **RDKit**: 用于2D分子结构渲染和SMILES处理
- **3DMol.js**: 用于3D分子结构可视化
- **Vue 3**: 使用Composition API和TypeScript
- **现代浏览器**: 需要支持ES6+和WebGL

## 🚀 基本用法

### 1. 缩略图模式（推荐，默认）

```vue
<template>
  <div>
    <!-- 基础用法：显示缩略图，点击弹出详细查看器 -->
    <MoleculeViewer v-model="smiles" />
    
    <!-- 自定义缩略图尺寸 -->
    <MoleculeViewer 
      v-model="smiles"
      :width="150"
      :height="150"
      @thumbnailClick="onThumbnailClick"
      @structureLoaded="onStructureLoaded"
      @error="onError"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const smiles = ref('CC1(C(N2C(S1)C(C2=O)NC(=O)C(C3=CC=C(C=C3)O)N)C(=O)[O-])C.[Na+]');

const onThumbnailClick = (smiles) => {
  console.log('点击了分子缩略图:', smiles);
};

const onStructureLoaded = (data) => {
  console.log('分子结构加载完成:', data);
};

const onError = (error) => {
  console.error('分子结构错误:', error);
};
</script>
```

### 2. 单独使用子组件

```vue
<template>
  <div>
    <!-- 只显示缩略图 -->
    <MoleculeThumbnail
      :modelValue="smiles"
      :width="200"
      :height="200"
      @click="handleClick"
    />
    
    <!-- 纯分子渲染器 -->
    <MoleculePureViewer
      :smiles="smiles"
      :viewMode="viewMode"
      :width="400"
      :height="300"
      @loaded="onLoaded"
    />
    
    <!-- 弹窗查看器（由外部控制显示） -->
    <MoleculeModal
      v-model="showModal"
      :smiles="smiles"
      :defaultViewMode="'3d'"
      @structureLoaded="onLoaded"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import MoleculeThumbnail from './molecule-viewer/thumbnail.vue';
import MoleculePureViewer from './molecule-viewer/pure-viewer.vue';
import MoleculeModal from './molecule-viewer/modal.vue';

const smiles = ref('CCO'); // 乙醇
const viewMode = ref('2d');
const showModal = ref(false);

const handleClick = (smiles) => {
  showModal.value = true;
};

const onLoaded = (data) => {
  console.log('加载完成:', data);
};
</script>
```

## 🎛️ API 参考

### MoleculeViewer Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string` | `''` | SMILES字符串 |
| `mode` | `'thumbnail' \| 'inline' \| 'modal'` | `'thumbnail'` | 显示模式 |
| `width` | `number \| string` | `'200px'` | 组件宽度 |
| `height` | `number \| string` | `'200px'` | 组件高度 |
| `defaultViewMode` | `'2d' \| '3d'` | `'2d'` | 弹窗默认视图模式 |
| `clickable` | `boolean` | `true` | 缩略图是否可点击 |

### MoleculeViewer Events

| 事件 | 参数 | 说明 |
|------|------|------|
| `update:modelValue` | `string` | v-model更新 |
| `error` | `string` | 错误信息 |
| `structureLoaded` | `{smiles: string, viewMode: '2d' \| '3d'}` | 结构加载完成 |
| `thumbnailClick` | `string` | 缩略图点击事件 |

### MoleculeThumbnail Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string` | `''` | SMILES字符串 |
| `width` | `number \| string` | `'200px'` | 缩略图宽度 |
| `height` | `number \| string` | `'200px'` | 缩略图高度 |
| `clickable` | `boolean` | `true` | 是否可点击 |

### MoleculeModal Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `boolean` | `false` | 控制弹窗显示 |
| `smiles` | `string` | `''` | SMILES字符串 |
| `defaultViewMode` | `'2d' \| '3d'` | `'2d'` | 默认视图模式 |
| `width` | `number \| string` | `'600px'` | 弹窗宽度 |
| `height` | `number \| string` | `'500px'` | 弹窗内容高度 |

### MoleculePureViewer Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `smiles` | `string` | `''` | SMILES字符串 |
| `viewMode` | `'2d' \| '3d'` | `'2d'` | 视图模式 |
| `width` | `number \| string` | `'100%'` | 宽度 |
| `height` | `number \| string` | `'300px'` | 高度 |
| `auto` | `boolean` | `true` | 是否自动渲染 |

## 💡 使用场景

### 场景1：数据展示列表
```vue
<!-- 在列表中显示分子缩略图 -->
<div v-for="compound in compounds" :key="compound.id">
  <h3>{{ compound.name }}</h3>
  <MoleculeViewer 
    :modelValue="compound.smiles"
    :width="120"
    :height="120"
  />
</div>
```

### 场景2：详情页面
```vue
<!-- 在详情页面中显示大图 -->
<MoleculePureViewer
  :smiles="compound.smiles"
  :viewMode="currentViewMode"
  :width="500"
  :height="400"
/>
```

### 场景3：弹窗查看
```vue
<!-- 点击按钮弹出查看器 -->
<button @click="showModal = true">查看分子结构</button>
<MoleculeModal
  v-model="showModal"
  :smiles="compound.smiles"
  :defaultViewMode="'3d'"
/>
```

## 🔧 安装依赖

确保项目中已安装以下依赖：

```json
{
  "3dmol": "^2.5.1"
}
```

## 📁 文件结构

```
molecule-viewer/
├── index.vue          # 主组件
├── thumbnail.vue      # 缩略图组件
├── modal.vue         # 弹窗组件
├── pure-viewer.vue   # 纯渲染组件
└── README.md         # 文档
```

## ⚠️ 注意事项

1. **RDKit文件**: 确保 `public/rdkit/` 目录下有必要的文件：
   - `RDKit_minimal.js`
   - `RDKit_minimal.wasm`

2. **浏览器支持**: 3D功能需要WebGL支持

3. **性能**: 大量分子同时渲染时建议使用缩略图模式

## 🆕 更新日志

### v2.0.0
- ✨ 新增组件化架构
- ✨ 新增缩略图+弹窗模式  
- ✨ 新增多种显示模式
- ✨ 拆分为可独立使用的子组件
- 🐛 修复3D显示尺寸问题
- 💄 优化用户界面和交互体验 
