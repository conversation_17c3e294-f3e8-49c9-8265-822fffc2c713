import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '日志',
    },
    name: 'tSysLog',
    path: '/tSysLog',
    children: [
      {
        meta: {
          title: '日志编辑',
        },
        name: 'tSysLogIndex',
        path: '/tSysLog/index',
        component: () =>
          import('#/views/modules/tSysLog/index.vue'),
      },
    ],
  },
];

export default routes;
