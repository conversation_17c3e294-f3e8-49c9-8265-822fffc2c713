import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '非结构化数据',
    },
    name: 'tDataUnstructured',
    path: '/tDataUnstructured',
    children: [
      {
        meta: {
          title: '非结构化数据编辑',
        },
        name: 'tDataUnstructuredIndex',
        path: '/tDataUnstructured/index',
        component: () =>
          import('#/views/modules/tDataUnstructured/index.vue'),
      },
    ],
  },
];

export default routes;
