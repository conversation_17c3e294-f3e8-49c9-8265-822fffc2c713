// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/user/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/user/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/user/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/user/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/user/getByIds/${data}`);
}
