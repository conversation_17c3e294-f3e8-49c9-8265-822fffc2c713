<script setup lang="ts">
import { defineOptions, defineProps } from 'vue';
import { Codemirror } from 'vue-codemirror';

import { json } from '@codemirror/lang-json';

defineOptions({ name: 'FormEdit', inheritAttrs: true });
const props = defineProps({
  code: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <div class="h-full w-full overflow-x-auto overflow-y-auto">
    <Codemirror
      v-model="props.code"
      :extensions="[json()]"
      class="h-full w-full"
    />
  </div>
</template>

<style scoped></style>
