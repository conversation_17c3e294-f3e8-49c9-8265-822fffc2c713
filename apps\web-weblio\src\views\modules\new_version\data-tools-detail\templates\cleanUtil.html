<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>化工知识协同加工与管理平台</title>
    <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
    <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
    <link rel="stylesheet" href="../static/vue/theme/index.css">
    <script src="../static/vue/min/vue.min.js"></script>
    <script src="../static/vue/element-ui2.15.13_index.js"></script>
    <script src="../static/vue/axios0.26.0_axios.min.js"></script>
    <!-- Add Ace Editor CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.35.4/ace.min.js"></script>
    <style>
        html,
        body {
            min-width: 100%;
        }

        .mon_warp {
            margin: 0px;
            width: 100%;
            background-size: cover;
        }

        .mon_body {
            display: none;
            width: 100%;
        }

        .el-menu-vertical-demo {
            height: 100%;
        }

        .el-card {
            margin-top: 20px;
        }

        .el-upload__tip {
            margin-top: 10px;
        }

        .clearfix:before,
        .clearfix:after {
            display: table;
            content: "";
        }

        .clearfix:after {
            clear: both;
        }

        .center {
            border: 1px solid #ccc;
            width: 60%;
            margin: 20px auto 20px;
            border-radius: 20px;
            padding: 30px;
            min-width: 1200px;
        }

        .upload-demo {
            width: 100%;
        }

        .el-upload-dragger {
            width: 660px;
            height: 250px;
            padding: 40px;
        }

        .el-upload__text {
            font-size: 16px;
            margin: 20px 0;
        }

        .el-icon-upload {
            font-size: 67px;
            margin: 20px 0;
        }

        .el-menu-item.is-active {
            background-color: #ecf5ff;
            color: #409EFF;
        }

        .el-menu-item {
            font-size: 14px;
            height: 56px;
            line-height: 56px;
        }

        .el-menu-item:hover {
            background-color: #ecf5ff;
        }

        .download-notice {
            font-size: 14px;
            color: #666;
            display: inline-block;
            margin-top: 10px;
        }

        .notice-icon {
            color: #ff9800;
            margin-right: 5px;
        }

        .download-all-container {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .left {
            width: 70%;
        }

        .np_input_senior {
            width: 100%;
            min-height: 200px;
            margin-top: 15px;
            background: #f6f6f6;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }

        .bre_body {
            padding: 40px 0px 40px 0px;
            width: 100%;
            max-width: 800px;
            margin: auto;
        }

        .bre_content {
            width: 100%;
        }

        .bre_cols {
            width: 100%
        }

        .bre_content+.bre_content {
            margin-top: 15px;
        }

        .bre_col_fields {
            width: 230px;
            float: left;
        }

        .bre_col_values {
            width: calc(100% - 240px);
            float: right;
        }

        .dc_col_condition {
            width: 90px;
            float: left;
        }

        .bre_col_field {
            width: 130px;
            float: left;
            margin-left: 10px;
        }

        .bre_col_match {
            width: 90px;
            float: left;
            margin-left: 10px;
        }

        .bre_col_value {
            width: calc(100% - 294px);
            float: left;
            margin-left: 10px;
        }

        .bre_col_btn {
            width: 44px;
            float: right;
            margin-top: 4px;
        }

        .bre_btn {
            margin-top: 20px;
        }

        .np_table_history {
            margin-top: 15px;
        }

        .mls_card_list p {
            padding: 3px 0px 0px 0px;
            line-height: 22px;
        }

        .mls_card_abstract {
            height: 66px;
        }

        .mls_card_content {
            padding-bottom: 15px;
            font-size: 13px;
            margin-top: 5px;
        }

        .np_title_icon {
            top: 0px;
        }

        .datatitle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .cleanbox {
            display: flex;
            justify-content: space-between;
            width: 100%;
            height: calc(100vh - 340px);
            height: auto;
            margin: 0 auto 0;
        }

        main {
            width: 99%;
            margin: 0 auto 0px;
            padding: 0 10px 0 10px;
        }

        .in-coder-panel {
            position: relative;
        }

        .content {
            text-align: left;
        }

        .clean_li {
            width: 33.3%;
            height: 100%;
        }

        /* Style for Ace Editor */
        #python-editor {
            width: 100%;
            height: 526px;
            /* Match the min-height of the tables */
            border: 1px solid #93a1a1;
            /* Border to match Solarized Light theme */
            border-radius: 4px;
            background-color: #FDF6E3;
            /* Solarized Light background color */
        }

        /* Ensure base text is dark for high contrast */
        #python-editor .ace_text-layer {
            color: #000000 !important;
            /* Black text for maximum contrast */
        }
    </style>
</head>

<body>
    <div class="header_app" id="header_app"></div>

    <div class="mon_warp clearfix" id="app">
        <div class="mon_body clearfix">
            <el-row :gutter="20">
                <!-- 左侧菜单 -->
                <el-col :span="3">
                    <div style="padding-top:20px">
                        <el-menu :default-active="menuActive"  class="el-menu-vertical-demo" 
             @select="handleMenuSelect"
            style="padding-top:20px;"
              active-text-color="#409EFF">
               <el-menu-item index="cleanTool">
                   <el-tooltip content="数据汇聚与清洗工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据汇聚与清洗工具
                  </span>
                </el-tooltip>
                  </el-menu-item>
              <el-menu-item index="classiFication">

                 <el-tooltip content="数据整编与分类工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据整编与分类工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="home">
                <el-tooltip content="全文多模态解析重组工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    全文多模态解析重组工具
                  </span>
                </el-tooltip>
              </el-menu-item><el-menu-item index="relationship">
                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    知识对象及关系挖掘工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>

            </el-menu>
                    </div>
                </el-col>

                <!-- 右侧内容 -->
                <el-col :span="21">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <h2>数据汇聚与清洗工具</h2>
                        </div>
                        <div class="placeHolder" style="width: 100%;">
                            <el-form ref="form" :model="QueryForm" label-width="120px">
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="任务名称：">
                                            <el-input v-model="QueryForm.name" disabled></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="字段：">
                                            <!-- <el-input v-model="QueryForm.field"></el-input> -->
                                            <el-select v-model="QueryForm.field_name" @change="selectChange">
                                                <el-option :value="item.value" :label="item.label"
                                                    v-for="item in field_list"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="清洗程序名称：">
                                            <el-input v-model="QueryForm.code_name"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>

                            <div class="cleanbox">
                                <div class="clean_li">
                                    <el-table :data="data_list" stripe border :header-cell-style="{
                                            background: '#dcdfe6b5',
                                            'vertical-align': 'center',
                                            'text-align': 'center',
                                            'font-weight': 'bold',
                                            color: '#666',
                                        }" style="
                                            width: 100%;
                                            border: 1px solid #ebeef5;
                                            border-bottom: none;
                                            min-height: 500px;
                                        ">
                                        <el-table-column prop="id" label="id" align="center" width="120">
                                        </el-table-column>
                                        <el-table-column :show-overflow-tooltip="true" prop="value" label="value"
                                            align="center">
                                        </el-table-column>
                                    </el-table>

                                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                        :current-page="currentPage" :page-sizes="[10, 20, 50, 100]"
                                        :page-size="pageSize" background layout="prev, pager, next" :total="totalItems"
                                        style="margin-top: 10px; text-align: center;">
                                    </el-pagination>
                                </div>
                                <div class="clean_li" style="position: relative">
                                    <div id="python-editor"></div>

                                    <div style="text-align: right;margin-top: 10px;">
                                         <el-button type="success" @click="cleanData">保存</el-button>
    <el-button type="primary" @click="previewData">预览</el-button>
                                    </div>
                                </div>
                                <div class="clean_li">
                                    <el-table :data="result_data_list" stripe border :header-cell-style="{
                                            background: '#dcdfe6b5',
                                            'vertical-align': 'center',
                                            'text-align': 'center',
                                            'font-weight': 'bold',
                                            color: '#666',
                                        }" style="
                                            width: 100%;
                                            border: 1px solid #ebeef5;
                                            border-bottom: none;
                                            min-height: 500px;
                                        ">
                                        <el-table-column prop="id" label="id" align="center" width="120">
                                        </el-table-column>
                                        <el-table-column :show-overflow-tooltip="true" prop="value" label="value"
                                            align="center">
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>
    </div>

    <div id="dataid" data="{{session.user}}" style="display:none"></div>
    <div class="mon_footer"></div>

    <script src="../static/js/jquery.min.js"></script>
    <script src="../static/js/monitor.js"></script>

   <script>
    let _this = this;
    var $_GET = (function () {
        var url = window.document.location.href.toString();
        var u = url.split("?");
        if (typeof (u[1]) == "string") {
            u = u[1].split("&");
            var get = {};
            for (var i in u) {
                var j = u[i].split("=");
                get[j[0]] = j[1];
            }
            return get;
        } else {
            return {};
        }
    })();
    const vm = new Vue({
        el: '#app',
        data: {
            menuActive: '3',
            QueryForm: {
                id: '',
                name: "",
                field_name: "DOI",
                code_name:"",
            },
            result_data_list: [],
            popoverContent: "",
            data_list: [],
            field_list: [],
            // Pagination data
            currentPage: 1,
            pageSize: 10,
            totalItems: 0
        },
        mounted() {
            $('.mon_body').css({ 'display': 'revert' });

            // Initialize Ace Editor
            this.editor = ace.edit("python-editor");
            this.editor.setTheme("ace/theme/solarized_light");
            this.editor.session.setMode("ace/mode/python");
            this.editor.setOptions({
                fontSize: "18px",
                enableBasicAutocompletion: true,
                enableSnippets: true,
                enableLiveAutocompletion: true,
                showPrintMargin: false,
                wrap: true
            });
            // this.editor.setValue("# Write your Python code here\n\ndef clean_data(input_data):\n    # Example data cleaning function\n    return input_data\n");
            this.QueryForm.name = $_GET["table_name"];
            this.getTableData();
        },
        methods: {
            generalSearch() { },
            selectChange() {
                this.currentPage = 1; // Reset to first page when field changes
                this.getTableData();
            },
            getTableData() {
                let that = this;
                let param = {
                    "table_name": that.QueryForm.name,
                    "field_name": that.QueryForm.field_name,
                    "page": that.currentPage,
                    "page_size": that.pageSize
                };
                axios.post(server_url + '/gather_api/go_to_clean',
                    JSON.stringify(param), {
                    headers: { 'Content-Type': "application/json;charset=utf-8" },
                    timeout: 60000
                }).then(function (res) {
                    const data = res.data.data
                    console.log(data);
                    that.field_list = data.field_list;
                    that.data_list = data.data_list;
                    // Update totalItems for pagination (assuming the API returns total count)
                    that.totalItems = data.total || data.data_list.length;
                }).catch((error) => {
                    console.error("Error fetching table data:", error);
                    that.$message.error('Failed to fetch table data');
                });
            },
            previewData() {
                let that = this;
                // Get the code from Ace Editor
                const codeContent = this.editor.getValue();
                let param = {
                    "table_name": that.QueryForm.name,
                    "field": that.QueryForm.field_name,
                    "data_list": that.data_list,
                    "code_content": codeContent
                };
                axios.post(server_url + '/gather_api/clean_code_preview',
                    JSON.stringify(param), {
                    headers: { 'Content-Type': "application/json;charset=utf-8" },
                    timeout: 60000
                }).then(function (res) {
                    const data = res.data;
                    if(data.code == 0){
                     that.$message.success(data.message)
                    that.result_data_list = data.result_data_list || [];
                    }
                   
                }).catch((error) => {
                    console.error("Error fetching preview data:", error);
                    that.$message.error('Failed to fetch preview data');
                });
            },
            cleanData() {
                 let that = this;
                 if(!that.QueryForm.code_name){
                    that.$message.error('请先填写清洗程序名称！')
                 }
                 const codeContent = this.editor.getValue();
                let param = {
                 
                    "table_name":that.QueryForm.name,
                    "field": that.QueryForm.field_name,
                    "code_content":codeContent,
                    code_name:that.QueryForm.code_name
                };
                axios.post(server_url + '/gather_api/configure_clean_code',
                    JSON.stringify(param), {
                    headers: { 'Content-Type': "application/json;charset=utf-8" },
                    timeout: 60000
                }).then(function (res) {
                    const data = res.data;
                   if(data.code == 0){
                    that.$message.success('保存成功')
                   }
                })
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1; // Reset to first page when page size changes
                this.getTableData();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getTableData();
            },
     handleMenuSelect(index) {
    if (index === 'cleanTool') {
      window.location.href = '/cleanTool'; // 跳转到对应的页面
    } else if (index === 'classiFication') {
      window.location.href = '/classiFication';
    } else if (index === 'relationship') {
      window.location.href = '/relationship';
    } else if (index === 'home') {
      window.location.href = '/'; // 跳转到首页
    } else if (index === 'qualitycontrol') {
      window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
    } 
    this.menuActive = index; // 更新高亮状态
  },
        }
    });
</script>
</body>

</html>