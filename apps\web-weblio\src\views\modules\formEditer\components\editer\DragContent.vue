<script lang="ts">
import { EventBus, offEvent, onEvent } from '#/utils/eventBus.ts';
import FormItemRender from '#/views/modules/formEditer/components/render/FormItemRender.vue';
import { DeleteIcon, Icon } from 'tdesign-icons-vue-next';
import { Form } from 'tdesign-vue-next';
import { ref } from 'vue';
import draggable from 'vuedraggable';

export default {
  name: 'DragContent',
  components: {
    Form,
    Icon,
    DeleteIcon,
    FormItemRender,
    Draggable: draggable,
  },
  props: {
    eventPrefix: {
      type: String,
      default: '',
    },
    modelValue: {
      type: Array,
      default: () => [],
    },
  },

  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const formData = ref({});
    const selectedTag = ref();
    const list = ref(props.modelValue || []);
    const tagMoveStart = (item) => {};
    const defHandler = {
      widgetList_itemClick: (data) => {
        if (data) {
          list.value.push(data);
          selectedTag.value = data;
          // formData.value = {};
          list.value.forEach((item) => {
            formData.value[item.schema.name] = item.schema.value;
          });
          // emit('update:modelValue', list.value);
        }
      },
      formEditer_itemChange: (data) => {
        if (data) {
          const index = list.value.findIndex((i) => i.id === data.id);
          if (index != -1) {
            list.value[index] = data;
          }
          list.value.forEach((item) => {
            formData.value[item.schema.name] = item.schema.value;
          });
          // emit('update:modelValue', list.value);
        }
      },
    };
    const eventHandler = {} as any;
    const makeEventHandler = () => {
      Object.keys(defHandler).forEach((item) => {
        eventHandler[`${props.eventPrefix}${item}`] = defHandler[item];
      });
    };

    const tagClone = (item) => {
      selectedTag.value = item;
    };
    const tagMoveEnd = () => {
      // emit('update:modelValue', list.value);
    };
    const itemClick = (item) => {
      if (item) {
        selectedTag.value = item;
        setTimeout(() => {
          EventBus.emit(`${props.eventPrefix}formEditer_itemClick`, {
            ...item,
            syncOptions: item.syncOptions || {},
          });
        }, 0);
      }
    };
    const removeItem = (event, item, index) => {
      // 关闭事件冒泡
      event.e.stopPropagation();
      list.value.splice(index, 1);
      EventBus.emit(`${props.eventPrefix}formEditer_itemDelete`);
    };

    return {
      tagMoveStart,
      tagClone,
      selectedTag,
      list,
      tagMoveEnd,
      removeItem,
      makeEventHandler,
      eventHandler,
      itemClick,
      formData,
    };
  },
  computed: {
    dragOptions() {
      return {
        animation: 300,
        group: 'dbox',
        disabled: false,
        ghostClass: 'ghost',
      };
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        this.list.value = [...val];
      },
      deep: true,
    },
    list: {
      handler(val) {
        this.$emit('update:modelValue', val);
      },
      deep: true,
    },
  },
  mounted() {
    this.itemClick(this.list?.[0]);
    this.makeEventHandler();
    onEvent(this.eventHandler);
  },
  unmounted() {
    this.makeEventHandler();
    offEvent(this.eventHandler);
  },
};
</script>

<template>
  <Form v-model="formData">
    <Draggable
      :list="list"
      class="grid w-full grid-cols-1 gap-1"
      item-key="id"
      v-bind="dragOptions"
      :clone="tagClone"
      @end="tagMoveEnd"
      @start="tagMoveStart"
    >
      <template #item="{ element, index }">
        <div
          :key="element.id"
          :class="`${selectedTag?.id == element?.id ? 'bg-accent border-blue-400' : ''} hover:bg-accent flex cursor-pointer items-center justify-between border border-dashed p-2`"
          style="position: relative"
          @click="itemClick(element)"
        >
          <FormItemRender
            v-model="formData"
            :config="element"
            :is-edit="true"
            class="w-full"
          />
          <DeleteIcon
            v-if="selectedTag?.id === element?.id"
            class="ml-2 flex h-full items-center justify-center text-red-600"
            name="delete"
            @click="removeItem($event, element, index)"
          />
          <!--          </div>-->
          <!--        id:{{ element.id }},name:{{ element.name }}-->
        </div>
      </template>
    </Draggable>
  </Form>
</template>

<style scoped></style>
