<script>
import nodeWrap from './nodeWrap.vue';
import useSelect from './select.vue';

export default {
  components: {
    NodeWrap: nodeWrap,
    UseSelect: useSelect,
  },
  provide() {
    return {
      select: this.selectHandle,
    };
  },
  props: {
    modelValue: { type: Object, default: () => {} },
  },
  data() {
    return {
      nodeConfig: this.modelValue,
      selectVisible: false,
    };
  },
  watch: {
    modelValue(val) {
      this.nodeConfig = val;
    },
    nodeConfig(val) {
      this.$emit('update:modelValue', val);
    },
    deep: true,
  },
  mounted() {},
  methods: {
    selectHandle(type, data) {
      this.selectVisible = true;
      // this.$refs.useselect.open(type, data);
      this.$nextTick(() => {
        this.$refs.useselect.open(type, data);
      });
    },
  },
};
</script>

<template>
  <div class="sc-workflow-design">
    <div class="box-scale">
      <NodeWrap v-if="nodeConfig" v-model="nodeConfig" />
      <div class="end-node">
        <div class="end-node-circle"></div>
        <div class="end-node-text">流程结束</div>
      </div>
    </div>
    <UseSelect
      v-show="selectVisible"
      ref="useselect"
      @closed="selectVisible = false"
    />
  </div>
</template>

<style lang="scss">
.sc-workflow-design {
  width: 100%;
}

.sc-workflow-design .box-scale {
  position: relative;
  display: inline-block;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  min-width: min-content;
}

.sc-workflow-design {
  .node-wrap {
    position: relative;
    z-index: 1;
    display: inline-flex;
    flex-flow: column wrap;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    padding: 0;
  }

  .node-wrap-box {
    position: relative;
    display: inline-flex;
    flex-shrink: 0;
    flex-direction: column;
    width: 220px;
    min-height: 72px;
    cursor: pointer;
    background: rgb(255 255 255);
    border-radius: 4px;
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 10%);
  }

  .node-wrap-box::before {
    position: absolute;
    top: -12px;
    left: 50%;
    width: 0;
    content: '';
    border-color: rgb(202 202 202) transparent transparent;
    border-style: solid;
    border-width: 8px 6px 4px;
    transform: translateX(-50%);
  }

  .node-wrap-box.start-node::before {
    content: none;
  }

  .node-wrap-box .title {
    position: relative;
    display: flex;
    align-items: center;
    height: 24px;
    padding-right: 30px;
    padding-left: 16px;
    line-height: 24px;
    color: #fff;
    border-radius: 4px 4px 0 0;
  }

  .node-wrap-box .title .icon {
    margin-right: 5px;
  }

  .node-wrap-box .title .close {
    position: absolute;
    top: 50%;
    right: 10px;
    display: none;
    font-size: 15px;
    transform: translateY(-50%);
  }

  .node-wrap-box .content {
    position: relative;
    padding: 15px;
  }

  .node-wrap-box .content .placeholder {
    color: #999;
  }

  .node-wrap-box:hover .close {
    display: block;
  }

  .add-node-btn-box {
    position: relative;
    z-index: 1;
    display: inline-flex;
    flex-shrink: 0;
    width: 240px;
  }

  .add-node-btn-box::before {
    position: absolute;
    inset: 0;
    z-index: -1;
    width: 2px;
    height: 100%;
    margin: auto;
    content: '';
    background-color: rgb(202 202 202);
  }

  .add-node-btn {
    display: flex;
    flex-grow: 1;
    flex-shrink: 0;
    justify-content: center;
    width: 240px;
    padding: 20px 0 32px;
    user-select: none;
  }

  .add-node-btn span {
  }

  .add-branch {
    position: absolute;
    top: -16px;
    left: 50%;
    z-index: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    transform: translateX(-50%);
    transform-origin: center center;
  }

  .branch-wrap {
    display: inline-flex;
    width: 100%;
  }

  .branch-box-wrap {
    display: flex;
    flex-shrink: 0;
    flex-flow: column wrap;
    align-items: center;
    width: 100%;
    min-height: 270px;
  }

  .col-box {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
  }

  .branch-box {
    position: relative;
    display: flex;
    height: auto;
    min-height: 180px;
    margin-top: 15px;
    overflow: visible;
    border-top: 2px solid #ccc;
    border-bottom: 2px solid #ccc;
  }

  .branch-box .col-box::before {
    position: absolute;
    inset: 0;
    z-index: 0;
    width: 2px;
    height: 100%;
    margin: auto;
    content: '';
    background-color: rgb(202 202 202);
  }

  .condition-node {
    display: inline-flex;
    flex-direction: column;
    min-height: 220px;
  }

  .condition-node-box {
    position: relative;
    display: inline-flex;
    flex-grow: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 30px;
    padding-right: 50px;
    padding-left: 50px;
  }

  .condition-node-box::before {
    position: absolute;
    inset: 0;
    width: 2px;
    height: 100%;
    margin: auto;
    content: '';
    background-color: rgb(202 202 202);
  }

  .auto-judge {
    position: relative;
    width: 220px;
    min-height: 72px;
    padding: 15px;
    cursor: pointer;
    background: rgb(255 255 255);
    border-radius: 4px;
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 10%);
  }

  .auto-judge::before {
    position: absolute;
    top: -12px;
    left: 50%;
    width: 0;
    content: '';
    background: rgb(239 239 239);
    border-color: rgb(202 202 202) transparent transparent;
    border-style: solid;
    border-width: 8px 6px 4px;
    transform: translateX(-50%);
  }

  .auto-judge .title {
    line-height: 16px;
  }

  .auto-judge .title .node-title {
    color: #15bc83;
  }

  .auto-judge .title .close {
    position: absolute;
    top: 15px;
    right: 15px;
    display: none;
    font-size: 15px;
    color: #999;
  }

  .auto-judge .title .priority-title {
    position: absolute;
    top: 15px;
    right: 15px;
    color: #999;
  }

  .auto-judge .content {
    position: relative;
    padding-top: 15px;
  }

  .auto-judge .content .placeholder {
    color: #999;
  }

  .auto-judge:hover {
    .close {
      display: block;
    }

    .priority-title {
      display: none;
    }
  }

  .top-left-cover-line,
  .top-right-cover-line {
    position: absolute;
    top: -3px;
    width: 50%;
    height: 5px;
    background-color: hsl(var(--background-deep));
  }

  .bottom-left-cover-line,
  .bottom-right-cover-line {
    position: absolute;
    bottom: -3px;
    width: 50%;
    height: 5px;
    background-color: hsl(var(--background-deep));
  }

  .top-left-cover-line {
    left: -1px;
  }

  .top-right-cover-line {
    right: -1px;
  }

  .bottom-left-cover-line {
    left: -1px;
  }

  .bottom-right-cover-line {
    right: -1px;
  }

  .end-node {
    font-size: 14px;
    color: rgb(25 31 37 / 40%);
    text-align: left;
    border-radius: 50%;
  }

  .end-node-circle {
    width: 10px;
    height: 10px;
    margin: auto;
    background: #ccc;
    border-radius: 50%;
  }

  .end-node-text {
    margin-top: 5px;
    text-align: center;
  }

  .auto-judge:hover {
    .sort-left {
      display: flex;
    }

    .sort-right {
      display: flex;
    }
  }

  .auto-judge .sort-left {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .auto-judge .sort-right {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .auto-judge .sort-left:hover,
  .auto-judge .sort-right:hover {
    background: #eee;
  }

  .auto-judge::after {
    position: absolute;
    inset: 0;
    z-index: 2;
    pointer-events: none;
    content: '';
    border-radius: 4px;
    transition: all 0.1s;
  }

  .auto-judge:hover::after {
    border: 1px solid #3296fa;
    box-shadow: 0 0 6px 0 rgb(50 150 250 / 30%);
  }

  .node-wrap-box::after {
    position: absolute;
    inset: 0;
    z-index: 2;
    pointer-events: none;
    content: '';
    border-radius: 4px;
    transition: all 0.1s;
  }

  .node-wrap-box:hover::after {
    border: 1px solid #3296fa;
    box-shadow: 0 0 6px 0 rgb(50 150 250 / 30%);
  }
}

.tags-list {
  width: 100%;
  margin-top: 15px;
}

.add-node-popover-body {
}

.add-node-popover-body li {
  display: inline-block;
  width: 80px;
  padding: 10px 0;
  text-align: center;
}

.add-node-popover-body li i {
  width: 40px;
  height: 40px;
  font-size: 18px;
  line-height: 38px;
  text-align: center;
  cursor: pointer;
  border: 1px solid var(--brand-main);
  border-radius: 50%;
}

.add-node-popover-body li i:hover {
  color: #fff !important;
  background: #3296fa;
  border: 1px solid #3296fa;
}

.add-node-popover-body li p {
  margin-top: 5px;
  font-size: 12px;
}

.node-wrap-drawer__title {
  padding-right: 40px;
}

.node-wrap-drawer__title label {
  cursor: pointer;
}

.node-wrap-drawer__title label:hover {
  border-bottom: 1px dashed #409eff;
}

.node-wrap-drawer__title .node-wrap-drawer__title-edit {
  margin-left: 10px;
  color: #409eff;
  vertical-align: middle;
}

.dark .sc-workflow-design {
  .node-wrap-box,
  .auto-judge {
    background: #2b2b2b;
  }

  .col-box {
    background: var(--background-deep);
  }

  .top-left-cover-line,
  .top-right-cover-line,
  .bottom-left-cover-line,
  .bottom-right-cover-line {
    background-color: hsl(var(--background-deep));
  }

  .node-wrap-box::before,
  .auto-judge::before {
    background-color: var(--background-deep);
  }

  .branch-box .add-branch {
    background: var(--brand-main);
  }

  .end-node .end-node-text {
    color: #ccc;
  }

  .auto-judge .sort-left:hover,
  .auto-judge .sort-right:hover {
    background: var(--background-deep);
  }
}
</style>
