// 高级3D性能优化模块

export interface MoleculeComplexity {
  atomCount: number;
  bondCount: number;
  ringCount: number;
  complexity: 'complex' | 'medium' | 'simple' | 'very_complex';
}

export interface RenderSettings {
  renderMode: 'cartoon' | 'sphere' | 'stick' | 'wireframe';
  quality: 'high' | 'low' | 'medium';
  enableAnimation: boolean;
  maxFPS: number;
}

// 分子渲染缓存
class MoleculeCache {
  private cache = new Map<string, any>();
  private maxSize = 50; // 最大缓存50个分子

  clear() {
    this.cache.clear();
  }

  get(smiles: string) {
    const item = this.cache.get(smiles);
    if (item) {
      // 更新访问时间
      item.timestamp = Date.now();
      return item;
    }
    return null;
  }

  set(smiles: string, data: any) {
    if (this.cache.size >= this.maxSize) {
      // 删除最旧的缓存项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(smiles, {
      ...data,
      timestamp: Date.now(),
    });
  }

  size() {
    return this.cache.size;
  }
}

// 帧率控制器
class FrameRateController {
  private frameInterval: number;
  private lastRenderTime = 0;

  constructor(maxFPS: number = 30) {
    this.frameInterval = 1000 / maxFPS;
  }

  setMaxFPS(fps: number) {
    this.frameInterval = 1000 / fps;
  }

  shouldRender(): boolean {
    const now = Date.now();
    if (now - this.lastRenderTime >= this.frameInterval) {
      this.lastRenderTime = now;
      return true;
    }
    return false;
  }
}

// 分子复杂度分析器
export const analyzeMoleculeComplexity = (
  molBlock: string,
): MoleculeComplexity => {
  const lines = molBlock.split('\n');
  const countsLine = lines.find(
    (line) => line.trim().length > 0 && !line.startsWith('M  '),
  );

  let atomCount = 0;
  let bondCount = 0;

  if (countsLine) {
    const parts = countsLine.trim().split(/\s+/);
    if (parts.length >= 2) {
      atomCount = Number.parseInt(parts[0] || '0', 10) || 0;
      bondCount = Number.parseInt(parts[1] || '0', 10) || 0;
    }
  }

  // 估算环的数量（简化算法）
  const ringCount = Math.max(0, bondCount - atomCount + 1);

  // 根据原子数量判断复杂度
  let complexity: MoleculeComplexity['complexity'];
  if (atomCount <= 20) {
    complexity = 'simple';
  } else if (atomCount <= 50) {
    complexity = 'medium';
  } else if (atomCount <= 100) {
    complexity = 'complex';
  } else {
    complexity = 'very_complex';
  }

  return {
    atomCount,
    bondCount,
    ringCount,
    complexity,
  };
};

// 根据复杂度获取最佳渲染设置
export const getOptimalRenderSettings = (
  complexity: MoleculeComplexity,
): RenderSettings => {
  switch (complexity.complexity) {
    case 'complex': {
      return {
        renderMode: 'wireframe',
        quality: 'medium',
        enableAnimation: false,
        maxFPS: 30,
      };
    }
    case 'medium': {
      return {
        renderMode: 'stick',
        quality: 'medium',
        enableAnimation: true,
        maxFPS: 45,
      };
    }
    case 'simple': {
      return {
        renderMode: 'stick',
        quality: 'high',
        enableAnimation: true,
        maxFPS: 60,
      };
    }
    case 'very_complex': {
      return {
        renderMode: 'wireframe',
        quality: 'low',
        enableAnimation: false,
        maxFPS: 20,
      };
    }
    default: {
      return {
        renderMode: 'stick',
        quality: 'medium',
        enableAnimation: true,
        maxFPS: 30,
      };
    }
  }
};

// 全局实例
export const moleculeCache = new MoleculeCache();

// 3DMol高级性能优化器
export class Advanced3DOptimizer {
  private frameController: FrameRateController;
  private isOptimized = false;
  private originalRender: any;
  private viewer: any;

  constructor(viewer: any, maxFPS: number = 30) {
    this.viewer = viewer;
    this.frameController = new FrameRateController(maxFPS);
    this.originalRender = viewer.render.bind(viewer);
  }

  // 内存优化
  private enableMemoryOptimization() {
    // 定期清理不必要的资源
    setInterval(() => {
      try {
        if (this.viewer.dispose) {
          // 清理未使用的纹理和几何体
          this.viewer.dispose();
        }
      } catch (error) {
        console.warn('内存清理错误:', error);
      }
    }, 30_000); // 每30秒清理一次
  }

  // 启用视口裁剪
  private enableViewportCulling() {
    try {
      if (this.viewer.enableCulling) {
        this.viewer.enableCulling(true);
      }
    } catch (error) {
      console.warn('视口裁剪不支持:', error);
    }
  }

  // 禁用优化
  disable() {
    if (this.isOptimized && this.viewer) {
      this.viewer.render = this.originalRender;
      this.isOptimized = false;
    }
  }

  // 应用高级优化
  optimize() {
    if (this.isOptimized || !this.viewer) return;

    // 帧率限制
    this.viewer.render = () => {
      if (this.frameController.shouldRender()) {
        this.originalRender();
      }
    };

    // 视口裁剪优化
    this.enableViewportCulling();

    // 内存管理
    this.enableMemoryOptimization();

    this.isOptimized = true;
  }

  // 设置帧率
  setMaxFPS(fps: number) {
    this.frameController.setMaxFPS(fps);
  }
}

// 分子简化处理器
export const simplifyMolecule = (
  molBlock: string,
  complexity: MoleculeComplexity,
): string => {
  // 对于非常复杂的分子，可以进行简化处理
  if (complexity.complexity === 'very_complex') {
    // 这里可以实现分子简化算法
    // 比如：移除氢原子、合并简单基团等
    console.warn('应用分子简化处理');
  }

  return molBlock; // 暂时返回原始数据
};

// 预加载常用分子
export const preloadCommonMolecules = async () => {
  const commonSMILES = [
    'CCO', // 乙醇
    'O', // 水
    'C', // 甲烷
    'C1=CC=CC=C1', // 苯
    'CC(=O)O', // 乙酸
  ];

  // 这里可以预加载并缓存常用分子
  console.warn('预加载常用分子:', commonSMILES.length);
};

// 渲染模式管理器
export class RenderModeManager {
  private currentMode: RenderSettings['renderMode'] = 'stick';
  private viewer: any;

  constructor(viewer: any) {
    this.viewer = viewer;
  }

  private getRadiusForComplexity(complexity: MoleculeComplexity): number {
    switch (complexity.complexity) {
      case 'complex': {
        return 0.1;
      }
      case 'medium': {
        return 0.12;
      }
      case 'simple': {
        return 0.15;
      }
      case 'very_complex': {
        return 0.08;
      }
      default: {
        return 0.12;
      }
    }
  }

  private getScaleForComplexity(complexity: MoleculeComplexity): number {
    switch (complexity.complexity) {
      case 'complex': {
        return 0.2;
      }
      case 'medium': {
        return 0.25;
      }
      case 'simple': {
        return 0.3;
      }
      case 'very_complex': {
        return 0.15;
      }
      default: {
        return 0.25;
      }
    }
  }

  // 切换渲染模式
  setRenderMode(
    mode: RenderSettings['renderMode'],
    complexity: MoleculeComplexity,
  ) {
    if (!this.viewer || this.currentMode === mode) return;

    this.currentMode = mode;

    try {
      switch (mode) {
        case 'cartoon': {
          {
            this.viewer.setStyle(
              {},
              {
                cartoon: {
                  color: 'spectrum',
                  thickness: 0.5,
                },
              },
            );
            break;
          }
          break;
        }

        case 'sphere': {
          {
            this.viewer.setStyle(
              {},
              {
                sphere: {
                  scale: this.getScaleForComplexity(complexity),
                  colorscheme: 'default',
                },
              },
            );
            break;
          }
          break;
        }

        case 'stick': {
          {
            this.viewer.setStyle(
              {},
              {
                stick: {
                  radius: this.getRadiusForComplexity(complexity),
                  colorscheme: 'default',
                },
              },
            );
            break;
          }
          break;
        }

        case 'wireframe': {
          this.viewer.setStyle(
            {},
            {
              line: {
                linewidth: 2,
                colorscheme: 'default',
              },
            },
          );
          break;
        }
      }

      this.viewer.render();
    } catch (error) {
      console.warn('渲染模式切换失败:', error);
    }
  }
}

// 性能监控器
export class PerformanceMonitor {
  private fpsHistory: number[] = [];
  private frameCount = 0;
  private startTime = 0;

  getAverageFPS(): number {
    if (this.fpsHistory.length === 0) return 0;

    const sum = this.fpsHistory.reduce((a, b) => a + b, 0);
    return Math.round(sum / this.fpsHistory.length);
  }

  getCurrentPerformance(): 'excellent' | 'fair' | 'good' | 'poor' {
    const avgFPS = this.getAverageFPS();

    if (avgFPS >= 45) return 'excellent';
    if (avgFPS >= 30) return 'good';
    if (avgFPS >= 20) return 'fair';
    return 'poor';
  }

  recordFrame() {
    this.frameCount++;

    const elapsed = Date.now() - this.startTime;
    if (elapsed >= 1000) {
      // 每秒计算一次FPS
      const fps = Math.round((this.frameCount * 1000) / elapsed);
      this.fpsHistory.push(fps);

      // 只保留最近10次的FPS记录
      if (this.fpsHistory.length > 10) {
        this.fpsHistory.shift();
      }

      this.start(); // 重新开始计算
      return fps;
    }

    return null;
  }

  start() {
    this.startTime = Date.now();
    this.frameCount = 0;
  }
}
