<script setup lang="ts">
import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import DictItemTable from '#/views/_sys/base-data/dict/components/DictItemTable.vue';
import EditForm from '#/views/_sys/base-data/dict/components/EditForm.vue';
import IndexTable from '#/views/_sys/base-data/dict/components/IndexTable.vue';

const indexTable = ref();
const [EditFormModle, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: EditForm,
});
const [DictItemTableModel, dictItemModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: DictItemTable,
});
const add = () => {
  modalApi.setState({ title: '新增' });
  modalApi.setData({ refresh: indexTable.value.loadData });
  modalApi.open();
};

const edit = (record: any) => {
  modalApi.setState({ title: '编辑' });
  modalApi.setData({ record, refresh: indexTable.value.loadData });
  modalApi.open();
};
const showDictItem = (record: any) => {
  dictItemModalApi.setData({
    tagObj: record,
  });
  dictItemModalApi.open();
};
</script>

<template>
  <Page description="基础数据字典" title="字典列表">
    <IndexTable
      ref="indexTable"
      @add="add"
      @edit="edit"
      @show-dict-item="showDictItem"
    />
    <EditFormModle />
    <DictItemTableModel :is-search-form="false" />
  </Page>
</template>

<style scoped></style>
