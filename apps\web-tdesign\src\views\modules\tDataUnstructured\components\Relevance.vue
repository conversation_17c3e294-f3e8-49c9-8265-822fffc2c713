<script setup lang="tsx">
import {defineEmits, defineProps, onMounted, reactive, ref} from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {
  Button,
  Card, Cascader,
  Input,
  Link, MessagePlugin, type PageInfo,
  Popconfirm,
  Select,
  Space,
  Table,
  Upload
} from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import {
  deleteAttachBatch,
  editItemListByPage,
  editItemListByPageImage,
  save,
  saveAttach
} from '../api.ts';
import {DownloadIcon, Icon, RefreshIcon, SearchIcon, UploadIcon} from "tdesign-icons-vue-next";
import {getCategorys, getDatasetList, getLabelList} from "#/views/modules/tData/api.ts";
import {BaseTableConfig, Pagination} from "#/utils/constant.ts";
import {removalUnderline} from "#/utils/sort.ts";
import {baseDownloadFile, baseDownloadFileGet, baseUploadFile, getDictItems} from "#/api";
import {useAccessStore} from "@vben/stores";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      loadData: Function,
    },
    default: null,
  },
});
const tableConfig = ref(BaseTableConfig);
const data: any = ref([]);
const selectedRowKeys = ref([]);
const pagination: any = ref(Pagination);
const loading = ref(false);
const sort = ref([]);
const dataForm = ref();
const formData = ref({});
const FORM_RULES: FormProps['rules'] = {
  classCode: [
    {
      required: true,
      message: '请选择分级',
    },
  ],
  categoryCode: [
    {
      required: true,
      message: '请选择分类',
    },
  ],
  datasetCode: [
    {
      required: true,
      message: '请选择数据集',
    },
  ],
  remark: [
    {
      required: true,
      message: '请输入文件别名',
    },
  ],
  operationFlag: [
    {
      required: true,
      message: '请选择资源类型',
    },
  ],
};
const columns: any = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
  },
  {
    colKey: 'fileName',
    title: '文件名',
    ellipsis: true,
    sorter: false,
  },
  {
    colKey: 'createTime',
    title: '上传时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: 200,
    fixed: 'center',
  },
]);
const state = reactive({
  tagObj: {},
});
const reqRunner = {
  save: useRequest(saveAttach, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      modalApi.close();
    },
  }),
  upload: useRequest(baseUploadFile, {
    manual: true,
    onError: () => {
    },
    onSuccess: (res: any) => {
      loadImgData();
    },
  }),
  editItemListByPage: async (params: any) => {
    loading.value = true;
    try {
      const {records, total} = await editItemListByPageImage(params);
      data.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
  down: useRequest(baseDownloadFileGet, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      selectedRowKeys.value = [];
      loadImgData();
    },
  }),
  dataExportBatch: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      selectedRowKeys.value = [];
      loadImgData();
    },
  }),
};
const loadImgData = async () => {
  const params = {
    param: {
      pcode: formData.value.fileCode,
    },
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await reqRunner.editItemListByPage(params);
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
const [Modal, modalApi] = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (isOpen) {
      open(modalApi.getData()?.tagObj);
    }
  },
  onConfirm: async () => {
    const vali = await dataForm.value.validate();
    if (vali === true) {
      let param = {
        categoryCode: JSON.stringify(formData.value.categoryCode),
        labelCode: JSON.stringify(formData.value.labelCode)
      }
      if (relevanceFile.value.length > 0) {
        formData.value.relevancePath = relevanceFile.value[0].path
        formData.value.relevanceName = relevanceFile.value[0].name
      }
      if (describeFile.value.length > 0) {
        formData.value.describePath = describeFile.value[0].path
        formData.value.describeName = describeFile.value[0].name
      }
      reqRunner.save.run({...formData.value, ...param});
    }
  },
  onCancel: () => {
    modalApi.close();
  },
});
const open = async (data?: any) => {
  if (data) {
    isMd.value = false;
    formData.value = data
    categorys.value = await getCategorys(formData.value.classCode);
    datasets.value = await getDatasetList({categoryList:data.categoryCode});
    if(formData.value.relevanceFile){
      relevanceFile.value = JSON.parse(formData.value.relevanceFile)
    }else{
      relevanceFile.value = []
    }
    if(formData.value.describeFile){
      describeFile.value = JSON.parse(formData.value.describeFile)
    }else{
      describeFile.value = []
    }
    if(data.fileType == 'md'){
      loadImgData()
      isMd.value = true
    }else{
      data.value = []
    }
  }
};
const isMd = ref(false);
const classess = ref();
const operationFlags = ref();
const categorys = ref();
const labelses = ref();
const changeClass = async () => {
  formData.value.categoryCode = []
  categorys.value = []
  //获取分类列表
  categorys.value = await getCategorys(formData.value.classCode);
}
const handleRowClick = (record: any) => {
};

const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadImgData();
};
const selectedRows = ref([])
const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRows.value = ctx.selectedRowData
  selectedRowKeys.value = value;
};
const sortChange = (val: any) => {
  sort.value = val;
  loadImgData();
};
const accessStore = useAccessStore();
const suffix = ref('.md,.txt')
const beforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!suffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
    return false;
  }
  return true;
};
const suffixImg = ref('.png,.svg,.jpeg,.jpg')
const beforeUploadImg = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!suffixImg.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+suffixImg.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+suffixImg.value+"的文件")
    return false;
  }
  return true;
};
const relevanceFile = ref([])
const relevanceUploadSuccess = (context: { fileList: any[] }) => {
  relevanceFile.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
const describeFile = ref([])
const describeUploadSuccess = (context: { fileList: any[] }) => {
  describeFile.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
onMounted(async () => {
  //数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  classess.value.forEach((op) => {
    if(op.value == 0 || op.value == 1){
      op["disabled"] = true;
    }
  })
  operationFlags.value = await getDictItems('UNSTRUCTURED_DATA_TYPE');
  //标签库
  labelses.value = await getLabelList();
});
defineExpose({
  open,
});

const add = async (...args: any) => {
  await reqRunner.upload.run('/tSysAttach/uploadImg', {
    pCode: formData.value.fileCode,
    operationCode: formData.value.operationCode,
    file: args[0].raw ? args[0].raw : args[0][0].raw,
  });
};
const removeBatch = async () => {
  const ids = selectedRowKeys.value.join(',');
  await deleteAttachBatch(ids);
  selectedRowKeys.value = [];
  selectedRows.value = {}
  MessagePlugin.success("删除成功")
  loadImgData();
};
const remove = async (row: any) => {
  await deleteAttachBatch(row.id);
  MessagePlugin.success("删除成功")
  loadImgData();
};
const down = async (row: any) => {
  const {run} = reqRunner.down;
  run(`/file/download`, {path: row.filePath});
};
const downBatch = async () => {
  formData.value.ids_list = selectedRowKeys.value
  const {run} = reqRunner.dataExportBatch;
  run(`/tSysAttach/downAttachBatch`, {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  });
};
const handleTipClick = async () => {
  const fileUrl = '/描述文件模板.md';
  const link = document.createElement('a');
  link.href = fileUrl;
  link.download = '描述文件模板.md'; // 可选：指定下载后的文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
const datasets = ref([])
const changeCategory = async () => {
  formData.value.datasetCode = ''
  datasets.value = []
  if(formData.value.categoryCode){
    datasets.value = await getDatasetList({categoryList:formData.value.categoryCode.map(item => String(item))});
  }
}
</script>
<template>
  <Modal title="编辑" class="w-[80%]">
    <Card style="background-color: #0071bc1a;margin-bottom: 10px">
      <Form ref="dataForm" :data="formData" class="w-full" label-align="top" :rules="FORM_RULES">
        <div class="mt-5 grid w-full grid-cols-3 gap-10">
          <FormItem label="分级" name="classCode">
            <Select v-model="formData.classCode" :options="classess" clearable placeholder="请选择"
                    :on-change="changeClass"/>
          </FormItem>
          <FormItem label="分类" name="categoryCode">
            <Cascader v-model="formData.categoryCode" :options="categorys" clearable placeholder="请选择"
                      multiple check-strictly value-mode="onlyLeaf" :show-all-levels="false"
                      :min-collapsed-num="1" :on-change="changeCategory"
                      :disabled="formData.classCode != 0 && (formData.classCode == '' || formData.classCode == null)"/>
          </FormItem>
          <FormItem label="数据集" name="datasetCode">
            <Select v-model="formData.datasetCode" :options="datasets" clearable placeholder="请选择"
                    :disabled="formData.categoryCode.length == 0"/>
          </FormItem>
          <FormItem label="标签">
            <Select v-model="formData.labelCode" :options="labelses" multiple clearable placeholder="请选择"/>
          </FormItem>
          <FormItem label="资源类型" name="operationFlag">
            <Select v-model="formData.operationFlag" :options="operationFlags" clearable placeholder="请选择"/>
          </FormItem>
          <FormItem label="文件别名" name="remark">
            <Input v-model="formData.remark" clearable placeholder="请输入"/>
          </FormItem>
          <FormItem v-if="formData.fileType != 'md'" label="关联文件">
            <Upload ref="img" v-model="relevanceFile" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                    action="/rgdc-sys/file/upload" :beforeUpload="beforeUpload" @success="relevanceUploadSuccess"
                    tips="请上传包含该非结构化文件搜索关键词的文本文件(.txt或.md格式)"/>
          </FormItem>
          <FormItem label="描述文件">
            <template #tips>
              <Link theme="primary" @click="handleTipClick"> 模板下载 </Link>
            </template>
            <Upload ref="img" v-model="describeFile" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                    action="/rgdc-sys/file/upload" :beforeUpload="beforeUpload" @success="describeUploadSuccess"/>
          </FormItem>
        </div>
      </Form>
    </Card>
    <Card v-if="isMd">
      <Table v-model:column-controller-visible="tableConfig.columnControllerVisible" :bordered="true"
             :columns="columns" :data="data" :hover="true" :loading="loading"
             :pagination="pagination" :pagination-affixed-bottom="false" :sort="sort" :stripe="true"
             :selected-row-keys="selectedRowKeys" cell-empty-content="-" lazy-load resizable
             row-key="id" table-layout="fixed" @page-change="rehandlePageChange" @sort-change="sortChange"
             @row-click="handleRowClick" @select-change="rehandleSelectChange" v-bind="tableConfig">
        <template #topContent="slotProps">
          <div class="mb-2 mt-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">非结构化数据图片关联文件列表</div>
              <div v-if="selectedRowKeys?.length > 0" class="text-blue-600/80">
                已选择 [{{ selectedRowKeys?.length || 0 }}] 条数据
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <Popconfirm v-if="selectedRowKeys && selectedRowKeys.length > 0" content="确定删除？"
                          theme="danger" @confirm="removeBatch">
                <Button theme="danger">
                  <template #icon>
                    <Icon name="delete"/>
                  </template>
                  批量删除
                </Button>
              </Popconfirm>
              <Button theme="primary" @click="downBatch">
                <template #icon>
                  <DownloadIcon/>
                </template>
                批量下载
              </Button>
            </div>
          </div>
        </template>
        <template #firstFullRow>
          <div class="w-[100%] bg-white p-1">
            <Upload :request-method="add" class="w-[100%]" :beforeUpload="beforeUploadImg">
              <Button class="w-[100%]" theme="primary" variant="dashed">
                <template #icon>
                  <Icon name="upload"/>
                </template>
                批量上传
              </Button>
            </Upload>
          </div>
        </template>
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="down(slotProps.row)">下载</Link>
            <Popconfirm content="确定删除？" theme="warning" @confirm="remove(slotProps.row)">
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
            <Upload :request-method="add" :multiple="true" :beforeUpload="beforeUploadImg">
              <Button class="w-[100%]" theme="primary" variant="text">
                <template #icon>
                  <Icon name="upload"/>
                </template>
                点击批量上传文件
              </Button>
            </Upload>
          </div>
        </template>
      </Table>
    </Card>
  </Modal>
</template>
<style scoped>
:deep(.t-upload .t-upload__trigger) {
  width: 100%;
}
</style>
