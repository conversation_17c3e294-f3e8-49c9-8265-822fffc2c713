<script setup>
import {
  AddIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  CloseIcon,
  DeleteIcon,
  EditIcon,
} from 'tdesign-icons-vue-next';
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Input, Option, Select } from 'tdesign-vue-next';
</script>
<script>
import addNode from './addNode.vue';

export default {
  components: {
    AddNode: addNode,
    Input,
  },
  props: {
    modelValue: { type: Object, default: () => {} },
  },
  data() {
    return {
      nodeConfig: {},
      drawer: false,
      isEditTitle: false,
      index: 0,
      form: {},
    };
  },
  watch: {
    modelValue() {
      this.nodeConfig = this.modelValue;
    },
  },
  mounted() {
    this.nodeConfig = this.modelValue;
  },
  methods: {
    show(index) {
      this.index = index;
      this.form = {};
      this.form = JSON.parse(
        JSON.stringify(this.nodeConfig.conditionNodes[index]),
      );
      this.drawer = true;
    },
    editTitle() {
      this.isEditTitle = true;
      this.$nextTick(() => {
        this.$refs.nodeTitle.focus();
      });
    },
    saveTitle() {
      this.isEditTitle = false;
    },
    save() {
      this.nodeConfig.conditionNodes[this.index] = this.form;
      this.$emit('update:modelValue', this.nodeConfig);
      this.drawer = false;
    },
    addTerm() {
      const len = this.nodeConfig.conditionNodes.length + 1;
      this.nodeConfig.conditionNodes.push({
        nodeName: `条件${len}`,
        type: 3,
        priorityLevel: len,
        conditionMode: 1,
        conditionList: [],
      });
    },
    delTerm(index) {
      this.nodeConfig.conditionNodes.splice(index, 1);
      if (this.nodeConfig.conditionNodes.length === 1) {
        if (this.nodeConfig.childNode) {
          if (this.nodeConfig.conditionNodes[0].childNode) {
            this.reData(
              this.nodeConfig.conditionNodes[0].childNode,
              this.nodeConfig.childNode,
            );
          } else {
            this.nodeConfig.conditionNodes[0].childNode =
              this.nodeConfig.childNode;
          }
        }
        this.$emit(
          'update:modelValue',
          this.nodeConfig.conditionNodes[0].childNode,
        );
      }
    },
    reData(data, addData) {
      if (data.childNode) {
        this.reData(data.childNode, addData);
      } else {
        data.childNode = addData;
      }
    },
    arrTransfer(index, type = 1) {
      this.nodeConfig.conditionNodes[index] =
        this.nodeConfig.conditionNodes.splice(
          index + type,
          1,
          this.nodeConfig.conditionNodes[index],
        )[0];
      this.nodeConfig.conditionNodes.map((item, index) => {
        item.priorityLevel = index + 1;
      });
      this.$emit('update:modelValue', this.nodeConfig);
    },
    addConditionList(conditionList) {
      conditionList.push({
        label: '',
        field: '',
        operator: '==',
        value: '',
      });
    },
    deleteConditionList(conditionList, index) {
      conditionList.splice(index, 1);
    },
    addConditionGroup() {
      this.addConditionList(
        this.form.conditionList[this.form.conditionList.push([]) - 1],
      );
    },
    deleteConditionGroup(index) {
      this.form.conditionList.splice(index, 1);
    },
    toText(nodeConfig, index) {
      const { conditionList } = nodeConfig.conditionNodes[index];
      if (conditionList && conditionList.length === 1) {
        const text = conditionList
          .map((conditionGroup) =>
            conditionGroup.map(
              (item) => `${item.label}${item.operator}${item.value}`,
            ),
          )
          .join(' 和 ');
        return text;
      } else if (conditionList && conditionList.length > 1) {
        return `${conditionList.length}个条件，或满足`;
      } else {
        return index === nodeConfig.conditionNodes.length - 1
          ? '其他条件进入此流程'
          : false;
      }
    },
  },
};
</script>
<template>
  <div class="branch-wrap">
    <div class="branch-box-wrap">
      <div class="branch-box">
        <Button
          class="add-branch"
          shape="round"
          theme="primary"
          variant="base"
          @click="addTerm"
        >
          添加条件
        </Button>
        <div
          v-for="(item, index) in nodeConfig.conditionNodes"
          :key="index"
          class="col-box"
        >
          <div class="condition-node">
            <div class="condition-node-box">
              <div class="auto-judge" @click="show(index)">
                <div
                  v-if="index !== 0"
                  class="sort-left"
                  @click.stop="arrTransfer(index, -1)"
                >
                  <ArrowLeftIcon />
                </div>
                <div class="title">
                  <span class="node-title">{{ item.nodeName }}</span>
                  <span class="priority-title"
                    >优先级{{ item.priorityLevel }}</span
                  >

                  <CloseIcon class="close" @click="delTerm(index)" />
                </div>
                <div class="content">
                  <span v-if="toText(nodeConfig, index)">{{
                    toText(nodeConfig, index)
                  }}</span>
                  <span v-else class="placeholder"> 请设置条件 </span>
                </div>
                <div
                  v-if="index !== nodeConfig.conditionNodes.length - 1"
                  class="sort-right"
                  @click.stop="arrTransfer(index)"
                >
                  <ArrowRightIcon />
                </div>
              </div>
              <AddNode v-model="item.childNode" />
            </div>
          </div>
          <slot v-if="item.childNode" :node="item"></slot>
          <div v-if="index === 0" class="top-left-cover-line"></div>
          <div v-if="index === 0" class="bottom-left-cover-line"></div>
          <div
            v-if="index === nodeConfig.conditionNodes.length - 1"
            class="top-right-cover-line"
          ></div>
          <div
            v-if="index === nodeConfig.conditionNodes.length - 1"
            class="bottom-right-cover-line"
          ></div>
        </div>
      </div>
      <AddNode v-model="nodeConfig.childNode" />
    </div>
    <Drawer
      v-model:visible="drawer"
      :size="660"
      attach="body"
      destroy-on-close
      title="条件设置"
    >
      <template #header>
        <div class="node-wrap-drawer__title">
          <label v-if="!isEditTitle" @click="editTitle">
            {{ form.nodeName }}
            <EditIcon />
          </label>
          <Input
            v-if="isEditTitle"
            ref="nodeTitle"
            v-model="form.nodeName"
            clearable
            @blur="saveTitle"
            @enter="saveTitle"
          />
        </div>
      </template>

      <div style="padding: 0 20px 20px">
        <Alert class="top-tips">满足以下条件时进入当前分支</Alert>
        <template
          v-for="(conditionGroup, conditionGroupIdx) in form.conditionList"
        >
          <div v-if="conditionGroupIdx !== 0" class="or-branch-link-tip">
            或满足
          </div>
          <div class="condition-group-editor">
            <div class="header">
              <span>条件组 {{ conditionGroupIdx + 1 }}</span>
              <div @click="deleteConditionGroup(conditionGroupIdx)">
                <el-icon class="branch-delete-icon"><el-icon-delete /></el-icon>
              </div>
            </div>

            <div class="main-content">
              <!-- 单个条件 -->
              <div class="condition-content-box cell-box">
                <div>描述</div>
                <div>条件字段</div>
                <div>运算符</div>
                <div>值</div>
              </div>
              <div
                v-for="(condition, idx) in conditionGroup"
                :key="idx"
                class="condition-content"
              >
                <div class="condition-relation">
                  <span>{{ idx === 0 ? '当' : '且' }}</span>
                  <div @click="deleteConditionList(conditionGroup, idx)">
                    <DeleteIcon />
                  </div>
                </div>
                <div class="condition-content">
                  <div class="condition-content-box">
                    <Input v-model="condition.label" placeholder="描述" />
                    <Input v-model="condition.field" placeholder="条件字段" />
                    <Select v-model="condition.operator" placeholder="Select">
                      <Option label="等于" value="==" />
                      <Option label="不等于" value="!=" />
                      <Option label="大于" value=">" />
                      <Option label="大于等于" value=">=" />
                      <Option label="小于" value="<" />
                      <Option label="小于等于" value="<=" />
                      <Option label="包含" value="include" />
                      <Option label="不包含" value="notinclude" />
                    </Select>
                    <Input v-model="condition.value" placeholder="值" />
                  </div>
                </div>
              </div>
            </div>
            <div class="sub-content">
              <Button type="primary" @click="addConditionList(conditionGroup)">
                添加条件
              </Button>
            </div>
          </div>
        </template>
        <Button style="width: 100%" type="info" @click="addConditionGroup">
          <template #icon> <AddIcon /></template>
          添加条件组
        </Button>
      </div>
      <template #footer>
        <Button type="primary" @click="save"> 保存 </Button>
        <Button @click="drawer = false">取消</Button>
      </template>
    </Drawer>
  </div>
</template>

<style scoped lang="scss">
.top-tips {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  color: #646a73;
}

.or-branch-link-tip {
  margin: 10px 0;
  color: #646a73;
}

.condition-group-editor {
  position: relative;
  margin-bottom: 16px;
  user-select: none;
  border: 1px solid #e4e5e7;
  border-radius: 4px;

  .branch-delete-icon {
    font-size: 18px;
  }

  .header {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
    color: #171e31;
    background-color: #f4f6f8;

    span {
      flex: 1;
    }
  }

  .main-content {
    padding: 0 12px;

    .condition-relation {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      padding: 0 2px;
      color: #9ca2a9;
    }

    .condition-content-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      div {
        width: 100%;
        min-width: 120px;
      }

      div:not(:first-child) {
        margin-left: 16px;
      }
    }

    .cell-box {
      div {
        width: 100%;
        min-width: 120px;
        padding: 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #909399;
        text-align: center;
      }
    }

    .condition-content {
      display: flex;
      flex-direction: column;

      :deep(.el-input__wrapper) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      .content {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: center;
        min-height: 31.6px;
        padding: 0 0 4px;
      }
    }
  }

  .sub-content {
    padding: 12px;
  }
}
</style>
