<script setup lang="tsx">
import { useVbenDrawer } from '@vben/common-ui';
import { defineProps, reactive, ref } from 'vue';

import IndexTable from './components/IndexTable.vue';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
const tableRef = ref();

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenDrawer({
  footer: false,
  // placement: 'top',
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});
</script>

<template>
  <Modal title="部门成员" class="h-full w-full">
    <IndexTable ref="tableRef" :extend-search-obj="state.tagObj" />
  </Modal>
</template>

<style scoped></style>
