import { requestClient } from '#/api/request';
import { useAccessStore } from '@vben/stores';

export async function chatMessages(data: any) {
  const controllers = new AbortController();
  const signal = controllers.signal;
  const accessStore = useAccessStore();
  return await fetch(
    `${import.meta.env.VITE_GLOB_API_URL}/rgdc-search/dify/chatMessages`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessStore.accessToken}`,
        Accept: 'text/event-stream',
      },
      body: JSON.stringify(data),
    },
    { signal },
  );
}
export async function stopChatMessage(taskId: string, data: any) {
  return requestClient.post<any[]>(
    `/rgdc-search/dify/stopChatMessage/${taskId}`,
    data,
  );
}
