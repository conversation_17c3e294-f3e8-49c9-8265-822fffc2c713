<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { SearchIcon } from 'tdesign-icons-vue-next';
import { Col, Empty, Input, Pagination, Row } from 'tdesign-vue-next';

import { ListByPageApi } from './api';
import PackageCard from './components/process-card/index.vue';
import PackageModal from './components/process-modal/index.vue';

const searchText = ref('');

const data = ref([]);
const pagination = ref({
  current: 1,
  pageSize: 12,
  total: 0,
});

const [PackageModalComp, packageModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: PackageModal,
});

const loadData = async () => {
  const res = await ListByPageApi({
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    param: {
      processName: searchText.value,
    },
  });
  data.value = res.records;
  pagination.value.total = res.total;
};
const search = async () => {
  await loadData();
};

const change = async (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  await loadData();
};

onMounted(async () => {
  await loadData();
});

const openModal = (data: any) => {
  packageModalApi.setState({ title: '发布' });
  packageModalApi.setData({ record: data, refresh: loadData });

  packageModalApi.open();
};
</script>

<template>
  <Page class="h-full">
    <div class="flex h-full w-full flex-col gap-y-[16px]">
      <div class="flex flex-row items-center justify-between">
        <div></div>
        <div class="w-[300px]">
          <Input
            v-model="searchText"
            clearable
            placeholder="请输入搜索流程名称"
            @enter="search"
            @input="search"
          >
            <template #suffix-icon>
              <SearchIcon size="var(--td-comp-size-xxxs)" />
            </template>
          </Input>
        </div>
      </div>

      <div v-if="data.length > 0">
        <Row :gutter="[16, 16]">
          <Col v-for="item in data" :key="item" :lg="4" :xl="3" :xs="6">
            <PackageCard :record="item" @edit="openModal" />
          </Col>
        </Row>
      </div>
      <div v-if="data.length > 0">
        <Pagination
          v-model:page-size="pagination.pageSize"
          :page-size-options="[12, 24, 36]"
          :total="pagination.total"
          @change="change"
        />
      </div>
      <div
        v-if="data.length === 0"
        class="flex h-full flex-col items-center justify-center"
      >
        <Empty align="center" direction="vertical" />
      </div>
    </div>
    <PackageModalComp />
  </Page>
</template>

<style lang="less" scoped></style>
