<script setup lang="ts">
import { ref } from 'vue';
import { Page } from '@vben/common-ui';
import { Button, Upload, Card, Space } from 'tdesign-vue-next';
import { baseDownloadFile, baseUploadFile } from '#/api';

const files: any = ref([]);
const uploadRef: any = ref();
const Export = async () => {
  // 下载Excel
  await baseDownloadFile('/demo/excelExport', {});
};

const Import = async (...args: any) => {
  console.log('参数', args);
  await baseUploadFile('/demo/excelImport', { file: args[0].raw });
};

const handleRequestFail = () => {};
</script>

<template>
  <Page description="Excel导入导出示例" title="Excel">
    <Card>
      <Space direction="vertical">
        <Button @click="Export">导出Excel</Button>

        <Upload
          ref="uploadRef"
          v-model="files"
          :request-method="Import"
          placeholder="导入Excel文件"
          :on-fail="handleRequestFail"
        ></Upload>
      </Space>
    </Card>
  </Page>
</template>
