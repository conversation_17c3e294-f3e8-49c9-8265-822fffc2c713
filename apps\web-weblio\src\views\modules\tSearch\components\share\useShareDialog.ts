import { MessagePlugin } from 'tdesign-vue-next';
import { h, ref } from 'vue';

/**
 * 分享弹窗 hooks
 * @returns openShareDialog, ShareDialog 组件
 */
export function useShareDialog() {
  const visible = ref(false);
  const shareUrl = ref('');

  // 打开弹窗并设置分享链接
  function openShareDialog(url: string) {
    shareUrl.value = url;
    visible.value = true;
  }

  // 关闭弹窗
  function closeShareDialog() {
    visible.value = false;
  }

  // 复制链接
  function copyLink() {
    if (shareUrl.value) {
      navigator.clipboard.writeText(shareUrl.value);
      MessagePlugin.success('链接复制成功');
    }
  }

  // 弹窗组件（TSX语法，需用 h 创建 style 标签）
  const ShareDialog = () =>
    visible.value
      ? h('div', { class: 'share-dialog-mask' }, [
          h('div', { class: 'share-dialog-box' }, [
            h('div', { class: 'share-dialog-title' }, '数据分享链接'),
            h('div', { class: 'share-dialog-content' }, [
              h('input', {
                class: 'share-dialog-input',
                readonly: true,
                value: shareUrl.value,
              }),
              h(
                'button',
                { class: 'share-dialog-copy', onClick: copyLink },
                '复制链接',
              ),
            ]),
            h('div', { class: 'share-dialog-footer' }, [
              h(
                'button',
                { class: 'share-dialog-close', onClick: closeShareDialog },
                '关闭',
              ),
            ]),
          ]),
          h(
            'style',
            null,
            `.share-dialog-mask { position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.3); z-index: 9999; display: flex; align-items: center; justify-content: center; }
            .share-dialog-box { background: #fff; border-radius: 8px; padding: 24px; min-width: 320px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); }
            .share-dialog-title { font-size: 18px; font-weight: bold; margin-bottom: 16px; }
            .share-dialog-content { display: flex; align-items: center; gap: 8px; margin-bottom: 16px; }
            .share-dialog-input { flex: 1; padding: 6px 8px; border: 1px solid #eee; border-radius: 4px; }
            .share-dialog-copy { padding: 6px 12px; background: #0052d9; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
            .share-dialog-footer { text-align: right; }
            .share-dialog-close { padding: 6px 12px; background: #eee; border: none; border-radius: 4px; cursor: pointer; }`,
          ),
        ])
      : null;

  return {
    openShareDialog,
    ShareDialog,
    visible,
    shareUrl,
  };
}
