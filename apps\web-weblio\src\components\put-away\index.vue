<script lang="ts" setup>
import { Icon } from 'tdesign-icons-vue-next';
import { Button } from 'tdesign-vue-next';

const emit: any = defineEmits(['update:modelValue', 'onchange']);
const modelValue = defineModel<boolean>({ required: true });
const toggleAdvanced = () => {
  modelValue.value = !modelValue.value;
  emit('onchange', modelValue.value);
};
</script>

<template>
  <Button v-bind="$attrs" @click="toggleAdvanced">
    <div class="flex items-center">
      {{ modelValue ? '收起' : '展开' }}
      <Icon :name="modelValue ? 'chevron-up' : 'chevron-down'" />
    </div>
  </Button>
</template>
