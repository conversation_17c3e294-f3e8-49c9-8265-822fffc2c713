import { requestClient } from '#/api/request';
import { jsonToUrlParams } from '#/utils';

export const doHttp = async (data, asyncOptions) => {
  if (data?.url) {
    let url = data.url;
    if (data.method === 'GET') {
      url = `${data.url}?${jsonToUrlParams(JSON.parse(data?.payload || '{}'))}`;
    }
    const res = await requestClient[data?.method?.toLowerCase() || 'get']?.<
      any[]
    >(url, data.payload);
    asyncOptions.value = res.map((item) => {
      return {
        value: `${item[data?.value || 'value']}`,
        label: `${item[data?.label || 'label']}`,
      };
    });
  }
};
