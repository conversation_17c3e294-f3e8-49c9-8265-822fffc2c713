# RDKit 浏览器版本文件设置

此目录需要包含 RDKit 的浏览器版本文件以支持分子结构组件。

## 需要的文件

请从 RDKit 官方源下载以下文件并放置在此目录中：

1. **RDKit_minimal.js** - RDKit JavaScript 主文件
2. **RDKit_minimal.wasm** - RDKit WebAssembly 文件

## 下载方法

### 方法1：手动下载
访问以下链接下载文件：
- https://unpkg.com/@rdkit/rdkit/dist/RDKit_minimal.js
- https://unpkg.com/@rdkit/rdkit/dist/RDKit_minimal.wasm

### 方法2：使用 curl 命令

在此目录 (`apps/web-weblio/public/rdkit/`) 中运行：

```bash
# 下载 JavaScript 文件
curl -o RDKit_minimal.js https://unpkg.com/@rdkit/rdkit/dist/RDKit_minimal.js

# 下载 WASM 文件  
curl -o RDKit_minimal.wasm https://unpkg.com/@rdkit/rdkit/dist/RDKit_minimal.wasm
```

### 方法3：使用 PowerShell (Windows)

```powershell
# 下载 JavaScript 文件
Invoke-WebRequest -Uri "https://unpkg.com/@rdkit/rdkit/dist/RDKit_minimal.js" -OutFile "RDKit_minimal.js"

# 下载 WASM 文件
Invoke-WebRequest -Uri "https://unpkg.com/@rdkit/rdkit/dist/RDKit_minimal.wasm" -OutFile "RDKit_minimal.wasm"
```

## 验证文件

下载完成后，此目录应包含：
```
rdkit/
├── README.md (本文件)
├── RDKit_minimal.js
└── RDKit_minimal.wasm
```

## 文件大小参考
- RDKit_minimal.js: 约 100-200 KB
- RDKit_minimal.wasm: 约 2-4 MB

## 注意事项
- 确保文件名完全匹配（区分大小写）
- 两个文件必须在同一目录 (`public/rdkit/`)
- WASM 文件较大，下载可能需要一些时间
- 文件下载完成后重启开发服务器以确保文件被正确加载

## 组件使用说明
文件准备好后，组件会自动加载本地RDKit文件。不再需要输入框，直接通过v-model传入SMILES：

```vue
<template>
  <MoleculeViewer v-model="smiles" />
</template>

<script setup>
import { ref } from 'vue'
import MoleculeViewer from '@/components/molecule-viewer/index.vue'

const smiles = ref('CCO') // 乙醇
</script>
``` 
