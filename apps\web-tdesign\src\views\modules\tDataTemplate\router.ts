import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据模板',
    },
    name: 'tDataTemplate',
    path: '/tDataTemplate',
    children: [
      {
        meta: {
          title: '数据模板管理',
        },
        name: 'tDataTemplateIndex',
        path: '/tDataTemplate/index',
        component: () => import('#/views/modules/tDataTemplate/index.vue'),
      },
    ],
  },
];

export default routes;
