<template>
  <div class="combined-info-container">
    <!-- 基本信息 -->
    <div class="info-section">
      <div class="section-header">
        <h3 class="section-title">基本信息</h3>
      </div>
      <div class="section-content">
        <BasicInfo 
          :basic-data="basicData.dSubstanceBase" 
          :loading="loading"
        />
      </div>
    </div>

    <!-- 物化性质 -->
    <div class="info-section">
      <div class="section-header">
        <h3 class="section-title">物化性质</h3>
      </div>
      <div class="section-content">
        <PhysicalInfo 
          :physical-data="basicData.dSubstancePhysical" 
          :loading="loading"
        />
      </div>
    </div>

    <!-- 计算性质 -->
    <div class="info-section">
      <div class="section-header">
        <h3 class="section-title">计算性质</h3>
      </div>
      <div class="section-content">
        <CalculateInfo 
          :calculate-data="basicData.dSubstanceCalculate" 
          :loading="loading"
        />
      </div>
    </div>

    <!-- 用途信息 -->
    <div class="info-section">
      <div class="section-header">
        <h3 class="section-title">用途信息</h3>
      </div>
      <div class="section-content">
        <PurposeInfo 
          :purpose-data="basicData.dSubstancePurposeInfo" 
          :loading="loading"
        />
      </div>
    </div>

    <!-- 毒性 -->
    <div class="info-section">
      <div class="section-header">
        <h3 class="section-title">毒性</h3>
      </div>
      <div class="section-content">
        <Toxicity 
          :toxicity-data="basicData.dSubstanceToxicity" 
          :loading="loading"
        />
      </div>
    </div>

    <!-- 结构 -->
    <div class="info-section">
      <div class="section-header">
        <h3 class="section-title">结构</h3>
      </div>
      <div class="section-content">
        <Structure 
          :structure-data="basicData.dSubstanceStructure" 
          :loading="loading"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue';
import { useSearchStore } from '#/store/search';
import Structure from './Structure.vue';
import CalculateInfo from './CalculateInfo.vue';
import PhysicalInfo from './PhysicalInfo.vue';
import Toxicity from './Toxicity.vue';
import PurposeInfo from './PurposeInfo.vue';
import BasicInfo from './BasicInfo.vue';
import {
  getBasicCombinedInfo
} from '../api';

/**
 * 内部静态数据定义
 */
 const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},

});

// Store
const searchStore = useSearchStore();

// 获取当前详情数据
state.detailItem = computed(() => {
  const detail = searchStore.currentDetailItem;
  if (detail) {
    return detail;
  } else {
    const local = localStorage.getItem('currentDetailItem');
    return JSON.parse(local ?? '{}');
  }
});

// 响应式数据
const loading = ref(true);
const basicData = ref({
  // 基本信息
  dSubstanceBase: {},
  // 物化性质
  dSubstancePhysical: {},
  // 结构
  dSubstanceStructure: {},
  // 计算性质
  dSubstanceCalculate: {},
  // 用途信息
  dSubstancePurposeInfo: {},
  // 毒性
  dSubstanceToxicity: {},
});

// 获取基本信息的API
const fetchBasicInfo = async () => {
  try {
    const data = await getBasicCombinedInfo({ inchikey: state.detailItem.baseCode });
    basicData.value = data;
    loading.value = false;
  } catch (error) {
    console.error('获取基本信息失败:', error);
    basicData.value = {
      // 基本信息
      dSubstanceBase: {},
      // 物化性质
      dSubstancePhysical: {},
      // 结构
      dSubstanceStructure: {},
      // 计算性质
      dSubstanceCalculate: {},
      // 用途信息
      dSubstancePurposeInfo: {},
      // 毒性
      dSubstanceToxicity: {},
    };
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  // watchDetailItem();
  fetchBasicInfo();
});

// 监听detailItem变化
// import { watch } from 'vue';
// watch(detailItem, () => {
//   watchDetailItem();
// }, { deep: true });
</script>

<style scoped lang="scss">
.combined-info-container {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #2c5aa0;
}

.section-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    width: 4px;
    height: 18px;
    background: #2c5aa0;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  padding: 0 4px;
}

/* // 响应式设计 */
@media (max-width: 768px) {
  .combined-info-container {
    padding: 16px;
  }
  
  .info-section {
    margin-bottom: 24px;
  }
  
  .section-title {
    font-size: 16px;
  }
}
</style> 
