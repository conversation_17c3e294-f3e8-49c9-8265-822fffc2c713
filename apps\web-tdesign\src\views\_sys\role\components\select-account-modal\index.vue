<script lang="ts" setup>
import type { PageInfo, TableRowData } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import PutAway from '#/components/put-away/index.vue';
import { removalUnderline } from '#/utils/sort';
import { listByPage } from '#/views/_sys/account/api';
import { useVbenModal } from '@vben/common-ui';
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  Row,
  Select,
  Space,
  Table,
} from 'tdesign-vue-next';
import { computed, ref } from 'vue';

import { roleBindAccountSave } from '../../api';

// const { t } = useI18n();

// 表单控制属性
const formData: any = ref({});
const record: any = ref({});
const form = ref();
// 是否隐藏
const hideQuery = ref(true);

// 数据表控制属性
const data: any = ref([]);
const loading = ref(false);
const queryItemsLoading = ref(false);
const sort = ref([]);
const pagination: any = ref({
  current: 1,
  pageSize: 20,
  total: 0,
});

const stripe = ref(true);
const bordered = ref(false);
const hover = ref(true);
const tableLayout = ref(false);
const size: any = ref('medium');
const showHeader = ref(true);
const selectedRowKeys = ref([]);
const columnControllerVisible = ref(false);
const columns: any = ref([
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 50,
  },
  {
    title: '序号',
    colKey: 'serial-number',
    width: '100',
  },
  {
    colKey: 'accountNumber',
    title: '账号',
    ellipsis: true,
    sorter: true,
  },

  {
    colKey: 'status_text',
    title: '账号状态',
    width: '160',
    sorter: true,
  },
  {
    colKey: 'delFlag_text',
    title: '注销状态',
    width: '160',
    sorter: true,
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'remark',
    title: '备注',
    ellipsis: true,
    sorter: true,
  },
]);

const status = ref([]);
const delFlag = ref([]);
const groupColumn = ref(true);
const placement = ref<any['placement']>('top-right');
const customText = ref(true);
const displayColumns = ref<any['displayColumns']>([
  'row-select',
  'serial-number',
  'accountNumber',
  'status_text',
  'delFlag_text',
  'status',
  'delFlag',
  'remark',
  'createTime',
  'op',
]);
// 列配置
const columnControllerConfig = computed<any['columnController']>(() => ({
  // 列配置按钮位置
  placement: placement.value,
  hideTriggerButton: true,
  // 用于设置允许用户对哪些列进行显示或隐藏的控制，默认为全部字段
  fields: [
    'row-select',
    'serial-number',
    'accountNumber',
    'status',
    'status_text',
    'delFlag_text',
    'delFlag',
    'remark',
    'createTime',
    'updateTime',
  ],
  // 弹框组件属性透传
  dialogProps: {
    preventScrollThrough: true,
  },
  // 列配置按钮组件属性透传
  buttonProps: customText.value
    ? {
        content: '',
        theme: '',
        variant: 'text',
      }
    : undefined,
  // 数据字段分组显示
  groupColumns: groupColumn.value
    ? [
        {
          label: '业务字段',
          value: 'a',
          columns: [
            'accountNumber',
            'status',
            'delFlag',
            'status_text',
            'delFlag_text',
            'remark',
          ],
        },
        {
          label: '系统字段',
          value: 'b',
          columns: ['serial-number'],
        },
        {
          label: '时间字段',
          value: 'c',
          columns: ['createTime', 'updateTime'],
        },
      ]
    : undefined,
}));

const fetchData = async (params: any) => {
  loading.value = true;
  try {
    const { records, total } = await listByPage(params);

    data.value = records;
    pagination.value = {
      ...pagination.value,
      total,
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const loadData = async () => {
  const params = {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await fetchData(params);
};
const rehandlePageChange = (
  pageInfo: PageInfo,
  newDataSource: TableRowData[],
) => {
  // eslint-disable-next-line no-console
  console.log('分页变化', pageInfo, newDataSource);
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};
const onReset = () => {
  form.value.reset();
  loadData();
};
const onSubmit = async () => {
  loadData();
};

const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRowKeys.value = value;
};

const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

const loadQueryItems = async () => {
  queryItemsLoading.value = true;
  try {
    status.value = await getDictItems('ACCOUNT_STATUS');
    delFlag.value = await getDictItems('ACCOUNT_DEL_FLAG');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    queryItemsLoading.value = false;
  }
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    // 保存选择账号列表
    const params = selectedRowKeys.value.map((item) => {
      return { id: item };
    });
    await roleBindAccountSave(params, record.value.code);
    // 刷新表格
    modalApi.getData()?.refresh();
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      selectedRowKeys.value = [];
      record.value = isOpen
        ? { ...modalApi.getData<Record<string, any>>()?.record }
        : {};
      loadData();
      loadQueryItems();
    }
  },

  title: '添加账号授权',
});
const handleRowClick = () => {};
</script>

<template>
  <Modal class="w-8/12">
    <Space direction="vertical">
      <Card>
        <Form
          ref="form"
          :data="formData"
          :label-width="80"
          colon
          @reset="onReset"
          @submit="onSubmit"
        >
          <Row :gutter="[24, 24]">
            <Col :span="4">
              <FormItem label="账号" name="accountNumber">
                <Input
                  v-model="formData.accountNumber"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入账号"
                  type="search"
                />
              </FormItem>
            </Col>
            <Col :span="4">
              <FormItem label="状态" name="status">
                <Select
                  v-model="formData.status"
                  :loading="queryItemsLoading"
                  :options="status"
                  clearable
                  placeholder="选择状态"
                />
              </FormItem>
            </Col>
            <Col :span="4">
              <FormItem label="注销状态" name="delFlag">
                <Select
                  v-model="formData.delFlag"
                  :loading="queryItemsLoading"
                  :options="delFlag"
                  clearable
                  placeholder="选择注销状态"
                />
              </FormItem>
            </Col>
            <Col v-show="hideQuery" :span="4">
              <FormItem label="备注" name="remark">
                <Input
                  v-model="formData.remark"
                  :style="{ minWidth: '134px' }"
                  placeholder="请输入备注"
                />
              </FormItem>
            </Col>
          </Row>
          <Row justify="end">
            <Col :span="24" class="mt-4">
              <Space size="small">
                <Button
                  :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
                  theme="primary"
                  type="submit"
                >
                  搜索
                </Button>
                <Button theme="default" type="reset" variant="base">
                  重置
                </Button>
                <PutAway v-model="hideQuery" variant="text" />
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card title="账号列表">
        <template #actions>
          <Space>
            <ColumnDisplay v-model="columnControllerVisible" />
          </Space>
        </template>
        <Table
          v-model:column-controller-visible="columnControllerVisible"
          v-model:display-columns="displayColumns"
          :bordered="bordered"
          :column-controller="columnControllerConfig"
          :columns="columns"
          :data="data"
          :hover="hover"
          :loading="loading"
          :pagination="pagination"
          :pagination-affixed-bottom="false"
          :selected-row-keys="selectedRowKeys"
          :show-header="showHeader"
          :size="size"
          :sort="sort"
          :stripe="stripe"
          :table-layout="tableLayout ? 'auto' : 'fixed'"
          cell-empty-content="-"
          drag-sort="col"
          lazy-load
          multiple-sort
          resizable
          row-key="id"
          @drag-sort="onDragSort"
          @page-change="rehandlePageChange"
          @row-click="handleRowClick"
          @select-change="rehandleSelectChange"
          @sort-change="sortChange"
        />
      </Card>
    </Space>
  </Modal>
</template>
