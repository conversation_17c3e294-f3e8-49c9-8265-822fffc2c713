<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

import { getAboutUs } from './api';

// 团队介绍
const team = ref({
  introduction:
    '我们是一支专业的石化化工数据研究团队，致力于为行业提供全方位的数据服务和技术支持。团队成员来自各个领域的专家，拥有丰富的行业经验和深厚的技术背景。',
  images: [
    {
      src: '/static/images/team-working.jpg',
      alt: '团队工作场景',
      description: '专业团队协作',
    },
    {
      src: '/static/images/team-meeting.jpg',
      alt: '团队会议',
      description: '团队讨论交流',
    },
  ],
  highlights: [
    { number: '50+', label: '专业团队' },
    { number: '10+', label: '年行业经验' },
    { number: '100+', label: '成功项目' },
  ],
});

// 科研方向
const research = ref({
  introduction:
    '我们的科研方向涵盖石化化工全产业链的数据分析、工艺优化、设备监测、安全预警等多个领域。通过先进的数据挖掘技术和人工智能算法，为行业提供精准的决策支持。',
  image: {
    src: '/static/images/research-lab.jpg',
    alt: '科研实验室',
    description: '先进的科研设备',
  },
  tags: ['数据分析', '工艺优化', '安全预警'],
});

// 产品介绍
const product = ref<any>({
  introduction:
    '我们的产品涵盖石化化工行业的多项创新成果，助力企业数字化转型。',
  image: {
    src: '/static/images/product.jpg',
    alt: '产品图片',
    description: '创新产品展示',
  },
  features: [
    // { icon: '💡', text: '创新设计' },
    // { icon: '⚡', text: '高效性能' },
    // { icon: '🤖', text: '智能化体验' },
  ],
});

const visibleSections = ref(new Set());
const observer = ref<IntersectionObserver | null>(null);

onMounted(async () => {
  try {
    const res = await getAboutUs();

    const {
      teamIntroduction,
      team1Url,
      team2Url,
      researchIntroduction,
      researchUrl,
      productIntroduction,
      productUrl,
    } = res || {};

    // 团队介绍
    if (teamIntroduction || team1Url || team2Url) {
      if (teamIntroduction) team.value.introduction = teamIntroduction;
      team.value.images = [
        {
          src: team1Url || '/static/images/team-working.jpg',
          alt: '团队工作场景',
          description: '专业团队协作',
        },
        {
          src: team2Url || '/static/images/team-meeting.jpg',
          alt: '团队会议',
          description: '团队讨论交流',
        },
      ];
    }
    // 科研方向
    if (researchIntroduction || researchUrl) {
      if (researchIntroduction)
        research.value.introduction = researchIntroduction;
      research.value.image = {
        src: researchUrl || '/static/images/research-lab.jpg',
        alt: '科研实验室',
        description: '先进的科研设备',
      };
    }
    // 产品介绍
    if (productIntroduction) product.value.introduction = productIntroduction;
    if (productUrl) product.value.image.src = productUrl;
  } catch (error) {
    console.error(error);
  }

  observer.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          visibleSections.value.add(entry.target.id);
        }
      });
    },
    {
      threshold: 0.3,
      rootMargin: '-50px 0px',
    },
  );

  ['team', 'research', 'product'].forEach((id) => {
    const element = document.querySelector(`#${id}`);
    if (element) {
      observer.value?.observe(element);
    }
  });
});

onUnmounted(() => {
  observer.value?.disconnect();
});
</script>

<template>
  <div class="about-page">
    <!-- 团队介绍 -->
    <section id="team" class="page-section team-section">
      <div
        class="section-content"
        :class="{ 'fade-in': visibleSections.has('team') }"
      >
        <div class="section-header">
          <h2 class="section-title">团队介绍</h2>
          <div class="title-underline"></div>
        </div>
        <div class="team-content">
          <div class="team-text">
            <p class="team-description">{{ team.introduction }}</p>
            <div class="team-stats">
              <div
                class="stat-item"
                v-for="h in team.highlights"
                :key="h.label"
              >
                <div class="stat-number">{{ h.number }}</div>
                <div class="stat-label">{{ h.label }}</div>
              </div>
            </div>
          </div>
          <div class="team-images">
            <div
              class="image-container"
              v-for="(image, index) in team.images"
              :key="index"
            >
              <div class="image-placeholder">
                <div class="placeholder-content">
                  <img
                    v-if="image.src"
                    :src="image.src"
                    :alt="image.alt"
                    class="about-img"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 科研方向 -->
    <section id="research" class="page-section research-section">
      <div
        class="section-content"
        :class="{ 'fade-in': visibleSections.has('research') }"
      >
        <div class="section-header">
          <h2 class="section-title">科研方向</h2>
          <div class="title-underline"></div>
        </div>
        <div class="research-content">
          <div class="research-image">
            <div class="image-placeholder large">
              <div class="placeholder-content">
                <img
                  v-if="research.image.src"
                  :src="research.image.src"
                  :alt="research.image.alt"
                  class="about-img"
                />
              </div>
            </div>
          </div>
          <div class="research-text">
            <p class="research-description">{{ research.introduction }}</p>
            <div class="research-tags">
              <span class="tag" v-for="tag in research.tags" :key="tag">{{
                tag
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品案例 -->
    <section id="product" class="page-section product-section">
      <div
        class="section-content"
        :class="{ 'fade-in': visibleSections.has('product') }"
      >
        <div class="section-header">
          <h2 class="section-title">产品案例</h2>
          <div class="title-underline"></div>
        </div>
        <div class="product-content">
          <div class="product-image">
            <div class="image-placeholder">
              <div class="placeholder-content">
                <img
                  v-if="product.image.src"
                  :src="product.image.src"
                  :alt="product.image.alt"
                  class="about-img"
                />
              </div>
            </div>
          </div>
          <div class="product-text">
            <p class="product-description">{{ product.introduction }}</p>
            <ul class="product-features">
              <li v-for="f in product.features" :key="f.text">
                <span class="feature-icon">{{ f.icon }}</span>
                <span class="feature-text">{{ f.text }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">
.about-page {
  min-height: 100vh;
  background: hsl(var(--background-deep));
  .dark & {
    background: hsl(var(--background-deep));
  }
}

.page-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  position: relative;
  &:nth-child(even) {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--primary-600)) 100%
    );
  }
  &:nth-child(odd) {
    background: linear-gradient(
      135deg,
      hsl(var(--primary-400)) 0%,
      hsl(var(--primary-700)) 100%
    );
  }
  .dark & {
    &:nth-child(even) {
      background: linear-gradient(
        135deg,
        hsl(var(--primary-600)) 0%,
        hsl(var(--primary-800)) 100%
      );
    }
    &:nth-child(odd) {
      background: linear-gradient(
        135deg,
        hsl(var(--primary-500)) 0%,
        hsl(var(--primary-900)) 100%
      );
    }
  }
}

.section-content {
  max-width: 1200px;
  width: 100%;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease-out;
  &.fade-in {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}
.section-title {
  font-size: 3rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}
.title-underline {
  width: 80px;
  height: 4px;
  background: #fff;
  margin: 0 auto;
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

// 团队介绍样式
.team-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 60px;
  align-items: center;
}
.team-text {
  color: #fff;
}
.team-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}
.team-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 20px;
}
.stat-item {
  text-align: center;
}
.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}
.stat-label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 8px;
}
.team-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

// 科研方向样式
.research-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 60px;
  align-items: center;
}
.research-text {
  color: #fff;
}
.research-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}
.research-tags {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}
.tag {
  background: #fff2;
  color: #fff;
  border-radius: 8px;
  padding: 4px 16px;
  font-size: 1rem;
  font-weight: 500;
}

// 产品介绍样式
.product-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 60px;
  align-items: center;
}
.product-text {
  color: #fff;
}
.product-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}
.product-features {
  list-style: none;
  padding: 0;
  margin: 0;
}
.product-features li {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 1.1rem;
}
.feature-icon {
  font-size: 2rem;
  margin-right: 12px;
}
.feature-text {
  color: #b2ff59;
  font-weight: 600;
}

// 图片占位符样式
.image-placeholder {
  border-radius: 24px;
  padding: 24px 12px 16px 12px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: none;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.about-img {
  width: 100%;
  max-width: 540px;
  min-width: 120px;
  height: 180px;
  border-radius: 20px;
  margin-bottom: 10px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.18),
    0 1.5px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  transition: transform 0.3s cubic-bezier(0.4, 2, 0.6, 1);
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.about-img:hover {
  transform: scale(1.04) translateY(-4px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.32),
    0 2px 12px rgba(0, 0, 0, 0.12);
}

// 响应式设计
@media (max-width: 900px) {
  .team-content,
  .research-content,
  .product-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}
@media (max-width: 768px) {
  .page-section {
    padding: 40px 20px;
  }
  .section-title {
    font-size: 2rem;
  }
  .about-img {
    min-width: 80px;
    max-width: 100%;
    height: 100px;
  }
}
@media (max-width: 480px) {
  .section-title {
    font-size: 1.5rem;
  }
  .team-description,
  .research-description,
  .product-description {
    font-size: 1rem;
  }
  .about-img {
    min-width: 60px;
    height: 60px;
  }
}
</style>
