.top-card {
  background: #fff;
  // border-radius: 12px;
  box-shadow: 0 2px 12px rgba(44, 90, 160, 0.06);
  padding: 24px;
  margin-bottom: 16px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.top-card-main {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: 24px;
}

.top-card-cover {
  height: 100%;
  aspect-ratio: 4 / 3;
  min-width: 0;
  max-width: 100%;
  background: #e5eefa;
  border-radius: 12px;
  display: flex;
  align-items: stretch;
  justify-content: center;
  margin-right: 0;
  font-size: 28px;
  color: #1976d2;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(44, 90, 160, 0.06);
  position: relative;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
  }
}

.top-card-info {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.top-card-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.top-card-title {
  font-size: 20px;
  font-weight: bold;
  color: #222;
}

.top-card-extra {
  margin-left: 16px;
}

.top-card-info-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.top-card-info-item {
  display: flex;
  align-items: center;
  min-width: 180px;
  font-size: 15px;
  color: #333;
}

.top-card-info-icon {
  margin-right: 4px;
  color: #1976d2;
  font-size: 16px;
}

.top-card-info-format-row {
  display: flex;
  align-items: center;
  gap: 24px;
}
.top-card-info-format {
  display: flex;
  align-items: center;
}
.top-card-info-format-icon {
  margin-right: 6px;
  font-size: 18px;
  color: #1890ff;
  vertical-align: middle;
}
.top-card-info-label {
  margin-right: 4px;
}
.top-card-info-value {
  font-weight: 500;
  color: #222;
}

.top-card-keywords {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.top-card-keyword {
  margin-right: 4px;
  font-size: 13px;
  color: #1976d2;
  background: #e5eefa;
  border-radius: 8px;
  padding: 2px 10px;
}

.top-card-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  right: 32px;
  bottom: 24px;
}
.top-card-action-item {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}

.top-card-meta {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #888;
  margin-bottom: 8px;
  a {
    color: #7c3aed;
    text-decoration: underline;
    margin-right: 4px;
  }
  span {
    margin-right: 8px;
  }
}

// 响应式
@media (max-width: 1200px) {
  .top-card {
    padding: 16px;
  }
  .top-card-main {
    gap: 12px;
  }
  .top-card-cover {
    width: 100%;
    height: 100px;
    border-radius: 8px;
    min-height: 80px;
    max-height: 160px;
  }
  .top-card-info {
    gap: 12px;
  }
  .top-card-info-list {
    gap: 12px;
  }
  .top-card-keywords {
    gap: 12px;
  }
}

@media (max-width: 1024px) {
  .top-card {
    padding: 16px;
    border-radius: 8px;
  }
  .top-card-main {
    flex-direction: column;
    gap: 16px;
  }
  .top-card-cover {
    width: 100%;
    height: 120px;
    margin-bottom: 8px;
    border-radius: 8px;
    min-height: 80px;
    max-height: 160px;
  }
  .top-card-info {
    gap: 16px;
  }
  .top-card-info-list {
    gap: 16px;
  }
  .top-card-keywords {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .top-card {
    padding: 8px;
    border-radius: 6px;
  }
  .top-card-main {
    flex-direction: column;
    gap: 8px;
  }
  .top-card-cover {
    width: 100%;
    height: 40vw;
    min-height: 60px;
    max-height: 120px;
    margin-bottom: 8px;
    border-radius: 6px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }
  .top-card-title {
    font-size: 16px;
  }
  .top-card-info {
    gap: 8px;
  }
  .top-card-info-list {
    gap: 8px;
  }
  .top-card-keywords {
    gap: 8px;
  }
  .top-card-keyword {
    font-size: 12px;
    padding: 1px 8px;
  }
} 
