<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';

import { Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import {
  MemberIcon,
  RefreshIcon,
  SearchIcon,
  TimeIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  MessagePlugin,
  Space,
  Tooltip,
  Typography,
} from 'tdesign-vue-next';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { listByPage } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});
const initStatus = () => {
  /**
   * 默认选中全部
   */
  state.selectedRowKeys = [];
  /**
   * 默认不显示删除按钮
   */
  state.delDailogShow = false;
  /**
   * 默认不显示查询
   */
  state.hideQuery = false;
  /**
   * 默认不显示加载
   */
  state.loading = false;
};
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

/**
 * 查询参数
 */
const formData: any = ref({});

/**
 * 分页参数
 */
const pagination: any = ref(Pagination);

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      for (const item of state.dataSource) {
        item.imgPath = JSON.parse(item.imgPath)[0].url;
      }
      pagination.value = {
        ...pagination.value,
        total,
      };

      if (records.length === 0 && total !== 0) {
        pagination.value.current = 1;
        reload();
      }
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run({
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};
/**
 * 查询Form提交，执行查询
 */
const searchFormSubmit = () => {
  // 重置分页
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
/**
 * 查询Form重置，执行查询
 */
const resetSearch = () => {
  formData.value = {};
  searchForm.value.reset();
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);
};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};

onMounted(() => {
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});

const ellipsisState = {
  row: 5,
  expandable: false,
  collapsible: false,
};

const toLinkService = (link: any) => {
  /* const newWindow = window.open(link, '_blank');
  if (newWindow) {
    newWindow.opener = null; // 增强安全性，防止 opener 攻击
  } else {
    console.warn('请允许弹出窗口以继续');
  }*/
  MessagePlugin.warning('功能正在开发中，敬请期待！');
};
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <Space
        :size="8"
        class="tiny-tdesign-style-patch w-full pl-[300px] pr-[300px]"
        direction="vertical"
      >
        <!--    查询表单定义区域-->
        <Card v-if="isSearchForm">
          <Form
            ref="searchForm"
            :data="formData"
            class="w-full"
            @reset="resetSearch"
            @submit="searchFormSubmit"
          >
            <div class="grid w-full grid-cols-2 gap-1">
              <FormItem label="数据工具名称" name="serviceTitle">
                <Input
                  v-model="formData.serviceTitle"
                  clearable
                  placeholder="请输入内容"
                />
              </FormItem>
              <FormItem label="数据工具归属" name="serviceOwner">
                <Input
                  v-model="formData.serviceOwner"
                  clearable
                  placeholder="请输入内容"
                />
              </FormItem>
            </div>
            <div class="mt-2 flex items-center justify-end space-x-2">
              <Button theme="primary" type="submit">
                <template #icon>
                  <SearchIcon />
                </template>
                查询
              </Button>
              <Button theme="default" type="reset">
                <template #icon>
                  <RefreshIcon />
                </template>
                重置
              </Button>
            </div>
          </Form>
        </Card>
        <Card>
          <div class="grid w-full grid-cols-2 gap-5">
            <div v-for="row in state.dataSource">
              <Card
                :title="row.serviceTitle"
                :hover-shadow="true"
                @click="toLinkService(row.serviceLink)"
                style="cursor: pointer"
              >
                <div class="card-body">
                  <img
                    :src="row.imgPath"
                    referrerpolicy="no-referrer"
                    class="quick-link-icon"
                  />
                  <Tooltip
                    :content="row.serviceDescribe"
                    :destroy-on-close="false"
                    :show-arrow="false"
                    theme="light"
                    placement="bottom"
                  >
                    <div class="describe-out">
                      <Typography :ellipsis="ellipsisState">
                        <div class="describe-data">
                          {{ row.serviceDescribe }}
                        </div>
                      </Typography>
                    </div>
                  </Tooltip>
                </div>
                <div class="card-foot">
                  <div style="margin-left: 120px">
                    <MemberIcon />
                    {{ row.serviceOwner }}
                  </div>
                  <div style="margin-left: 20px">
                    <TimeIcon />
                    {{ row.createTime }}
                  </div>
                </div>
              </Card>
            </div>
          </div>
          <div class="mt-4" v-if="state.dataSource.length > 0">
            <Pagination
              v-model="pagination.current"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              show-jumper
              @change="rehandlePageChange"
              @page-size-change="rehandlePageChange"
              @current-change="rehandlePageChange"
            />
          </div>
        </Card>
      </Space>
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
.t-form__item {
  margin-bottom: 0;
}
.quick-link-icon {
  width: 100px;
  height: 100px;
}

.card-body {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.describe-out {
  display: flex;
}

.card-foot {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.describe-data {
  margin-left: 15px;
  width: 95%;
}
</style>
