import { requestClient } from '#/api/request';

// 知识对象抽取API
export async function extractKnowledge(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/extract', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 60_000,
  });
}

// 知识对象下载API
export async function downloadKnowledge(download_url: string) {
  return requestClient.get(`/rgdc-sys/dataTools/download/${download_url}`, {
    responseType: 'blob',
  });
}
// 知识对象查看API
export async function viewKnowledge(view_url: string) {
  return requestClient.get(`/rgdc-sys/dataTools/view`, {
    params: {
      view_url,
    },
  });
}
// 跨模态对齐API
export async function alignKnowledge(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/align', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 60_000,
  });
}

// 获取上传列表API
export async function getUploadList() {
  return requestClient.get('/rgdc-sys/dataTools/upload_list');
}
