<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>化工知识协同加工与管理平台</title>
  <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
  <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
  <link rel="stylesheet" href="../static/vue/theme/index.css">
  <script src="../static/vue/min/vue.min.js"></script>
  <script src="../static/vue/element-ui2.15.13_index.js"></script>
  <script src="../static/vue/axios0.26.0_axios.min.js"></script>
  <style>
    html, body {
      min-width: 100%;
    }
    .mon_warp {
      margin: 0px;
      width: 100%;
      background-size: cover;
    }
    .mon_body {
      display: none;
      width: 100%;
    }
    .el-menu-vertical-demo {
      height: 100%;
    }
    .el-card {
      margin-top: 20px;
    }
    .el-upload__tip {
      margin-top: 10px;
    }
    .clearfix:before,
    .clearfix:after {
      display: table;
      content: "";
    }
    .clearfix:after {
      clear: both;
    }
    .center{
      border: 1px solid #ccc;
      width: 100%;
      margin: 20px auto 20px;
      border-radius: 20px;
      padding: 30px;
      min-width: 1200px;
    }
    .upload-demo {
      width: 100%;
    }
    .el-upload-dragger {
      width: 400px;
      height: 250px;
      padding: 40px;
    }
    .el-upload__text {
      font-size: 16px;
      margin: 20px 0;
    }
    .el-icon-upload {
      font-size: 67px;
      margin: 20px 0;
    }
    .el-menu-item.is-active {
      background-color: #ecf5ff;
      color: #409EFF;
    }
    .el-menu-item {
      font-size: 14px;
      height: 56px;
      line-height: 56px;
    }
    .el-menu-item:hover {
      background-color: #ecf5ff;
    }
     .download-notice {
            font-size: 14px;
            color: #666;
            display: inline-block;
            margin-top: 10px;
        }
        .notice-icon {
            color: #ff9800;
            margin-right: 5px;
        }
         .download-all-container {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
  </style>
</head>
<body>
  <div class="header_app" id="header_app"></div>

  <div class="mon_warp clearfix" id="app">
    <div class="mon_body clearfix">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="3">
          <div style="padding-top:20px">
            <el-menu 
              :default-active="menuActive" 
              @select="handleMenuSelect"
              class="el-menu-vertical-demo" 
              style="padding-top:20px;"
              active-text-color="#409EFF">
              <el-menu-item index="cleanTool">数据汇聚与清洗工具</el-menu-item>
              <el-menu-item index="classiFication">数据整编与分类工具</el-menu-item>
              <el-menu-item index="3">
  <el-tooltip content="全文多模态解析重组工具" placement="right">
    <span style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
      全文多模态解析重组工具
    </span>
  </el-tooltip>
</el-menu-item><el-menu-item index="relationship">
  <el-tooltip content="知识对象及关系挖掘工具" placement="right">
    <span style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
      知识对象及关系挖掘工具
    </span>
  </el-tooltip>
</el-menu-item>
 <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>
            </el-menu>
          </div>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="21">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <h2>全文多模态解析重组工具</h2>
            </div>
            <div>
              <p style="font-size: 16px;">
                综合深度神经网络技术，PDF 乱码解析技术和基于 PDF 元素块建模识别技术，
                基于 PYMuPDF、PaddlePaddle、CNSTD 等多种开源工具，实现对科技文献全文中"文本、图片、表格、公式"等多模态数据对象的抽取和组织，
                重新组合为计算机可利用的结构化数据。在工程层面，本工具将支持分布式部署，多进程运行，保证充分利用计算资源实现最大效率的语义分割重组语料的建设。
              </p>
                 <div style="text-align: right;width: 100%;padding-top: 10px;">
              <el-button type="success" style="margin-right: 60px;" @click="startopen">任务列表</el-button>
            </div>
              <div class="center">
                <div style="display: flex;width: 100%;justify-content: space-around;">
                  <div class="left" style="padding-top: 34px;">
                    <el-form label-width="120px" label-position="left">
                      <el-form-item label="文献类型：">
                        <el-radio-group v-model="literatureType">
                          <el-radio label="论文">论文</el-radio>
                          <el-radio label="专利">专利</el-radio>
                          <el-radio label="图书">图书</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <el-form-item label="文档类型：">
                        <el-radio-group v-model="docType">
                          <el-radio label="PDF">PDF</el-radio>
                          <el-radio label="DOC">DOC</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <el-form-item  label="是否是扫描版：">
                        <el-radio-group v-model="isScanned">
                          <el-radio label="否">否</el-radio>
                          <el-radio label="是">是</el-radio>
                          
                        </el-radio-group>
                      </el-form-item>

                       <el-form-item label="任务名称：" required>
  <el-input v-model="fileName" placeholder="请输入任务名称"></el-input>
</el-form-item>
                    </el-form>
                  </div>

                  <div class="right" v-loading="loading">
                    <el-upload
                      class="upload-demo"
                      drag
                      :http-request="customUpload"
                      :auto-upload="false"
                      :file-list="fileList"
                      :on-change="(file, fileList) => { this.fileList = fileList }"
                      multiple>
                      <i class="el-icon-upload"></i>
                      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                      <div class="el-upload__tip" slot="tip">仅支持PDF文件上传</div>
                    </el-upload>
                  </div>
                </div>
              </div>
            </div>
            <div style="text-align: right;width: 100%;">
              <el-button type="primary" style="margin-right: 60px;" @click="handleUpload" v-text="btnStart"></el-button>
            </div>
               <div class="download-all-container">
            <!-- <button class="download-all">全部下载</button> -->
            <div class="download-notice"> 
                <span class="notice-icon">⚠</span>解构过程中请勿刷新页面
            </div>
        </div>
          </el-card>
        </el-col>
      </el-row>

       <!-- 上传记录对话框 -->
        <el-dialog title="上传记录" :visible.sync="dialogVisible" width="60%">
          <el-table :data="uploadLists" style="width: 100%;" :border="true" stripe>
            <el-table-column prop="created_at" label="上传时间" width="180"></el-table-column>
            <el-table-column prop="task_name" label="任务名称"></el-table-column>
            <el-table-column label="操作" width="180">
              <template slot-scope="scope">
                <el-button size="mini" @click="handlePreview(scope.row)">预览</el-button>
                <el-button size="mini" type="primary" @click="handleDownload(scope.row)">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>
    </div>
  </div>

  <div id="dataid" data="{{session.user}}" style="display:none"></div>
  <div class="mon_footer"></div>

  <script src="../static/js/jquery.min.js"></script>
  <script src="../static/js/monitor.js"></script>

  <script>
    let _this = this;

    const vm = new Vue({
      el: '#app',
      data: {
        activeIndex: '3',
        literatureType: '论文',
        docType: 'PDF',
        isScanned: '否',
        fileList: [],
        dialogVisible: false,
        btnStart:"开始解构",
        uploadLists: [],
        loading: false,
        fileName: '', // 新增：文件名称输入框
        menuActive:"3"
      },
      mounted() {
        $('.mon_body').css({ 'display': 'revert' });
        this.getTableData(); // 获取上传记录
        
      },
      methods: {
        handleMenuSelect(index) {
    if (index === 'cleanTool') {
      window.location.href = '/cleanTool'; // 跳转到对应的页面
    } else if (index === 'classiFication') {
      window.location.href = '/classiFication';
    } else if (index === 'relationship') {
      window.location.href = '/relationship';
    } else if (index === 'home') {
      window.location.href = '/'; // 跳转到首页
    } else if (index === 'qualitycontrol') {
      window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
    } 
    this.menuActive = index; // 更新高亮状态
  },
        startopen() {
          this.dialogVisible = true;
          this.getTableData(); // 打开对话框时获取最新的上传记录
        },
        handleRemove(file, fileList) {
          console.log('File removed:', file);
        },
        handlePreview(row) {
          // 预览功能可以根据需要实现
         location.href = `./demonstration?session_id=${row.session_id}`;
        },
        // 已废弃：beforeAvatarUploadFile 不再使用（你可以保留做备用）
        beforeAvatarUploadFile(file, type = '') {
          console.log(file);
          let that = this;
          const isLt100M = file.size / 1024 / 1024 < 500;
          if (!isLt100M) {
            this.$message.error('上传文件大小不能超过 50MB!');
          }
          if (isLt100M) {
            event.preventDefault();
            let formData = new FormData();
            formData.append('pdf_files', file);
            formData.append('task_name', that.fileName);
             axios.post(`${server_url}/api/upload_pdfs?task_name=${encodeURIComponent(this.fileName)}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(function (response) {
              let data = response.data;
              _this.response_handle(data, true);
            }).catch(function (error) {
              that.$message({
                message: '上传失败了!!!',
                type: 'error',
                duration: 3000,
                center: true
              });
              console.log(error);
            });
          }
          return isLt100M;
        },
        // 新增：阻止自动上传
        customUpload(request) {
          // 不做处理，这只是为了拦截默认上传
        },
        // 新增：点击“开始解构”后统一上传
        handleUpload() {
      
        
          if (this.fileList.length === 0) {
            this.$message.warning("请先选择文件");
            return;
          }
          if (!this.fileName) {
            this.$message.warning("请填写文件名称再进行上传");
            return;
          }
          this.loading = true;
          const formData = new FormData();
          let overSize = false;

          this.fileList.forEach(file => {
            if (file.raw.size / 1024 / 1024 > 500) {
              overSize = true;
            } else {
              formData.append('pdf_files', file.raw);
            }
          });

          if (overSize) {
            this.$message.error("有文件大小超过 50MB，无法上传");
            return;
          }
          this.btnStart = "正在解构...";
           axios.post(`${server_url}/api/upload_pdfs?task_name=${encodeURIComponent(this.fileName)}`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          }).then(response => {
             this.btnStart = "开始解构";
 
            this.$message.success("上传成功");
           this.loading = false;
              location.href = `./demonstration?session_id=${response.data.data}`;
            this.response_handle(response.data, true);
          }).catch(error => {
            this.$message.error("上传失败");
            console.error(error);
          });
        },
        // 你的后端响应处理方法
        response_handle(data, success) {
          console.log("上传结果：", data);
          // TODO: 你可以在这里添加逻辑，比如跳转或展示解析结果
        },
           handleDownload(row) {
    const that = this;
    axios({
      url: server_url + '/api/get_zip_by_session_id',
      method: 'GET',
      params: { session_id: row.session_id },
      responseType: 'blob' // 告诉 axios 这是一个二进制文件
    }).then(function(response) {
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;

      // 可选：给文件命名
      const fileName = `下载文件_${row.session_id || 'unknown'}.zip`;
      link.download = fileName;

      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(link);
    }).catch(function(error) {
      console.log('文件下载失败：', error);
    });
  },
  
          getTableData() {
            // 这里可以添加获取表格数据的逻辑
                let that = this;
    
        axios.get(server_url + '/api/upload_list').then(function(res) {
          if(res.data.code === 0){
            
            that.$nextTick(() => {
            that.uploadLists = res.data.data;
            console.log(that.uploadLists);
            });
          
          }
        }).catch(function(error) {
          console.log(error);
          that.enterpriseLoading = false; // Ensure loading state is turned off in case of an error
        });
          },
      }
    });
  </script>
</body>
</html>
