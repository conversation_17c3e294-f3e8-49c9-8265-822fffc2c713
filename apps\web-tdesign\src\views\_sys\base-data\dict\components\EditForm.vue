<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Form,
  FormItem,
  type FormProps,
  Input,
  Select,
  SelectInput,
  Textarea,
} from 'tdesign-vue-next';

import { save, tagList } from '../api';

const popupVisible = ref(false);
const formData: any = ref({});
const form = ref();
const tagSelectOptions = ref([]);
const FORM_RULES: FormProps['rules'] = {
  name: [
    {
      required: true,
      message: '必填',
    },
  ],
  type: [
    {
      required: true,
      message: '必填',
    },
  ],
  code: [
    {
      required: true,
      message: '必填',
    },
  ],
};

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    const vali = await form.value.validate();

    if (vali === true) {
      // 验证通过提交请求 并且关闭窗口
      await save(formData.value);
      modalApi.getData().refresh();
      modalApi.close();
    }
  },
  async onOpenChange(isOpen: boolean) {
    const res = await tagList();
    tagSelectOptions.value = res;
    formData.value = isOpen
      ? { ...modalApi.getData<Record<string, any>>()?.record }
      : {};
  },
  title: '新增',
});
const onOptionClick = (item: any) => {
  formData.value.tag = item;
  popupVisible.value = false;
};

const onInputChange = (keyword: any) => {
  formData.value.tag = keyword;
};

const onPopupVisibleChange = (val: any) => {
  popupVisible.value = val;
};
onMounted(async () => {});
</script>
<template>
  <Modal class="w-[30%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES">
      <FormItem label="字典名称" name="name">
        <Input v-model="formData.name" clearable placeholder="请输入项目名称" />
      </FormItem>
      <FormItem label="字典编码" name="code">
        <Input v-model="formData.code" clearable placeholder="请输入项目名称" />
      </FormItem>
      <FormItem label="数据类型" name="type">
        <Select
          v-model="formData.type"
          :options="[
            { label: '数字', value: 1 },
            { label: '字符', value: 0 },
          ]"
          clearable
          placeholder="请输入项目名称"
        />
      </FormItem>
      <FormItem label="分组" name="tag">
        <SelectInput
          v-model:input-value="formData.tag"
          :allow-input="true"
          :popup-props="{ overlayInnerStyle: { padding: '6px' } }"
          :popup-visible="popupVisible"
          :value="formData.tag"
          clearable
          placeholder="请输入项目名称"
          @input-change="onInputChange"
          @popup-visible-change="onPopupVisibleChange"
        >
          <template #panel>
            <ul class="select-input-ul-single">
              <li
                v-for="item in tagSelectOptions"
                :key="item"
                @click="() => onOptionClick(item)"
              >
                {{ item }}
              </li>
            </ul>
          </template>
        </SelectInput>
      </FormItem>
      <FormItem label="备注" name="remark">
        <Textarea
          v-model="formData.remark"
          class="form-item-content"
          placeholder="请输入备注"
        />
      </FormItem>
    </Form>
  </Modal>
</template>
<style lang="less" scoped>
.form-item-content {
  width: 100%;
}

.select-input-ul-single {
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 2px;
}

.select-input-ul-single > li {
  display: block;
  border-radius: 3px;
  line-height: 22px;
  cursor: pointer;
  padding: 3px 8px;
  color: var(--td-text-color-primary);
  transition: background-color 0.2s linear;
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  text-overflow: ellipsis;
}

.select-input-ul-single > li:hover {
  background-color: var(--td-bg-color-container-hover);
}
</style>
