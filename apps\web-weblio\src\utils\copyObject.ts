export function cloneDeep(source: any, hash = new WeakMap()) {
  if (typeof source !== 'object' || source === null) {
    return source;
  }
  if (hash.has(source)) {
    return hash.get(source);
  }
  const target: any = Array.isArray(source) ? [] : {};
  Reflect.ownKeys(source).forEach((key) => {
    const val = source[key];
    target[key] =
      typeof val === 'object' && val !== null ? cloneDeep(val, hash) : val;
  });
  return target;
}
