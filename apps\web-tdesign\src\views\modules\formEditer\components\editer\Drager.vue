<!--eslint-disable-next-line @typescript-eslint/ban-ts-comment-->
<!--@ts-nocheck-->
<script lang="ts">
import { EventBus, offEvent, onEvent } from '#/utils/eventBus.ts';
import FormItemRender from '#/views/modules/formEditer/components/render/FormItemRender.vue';
import { DeleteIcon, Icon } from 'tdesign-icons-vue-next';
import {Form, MessagePlugin} from 'tdesign-vue-next';
import { ref } from 'vue';
import draggable from 'vuedraggable';

export default {
  name: 'Drager',
  components: {
    Form,
    Icon,
    DeleteIcon,
    FormItemRender,
    Draggable: draggable,
  },
  props: {
    columns: {
      type: Number,
      default: 1,
    },
    eventPrefix: {
      type: String,
      default: '',
    },
    modelValue: {
      type: Array,
      default: () => [],
    },
    dataType: {
      type: Object,
      default: () => ({}),
    },
  },

  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const formData = ref({});
    const selectedTag = ref();
    const list = ref(props.modelValue || []);
    const tagMoveStart = (item) => {};
    const setFormValue = (list) => {
      list?.forEach((item: any) => {
        formData.value[item.schema.name] = item.schema.value;
        if (item.schema?.children?.length >= 0) {
          setFormValue(item.schema.children);
        }
      });
    };
    const defHandler = {
      widgetList_itemClick: (data) => {
        if (data) {
          const tmp = JSON.parse(JSON.stringify(data));
          if (selectedTag?.value?.isGroup) {
            selectedTag.value.schema.children.push(tmp);
            setTimeout(() => {
              setFormValue(selectedTag.value.schema.children);
            }, 0);
          } else {
            list.value.push(tmp);
            selectedTag.value = tmp;
            // formData.value = {};
            // list.value.forEach((item) => {
            //   formData.value[item.schema.name] = item.schema.value;
            // });
            setFormValue(list.value);
          }
          // emit('update:modelValue', list.value);
        }
      },
    };
    const eventHandler = {
      // clear_selected: (eventPrefix) => {
      //   if (eventPrefix !== props.eventPrefix) selectedTag.value = null;
      // },
      formEditer_listRefresh: () => {
        // 无效
      },
      formEditer_itemClick: (data) => {
        selectedTag.value = data;
      },
      childrenPanel_select: (data) => {
        selectedTag.value = data;
      },
      formEditer_itemChange: (data) => {
        if (data) {
          const index = list.value.findIndex((i) => i.id === data.id);
          if (index != -1) {
            list.value[index] = data;
          }
          list.value.forEach((item) => {
            formData.value[item.schema.name] = item.schema.value;
          });
          // emit('update:modelValue', list.value);
        }
      },
    } as any;
    const makeEventHandler = () => {
      Object.keys(defHandler).forEach((item) => {
        eventHandler[`${props.eventPrefix}${item}`] = defHandler[item];
      });
    };

    const tagClone = (item) => {
      selectedTag.value = JSON.parse(JSON.stringify(item));
    };
    const tagMoveEnd = () => {
      // emit('update:modelValue', list.value);
    };
    const itemClick = (item) => {
      // ${props.eventPrefix}
      if (item) {
        setTimeout(() => {
          EventBus.emit(`formEditer_itemClick`, {
            ...item,
            syncOptions: item.syncOptions || {},
          });
          EventBus.emit(`reSetChildrenPanel_select`, {
            ...item,
            syncOptions: item.syncOptions || {},
          });
        }, 0);
      }
    };
    const removeItem = (event, item, index) => {
      if(item.schema.name == 'data_name' || item.schema.name == 'describe'){
        MessagePlugin.warning('此模板为内置模块，若删除请联系管理员！');
        return;
      }
      // 关闭事件冒泡
      event.e.stopPropagation();
      list.value.splice(index, 1);
      EventBus.emit(`${props.eventPrefix}formEditer_itemDelete`);
    };

    return {
      tagMoveStart,
      tagClone,
      selectedTag,
      list,
      tagMoveEnd,
      removeItem,
      makeEventHandler,
      eventHandler,
      itemClick,
      formData,
      setFormValue,
    };
  },
  computed: {
    dragOptions() {
      return {
        animation: 300,
        group: 'dbox',
        disabled: false,
        ghostClass: 'ghost',
      };
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        this.list.value = [...val];
      },
      deep: true,
    },
    list: {
      handler(val) {
        this.$emit('update:modelValue', val);
      },
      deep: true,
    },
  },
  mounted() {
    // this.itemClick(this.list?.[0]);
    this.makeEventHandler();
    onEvent(this.eventHandler);
  },
  created() {
    console.info('created', this.list);
    this.setFormValue(this.list);
  },
  unmounted() {
    this.makeEventHandler();
    offEvent(this.eventHandler);
  },
};
</script>

<template>
  <Draggable
    :class="`grid grid-cols-${columns} border-gray-70 min-h-[40px] w-full gap-1 border p-1`"
    :list="list"
    item-key="id"
    v-bind="dragOptions"
    :clone="tagClone"
    @end="tagMoveEnd"
    @start="tagMoveStart"
  >
    <template #item="{ element, index }">
      <div
        :key="element.id"
        :class="`${selectedTag?.id === element?.id ? 'bg-accent border-blue-400' : ''} hover:bg-accent flex cursor-pointer items-center justify-between border border-dashed p-2`"
        style="position: relative"
        @click.stop="itemClick(element)"
      >
        <FormItemRender
          v-model="formData"
          :config="element"
          :is-edit="true"
          class="w-full"
        />
        <DeleteIcon
          v-if="selectedTag?.id === element?.id"
          class="ml-2 flex h-full items-center justify-center text-red-600"
          name="delete"
          @click="removeItem($event, element, index)"
        />
        <!--          </div>-->
        <!--        id:{{ element.id }},name:{{ element.name }}-->
      </div>
    </template>
  </Draggable>
</template>

<style scoped></style>
