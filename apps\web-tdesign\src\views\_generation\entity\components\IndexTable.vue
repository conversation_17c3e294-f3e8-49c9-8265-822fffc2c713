<script lang="ts" setup>
import type { PageInfo, TableRowData } from 'tdesign-vue-next';

import ColumnDisplay from '#/components/column-display/index.vue';
import PutAway from '#/components/put-away/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import { Icon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
} from 'tdesign-vue-next';
import { defineEmits, defineExpose, defineProps, onMounted, ref } from 'vue';

import {
  generationEntityDeleteBatch,
  generationEntityListByPageApi,
  tagList,
} from '../api';

const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['edit', 'add']);
/**
 * 表格定义
 */
const columns: any = ref([
  {
    title: '选中标志',
    colKey: 'row-select',
    type: 'multiple',
    width: 64,
  },
  {
    title: '序号',
    colKey: 'serial-number',
    width: 100,
  },
  {
    colKey: 'name',
    title: '实体名称',
    ellipsis: true,
    sorter: true,
  },

  {
    colKey: 'tag',
    title: '标签',
    sorter: true,
  },
  {
    colKey: 'tableName',
    title: '表名称',
    sorter: true,
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'remark',
    title: '备注',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: 100,
    fixed: 'center',
  },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
const formData: any = ref({});
const form = ref();
const hideQuery = ref(false);
const data: any = ref([]);
const selectedRowKeys = ref([]);
const tableConfig = ref(BaseTableConfig);
const pagination: any = ref(Pagination);
const loading = ref(false);
const sort = ref([]);
/** -----------------------------------------------  */

/**
 * 网络请求调用定义
 */
const reqRunner = {
  generationEntityListByPageApi: async (params: any) => {
    loading.value = true;
    try {
      const { records, total } = await generationEntityListByPageApi(params);
      data.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
};

const tags = ref([]);

/**
 * table初始化方法
 */
const loadData = async () => {
  const params = {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await reqRunner.generationEntityListByPageApi(params);
};
/**
 * 分页栏点击/更改响应方法
 * @param pageInfo
 * @param newDataSource
 */
const rehandlePageChange = (
  pageInfo: PageInfo,
  newDataSource: TableRowData[],
) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};
/**
 * 搜索表单重置方法
 */
const onReset = () => {
  form.value.reset();
  loadData();
};
/**
 * 搜索表单提交方法
 */
const onSubmit = async () => {
  loadData();
};

/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRowKeys.value = value;
};

/**
 * 列拖动交换位置响应
 * @param newData
 * @param sort
 */
const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

/**
 * vue 生命周期 页面加载结束
 */
onMounted(async () => {
  const res: any = await tagList({});
  console.log(res);
  tags.value = res.map((item: any) => {
    return { value: item, label: item };
  });
  loadData();
  // delFlag.value = await getDictItems('ACCOUNT_DEL_FLAG');
});

/**
 * 新增按钮响应
 */
const add = () => {
  emit('add');
};

/**
 * 编辑按钮响应
 * @param record
 */
const edit = (record: any) => {
  emit('edit', record);
};

/**
 * 行点击时间
 */
const handleRowClick = () => {};

/**
 * 批量删除按钮响应
 */
const removeBatch = async () => {
  const ids = selectedRowKeys.value.join(',');
  await generationEntityDeleteBatch(ids);
  selectedRowKeys.value = [];
  loadData();
};

/**
 * 单行删除按钮响应
 * @param row
 */
const remove = async (row: any) => {
  await generationEntityDeleteBatch(row.id);
  loadData();
};
/**
 * 方法/属性导出
 */
defineExpose({
  loadData,
});
</script>

<template>
  <Space class="w-full" direction="vertical" size="small">
    <Card v-if="isSearchForm">
      <Form
        ref="form"
        :data="formData"
        :label-width="80"
        @reset="onReset"
        @submit="onSubmit"
      >
        <Row :gutter="[24, 24]">
          <Col :span="4">
            <FormItem label="实体名称" name="name">
              <Input
                v-model="formData.name"
                :style="{ minWidth: '134px' }"
                placeholder="请输入实体名称"
                type="search"
              />
            </FormItem>
          </Col>
          <Col :span="4">
            <FormItem label="标签" name="tag">
              <Select
                v-model="formData.tag"
                :options="tags"
                clearable
                placeholder="选择标签"
              />
            </FormItem>
          </Col>
          <Col :span="4">
            <FormItem label="表名称" name="tableName">
              <Input
                v-model="formData.tableName"
                :style="{ minWidth: '134px' }"
                placeholder="请输入表名称"
                type="search"
              />
            </FormItem>
          </Col>
          <Col v-show="hideQuery" :span="4">
            <FormItem label="备注" name="remark">
              <Input
                v-model="formData.remark"
                :style="{ minWidth: '134px' }"
                placeholder="请输入备注"
              />
            </FormItem>
          </Col>
        </Row>
        <Row justify="end">
          <Col :span="24" class="mt-4">
            <Space size="small">
              <Button
                :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
                theme="primary"
                type="submit"
              >
                <template #icon>
                  <Icon name="search" />
                </template>
                搜索
              </Button>
              <Button theme="default" type="reset" variant="base">
                重置
              </Button>
              <PutAway v-model="hideQuery" variant="text" />
            </Space>
          </Col>
        </Row>
      </Form>
    </Card>
    <Card>
      <div class="t-row--space-between mb-2 flex items-center justify-center">
        <div class="flex flex-wrap items-center justify-center gap-1">
          <div class="t-card__title ml-2">实体列表</div>
          <div
            v-if="selectedRowKeys && selectedRowKeys.length > 0"
            class="text-[gray]"
          >
            已选择 {{ selectedRowKeys?.length || 0 }} 条数据
          </div>
        </div>
        <div class="flex flex-wrap items-center justify-center gap-2">
          <Button @click="add">
            <template #icon>
              <Icon name="add-circle" />
            </template>
            新增
          </Button>
          <Popconfirm
            content="确定删除？"
            theme="danger"
            @confirm="removeBatch"
          >
            <Button
              v-if="selectedRowKeys && selectedRowKeys.length > 0"
              theme="danger"
            >
              <template #icon>
                <Icon name="delete" />
              </template>
              批量删除
            </Button>
          </Popconfirm>
          <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
        </div>
      </div>
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="selectedRowKeys"
        :sort="sort"
        v-bind="tableConfig"
        @drag-sort="onDragSort"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
      >
        <template #op="slotProps">
          <Space size="small">
            <Link theme="primary" @click="edit(slotProps.row)"> 编辑</Link>
            <Popconfirm
              content="确定删除？"
              theme="warning"
              @confirm="remove(slotProps.row)"
            >
              <Link theme="danger">删除</Link>
            </Popconfirm>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
