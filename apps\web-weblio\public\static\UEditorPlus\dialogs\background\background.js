/*! UEditorPlus v2.0.0*/
!function(){function initTabs(){for(var a=$G("tabHeads").children,b=0;b<a.length;b++)domUtils.on(a[b],"click",function(b){for(var c=b.target||b.srcElement,d=0;d<a.length;d++)if(a[d]==c){a[d].className="focus";var e=a[d].getAttribute("data-content-id");$G(e).style.display="block"}else a[d].className="",$G(a[d].getAttribute("data-content-id")).style.display="none"})}function initColorSelector(){var a=editor.queryCommandValue("background");if(a){var b=a["background-color"],c=a["background-repeat"]||"repeat",d=a["background-image"]||"",e=a["background-position"]||"center center",f=e.split(" "),g=parseInt(f[0])||0,h=parseInt(f[1])||0;"no-repeat"==c&&(g||h)&&(c="self"),d=d.match(/url[\s]*\(([^\)]*)\)/),d=d?d[1]:"",updateFormState("colored",b,d,c,g,h)}else updateFormState();var i=function(){updateFormState(),updateBackground()};domUtils.on($G("nocolorRadio"),"click",updateBackground),domUtils.on($G("coloredRadio"),"click",i),domUtils.on($G("url"),"keyup",function(){$G("url").value&&"none"==$G("alignment").style.display&&utils.each($G("repeatType").children,function(a){a.selected="repeat"==a.getAttribute("value")&&"selected"}),i()}),domUtils.on($G("repeatType"),"change",i),domUtils.on($G("x"),"keyup",updateBackground),domUtils.on($G("y"),"keyup",updateBackground),initColorPicker()}function initColorPicker(){var a=editor,b=$G("colorPicker"),c=new UE.ui.Popup({content:new UE.ui.ColorPicker({noColorText:a.getLang("clearColor"),editor:a,onpickcolor:function(a,b){updateFormState("colored",b),updateBackground(),UE.ui.Popup.postHide()},onpicknocolor:function(a,b){updateFormState("colored","transparent"),updateBackground(),UE.ui.Popup.postHide()}}),editor:a,onhide:function(){}});domUtils.on(b,"click",function(){c.showAnchor(this)}),domUtils.on(document,"mousedown",function(a){var b=a.target||a.srcElement;UE.ui.Popup.postHide(b)}),domUtils.on(window,"scroll",function(){UE.ui.Popup.postHide()})}function updateFormState(a,b,c,d,e,f){var g=$G("nocolorRadio"),h=$G("coloredRadio");if(a&&(g.checked="colored"!=a&&"checked",h.checked="colored"==a&&"checked"),b&&domUtils.setStyle($G("colorPicker"),"background-color",b),c&&/^\//.test(c)){var i=document.createElement("a");i.href=c,browser.ie&&(i.href=i.href),c=browser.ie?i.href:i.protocol+"//"+i.host+i.pathname+i.search+i.hash}(c||""===c)&&($G("url").value=c),d&&utils.each($G("repeatType").children,function(a){a.selected=d==a.getAttribute("value")&&"selected"}),(e||f)&&($G("x").value=parseInt(e)||0,$G("y").value=parseInt(f)||0),$G("alignment").style.display=h.checked&&$G("url").value?"":"none",$G("custom").style.display=h.checked&&$G("url").value&&"self"==$G("repeatType").value?"":"none"}function updateBackground(){if($G("coloredRadio").checked){var a=domUtils.getStyle($G("colorPicker"),"background-color"),b=$G("url").value,c=$G("repeatType").value,d={"background-repeat":"no-repeat","background-position":"center center"};a&&(d["background-color"]=a),b&&(d["background-image"]="url("+b+")"),"self"==c?d["background-position"]=$G("x").value+"px "+$G("y").value+"px":"repeat-x"!=c&&"repeat-y"!=c&&"repeat"!=c||(d["background-repeat"]=c),editor.execCommand("background",d)}else editor.execCommand("background",null)}function OnlineImage(a){this.container=utils.isString(a)?document.getElementById(a):a,this.init()}var onlineImage,backupStyle=editor.queryCommandValue("background");window.onload=function(){initTabs(),initColorSelector()},OnlineImage.prototype={init:function(){this.reset(),this.initEvents()},initContainer:function(){this.container.innerHTML="",this.list=document.createElement("ul"),this.clearFloat=document.createElement("li"),domUtils.addClass(this.list,"list"),domUtils.addClass(this.clearFloat,"clearFloat"),this.list.id="imageListUl",this.list.appendChild(this.clearFloat),this.container.appendChild(this.list)},initEvents:function(){var a=this;domUtils.on($G("imageList"),"scroll",function(b){var c=this;c.scrollHeight-(c.offsetHeight+c.scrollTop)<10&&a.getImageData()}),domUtils.on(this.container,"click",function(a){var b=a.target||a.srcElement,c=b.parentNode,d=$G("imageListUl").childNodes;if("li"==c.tagName.toLowerCase()){updateFormState("nocolor",null,"");for(var e,f=0;e=d[f++];)e!=c||domUtils.hasClass(e,"selected")?domUtils.removeClasses(e,"selected"):(domUtils.addClass(e,"selected"),updateFormState("colored",null,c.firstChild.getAttribute("_src"),"repeat"));updateBackground()}})},initData:function(){this.state=0,this.listSize=editor.getOpt("imageManagerListSize"),this.listIndex=0,this.listEnd=!1,this.getImageData()},reset:function(){this.initContainer(),this.initData()},getImageData:function(){var _this=this;if(!_this.listEnd&&!this.isLoadingData){this.isLoadingData=!0;var url=editor.getActionUrl(editor.getOpt("imageManagerActionName")),isJsonp=utils.isCrossDomainUrl(url);ajax.request(url,{timeout:1e5,dataType:isJsonp?"jsonp":"",data:utils.extend({start:this.listIndex,size:this.listSize},editor.queryCommandValue("serverparam")),headers:editor.options.serverHeaders||{},method:"get",onsuccess:function(r){try{var json=isJsonp?r:eval("("+r.responseText+")");"SUCCESS"==json.state&&(_this.pushData(json.list),_this.listIndex=parseInt(json.start)+parseInt(json.list.length),_this.listIndex>=json.total&&(_this.listEnd=!0),_this.isLoadingData=!1)}catch(e){if(r.responseText.indexOf("ue_separate_ue")!=-1){var list=r.responseText.split(r.responseText);_this.pushData(list),_this.listIndex=parseInt(list.length),_this.listEnd=!0,_this.isLoadingData=!1}}},onerror:function(){_this.isLoadingData=!1}})}},pushData:function(a){var b,c,d,e,f=this,g=editor.getOpt("imageManagerUrlPrefix");for(b=0;b<a.length;b++)a[b]&&a[b].url&&(c=document.createElement("li"),d=document.createElement("img"),e=document.createElement("span"),domUtils.on(d,"load",function(a){return function(){f.scale(a,a.parentNode.offsetWidth,a.parentNode.offsetHeight)}}(d)),d.width=113,d.setAttribute("src",g+a[b].url+(a[b].url.indexOf("?")==-1?"?noCache=":"&noCache=")+(+new Date).toString(36)),d.setAttribute("_src",g+a[b].url),domUtils.addClass(e,"icon"),c.appendChild(d),c.appendChild(e),this.list.insertBefore(c,this.clearFloat))},scale:function(a,b,c,d){var e=a.width,f=a.height;"justify"==d?e>=f?(a.width=b,a.height=c*f/e,a.style.marginLeft="-"+parseInt((a.width-b)/2)+"px"):(a.width=b*e/f,a.height=c,a.style.marginTop="-"+parseInt((a.height-c)/2)+"px"):e>=f?(a.width=b*e/f,a.height=c,a.style.marginLeft="-"+parseInt((a.width-b)/2)+"px"):(a.width=b,a.height=c*f/e,a.style.marginTop="-"+parseInt((a.height-c)/2)+"px")},getInsertList:function(){var a,b=this.list.children,c=[],d=getAlign();for(a=0;a<b.length;a++)if(domUtils.hasClass(b[a],"selected")){var e=b[a].firstChild,f=e.getAttribute("_src");c.push({src:f,_src:f,floatStyle:d})}return c}},dialog.onok=function(){updateBackground(),editor.fireEvent("saveScene")},dialog.oncancel=function(){editor.execCommand("background",backupStyle)}}();