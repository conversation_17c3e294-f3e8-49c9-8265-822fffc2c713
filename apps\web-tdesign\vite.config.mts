import { defineConfig } from '@vben/vite-config';
import VueJsx from '@vitejs/plugin-vue-jsx';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      plugins: [VueJsx()],
      server: {
        proxy: {
          '/rgdc-sys': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-sys/, ''),
            target: 'http://127.0.0.1:8080/rgdc-sys',
            ws: true,
          },
          '/rgdc-user': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-user/, ''),
            target: 'http://127.0.0.1:8081/rgdc-user',
            ws: true,
          },
          '/rgdc-workflow': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-workflow/, ''),
            target: 'http://127.0.0.1:8082/rgdc-workflow',
            ws: true,
          },
          '/rgdc-submit': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-submit/, ''),
            target: 'http://127.0.0.1:8083/rgdc-submit',
            ws: true,
          },
          '/rgdc-search': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-search/, ''),
            target: 'http://127.0.0.1:8084/rgdc-search',
            ws: true,
          },
          '/rgdc-statistics': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-statistics/, ''),
            target: 'http://127.0.0.1:8085/rgdc-statistics',
            ws: true,
          },
        },
      },
    },
  };
});
