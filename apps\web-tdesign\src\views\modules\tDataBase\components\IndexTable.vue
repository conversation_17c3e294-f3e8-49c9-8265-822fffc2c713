<script setup lang="ts">

import {
  AddCircleIcon,
  DeleteIcon, DownloadIcon,
  RefreshIcon,
  SearchIcon,
  ChevronUpDoubleIcon,
  ChevronDownDoubleIcon,
} from 'tdesign-icons-vue-next';

import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  MessagePlugin,
  type PageInfo,
  Popconfirm,
  Input,
  Link,
  Space,
  Table,
  TabPanel,
  Tabs, Select, TreeSelect, Cascader,
} from 'tdesign-vue-next';
import {defineProps, onMounted, reactive, ref} from 'vue';
import {useRequest} from 'vue-hooks-plus';

import {baseDownloadFile, baseUploadFile, getDictItems} from '#/api';
import ColumnDisplay from '#/components/column-display/index.vue';
import {BaseTableConfig, Pagination} from '#/utils/constant.ts';
import {removalUnderline} from '#/utils/sort';
import PutAway from '#/components/put-away/index.vue';
import {
  deleteBatch,
  listByPage,
  publishBatchSend,
  publishSend,
  unPublishBatchSend,
  unPublishSend
} from '../api';
import {useRouter} from 'vue-router'
import {
  getAllCodes,
  getDatasetList,
  getLabelList,
  getTemplateListByDataType
} from "#/views/modules/tData/api.ts";
import {listByTree} from "#/views/modules/tDataClassify/api.ts";

const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  editFormRef: {type: Object, default: null},
  cfgEditFormRef: {type: Object, default: null},
  extendSearchObj: {
    type: Object,
    default: () => {
    },
  },
});
const router = useRouter()
const state = reactive({
  tagObj: {},
  selectedRowKeys: [],
  delDailogShow: false,
  dataSource: [],
  hideQuery: false,
  loading: false,
  sort: [],
});
const initStatus = () => {
  state.selectedRowKeys = [];
  state.delDailogShow = false;
  state.hideQuery = false;
  state.loading = false;
};
const tableConfig = ref(BaseTableConfig);
const searchForm = ref();
const formData: any = ref({});
const pagination: any = ref(Pagination);
const columns = ref([
  {colKey: 'row-select', title: '选中标志', type: 'multiple', width: 64,},
  {colKey: 'dataName', ellipsis: true,  sorter: false, title: '数据名称'},
  {colKey: 'dataType_text', ellipsis: true,  sorter: false, title: '数据类型'},
  {colKey: 'version', ellipsis: true, title: '版本号'},
  {colKey: 'publishStatus_text', ellipsis: true, sorter: true, title: '发布状态'},
  {colKey: 'source_text', ellipsis: true, title: '数据来源'},
  {colKey: 'createdBy', ellipsis: true, title: '创建人'},
  {colKey: 'createTime', ellipsis: true, width: 200, sorter: true, title: '创建时间'},
  {colKey: 'op', width: 150, title: '操作', align: 'center', fixed: 'right'},
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item) => item.colKey),
);
const reqRunner = {
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      const {records, total} = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
  deleteBatch: useRequest(deleteBatch, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      state.selectedRowKeys = [];
      selectedRows.value = [];
      reload();
      initStatus();
    },
  }),
  dataExportBatch: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      state.selectedRowKeys = [];
      selectedRows.value = [];
      reload();
      initStatus();
      MessagePlugin.success('导出成功，请查看下载任务');
    },
  }),
  excelExport: useRequest(baseDownloadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
    },
  }),
  excelImport: useRequest(baseUploadFile, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      // 导入完成后刷新列表
      reload();
    },
  }),
  publish: useRequest(publishSend, {
    manual: true,
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      MessagePlugin.success('已发起发布流程，请等待审批完成！');
      reload();
    },
  }),
  unPublish: useRequest(unPublishSend, {
    manual: true,
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      MessagePlugin.success('已发起下架流程，请等待审批完成！');
      reload();
    },
  }),
  publishBatch: useRequest(publishBatchSend, {
    manual: true,
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      publishDailogShow.value = false;
      state.selectedRowKeys = [];
      selectedRows.value = [];
      MessagePlugin.success('已发起发布流程，请等待审批完成！');
      reload();
    },
  }),
  unPublishBatch: useRequest(unPublishBatchSend, {
    manual: true,
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      unPublishDailogShow.value = false;
      state.selectedRowKeys = [];
      selectedRows.value = [];
      MessagePlugin.success('已发起下架流程，请等待审批完成！');
      reload();
    },
  }),
  listByTree: useRequest(listByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {
    },
    onSuccess: (res: any) => {
      treeSelectData.value = res;
    },
  }),
};

const reload = (data?: any) => {
  if (data) {
    state.tagObj = data;
  }
  const {run, loading} = reqRunner.listByPage;
  state.loading = loading;
  run({
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};
const edit = (record?: any) => {
  props.editFormRef?.open(record ? {...record} : {});
};
const publishLink = (record?: any) => {
  record.esIndex = import.meta.env.VITE_ES_INDEX
  reqRunner.publish.run(record);
};
const unPublishLink = (record?: any) => {
  record.esIndex = import.meta.env.VITE_ES_INDEX
  reqRunner.unPublish.run(record);
};
const del = () => {
  for (const item of selectedRows.value) {
    if (item.publishStatus == '1') {
      let mess = '数据 ' + item.dataName + ' 正在发布中，暂不能删除'
      MessagePlugin.warning(mess)
      return
    }
    if (item.publishStatus == '3') {
      let mess = '数据 ' + item.dataName + ' 已发布，暂不能删除'
      MessagePlugin.warning(mess)
      return
    }
    if (item.publishStatus == '4') {
      let mess = '数据 ' + item.dataName + ' 正在下架中，暂不能删除'
      MessagePlugin.warning(mess)
      return
    }
  }
  state.delDailogShow = true;
};
const publishDailogShow = ref(false)
const publishLinkBatch = () => {
  for (const item of selectedRows.value) {
    if (item.publishStatus == '3') {
      let mess = '数据 ' + item.dataName + ' 已经发布，不能再次发布'
      publishDailogShow.value = false;
      MessagePlugin.warning(mess)
      return
    }
  }
  publishDailogShow.value = true;
};
const unPublishDailogShow = ref(false)
const unPublishLinkBatch = () => {
  for (const item of selectedRows.value) {
    if (item.publishStatus == '6') {
      let mess = '数据 ' + item.dataName + ' 已经下架，不能再次下架'
      unPublishDailogShow.value = false;
      MessagePlugin.warning(mess)
      return
    }
  }
  unPublishDailogShow.value = true;
};
const dataExportBatchMethod = () => {
  formData.value.ids_list = state.selectedRowKeys
  const {run} = reqRunner.dataExportBatch;
  run(`/tDataBase/dataExportBatch`, {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(state.sort),
  });
};
const goDataIndex = () => {
  router.push({path: '/tDataIndex'})
};
const delRun = () => {
  state.delDailogShow = false;
  reqRunner.deleteBatch.run(state.selectedRowKeys);
};
const publishBatch = () => {
  reqRunner.publishBatch.run({ids_list: state.selectedRowKeys,esIndex:import.meta.env.VITE_ES_INDEX});
};
const unPublishBatch = () => {
  reqRunner.unPublishBatch.run({ids_list: state.selectedRowKeys,esIndex:import.meta.env.VITE_ES_INDEX});
};
const remove = (record: any) => {
  reqRunner.deleteBatch.run([record.id]);
};
const searchFormSubmit = () => {
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    reload();
  }, 0);
};
const resetSearch = () => {
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  searchForm.value.reset();
  setTimeout(() => {
    reload();
  }, 0);
};

const handleRowClick = (record: any) => {
};

const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};
const selectedRows = ref([])
const rehandleSelectChange = (value: any, ctx: any) => {
  selectedRows.value = ctx.selectedRowData
  state.selectedRowKeys = value;
};

const sortChange = (val: any) => {
  state.sort = val;
  // Request: 发起远程请求进行排序
  reload();
};
const publishStatusDicts = ref()
const datasets = ref()
const dataSourceDicts = ref()
const templateList = ref()
const tabs = ref(1);
onMounted(async () => {
  publishStatusDicts.value = await getDictItems('PUBLISH_STATUS');
  dataSourceDicts.value = await getDictItems('DATA_SOURCE');
  templateList.value = await getAllCodes();
  datasets.value = await getDatasetList({});
  reqRunner.listByTree.run({})
  classess.value = await getDictItems('DATA_LEVEL');
  classess.value.forEach((op) => {
    if (op.value == 0 || op.value == 1) {
      op["disabled"] = true;
    }
  })
  labelses.value = await getLabelList();
  reload();
});

defineExpose({
  reload,
});
const oneDataEdit = (record?: any) => {
  props.cfgEditFormRef?.open(record ? {...record, isReadonly: false} : {});
};
const oneDataView = (record?: any) => {
  props.cfgEditFormRef?.open(record ? {...record, isReadonly: true} : {});
};
const treeSelectData: any = ref([]);
const classess = ref();
const labelses = ref();
</script>

<template>
  <Dialog
    v-model:visible="state.delDailogShow"
    :close-btn="false"
    body="数据删除后无法恢复，是否确认删除？"
    header="删除确认"
    theme="warning"
    @confirm="delRun"
  />
  <Dialog
    v-model:visible="publishDailogShow"
    :close-btn="false"
    body="选中的数据将要被发布，是否确认发布？"
    header="批量发布确认"
    theme="warning"
    @confirm="publishBatch"
  />
  <Dialog
    v-model:visible="unPublishDailogShow"
    :close-btn="false"
    body="选中的数据将要被下架，是否确认下架？"
    header="批量下架确认"
    theme="warning"
    @confirm="unPublishBatch"
  />
  <Space :size="8" class="tiny-tdesign-style-patch w-full" direction="vertical">
    <Card v-if="isSearchForm">
      <Form ref="searchForm" :data="formData" class="w-full" @reset="resetSearch"
            @submit="searchFormSubmit">
        <div class="grid w-full grid-cols-3 gap-3 p-3">
          <FormItem label="数据名称" name="dataName">
            <Input v-model="formData.dataName" clearable placeholder="请输入"/>
          </FormItem>
          <FormItem label="分级" name="classCodeList">
            <Select v-model="formData.classCodeList" :options="classess" multiple clearable
                    placeholder="请选择"/>
          </FormItem>
          <FormItem label="分类" name="categoryCode">
            <Cascader v-model="formData.categoryCode" :options="treeSelectData" clearable
                      placeholder="请选择"
                      check-strictly value-mode="onlyLeaf" :show-all-levels="false"
                      :min-collapsed-num="1"/>
          </FormItem>
          <FormItem v-if="state.hideQuery" label="标签" name="labelCode">
            <Select v-model="formData.labelCode" :options="labelses" clearable
                    placeholder="请选择"
                    :min-collapsed-num="1"/>
          </FormItem>
          <FormItem v-if="state.hideQuery" label="发布状态" name="publishStatus">
            <Select v-model="formData.publishStatus" :options="publishStatusDicts" clearable
                    placeholder="请选择"/>
          </FormItem>
          <FormItem v-if="state.hideQuery" label="数据来源" name="source">
            <Select v-model="formData.source" :options="dataSourceDicts" clearable
                    placeholder="请选择"/>
          </FormItem>
          <FormItem v-if="state.hideQuery" label="创建人" name="createdBy">
            <Input v-model="formData.createdBy" clearable placeholder="请输入"/>
          </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon/>
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon/>
            </template>
            重置
          </Button>
          <PutAway v-model="state.hideQuery" variant="text"/>
        </div>
      </Form>
    </Card>
    <Card>
      <!-- 表格定义区域 -->
      <Table v-model:column-controller-visible="tableConfig.columnControllerVisible"
             v-model:display-columns="displayColumns" :bordered="true" :columns="columns"
             :data="state.dataSource" :hover="true" :loading="state.loading"
             :pagination="pagination"
             :pagination-affixed-bottom="true" :selected-row-keys="state.selectedRowKeys"
             :sort="state.sort" :stripe="true" cell-empty-content="-" lazy-load resizable
             row-key="id" table-layout="fixed" @page-change="rehandlePageChange"
             @row-click="handleRowClick" @select-change="rehandleSelectChange"
             @sort-change="sortChange" v-bind="tableConfig">
        <!--        表格顶部按钮区域-->
        <template #topContent>
          <div class="mb-2 flex w-full justify-start">
            <div class="flex w-full items-center justify-start pl-2">
              <div class="t-card__title mr-2">数据管理表</div>
              <div v-if="state.selectedRowKeys?.length > 0" class="text-blue-600/80">
                选中了[{{ state.selectedRowKeys?.length || 0 }}]行
              </div>
            </div>
            <div class="flex w-full justify-end space-x-2">
              <Button v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                      theme="primary" @click="publishLinkBatch">
                <template #icon>
                  <ChevronUpDoubleIcon/>
                </template>
                批量发布
              </Button>
              <Button v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                      theme="primary" @click="unPublishLinkBatch">
                <template #icon>
                  <ChevronDownDoubleIcon/>
                </template>
                批量下架
              </Button>
<!--              <Button v-if="state.selectedRowKeys && state.selectedRowKeys.length > 0"
                      theme="danger" @click="del">
                <template #icon>
                  <DeleteIcon/>
                </template>
                批量删除
              </Button>-->
<!--              <Button theme="primary" @click="goDataIndex">
                <template #icon>
                  <AddCircleIcon/>
                </template>
                新增
              </Button>-->
<!--              <Button theme="primary" @click="dataExportBatchMethod">
                <template #icon>
                  <DownloadIcon/>
                </template>
                批量导出
              </Button>-->
              <Button variant="text" @click="reload">
                <RefreshIcon/>
              </Button>
              <ColumnDisplay v-model="tableConfig.columnControllerVisible"/>
            </div>
          </div>
        </template>
        <!--        空数据显示定义-->
        <template #empty>
          <div class="flex-col-center flex p-1">
            <div class="text-sx mb-2">暂无数据</div>
            <Button class="w-[100%]" theme="primary" variant="text">
              <template #icon>
                <AddCircleIcon/>
              </template>
              <router-link to="/tDataIndex">点击创建新数据</router-link>
            </Button>
          </div>
        </template>
        <template #op="slotProps">
          <Space size="large">
            <Popconfirm v-if="slotProps.row.publishStatus == '0' || slotProps.row.publishStatus == '2'
                      || slotProps.row.publishStatus == '6'"
                        content="是否确认发布？" theme="default"
                        @confirm="publishLink(slotProps.row)">
              <Link theme="primary"> 发布</Link>
            </Popconfirm>
            <Popconfirm
              v-if="slotProps.row.publishStatus == '3' || slotProps.row.publishStatus == '5'"
              content="是否确认下架？" theme="default" @confirm="unPublishLink(slotProps.row)">
              <Link theme="primary"> 下架</Link>
            </Popconfirm>
<!--            <Link theme="primary" @click="oneDataView(slotProps.row)">
              查看
            </Link>
            <Link v-if="slotProps.row.publishStatus != '1' && slotProps.row.publishStatus != '4'
                      && slotProps.row.publishStatus != '7'"
                  theme="primary" @click="oneDataEdit(slotProps.row)">
              编辑
            </Link>
            <Popconfirm v-if="slotProps.row.publishStatus == '0' || slotProps.row.publishStatus == '2'
                      || slotProps.row.publishStatus == '6' || slotProps.row.publishStatus == '7'"
                        content="确定删除？" theme="warning" @confirm="remove(slotProps.row)">
              <Link theme="danger"> 删除</Link>
            </Popconfirm>-->
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}
</style>
