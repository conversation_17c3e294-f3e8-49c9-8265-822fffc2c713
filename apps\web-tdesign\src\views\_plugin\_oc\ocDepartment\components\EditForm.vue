<script setup lang="tsx">
import type { FormProps } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import { useVbenModal } from '@vben/common-ui';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  TreeSelect,
} from 'tdesign-vue-next';
import { defineProps, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { listByTree, save } from '../api.ts';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
const status = ref([]);
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
  parentId: [
    {
      required: true,
      message: '必填',
    },
  ],
  code: [
    {
      required: true,
      message: '必填',
    },
  ],
  name: [
    {
      required: true,
      message: '必填',
    },
  ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const deptTree: any = ref([]);
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    },
  }),
  listByTree: useRequest(listByTree, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      res.push({
        id: '-1',
        name: '顶级部门',
        children: [],
      });
      deptTree.value = res;
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      /**
       * 保存数据
       */
      reqRunner.save.run({
        ...state.tagObj,
        ...formData.value,
        parentId: state.tagObj.parentId || '-1',
      });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
const loadDept = () => {
  reqRunner.listByTree.run({});
};
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = async (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
  }
  status.value = await getDictItems('BASE_STATUS');
  loadDept();
  /**
   * 打开弹出窗
   */
  modalApi.open();
};

/**
 * 导出资源
 */
defineExpose({
  open,
});
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[30%]">
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="w-full"
      label-align="right"
    >
      <div class="grid w-full grid-cols-1 gap-1">
        <FormItem label="上级部门" name="parentId">
          <TreeSelect
            v-model="formData.parentId"
            :data="deptTree"
            :keys="{
              value: 'id',
              label: 'name',
              children: 'children',
            }"
            placeholder="请选择上级部门"
          />
        </FormItem>
        <FormItem label="部门编号" name="code">
          <Input v-model="formData.code" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="部门名称" name="name">
          <Input v-model="formData.name" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="状态" name="status">
          <Select
            v-model="formData.status"
            :options="status"
            clearable
            placeholder="选择状态"
          />
        </FormItem>
        <FormItem label="备注" name="remark">
          <Textarea
            v-model="formData.remark"
            clearable
            placeholder="请输入内容"
          />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
