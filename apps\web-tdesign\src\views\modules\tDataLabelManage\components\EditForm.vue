<script setup lang="tsx">
import {defineProps, onMounted, reactive, ref} from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {Input, MessagePlugin, Select} from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import { save } from '../api.ts';
import {getDictItems} from "#/api";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
        color: [
           {
             max: 255,
             message: '最大长度为255',
           },
         ],
        labelName: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 255,
             message: '最大长度为255',
           },
         ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      if(res == '保存成功'){
        MessagePlugin.success(res);
        props.outRef?.reload();
        initStatus();
        modalApi.close();
      }else{
        MessagePlugin.warning(res);
      }
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      /**
       * 保存数据
       */
      reqRunner.save.run({ ...state.tagObj, ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const labelColorList = ref();
const open = (data?: any) => {
  if (data) {
    state.tagObj = data;
    formData.value = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
onMounted(async () => {
  labelColorList.value = await getDictItems('LABEL_COLOR');
});
/**
 * 导出资源
 */
defineExpose({
  open,
});
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="w-full"
      label-align="top"
    >
      <div class="grid w-full grid-cols-2 gap-1 p-3">
        <FormItem label="标签名称" name="labelName">
           <Input v-model="formData.labelName" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="标签颜色" name="color">
          <Select v-model="formData.color" :options="labelColorList" clearable placeholder="请选择"/>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
