import popSelect from './pop-select.vue';

const withInstall = <T extends CustomComponent>(
  component: T,
  alias?: string,
) => {
  (component as Record<string, unknown>).install = (app: App) => {
    const compName = component.name || component.displayName;
    if (!compName) return;
    app.component(compName, component);
    if (alias) {
      app.config.globalProperties[alias] = component;
    }
  };
  return component as WithInstall<T>;
};

export const TablePopSelect = withInstall(popSelect);
