<script setup lang="ts">
defineProps<{
  data: Array<{ label: string; value: string }>;
}>();
</script>

<template>
  <table class="overview-table">
    <tbody>
      <div>
        <tr v-for="(item, index) in data" :key="index">
          <td class="label">{{ item.label }}</td>
          <td class="value">{{ item.value }}</td>
        </tr>
      </div>
    </tbody>
  </table>
</template>

<style scoped lang="scss">
.overview-table {
  width: 100%;
  border-collapse: collapse;

  td {
    padding: 8px;
    border: 1px solid #ddd;
  }

  .label {
    font-weight: bold;
    width: 30%; /* 标签列宽度 */
  }

  .value {
    width: 70%; /* 值列宽度 */
  }
}
</style>
