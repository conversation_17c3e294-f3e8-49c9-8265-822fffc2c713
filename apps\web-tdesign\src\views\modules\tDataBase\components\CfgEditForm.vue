<script setup lang="tsx">
import FormRender from '#/views/modules/formEditer/components/render/FormRender.vue';
import {
  getByCode,
  getDatasetList,
  getLabelList,
} from '#/views/modules/tData/api.ts';
import { useVbenDrawer } from '@vben/common-ui';
import { CheckIcon, CloseIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Dialog,
  Form,
  MessagePlugin,
  Textarea,
  Alert,
  Select,
  Cascader,
  FormItem,
  Tag,
} from 'tdesign-vue-next';
import {
  defineEmits,
  defineModel,
  defineProps,
  onMounted,
  reactive,
  ref,
  watch,
} from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { getRealData, save, updateRealData, getCategorys } from '../api.ts';
import { getDictItems } from '#/api';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
  isTask: {
    type: Boolean,
    default: false,
  },
  isHis: {
    type: Boolean,
    default: false,
  },
});

const data = ref();
const datacfg = ref();
const record = ref();
const code = ref();
const isReadonly = ref();
const isTask = ref(props.isTask);
const realData: any = defineModel('realData', {
  type: Array,
  default: () => [
    {
      metadata_code: '',
      content_data: '',
    },
  ],
});
const emit = defineEmits(['edit', 'add', 'claimTask', 'remove']);
const reqRunner = {
  save: useRequest(save, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      props.outRef?.reload();
      initStatus();
      modalApi.close();
    },
  }),
  updateRealData: useRequest(updateRealData, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      if (res == '更新成功') {
        props.outRef?.reload();
        initStatus();
        modalApi.close();
        MessagePlugin.success('数据修改成功！');
      } else {
        MessagePlugin.warning(res);
      }
    },
  }),
};
/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});

/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};

/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenDrawer({
  footer: false,
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      initStatus();
    }
  },
  onConfirm: async () => {
    if (
      classes.value === null ||
      classes.value === undefined ||
      isNaN(classes.value)
    ) {
      MessagePlugin.warning('请选择分级');
      return;
    }
    if (category.value && category.value.length == 0) {
      MessagePlugin.warning('请选择分类');
      return;
    }
    if (!dataset.value) {
      MessagePlugin.warning('请选择数据集');
      return;
    }
    if (approval.value.publishStatus && approval.value.publishStatus == '3') {
      versionDialog.value = true;
    } else {
      state.tagObj.classCode = classes.value;
      state.tagObj.categoryList = category.value;
      state.tagObj.labelCodeList = labels.value;
      state.tagObj.datasetCode = dataset.value;
      reqRunner.updateRealData.run({
        ...record.value,
        ...state.tagObj,
        ...{ dataValue: data.value },
      });
      versionDialog.value = false;
    }
  },
  onCancel: () => {
    initStatus();
    modalApi.close();
  },
});
const cancel = () => {
  modalApi.close();
};
const edit = () => {
  modalApi.onConfirm();
};
/**
 * 供外部调用的方法：打开窗体
 * @param datas 外部传入数据
 */
const open = async (datas?: any) => {
  if (datas) {
    classes.value = datas.classCode ? Number(datas.classCode) : null;
    //获取分类列表
    // debugger;
    categorys.value = await getCategorys(classes.value);
    console.log('数据', categorys.value);
    category.value = JSON.parse(datas.categoryCode);
    //数据集
    datasets.value = await getDatasetList({ categoryList: category.value });
    category.value = category.value.map((item) => Number(item));
    labels.value = JSON.parse(datas.labelCode);
    dataset.value = datas.datasetCode;
    approval.value = datas;
    isReadonly.value = datas.isReadonly;
    categoryList.value = [];
    await getCategoryList(categorys.value);
    formatData(isReadonly.value);
    state.tagObj = datas;
    code.value = datas.templateCode;

    realData.value = await getRealData({
      temp_code: datas.templateCode,
      operation_code: datas.baseCode,
    });
    data.value = {};
    record.value = await getByCode(code.value);
    // 此行问题
    let list = JSON.parse(record.value?.cfg);
    setFormData(list);
    datacfg.value = list;
    // 判断是否可以保存
    if (classes.value != null && category.value.length != 0 && dataset.value) {
      disabled.value = false;
    } else {
      disabled.value = true;
    }
  }
  modalApi.open();
};
const analysis = (temp?: any, item?: any) => {
  item.schema.children[0].schema.children.forEach((item1) => {
    if (isReadonly.value) {
      item1.schema.placeholder = '';
    }
    if (item1.component === 'Collapse') {
      analysis(temp, item1);
    } else {
      let isValue = true;
      for (let i = 0; i < realData.value.length; i++) {
        const key = realData.value[i];
        if (key.metadata_code == item1.schema.name) {
          // item1.component === 'LuckySheet' || item1.component === 'Upload'
          if (item1.component === 'Upload') {
            temp[item1.schema.name] = JSON.parse(key.content_data);
          } else {
            temp[item1.schema.name] = key.content_data;
          }
          item1.schema.isReadonly = isReadonly.value;
          isValue = false;
          break;
        }
      }
      if (isValue) {
        data.value[item1.schema?.name] = item1.schema.value;
        item1.schema.isReadonly = isReadonly.value;
      }
    }
  });
};
const setFormData = (list) => {
  list.forEach((item) => {
    if (item.component === 'Collapse') {
      analysis(data.value, item);
    } else {
      let isValue = true;
      for (let i = 0; i < realData.value.length; i++) {
        const key = realData.value[i];
        if (key.metadata_code == item.schema.name) {
          // item.component === 'LuckySheet' || item.component === 'Upload'
          if (item.component === 'Upload') {
            data.value[item.schema.name] = JSON.parse(key.content_data);
          } else {
            data.value[item.schema.name] = key.content_data;
          }
          item.schema.isReadonly = isReadonly.value;
          isValue = false;
          break;
        }
      }
      if (isValue) {
        data.value[item.schema?.name] = item.schema.value;
        item.schema.isReadonly = isReadonly.value;
      }
    }
  });
};
/**
 * 导出资源
 */
defineExpose({
  open,
});

onMounted(async () => {
  //数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  classess.value.forEach((op) => {
    if (op.value == 0 || op.value == 1) {
      op['disabled'] = true;
    }
  });
  //标签库
  labelses.value = await getLabelList();
});
const rejectDialog = ref(false);
const versionDialog = ref(false);
const rejectReason = ref('');
const approval = ref({});
const pass = () => {
  emit('edit', approval.value);
  cancel();
};
// const labelsName = ref('');
const labelsTags = ref([]);
const datasetName = ref('');
const classesName = ref('');
const categoryName = ref('');
const formatData = (isReadOnly) => {
  let labelsTag;
  if (isReadOnly) {
    // labelsName.value = ''
    labelsTags.value = [];
    datasetName.value = '';
    classesName.value = '';
    categoryName.value = '';
    if (labels.value) {
      for (const item of labels.value) {
        for (const it of labelses.value) {
          if (item == it.value) {
            // labelsName.value += it.label+','
            labelsTag = {};
            labelsTag['key'] = it.value;
            labelsTag['content'] = it.label;
            labelsTag['color'] = it.color;
            labelsTags.value.push(labelsTag);
          }
        }
      }
    }
    if (dataset.value) {
      for (const it of datasets.value) {
        if (dataset.value == it.value) {
          datasetName.value += it.label;
        }
      }
    }
    for (const item of category.value) {
      for (const it of categoryList.value) {
        if (item == it.value) {
          categoryName.value += it.label + ',';
        }
      }
    }
    classesName.value = classess.value[classes.value].label;
    if (categoryName.value) {
      categoryName.value = categoryName.value.slice(
        0,
        categoryName.value.length - 1,
      );
    }
  }
};
const getCategoryList = async (list) => {
  for (const item of list) {
    if (item.children && item.children.length > 0) {
      await getCategoryList(item.children);
    }
    categoryList.value.push(item);
  }
};
const reject = () => {
  rejectDialog.value = true;
};
const handleConfirmRejectDialog = () => {
  if (rejectReason.value.trim() == '') {
    MessagePlugin.warning('请输入驳回原因');
    return;
  } else {
    approval.value.rejectReason = rejectReason.value;
    rejectDialog.value = false;
    remove();
  }
};
const handleCloseRejectDialog = () => {
  rejectDialog.value = false;
};
const handleConfirmVersionDialog = () => {
  state.tagObj.classCode = classes.value;
  state.tagObj.categoryList = category.value;
  state.tagObj.labelCodeList = labels.value;
  state.tagObj.datasetCode = dataset.value;
  reqRunner.updateRealData.run({
    ...state.tagObj,
    ...{ dataValue: data.value },
  });
  versionDialog.value = false;
};
const handleCloseVersionDialog = () => {
  versionDialog.value = false;
};
const remove = async () => {
  emit('remove', approval.value);
  cancel();
};
const disabled = ref(false);
const labels = ref();
const classes = ref();
const category = ref();
const labelses = ref();
const dataset = ref();
const datasets = ref();
const classess = ref();
const categorys = ref();
const categoryList = ref([]);
const changeClass = () => {
  category.value = [];
  if (classes.value != null && category.value.length != 0 && dataset.value) {
    disabled.value = false;
  } else {
    disabled.value = true;
  }
};
const changeCategory = async () => {
  dataset.value = '';
  datasets.value = [];
  datasets.value = await getDatasetList({ categoryList: category.value });
  if (classes.value != null && category.value.length != 0 && dataset.value) {
    disabled.value = false;
  } else {
    disabled.value = true;
  }
};
const changeDataset = () => {
  if (classes.value != null && category.value.length != 0 && dataset.value) {
    disabled.value = false;
  } else {
    disabled.value = true;
  }
};
</script>

<template>
  <Dialog
    v-if="rejectDialog"
    header="确认驳回么"
    theme="warning"
    cancel-btn="取消"
    confirm-btn="确认"
    :visible="rejectDialog"
    @confirm="handleConfirmRejectDialog"
    @cancel="handleCloseRejectDialog"
    :closeBtn="false"
  >
    <div style="padding-top: 20px">
      <Textarea
        v-model="rejectReason"
        placeholder="请输入驳回原因"
        maxlength="200"
      />
    </div>
  </Dialog>
  <Dialog
    v-if="versionDialog"
    header="此版本已上架，若修改会增加新版本，是否确认增加？"
    theme="warning"
    cancel-btn="取消"
    confirm-btn="确认"
    :visible="versionDialog"
    @confirm="handleConfirmVersionDialog"
    @cancel="handleCloseVersionDialog"
    :closeBtn="false"
  >
  </Dialog>
  <Modal
    :header="false"
    :title="state.tagObj?.id ? `编辑` : `新建`"
    class="w-[90%]"
  >
    <div
      class="border-1 mt-2 flex h-full w-full flex-col overflow-auto rounded-lg p-2"
    >
      <div v-if="approval.rejectReason" class="justify-left flex flex-wrap p-5">
        <Alert theme="warning">
          <template #message>驳回原因： {{ approval.rejectReason }}</template>
        </Alert>
      </div>
      <Form ref="searchForm" class="w-[95%]">
        <div v-if="!isReadonly">
          <div class="mt-5 grid w-full grid-cols-3 gap-1">
            <FormItem label="分级" name="dataType" :requiredMark="true">
              <Select
                v-model="classes"
                :options="classess"
                clearable
                placeholder="请选择"
                :on-change="changeClass"
              />
            </FormItem>
            <FormItem label="分类" name="code" :requiredMark="true">
              <Cascader
                v-model="category"
                :options="categorys"
                clearable
                placeholder="请选择"
                multiple
                check-strictly
                value-mode="onlyLeaf"
                :show-all-levels="false"
                :min-collapsed-num="1"
                :on-change="changeCategory"
                :disabled="classes != 0 && (classes == '' || classes == null)"
              />
            </FormItem>
            <FormItem label="数据集" name="dataset" :requiredMark="true">
              <Select
                v-model="dataset"
                :options="datasets"
                clearable
                placeholder="请选择"
                :on-change="changeDataset"
                :disabled="category.length == 0"
              />
            </FormItem>
            <FormItem label="标签" name="labels">
              <Select
                v-model="labels"
                :options="labelses"
                multiple
                clearable
                placeholder="请选择"
              />
            </FormItem>
          </div>
        </div>
        <div v-else>
          <div class="mt-5 grid w-full grid-cols-3 gap-1">
            <FormItem label="分级:" name="dataType">
              {{ classesName }}
            </FormItem>
            <FormItem label="分类:" name="code">
              {{ categoryName }}
            </FormItem>
            <FormItem label="数据集:" name="dataset">
              {{ datasetName }}
            </FormItem>
            <FormItem label="标签:" name="labels">
              <Tag
                v-for="item in labelsTags"
                :key="item.key"
                :color="item.color"
                variant="light-outline"
                style="margin-right: 10px"
              >
                {{ item.content }}
              </Tag>
            </FormItem>
          </div>
        </div>
      </Form>
      <Form
        ref="form"
        :data="data"
        :colon="true"
        class="gap-3"
        label-align="right"
      >
        <div
          class="bg-background flex w-full flex-col rounded-lg shadow"
          style="position: relative"
        >
          <div class="flex-1 rounded-lg p-2">
            <FormRender
              v-model="data"
              :form-config="datacfg"
              class="bg-background w-full p-2"
            />
          </div>
        </div>
      </Form>
      <div
        v-if="isTask"
        class="flex flex-wrap items-center justify-center gap-10 p-10"
      >
        <Button v-if="!isHis" theme="primary" @click="pass">
          <template #icon>
            <CheckIcon />
          </template>
          审批通过
        </Button>
        <Button v-if="!isHis" theme="danger" @click="reject">
          <template #icon>
            <CloseIcon />
          </template>
          驳回
        </Button>
        <Button theme="default" @click="cancel"> 取消 </Button>
      </div>
      <div
        v-else-if="!isTask && !isReadonly"
        class="flex flex-wrap items-center justify-center gap-10 p-10"
      >
        <Button theme="primary" @click="edit" :disabled="disabled">
          保存
        </Button>
        <Button theme="default" @click="cancel"> 取消 </Button>
      </div>
      <div
        v-else-if="!isTask && isReadonly"
        class="flex flex-wrap items-center justify-center gap-10 p-10"
      >
        <Button theme="default" size="large" @click="cancel"> 取消 </Button>
      </div>
    </div>
  </Modal>
</template>

<style scoped></style>
