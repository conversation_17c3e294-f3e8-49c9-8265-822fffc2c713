// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/tDataBase/listByPage2', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/tDataBase/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataBase/getOne/${data}`);
}
export async function getOneShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tDataBase/getOneShare`, data);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/tDataBase/getByIds/${data}`);
}
export async function getRealData(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/getRealData', data);
}
export async function getRealDataShare(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/getRealDataShare', data);
}
export async function updateRealData(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/updateRealData', data);
}
export async function publishSend(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/publish', data);
}
export async function unPublishSend(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/unPublish', data);
}
export async function publishBatchSend(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/publishBatch', data);
}
export async function unPublishBatchSend(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataBase/unPublishBatch', data);
}
export async function getCategorys(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tDataClassify/listByTree/${data}`);
}
export async function getCategorysShare(data: any) {
  return requestClient.post<any>(`/rgdc-submit/tDataClassify/listByTreeShare/${data}`);
}
export async function statistics() {
  return requestClient.get<any>(`/rgdc-submit/tDataBase/statistics`);
}
