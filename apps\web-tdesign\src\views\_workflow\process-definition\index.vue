<script setup lang="ts">
import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { MessagePlugin } from 'tdesign-vue-next';

import { deploy } from './api';
import DesignDrawer from './components/DesignDrawer.vue';
import IndexTable from './components/IndexTable.vue';
// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();

const drawerRef = ref();

const [Drawer, drawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: DesignDrawer,
});

const workFlowData = async (data: any) => {
  // 获取数据后整理提交
  const res: any = await deploy({
    repeat: data.repeat,
    jsonString: JSON.stringify(data.modelContent),
  });
  if (res) {
    drawerApi.close();
    MessagePlugin.success('部署成功');
    indexTable.value.refresh();
  }
};
const add: any = () => {
  drawerApi.open();
  drawerApi.setData({ onConfirm: workFlowData });
};

const edit: any = (record: any) => {
  // modalApi.setState({ title: '编辑' });
  drawerApi.setData({ record, onConfirm: workFlowData });
  drawerApi.open();
};
</script>

<template>
  <Page description="工作流流程定义列表" title="流程定义">
    <IndexTable ref="indexTable" @add="add" @edit="edit" />
    <Drawer ref="drawerRef" class="w-full" />
  </Page>
</template>
