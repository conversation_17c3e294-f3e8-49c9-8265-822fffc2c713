<script setup lang="tsx">
import { defineProps, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import { Input } from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import { save } from '../api.ts';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
    dataType: [
      {
        required: true,
        message: '请填写数据类型名称',
      },
      {
       max: 32,
       message: '最大长度为32',
      },
     ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      props.outRef?.reload();
      initStatus();
      modalApi.close();
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      initStatus();
    }
  },
  onConfirm: async () => {
    const vali = await form.value.validate();
    if (vali === true) {
      reqRunner.save.run({ ...state.tagObj, ...formData.value });
    }
  },
  onCancel: () => {
    initStatus();
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});
</script>
<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form ref="form" :data="formData"  :rules="FORM_RULES" class="w-full" label-align="left">
      <div class="items-center grid w-[90%] grid-cols-1 gap-1 pt-[6%]">
        <FormItem label="数据类型名称" name="dataType">
           <Input v-model="formData.dataType" clearable placeholder="请输入" />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>
<style scoped></style>
