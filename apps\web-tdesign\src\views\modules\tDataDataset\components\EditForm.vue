<script setup lang="tsx">
import {defineProps, onMounted, reactive, ref} from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {Cascader, Input, MessagePlugin, Select, Textarea} from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
} from 'tdesign-vue-next';

import { save } from '../api.ts';
import {getCategorys, getDatasetList, getLabelList} from "#/views/modules/tData/api.ts";
import {getDictItems} from "#/api";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
  datasetName: [
     {
       required: true,
       message: '必填',
     },
     {
       max: 90,
       message: '最大长度为90',
     },
  ],
  datasetDescribe: [
     {
       required: true,
       message: '必填',
     },
     {
       max: 4294967295,
       message: '最大长度为4294967295',
     },
  ],
  classCode: [
     {
       required: true,
       message: '必选',
     },
  ],
  categoryList: [
     {
       required: true,
       message: '必选',
     },
  ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      if(res == '保存成功'){
        MessagePlugin.success(res);
        props.outRef?.reload();
        initStatus();
        modalApi.close();
      }else{
        MessagePlugin.warning(res);
      }
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      /**
       * 保存数据
       */
      reqRunner.save.run({ ...state.tagObj, ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = async (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
    if(formData.value.classCode && data.categoryCode){
      categorys.value = await getCategorys(formData.value.classCode);
      let categoryList = JSON.parse(data.categoryCode)
      formData.value.categoryList = categoryList.map(item => Number(item))
    }
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});
const classess = ref()
const categorys = ref([])
const changeClass = async () => {
  formData.value.categoryList = []
  //获取分类列表
  categorys.value = await getCategorys(formData.value.classCode);
}
onMounted(async () => {
  //数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  classess.value.forEach((op) => {
    if(op.value == 0 || op.value == 1){
      op["disabled"] = true;
    }
  })
});
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form ref="form" :data="formData" :rules="FORM_RULES" class="w-full" label-align="top">
      <div class="grid w-full grid-cols-2 gap-1">
        <FormItem label="分级" name="classCode">
          <Select v-model="formData.classCode" :options="classess" clearable placeholder="请选择"
                  :on-change="changeClass" :on-clear="changeClass"/>
        </FormItem>
        <FormItem label="分类" name="categoryList">
          <Cascader v-model="formData.categoryList" :options="categorys" clearable placeholder="请选择"
                    multiple check-strictly value-mode="onlyLeaf" :show-all-levels="false" :min-collapsed-num="1"
                    :disabled="!categorys.length > 0"/>
        </FormItem>
      </div>
      <div class="grid w-full grid-cols-1 gap-1">
        <FormItem label="数据集名称" name="datasetName">
           <Input v-model="formData.datasetName" clearable placeholder="请输入数据集名称" />
        </FormItem>
        <FormItem label="数据集描述" name="datasetDescribe">
           <Textarea v-model="formData.datasetDescribe" clearable placeholder="请输入数据集描述" :autosize="{ minRows: 3, maxRows: 20 }"/>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
