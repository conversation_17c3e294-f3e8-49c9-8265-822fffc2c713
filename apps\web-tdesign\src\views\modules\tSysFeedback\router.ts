import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '反馈管理',
    },
    name: 'tSysFeedback',
    path: '/tSysFeedback',
    children: [
      {
        meta: {
          title: '反馈管理编辑',
        },
        name: 'tSysFeedbackIndex',
        path: '/tSysFeedback/index',
        component: () =>
          import('#/views/modules/tSysFeedback/index.vue'),
      },
    ],
  },
];

export default routes;
