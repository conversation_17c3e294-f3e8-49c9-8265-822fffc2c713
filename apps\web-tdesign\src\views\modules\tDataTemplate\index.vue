<script setup lang="tsx">
import { Page } from '@vben/common-ui';
import { ref } from 'vue';

import CfgEditForm from './components/CfgEditForm.vue';
import EditForm from './components/EditForm.vue';
import IndexTable from './components/IndexTable.vue';

const editFormRef = ref();
const tableRef = ref();
const cfgEditFormRef = ref();
</script>

<template>
  <Page title="数据模板管理">
    <CfgEditForm ref="cfgEditFormRef" :out-ref="tableRef" />
    <EditForm ref="editFormRef" :out-ref="tableRef"/>
    <IndexTable ref="tableRef" :cfg-edit-form-ref="cfgEditFormRef" :edit-form-ref="editFormRef"/>
  </Page>
</template>

<style scoped></style>
