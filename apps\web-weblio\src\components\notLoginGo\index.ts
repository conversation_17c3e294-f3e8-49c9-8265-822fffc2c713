import { useAuthStore } from '#/store/auth';
import { useUserStore } from '@vben/stores';
import { DialogPlugin } from 'tdesign-vue-next';

const userStore = useUserStore();
const authStore = useAuthStore();
// 登录校验
export const isLogin = () => {
  return new Promise((resolve, reject) => {
    const userInfo = userStore.userInfo;
    if (userInfo) {
      resolve(true);
    } else {
      const confirmDia = DialogPlugin({
        header: '提示',
        body: '您暂未登录，是否前往登录？',
        onConfirm: () => {
          authStore.goLoginPage();
          confirmDia.hide();
        },
        onCancel: () => {
          reject(new Error('请先登录'));
          confirmDia.hide();
        },
      });
    }
  });
};
