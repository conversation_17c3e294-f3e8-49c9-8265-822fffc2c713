<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>化工知识协同加工与管理平台</title>
    <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
    <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
    <link rel="stylesheet" href="../static/vue/theme/index.css">
    <script src="../static/vue/min/vue.min.js"></script>
    <script src="../static/vue/element-ui2.15.13_index.js"></script>
    <script src="../static/vue/axios0.26.0_axios.min.js"></script>
    <style>
        html,
        body {
            min-width: 100%;
        }

        .mon_warp {
            margin: 0px;
            width: 100%;
            background-size: cover;
        }

        .mon_body {
            display: none;
            width: 100%;
        }

        .el-menu-vertical-demo {
            height: 100%;
        }

        .el-card {
            margin-top: 20px;
        }

        .el-upload__tip {
            margin-top: 10px;
        }

        .clearfix:before,
        .clearfix:after {
            display: table;
            content: "";
        }

        .clearfix:after {
            clear: both;
        }

        .center {
            border: 1px solid #ccc;
            width: 60%;
            margin: 20px auto 20px;
            border-radius: 20px;
            padding: 30px;
            min-width: 1200px;
        }

        .upload-demo {
            width: 100%;
        }

        .el-upload-dragger {
            width: 660px;
            height: 250px;
            padding: 40px;
        }

        .el-upload__text {
            font-size: 16px;
            margin: 20px 0;
        }

        .el-icon-upload {
            font-size: 67px;
            margin: 20px 0;
        }

        .el-menu-item.is-active {
            background-color: #ecf5ff;
            color: #409EFF;
        }

        .el-menu-item {
            font-size: 14px;
            height: 56px;
            line-height: 56px;
        }

        .el-menu-item:hover {
            background-color: #ecf5ff;
        }

        .download-notice {
            font-size: 14px;
            color: #666;
            display: inline-block;
            margin-top: 10px;
        }

        .notice-icon {
            color: #ff9800;
            margin-right: 5px;
        }

        .download-all-container {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .left {
            width: 70%;
        }

        .np_input_senior {
            width: 100%;
            min-height: 200px;
            margin-top: 15px;
            background: #f6f6f6;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }

        .bre_body {
            padding: 40px 0px 40px 0px;
            width: 100%;
            max-width: 800px;
            margin: auto;
        }

        .bre_content {
            width: 100%;
        }

        .bre_cols {
            width: 100%
        }

        .bre_content+.bre_content {
            margin-top: 15px;
        }

        .bre_col_fields {
            width: 230px;
            float: left;
        }

        .bre_col_values {
            width: calc(100% - 240px);
            float: right;
        }

        .dc_col_condition {
            width: 90px;
            float: left;
        }

        .bre_col_field {
            width: 130px;
            float: left;
            margin-left: 10px;
        }

        .bre_col_match {
            width: 90px;
            float: left;
            margin-left: 10px;
        }

        .bre_col_value {
            width: calc(100% - 294px);
            float: left;
            margin-left: 10px;
        }

        .bre_col_btn {
            width: 44px;
            float: right;
            margin-top: 4px;
        }

        .bre_btn {
            margin-top: 20px;
        }

        .np_table_history {
            margin-top: 15px;
        }

        .mls_card_list p {
            padding: 3px 0px 0px 0px;
            line-height: 22px;
        }

        .mls_card_abstract {
            height: 66px;
        }

        .mls_card_content {
            padding-bottom: 15px;
            font-size: 13px;
            margin-top: 5px;
        }

        .np_title_icon {
            top: 0px;
        }

        .datatitle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* 新增表格样式 */
        .el-table .cell {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            display: inline-block;
            vertical-align: middle;
        }

        .el-table th>.cell {
            white-space: nowrap;
        }

        .centered-cell {
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            height: 100%;
        }

        .el-table td,
        .el-table th {
            padding: 8px 0;
        }

        .el-table--border td:first-child .cell,
        .el-table--border th:first-child .cell {
            padding-left: 10px;
        }

        .el-table--border td:last-child .cell,
        .el-table--border th:last-child .cell {
            padding-right: 10px;
        }
    </style>
</head>

<body>
    <div class="header_app" id="header_app"></div>

    <div class="mon_warp clearfix" id="app">
        <div class="mon_body clearfix">
            <el-row :gutter="20">
                <!-- 左侧菜单 -->
                <el-col :span="3">
                    <div style="padding-top:20px">
                        <el-menu :default-active="menuActive"  class="el-menu-vertical-demo" 
             @select="handleMenuSelect"
            style="padding-top:20px;"
              active-text-color="#409EFF">
                 <el-menu-item index="cleanTool">
                   <el-tooltip content="数据汇聚与清洗工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据汇聚与清洗工具
                  </span>
                </el-tooltip>
                  </el-menu-item>
              <el-menu-item index="classiFication">

                 <el-tooltip content="数据整编与分类工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据整编与分类工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="home">
                <el-tooltip content="全文多模态解析重组工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    全文多模态解析重组工具
                  </span>
                </el-tooltip>
              </el-menu-item><el-menu-item index="relationship">
                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    知识对象及关系挖掘工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>

            </el-menu>
                    </div>
                </el-col>

                <!-- 右侧内容 -->
                <el-col :span="21">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <h2>数据汇聚与清洗工具</h2>
                        </div>
                        <div>
                            <p style="font-size: 16px;">
                                多来源获取科技文献原始语料数据，能够对获取到的数据进行机器清洗和人工校正补全等操作，并邀请化工领域专家深入参与数据遴选流程，结合领域知识对原始数据进行筛选与评估，剔除无关或低质量数据，确保收集到的语料具有高质量和高相关性。

                            </p>
                            <div style="text-align: right;width: 100%;padding-top: 10px;">

                            </div>
                            <div class="np_input_senior clearfix">
                                <div class="bre_body np_from">
                                    <div class="bre_content clearfix" v-for="(item, index) in searchContent">
                                        <div class="bre_cols clearfix" v-if="index == 0">
                                            <div class="bre_col_fields">
                                                <el-select v-model="item.field">
                                                    <el-option value="Article_Title" label="Article_Title"></el-option>
                                                    <el-option value="DOI" label="DOI"></el-option>
                                                    <el-option value="Publication_Year"
                                                        label="Publication_Year"></el-option>
                                                    <el-option value="Source_Title" label="Source_Title"></el-option>
                                                </el-select>
                                            </div>

                                            <div class="bre_col_values">
                                                <el-input v-model="item.value" placeholder="请输入并检索"></el-input>
                                            </div>
                                        </div>
                                        <div class="bre_cols clearfix" v-else>
                                            <div class="dc_col_condition">
                                                <el-select v-model="item.condition" placeholder="请选择"
                                                    style="width: 100%;">
                                                    <el-option v-for="item in conditionList" :key="item.value"
                                                        :label="item.label" :value="item.value"></el-option>
                                                </el-select>
                                            </div>
                                            <div class="bre_col_field">
                                                <el-select v-model="item.field">
                                                    <el-option value="Article_Title" label="Article_Title"></el-option>
                                                    <el-option value="DOI" label="DOI"></el-option>
                                                    <el-option value="Publication_Year"
                                                        label="Publication_Year"></el-option>
                                                    <el-option value="Source_Title" label="Source_Title"></el-option>
                                                </el-select>
                                            </div>
                                            <div class="bre_col_value">
                                                <el-input v-model="item.value" placeholder="请输入"></el-input>
                                            </div>
                                            <div class="bre_col_btn">
                                                <el-button size="small" @click="delete_searchContent(item, index)"
                                                    type="danger" icon="el-icon-minus"></el-button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bre_btn clearfix">
                                        <el-button @click="add_searchContent" size="small" type="primary"><i
                                                class="el-icon-plus"></i> <span>添加行</span></el-button>
                                        <div style="float: right">
                                            <el-button size="small" type="primary" @click="generalSearch"
                                                :loading="dataLoading">&nbsp;&nbsp;&nbsp;检&nbsp;&nbsp;索&nbsp;&nbsp;&nbsp;</el-button>
                                            <el-button size="small"
                                                @click="clearGeneralSQL">&nbsp;&nbsp;&nbsp;清&nbsp;&nbsp;除&nbsp;&nbsp;&nbsp;</el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="datatitle">
                                <div>
                                    <span>找到数据 <span style="color: #167cc6;" v-html="assetsTotal"></span>条</span>
                                </div>
                                <div>
                                    <el-button type="primary" size="medium" @click="addClick">添加数据</el-button>
                                    <el-button type="warning" size="medium" @click="cleanUtil">数据清洗</el-button>
                                </div>
                            </div>
                            <div class="content_table">
                                <el-table :data="tableData" style="width: 100%" border>
                                    <!-- 数据状态 -->
                                    <el-table-column prop="is_down_pdf" label="数据状态" width="100" align="center">
                                        <template #default="scope">
                                            <div class="centered-cell">
                                                <el-tag type="success" v-if="scope.row.is_down_pdf > 0">已有</el-tag>
                                                <el-tag type="info" v-else>待获取</el-tag>
                                            </div>
                                        </template>
                                    </el-table-column>

                                    <!-- 全文图标 -->
                                    <el-table-column label="全文" width="100" align="center">
                                        <template #default="scope">
                                            <div class="centered-cell">
                                                <el-tooltip content="下载PDF" placement="top">
                                                    <el-button icon="el-icon-document" type="primary" circle
                                                        @click="downloadPDF(scope.row.file_name)"></el-button>
                                                </el-tooltip>
                                            </div>
                                        </template>
                                    </el-table-column>  
                                          <!-- DOI -->
                                    <el-table-column prop="DOI" label="DOI" width="180" align="center"
                                        show-overflow-tooltip>
                                        <template #default="scope">
                                            <span v-html="scope.row.DOI"></span>
                                        </template>
                                    </el-table-column>
                                    <!-- Article_Title -->
                                    <el-table-column prop="Article_Title" label="Article_Title" align="center"
                                        show-overflow-tooltip>
                                        <template #default="scope">
                                            <span v-html="scope.row.Article_Title"></span>
                                        </template>
                                    </el-table-column>

                                    <!-- Source_Title -->
                                    <el-table-column prop="Source_Title" label="Source_Title" align="center"
                                        show-overflow-tooltip>
                                        <template #default="scope">
                                            <span v-html="scope.row.Source_Title"></span>
                                        </template>
                                    </el-table-column>

                                    <!-- 发表年 -->
                                    <el-table-column prop="Publication_Year" label="Publication_Year" width="120" align="center"
                                        show-overflow-tooltip>
                                        <template #default="scope">
                                            <span v-html="scope.row.Publication_Year"></span>
                                        </template>
                                    </el-table-column>

                               

                                    <!-- 操作按钮 -->
                                    <el-table-column label="操作" width="150" align="center">
                                        <template #default="scope">
                                            <div class="centered-cell">
                                                <el-tooltip content="修改" placement="top" >
                                                    <el-button icon="el-icon-edit" size="mini" circle @click="editEvent(scope.row)"></el-button>
                                                </el-tooltip>
                                                <el-tooltip content="下载" placement="top">
                                                    <el-button icon="el-icon-download" size="mini" circle @click="downloadZIP(scope.row)"></el-button>
                                                </el-tooltip>
                                                <el-tooltip content="删除" placement="top">
                                                    <el-button icon="el-icon-delete" size="mini" circle type="danger"
                                                        @click="deleteData(scope.row.id)"></el-button>
                                                </el-tooltip>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>

                            <div class="son_pagination">
                                <el-pagination background  layout="total, prev, pager, next"
                                    @current-change="queryCurrentChange" @size-change="queryHandleSizeChange"
                                    :page-size="pageSize" :total="assetsTotal" :current-page="page">

                                </el-pagination>
                            </div>

                        </div>
                    </el-card>
                </el-col>
            </el-row>


        </div>
    </div>

    <div id="dataid" data="{{session.user}}" style="display:none"></div>
    <div class="mon_footer"></div>

    <script src="../static/js/jquery.min.js"></script>
    <script src="../static/js/monitor.js"></script>

    <script>
        let _this = this;
        var $_GET = (function () {
            var url = window.document.location.href.toString(); //获取的完整url
            var u = url.split("?");
            if (typeof (u[1]) == "string") {
                u = u[1].split("&");
                var get = {};
                for (var i in u) {
                    var j = u[i].split("=");
                    get[j[0]] = j[1];
                }
                return get;
            } else {
                return {};
            }
        })();
        const vm = new Vue({
            el: '#app',
            data: { 
                activeIndex: '3',
                page: 1,
                pageSize: 5,
                assetsTotal: 0,
                literatureType: '论文',
                docType: 'PDF',
                isScanned: '否',
                fileList: [],
                dialogVisible: false,
                btnStart: "开始汇聚",
                uploadLists: [],
                conditionList: [
                    { value: 'AND', label: 'AND' },
                    { value: 'OR', label: 'OR' },
                    { value: '<>', label: 'NOT' },
                ],
                menuActive: "cleanTool",
                loading: false,
                dataLoading: false,
                fileName: '', // 新增：文件名称输入框
                searchContent: [
                    {
                        'condition': 'AND',
                        'field': 'Article_Title',
                        'value': '',


                    },
                   
                ],
                tableData: [],
                task_name:'',
            },
            mounted() {
                $('.mon_body').css({ 'display': 'revert' });
                if ($_GET["table_name"]) {
                    this.getTableData(); // 获取数据
                }


            },
            methods: {

        //编辑回显
        editEvent(row){
        location.href = `./cleanDetail?table_name=${$_GET["table_name"] || ""}&data_id=${row.id}&isEdit=1&task_name=${this.task_name}`
        },
        
        //下载zip
       downloadZIP(row) {
  let that = this;
  this.$confirm('确定要下载此ZIP文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    let param = {
      data_id: row.id,
      table_name: $_GET["table_name"] || ""
    };
    axios.post(server_url + '/gather_api/export_one_data', JSON.stringify(param), {
      headers: { 'Content-Type': "application/json;charset=utf-8" },
      timeout: 60000,
      responseType: 'blob'
    }).then(function (res) {
      if (res.data) {
        // Create a blob from the response data
        const blob = new Blob([res.data], { type: 'application/zip' });
        
        // Create a download link
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        
        // Set the filename (you might want to get this from headers or response)
        const fileName = row.name || 'download.zip'; // Adjust as needed
        link.setAttribute('download', fileName);
        
        // Append to body, click and remove
        document.body.appendChild(link);
        link.click();
        
        // Clean up
        window.URL.revokeObjectURL(downloadUrl);
        document.body.removeChild(link);
        
        that.$message({
          type: 'success',
          message: '下载成功!'
        });
      } else {
        that.$message.error('下载失败: 无返回数据');
      }
    }).catch(error => {
      console.error('下载失败:', error);
      that.$message.error('下载失败: ' + (error.message || '未知错误'));
    });
  }).catch(() => {
    that.$message({
      type: 'info',
      message: '已取消下载'
    });
  });
},
             downloadPDF(name) {
  let that = this;
  this.$confirm('确定要下载此PDF文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    let param = {
      "file_name": name
    };
    axios.post(server_url + '/gather_api/get_pdf',
      JSON.stringify(param), {
        headers: { 'Content-Type': "application/json;charset=utf-8" },
        timeout: 60000,
        responseType: 'blob' // Expect binary data for PDF
      }).then(function (res) {
        if (res.status === 200) {
          const blob = new Blob([res.data], { type: 'application/pdf' });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = name || 'document.pdf'; // Use provided name or default
          document.body.appendChild(link);
          link.click();
          window.URL.revokeObjectURL(downloadUrl);
          document.body.removeChild(link);
          that.$message.success('PDF下载成功！');
        } else {
          that.$message.error('PDF下载失败！');
        }
      }).catch(function (error) {
        console.error('PDF下载失败：', error);
        that.$message.error('下载失败，请稍后重试！');
      });
  }).catch(() => {
    that.$message.info('已取消下载');
  });
},
                //删除单条数据
                deleteData(id) {
                    let that = this;
                    this.$confirm('确定要删除这条数据吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        let param = {
                            data_id: id,
                            table_name: $_GET["table_name"]
                        };
                        axios.post(server_url + '/gather_api/delete_one_data',
                            JSON.stringify(param), {
                            headers: { 'Content-Type': "application/json;charset=utf-8" },
                            timeout: 60000
                        }).then(function (res) {
                            if (res.data.code == 0) {
                                that.$message.success('删除成功！');
                                that.getTableData(); // 删除后刷新表格数据
                            } else {
                                that.$message.error('删除失败！');
                            }
                        }).catch(function (error) {
                            console.log(error);
                            that.$message.error('删除失败，请稍后重试！');
                        });
                    }).catch(() => {
                        // 用户点击取消时的操作
                        that.$message.info('已取消删除');
                    });
                },
                addClick() {
                     location.href = `./cleanDetail?table_name=${$_GET["table_name"] || ""}&isEdit=0&task_name=${this.task_name}`;
                },
                queryCurrentChange(val) {
                    this.page = val;
                    this.getTableData();

                },
                queryHandleSizeChange(val) {
                    this.page = 1;
                    this.pageSize = val;
                    this.getTableData();
                    // this.backToTop()
                },
                cleanUtil() {
                    location.href = `./cleanUtil?table_name=${$_GET["table_name"] || ""}`
                },
                add_searchContent() {
                    let sc_object = { 'condition': 'AND', 'field': 'Article_Title', 'value': '', 'match': 'like', }
                    this.$set(this.searchContent, this.searchContent.length, sc_object);
                },
                delete_searchContent(row, index) {
                    this.searchContent.splice(index, 1);
                },
                clearGeneralSQL() {
                    this.searchContent = [
                        {
                            'condition': 'AND',
                            'field': 'Article_Title',
                            'value': '',
                            'match': 'like',
                            'match_list': match_str_list
                        },
                        {
                            'condition': 'AND',
                            'field': 'Article_Title',
                            'value': '',
                            'match': 'like',
                            'match_list': match_str_list
                        }
                    ]
                },

                //输入检索词进行检索
                generalSearch() {
                    console.log(this.searchContent);
                    this.page = 1
                    this.getTableData()
                },
                handleMenuSelect(index) {
    if (index === 'cleanTool') {
      window.location.href = '/cleanTool'; // 跳转到对应的页面
    } else if (index === 'classiFication') {
      window.location.href = '/classiFication';
    } else if (index === 'relationship') {
      window.location.href = '/relationship';
    } else if (index === 'home') {
      window.location.href = '/'; // 跳转到首页
    } else if (index === 'qualitycontrol') {
      window.location.href = '/qualitycontrol'; // 跳转到质量控制工具
    } 
    this.menuActive = index; // 更新高亮状态
  },
                startopen() {
                    this.dialogVisible = true;
                    this.getTableData(); // 打开对话框时获取最新的上传记录
                },
                handleRemove(file, fileList) {
                    console.log('File removed:', file);
                },
                handlePreview(row) {
                    // 预览功能可以根据需要实现
                    location.href = `./demonstration?session_id=${row.session_id}`;
                },


                handleDownload(row) {
                    const that = this;
                    axios({
                        url: server_url + '/api/get_zip_by_session_id',
                        method: 'GET',
                        params: { session_id: row.session_id },
                        responseType: 'blob' // 告诉 axios 这是一个二进制文件
                    }).then(function (response) {
                        const blob = new Blob([response.data]);
                        const downloadUrl = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = downloadUrl;

                        // 可选：给文件命名
                        const fileName = `下载文件_${row.session_id || 'unknown'}.zip`;
                        link.download = fileName;

                        document.body.appendChild(link);
                        link.click();
                        window.URL.revokeObjectURL(downloadUrl);
                        document.body.removeChild(link);
                    }).catch(function (error) {
                        console.log('文件下载失败：', error);
                    });
                },

                getTableData() {
                    // 这里可以添加获取表格数据的逻辑
                    let that = this
                    let param = {
                        select_conditions: that.searchContent || [],
                        table_name: $_GET["table_name"] || "",
                        page: that.page,
                        page_size: that.pageSize
                    }
                    axios.post(server_url + '/gather_api/select_task_data',
                        JSON.stringify(param), {
                        headers: { 'Content-Type': "application/json;charset=utf-8", },
                        timeout: 60000
                    }).then(function (res) {
                        if (res.data.code == 0) {
                            const list = res.data.data
                            that.tableData = list
                            that.assetsTotal = res.data.total
                            that.task_name = res.data.task_name
                        }

                    }).catch(function (error) {
                        console.log(error);
                        that.enterpriseLoading = false; // Ensure loading state is turned off in case of an error
                    });
                },
            }
        });
    </script>
</body>

</html>