import { allAccountList } from '#/views/_sys/account/api';
import { allRoleList } from '#/views/_sys/role/api';

// 审批工作流人员/组织选择器配置

export default {
  user: {
    apiObj: allAccountList,
    format(data) {
      return data.map((item) => {
        return {
          value: item.id,
          label: item.accountNumber,
        };
      });
    },
    props: {
      value: 'value',
      label: 'label',
    },
  },
  // 配置角色
  role: {
    // 请求接口对象
    apiObj: allRoleList,
    format(data) {
      return data.map((item) => {
        return {
          code: item.code,
          label: item.name,
        };
      });
    },
    // 显示数据字段映射
    props: {
      value: 'code',
      label: 'label',
    },
  },
};
