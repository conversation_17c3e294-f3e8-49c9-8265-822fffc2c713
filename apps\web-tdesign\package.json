{"name": "@vben/web-tdesign", "version": "5.5.3", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-tdesign"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@iconify/json": "catalog:", "@iconify/vue": "catalog:", "@micro-zoe/micro-app": "1.0.0-rc.20", "@types/cross-storage": "catalog:", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "axios": "catalog:", "cross-storage": "catalog:", "dingtalk-jsapi": "^2.15.6", "dom-to-image": "^2.6.0", "json-editor-vue": "^0.17.3", "less": "catalog:", "luckyexcel": "^1.0.1", "pinia": "catalog:", "showdown": "^2.1.0", "tdesign-icons-vue-next": "catalog:", "tdesign-vue-next": "catalog:", "vue": "catalog:", "vue-clipboard3": "catalog:", "vue-codemirror": "^6.1.1", "vue-hooks-plus": "catalog:", "vue-router": "catalog:", "vue-ueditor-wrap": "3.x", "vuedraggable": "catalog:", "x-data-spreadsheet": "^1.1.9"}, "devDependencies": {"@types/showdown": "^2.0.6"}}