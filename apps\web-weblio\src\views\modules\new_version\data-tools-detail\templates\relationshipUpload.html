<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>化工知识协同加工与管理平台</title>
  <link rel="shortcut icon" type="image/x-icon" href="../static/img/logo3.png">
  <link rel="stylesheet" href="../static/css/common.css?v=20251111" />
  <link rel="stylesheet" href="../static/vue/theme/index.css">
  <script src="../static/vue/min/vue.min.js"></script>
  <script src="../static/vue/element-ui2.15.13_index.js"></script>
  <script src="../static/vue/axios0.26.0_axios.min.js"></script>
  <style>
    html,
    body {
      min-width: 100%;
    }

    .mon_warp {
      margin: 0px;
      width: 100%;
      background-size: cover;
    }

    .mon_body {
      display: none;
      width: 100%;
    }

    .el-menu-vertical-demo {
      height: 100%;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-upload__tip {
      margin-top: 10px;
    }

    .clearfix:before,
    .clearfix:after {
      display: table;
      content: "";
    }

    .clearfix:after {
      clear: both;
    }

    .center {
      border: 1px solid #ccc;
      width: 100%;
      margin: 20px auto 20px;
      border-radius: 20px;
      padding: 20px;
      min-width: 1200px;
    }

    .upload-demo {
      width: 100%;
    }

    .el-upload__text {
      font-size: 16px;
      margin: 20px 0;
    }

    .el-icon-upload {
      font-size: 67px;
      margin: 20px 0;
    }

    .el-menu-item.is-active {
      background-color: #ecf5ff;
      color: #409EFF;
    }

    .el-menu-item {
      font-size: 14px;
      height: 56px;
      line-height: 56px;
    }

    .el-menu-item:hover {
      background-color: #ecf5ff;
    }

    .download-notice {
      font-size: 14px;
      color: #666;
      display: inline-block;
      margin-top: 10px;
    }

    .notice-icon {
      color: #ff9800;
      margin-right: 5px;
    }

    .download-all-container {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .el-upload-dragger {
      width: 400px;
      height: 304px;
      padding: 20px;
      /* Reduced padding to fit content comfortably */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .el-upload__text {
      font-size: 16px;
      margin: 10px 0;
      /* Adjusted margin for better spacing */
    }

    .el-icon-upload {
      font-size: 50px;
      /* Slightly smaller icon to fit the new height */
      margin: 10px 0;
    }

    .el-textarea__inner {
      min-height: 300px;
    }

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .journal-entry {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding: 20px;
    align-items: center;
    border-radius: 20px;
    background-color: #ecf5ff;
}

.journal-image {
  width: 300px;
  height:300px;
  margin-right: 20px;
  object-fit: contain;
}

.journal-content {
  flex: 1;
}

.journal-content h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #333;
}

.journal-content p {
      margin: 0 0 10px 0;
    font-size: 19px;
    color: #02678D;
    font-weight: bold;
}

.journal-content p strong {
  color: #000;
}
  </style>
</head>

<body>
  <div class="header_app" id="header_app"></div>

  <div class="mon_warp clearfix" id="app">
    <div class="mon_body clearfix">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="3">
          <div style="padding-top:20px">
            <el-menu :default-active="menuActive"  class="el-menu-vertical-demo" 
             @select="handleMenuSelect"
            style="padding-top:20px;"
              active-text-color="#409EFF">
                <el-menu-item index="cleanTool">
                   <el-tooltip content="数据汇聚与清洗工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据汇聚与清洗工具
                  </span>
                </el-tooltip>
                  </el-menu-item>
              <el-menu-item index="classiFication">

                 <el-tooltip content="数据整编与分类工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    数据整编与分类工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="home">
                <el-tooltip content="全文多模态解析重组工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    全文多模态解析重组工具
                  </span>
                </el-tooltip>
              </el-menu-item><el-menu-item index="relationship">
                <el-tooltip content="知识对象及关系挖掘工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    知识对象及关系挖掘工具
                  </span>
                </el-tooltip>
              </el-menu-item>
              <el-menu-item index="qualitycontrol">
                <el-tooltip content="质量控制工具" placement="right">
                  <span
                    style="display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    质量控制工具
                  </span>
                </el-tooltip>
              </el-menu-item>

            </el-menu>
          </div>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="21">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <h2>知识对象及关系挖掘工具</h2>
            </div>
            <div>
              <p style="font-size: 16px;">
                化工领域知识对象抽取是针对化工科技文献中的文本、图像、表格等多源信息，精准抽取实体及实体之间关系的关键技术。
                其功能设计需深度适配化工领域的专业性、复杂性与多模态特性，知识对象抽取功能可实现从非结构化数据到可复用知识资产的转化，为研发、生产、安全等环节提供智能化支持，推动化工行业向数据驱动的新型研发范式转型。

              </p>
              <!-- <div style="text-align: right;width: 100%;padding-top: 10px;">
              <el-button type="success" style="margin-right: 60px;" @click="startopen">任务列表</el-button>
            </div> -->
              <div class="center">
                <div class="journal-entry" v-for="item in jsonData">
    <img :src="item.image_url" alt="Developmental Cell" class="journal-image">
    <div class="journal-content">
      <p v-for="it in item.text" v-html="it.value"></p>
    </div>
  </div>

              </div>
            </div>

          </el-card>
        </el-col>
      </el-row>


    </div>
  </div>

  <div id="dataid" data="{{session.user}}" style="display:none"></div>
  <div class="mon_footer"></div>

  <script src="../static/js/jquery.min.js"></script>
  <script src="../static/js/monitor.js"></script>

  <script>
    let _this = this;

    const vm = new Vue({
      el: '#app',
      data: {
        activeIndex: '3',
        lang: 'cn',
        domain_type: "common",
        extract_type: "text",
        model_type:2,
        fileList: [],
        dialogVisible: false,
        btnStart: "开始解构",
        uploadLists: [],
        loading: false,
        fileName: '', // 新增：文件名称输入框
        menuActive: "relationship",
        jsonData:[]

      },
      mounted() {
        $('.mon_body').css({ 'display': 'revert' });
        this.getTableData(); // 获取上传记录
        this.jsonData = JSON.parse(localStorage.getItem('jsonData'))

      },
      methods: {
        //抽取
        extract() {
          let that = this
          let param = {
            extract_type: that.extract_type,
            domain_type: that.domain_type,
            model_type: that.model_type || '',
            lang: that.lang,
            content: that.content,
          }
          // let data = {}
          // return
          // 创建 FormData 对象
          let formData = new FormData();
          // 如果存在文件，附加到 FormData
          if (that.files) {
            formData.append('files', that.files); // 附加原始文件
          }

          // 附加 JSON 数据
          formData.append('data', JSON.stringify(param));
          that.loading = true
          axios.post(server_url + '/api/extract', formData
            , {
              headers: { 'Content-Type': 'multipart/form-data' },
              timeout: 60000
            }).then(function (res) {
              console.log(res, 'res');

              const data = res.data
              if (data.code == 200) {
                that.loading = false
                that.$message.success('抽取成功！');
                location.href = `${server_url}${data.data.download_url}`;
                that.entities = data.data.result.entities || []
                that.relations = data.data.result.relations || []
              }
            }).catch((error) => {
              that.loading = false
            })
        },
        handleMenuSelect(index) {
          if (index === 'cleanTool') {
            window.location.href = '/cleanTool'; // 跳转到对应的页面
          } else if (index === 'classiFication') {
            window.location.href = '/classiFication';
          } else if (index === 'parseTool') {
            window.location.href = '/parseTool.html';
          }
          this.menuActive = index; // 更新高亮状态
        },
        startopen() {
          this.dialogVisible = true;
          this.getTableData(); // 打开对话框时获取最新的上传记录
        },
        handleAdd() { },
        handleRemove(file, fileList) {
          console.log('File removed:', file);
        },
        handlePreview(row) {
          // 预览功能可以根据需要实现
          location.href = `./demonstration?session_id=${row.session_id}`;
        },

        beforeAvatarUploadFile(file, type = '') {
          let that = this;
          const isLt100M = file.size / 1024 / 1024 < 500;
          if (!isLt100M) {
            this.$message.error('上传文件大小不能超过 500MB!');
            return false; // 阻止上传
          }
          // 直接存储原始文件对象
          that.files = file; // 存储 File 对象
          that.$message.success('上传成功！')
          return true; // 允许上传继续
        },
        // 新增：阻止自动上传
        customUpload(request) {
          // 不做处理，这只是为了拦截默认上传
        },
        // 新增：点击“开始解构”后统一上传
        handleUpload() {


          if (this.fileList.length === 0) {
            this.$message.warning("请先选择文件");
            return;
          }
          if (!this.fileName) {
            this.$message.warning("请填写文件名称再进行上传");
            return;
          }
          this.loading = true;
          const formData = new FormData();
          let overSize = false;

          this.fileList.forEach(file => {
            if (file.raw.size / 1024 / 1024 > 500) {
              overSize = true;
            } else {
              formData.append('pdf_files', file.raw);
            }
          });

          if (overSize) {
            this.$message.error("有文件大小超过 50MB，无法上传");
            return;
          }
          this.btnStart = "正在解构...";
          axios.post(`${server_url}/api/upload_pdfs?task_name=${encodeURIComponent(this.fileName)}`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          }).then(response => {
            this.btnStart = "开始解构";

            this.$message.success("上传成功");
            this.loading = false;
            location.href = `./demonstration?session_id=${response.data.data}`;
            this.response_handle(response.data, true);
          }).catch(error => {
            this.$message.error("上传失败");
            console.error(error);
          });
        },
        // 你的后端响应处理方法
        response_handle(data, success) {
          console.log("上传结果：", data);
          // TODO: 你可以在这里添加逻辑，比如跳转或展示解析结果
        },


        getTableData() {
          // 这里可以添加获取表格数据的逻辑
          let that = this;
   
          axios.get(server_url + '/api/upload_list').then(function (res) {
            if (res.data.code === 0) {

              that.$nextTick(() => {
                that.uploadLists = res.data.data;
                console.log(that.uploadLists);
              });

            }
          }).catch(function (error) {
            console.log(error);
            that.enterpriseLoading = false; // Ensure loading state is turned off in case of an error
          });
        },
      }
    });
  </script>
</body>

</html>