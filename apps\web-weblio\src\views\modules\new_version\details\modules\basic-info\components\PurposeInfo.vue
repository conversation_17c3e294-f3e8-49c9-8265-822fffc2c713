<script setup lang="ts">
import {
  Loading,
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref, watch } from 'vue';

import {
  getPurposeInfoData
} from '../api';
import { useSearchStore } from '#/store/search';

// 定义 props
const props = defineProps({
  purposeData: {
    type: Object,
    default: {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

interface PurposeInfoData {
  applicationDescription: string,
  field: string,
  industryUse: string
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: false,

});

// 定义加载状态，用于控制页面加载动画显示
const loading = ref(props.loading);

// 初始化数据对象，字段与接口定义保持一致，确保类型安全
const data = ref<PurposeInfoData>({
  applicationDescription: '',
  field: '',
  industryUse: ''
});

// 监听 props 变化，更新数据
watch(() => props.purposeData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    data.value = {
      applicationDescription: newData.applicationDescription || newData.applications || '',
      field: newData.field || newData.industry || '',
      industryUse: newData.industryUse || newData.industryUse || ''
    };
  }
}, { immediate: true, deep: true });

// 监听 loading 状态变化
watch(() => props.loading, (newLoading) => {
  loading.value = newLoading;
  console.log("loading", loading.value, 2222);
});

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

onMounted(async () => {
  // 如果父组件没有传递数据，则使用原有的逻辑
  // if (!props.purposeData || Object.keys(props.purposeData).length === 0) {
  //   try {
  //     // 调用用途信息查询API
  //     const response = await getPurposeInfoData({ inchikey: state.detailItem.baseCode });
  //     data.value = response;
  //   } catch (error) {
  //     console.error('获取用途信息失败:', error);
  //   } finally {
  //     // 关闭加载状态
  //     loading.value = false;
  //   }
  // }
});

const fieldLabels = {
  applicationDescription: '用途说明',
  field: '行业',
  industryUse: '行业用途'
};
</script>

<template>
  <div class="purpose-info">
    <!-- 加载状态提示 -->
    <div v-if="loading" class="loading-container">
      <Loading />
    </div>

    <!-- 主体内容展示 -->
    <div v-else class="content">
      <div class="info-items">
        <div v-for="(label, key) in fieldLabels" :key="key">
          <!-- 如果 data[key] 存在，则显示对应信息 -->
          <div v-if="data[key]" class="info-item">
            <!-- 字段标签 -->
            <span class="label">{{ label }}</span>
            <!-- 字段值 -->
            <span class="value">
              {{ data[key] }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.purpose-info {
  margin: 0 auto;
  padding: 20px;
  width: 80%;
}

.content {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.label {
  font-weight: bold;
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

.value {
  font-size: 14px;
  color: #555;
  line-height: 1.6;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
