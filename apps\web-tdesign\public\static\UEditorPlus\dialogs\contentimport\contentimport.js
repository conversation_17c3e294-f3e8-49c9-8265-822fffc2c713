/*! UEditorPlus v2.0.0*/
function processWord(a){$(".file-tip").html("正在转换Word文件，请稍后..."),$(".file-result").html("").hide();var b=new FileReader;b.onload=function(a){mammoth.convertToHtml({arrayBuffer:a.target.result}).then(function(a){$(".file-tip").html("转换成功"),contentImport.data.result=a.value,$(".file-result").html(a.value).show()},function(a){$(".file-tip").html("Word文件转换失败:"+a)})},b.onerror=function(a){$(".file-tip").html("Word文件转换失败:"+a)},b.readAsArrayBuffer(a)}function processMarkdown(a){var b=new showdown.Converter,c=b.makeHtml(a);$(".file-tip").html("转换成功"),contentImport.data.result=c,$(".file-result").html(c).show()}function processMarkdownFile(a){$(".file-tip").html("正在转换Markdown文件，请稍后..."),$(".file-result").html("").hide();var b=new FileReader;b.onload=function(a){processMarkdown(a.target.result)},b.onerror=function(a){$(".file-tip").html("Markdown文件转换失败:"+a)},b.readAsText(a,"UTF-8")}function addUploadButtonListener(){g("contentImport").addEventListener("change",function(){const a=this.files[0],b=a.name,c=b.substring(b.lastIndexOf(".")+1).toLowerCase();switch(c){case"docx":case"doc":processWord(a);break;case"md":processMarkdownFile(a);break;default:$(".file-tip").html("不支持的文件格式:"+c)}}),g("fileInputConfirm").addEventListener("click",function(){processMarkdown(g("fileInputContent").value),$(".file-input").hide()})}function addOkListener(){dialog.onok=function(){return contentImport.data.result?(editor.fireEvent("saveScene"),editor.execCommand("inserthtml",contentImport.data.result),void editor.fireEvent("saveScene")):(alert("请先上传文件识别内容"),!1)},dialog.oncancel=function(){}}var contentImport={},g=$G;contentImport.data={result:null},contentImport.init=function(a,b){addUploadButtonListener(),addOkListener()};