import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '数据填报',
    },
    name: 'tData',
    path: '/tData',
    children: [
      {
        meta: {
          title: '数据填报',
        },
        name: 'tDataIndex',
        path: '/tData/index',
        component: () =>
          import('#/views/modules/tData/index.vue'),
      },
    ],
  },
];

export default routes;
