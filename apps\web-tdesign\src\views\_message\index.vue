<script setup lang="ts">
import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import EditForm from './components/EditForm.vue';
import IndexTable from './components/IndexTable.vue';

// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();
const [EditFormModle, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: EditForm,

  onOpenChange(isOpen: boolean) {
    if (isOpen === false) {
      console.log('关闭时触发获取数据', modalApi.getData());
    }
  },
});
const add: any = () => {
  modalApi.setState({ title: '新增' });
  modalApi.setData({ refresh: indexTable.value.loadData });
  modalApi.open();
};

const edit: any = (record: any) => {
  modalApi.setState({ title: '查看' });
  modalApi.setData({ record, refresh: indexTable.value.loadData });
  modalApi.open();
};
</script>

<template>
  <Page title="我的消息">
    <IndexTable ref="indexTable" @add="add" @edit="edit" />
    <EditFormModle />
  </Page>
</template>
