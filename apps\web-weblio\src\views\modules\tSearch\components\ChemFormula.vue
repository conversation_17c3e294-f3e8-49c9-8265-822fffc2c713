<script setup lang="tsx">
import type { FormProps } from 'tdesign-vue-next';

import Ketcher from '#/views/modules/tSearch/components/Ketcher/index.vue';
import { useVbenModal } from '@vben/common-ui';
import { Form } from 'tdesign-vue-next';
import { defineProps, reactive, ref } from 'vue';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['moth']);
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
const chemFormulaData = ref(props.data);
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    console.log(chemFormulaData.value.smiles);
    emit('moth', chemFormulaData.value.smiles);
    modalApi.close();
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
const close = () => {
  modalApi.close();
};
/**
 * 导出资源
 */
defineExpose({
  open,
  close,
});
</script>

<template>
  <Modal title="化学式查询" class="h-screen w-[40%]">
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="h-full w-full"
      label-align="top"
    >
      <div class="h-full w-full">
        <Ketcher v-model="chemFormulaData" />
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
