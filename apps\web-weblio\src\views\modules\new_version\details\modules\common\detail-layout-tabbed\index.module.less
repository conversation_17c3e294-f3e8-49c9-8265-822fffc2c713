.layout-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background: #f7f8fa;
  font-size: 16px;
  overflow: visible;
}

.layout-header {
  padding: 0 24px;
  margin-bottom: 16px;
}

.layout-main {
  display: flex;
  flex: 1;
  width: calc(100% - 48px);
  min-height: 600px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(44, 90, 160, 0.06);
  gap: 0;
  padding: 0;
  margin: 0 24px 24px 24px;
  overflow: visible;
}

.layout-sidebar {
  width: 240px;
  min-width: 180px;
  border: none;
  border-right: 1px solid #e8e8e8;
  padding: 24px;
  background: #fff;
  border-radius: 0;
  box-shadow: none;
  height: fit-content;
  max-height: 100vh;
  margin: 0;
  position: sticky;
  top: 0;
  align-self: flex-start;
  z-index: 1600;
  overflow-y: auto;
}

// 大屏幕下的桌面版菜单
.menu-container-desktop {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 小屏幕下的移动版菜单（默认隐藏）
.menu-container-mobile {
  display: none;
}



.menu-item-wrapper {
  margin-bottom: 4px;
  
  &.has-children {
    overflow: visible;
  }
}

.menu-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  border-radius: 8px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px solid transparent;
  user-select: none;

  &.clickable {
    cursor: pointer;

    &:hover {
      background: #f5f7fa;
      color: #2c5aa0;
      font-weight: 600;
      border-color: #e5eefa;
    }

    &.active {
      background: #e5eefa;
      font-weight: 700;
      color: #2c5aa0;
      border-color: #2c5aa0;
    }
  }

  &.disabled {
    color: #ccc;
    cursor: not-allowed;
    background: #f9f9f9;
  }

  &.no-content {
    color: #666;
    cursor: default;
    font-weight: 600;
    background: #fafafa;
    border-color: #e8e8e8;
  }

  // 层级缩进样式
  &.depth-0 .menu-item-content {
    padding-left: 16px;
  }

  &.depth-1 {
    font-size: 13px;
    border-left: 2px solid #e8e8e8;
    margin-left: 8px;
    border-radius: 0 8px 8px 0;
    
    .menu-item-content {
      padding-left: 32px;
    }
  }

  &.depth-2 {
    font-size: 12px;
    border-left: 2px solid #e8e8e8;
    margin-left: 16px;
    border-radius: 0 8px 8px 0;
    
    .menu-item-content {
      padding-left: 48px;
    }
  }

  &.depth-3 {
    font-size: 12px;
    border-left: 2px solid #e8e8e8;
    margin-left: 24px;
    border-radius: 0 8px 8px 0;
    
    .menu-item-content {
      padding-left: 64px;
    }
  }
}

.menu-item-content {
  flex: 1;
  padding: 12px 16px;
  cursor: pointer;
}

.menu-item-title {
  display: block;
  word-break: break-word;
  line-height: 1.4;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  
  &:hover {
    background: #e9ecef;
    border-color: #adb5bd;
  }
}

.expand-icon {
  font-size: 12px;
  transition: transform 0.2s ease;
  color: #495057;
  font-weight: bold;
}

.expand-button.expanded .expand-icon {
  transform: rotate(180deg);
}

.menu-children {
  margin-top: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &.collapsed {
    max-height: 0;
    opacity: 0;
    margin-top: 0;
  }
  
  &.expanded {
    max-height: 1000px;
    opacity: 1;
    margin-top: 4px;
  }
}

// 桌面端子菜单保持内联显示
@media (min-width: 1025px) {
  .menu-children {
    position: relative;
    background: transparent;
    border: none;
    box-shadow: none;
    z-index: auto;
    padding: 0;
    
    .menu-item-wrapper {
      margin-bottom: 4px;
    }
  }
}

.layout-content {
  flex: 1;
  min-height: 600px;
  padding: 24px;
  background: #fff;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.content-wrapper {
  flex: 1;
  animation: fadeIn 0.3s ease-in-out;
}

.content-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.placeholder-text {
  color: #999;
  font-size: 16px;
  text-align: center;
}

// 淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .layout-container {
    font-size: 15px;
  }
  
  .layout-main {
    width: calc(100% - 32px);
    margin: 0 16px 16px 16px;
  }
  
  .layout-sidebar {
    width: 200px;
    min-width: 160px;
    padding: 16px;
  }
  
  .layout-content {
    padding: 16px;
  }
}

@media (max-width: 1024px) {
  .layout-container {
    font-size: 14px;
  }
  
  .layout-main {
    flex-direction: column;
    gap: 0;
    width: calc(100% - 24px);
    margin: 0 12px 12px 12px;
    background: #f5f6fa;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    padding: 12px;
    overflow: visible;
  }
  
  .layout-sidebar {
    order: 1;
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
    position: sticky;
    top: 0;
    z-index: 1600;
    max-height: none;
    overflow: visible;
    padding: 12px;
    width: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  }

  // 隐藏桌面版菜单，显示移动版菜单
  .menu-container-desktop {
    display: none !important;
  }

  .menu-container-mobile {
    display: flex !important;
    align-items: flex-start;
    gap: 12px;
  }

  // 移动版菜单项容器
  .mobile-menu-items {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    transition: all 0.3s ease;

    // 收起状态：只显示一行
    &.menu-collapsed {
      max-height: 40px;
      overflow: hidden;
    }

    // 展开状态：显示全部
    &.menu-expanded {
      max-height: none;
      overflow: visible;
    }
  }

  // 移动版菜单项
  .mobile-menu-item {
    padding: 8px 12px;
    background: #f8f9fb;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    color: #333;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    user-select: none;

    &:hover {
      background: #e5eefa;
      border-color: #2c5aa0;
      color: #2c5aa0;
    }

    &.active {
      background: #e5eefa;
      border-color: #2c5aa0;
      color: #2c5aa0;
      font-weight: 600;
    }

    &.disabled {
      color: #ccc;
      cursor: not-allowed;
      background: #f9f9f9;
    }
  }

  // 展开收起按钮
  .mobile-toggle-button {
    padding: 8px 12px;
    background: #f8f9fb;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    color: #2c5aa0;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;

    &:hover {
      background: #e5eefa;
      border-color: #2c5aa0;
    }
  }
  
  .layout-content {
    order: 2;
    min-height: 400px;
    width: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    padding: 12px;
  }



  .menu-container {
    flex-direction: row;
    overflow: visible;
    gap: 8px;
    padding: 0;
    width: auto;
    white-space: nowrap;
    transition: all 0.3s ease;
    
    // 收起状态 - 只显示前几个菜单项
    &.menu-collapsed {
      overflow: hidden;
      max-height: 50px; // 限制高度只显示一行
      mask: linear-gradient(to right, transparent 0%, black 10%, black 85%, transparent 100%);
      -webkit-mask: linear-gradient(to right, transparent 0%, black 10%, black 85%, transparent 100%);
      position: relative;
    }
    
    // 展开状态 - 显示全部菜单项，允许换行
    &.menu-expanded {
      max-height: none;
      overflow: visible;
      flex-wrap: wrap; // 允许换行
      align-items: flex-start;
      
      .menu-item-wrapper {
        margin-bottom: 8px; // 给换行的项目增加底部间距
      }
    }
  }
  
  
}

@media (max-width: 768px) {
  .layout-container {
    font-size: 13px;
  }
  
  .layout-header {
    padding: 0 12px;
    margin-bottom: 12px;
  }
  
  .layout-main {
    gap: 8px;
    width: calc(100% - 16px);
    margin: 0 8px 8px 8px;
    padding: 8px;
  }
  
  .layout-sidebar,
  .layout-content {
    padding: 8px;
    border-radius: 8px;
  }

  .layout-sidebar {
    position: sticky;
    top: 0;
    z-index: 1600;
  }

  .menu-item-wrapper {
    min-width: 100px;
  }

  .menu-item {
    font-size: 12px;
    
    .menu-item-content {
      padding: 6px 10px;
    }
  }

  // 确保展开按钮在768px以下也显示
  .menu-item-wrapper.has-children .menu-item .expand-button {
    display: flex !important;
    width: 22px;
    height: 22px;
    top: 3px;
    right: 3px;
    
    .expand-icon {
      font-size: 9px;
    }
  }
  
  .placeholder-text {
    font-size: 14px;
  }
}

@media (max-width: 600px) {
  .layout-container {
    font-size: 12px;
  }
  
  .layout-main {
    gap: 4px;
    width: calc(100% - 8px);
    padding: 4px;
  }

  .layout-sidebar {
    position: sticky;
    top: 0;
    z-index: 1600;
  }

  .menu-item-wrapper {
    min-width: 80px;
  }

  .menu-item {
    font-size: 11px;
    
    .menu-item-content {
      padding: 4px 8px;
    }
  }

  // 确保展开按钮在600px以下也显示
  .menu-item-wrapper.has-children .menu-item .expand-button {
    display: flex !important;
    width: 20px;
    height: 20px;
    top: 2px;
    right: 2px;
    
    .expand-icon {
      font-size: 8px;
    }
  }
  
  .placeholder-text {
    font-size: 12px;
  }
}


