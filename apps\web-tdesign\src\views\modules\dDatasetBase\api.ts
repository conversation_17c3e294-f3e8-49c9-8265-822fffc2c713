// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/dDatasetBase/listByPage', data);
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-submit/dDatasetBase/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-submit/dDatasetBase/deleteBatch/${data}`);
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-submit/dDatasetBase/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-submit/dDatasetBase/getByIds/${data}`);
}
export async function getOneByDataseCode(data: any) {
  return requestClient.post<any[]>('/rgdc-submit/dDatasetBase/getOneByDataseCode', data);
}
