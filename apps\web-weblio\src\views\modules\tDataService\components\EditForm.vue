<script setup lang="tsx">
import { defineProps, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useVbenModal } from '@vben/common-ui';

import {Input, MessagePlugin, Upload} from 'tdesign-vue-next';
import { InputNumber } from 'tdesign-vue-next';
import {
  Form,
  FormItem,
  type FormProps,
  Textarea
} from 'tdesign-vue-next';

import { save } from '../api.ts';
import {useAccessStore} from "@vben/stores";

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});
/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps['rules'] = {
        serviceDescribe: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 4294967295,
             message: '最大长度为4294967295',
           },
         ],
        serviceOwner: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 255,
             message: '最大长度为255',
           },
         ],
        serviceTitle: [
           {
             required: true,
             message: '必填',
           },
           {
             max: 90,
             message: '最大长度为90',
           },
         ],
        serviceLink: [
          {
            required: true,
            message: '必填',
          },
          {
            max: 4294967295,
            message: '最大长度为4294967295',
          },
        ],
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});
const reqRunner = {
  save: useRequest(save, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {},
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    },
  }),
};
/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (!isOpen) {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      /**
       * 保存数据
       */
      if(!file.value){
        MessagePlugin.error('请上传数据工具展示图片');
        return
      }
      formData.value.imgPath = JSON.stringify(file.value)
      reqRunner.save.run({ ...state.tagObj, ...formData.value });
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  },
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
    file.value = []
    if(data.imgPath) {
      file.value = JSON.parse(data.imgPath)
    }
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open,
});
const accessStore = useAccessStore();
const file = ref([])
const suffix = ref('.png,.jpg,.svg')
const beforeUpload = async (file) => {
  const index = file.name.lastIndexOf('.')
  if (index !== -1) {
    const suffix2 = file.name.substring(index + 1);
    if(!suffix.value.includes(suffix2)){
      MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
      return false;
    }
  }else{
    MessagePlugin.warning("请上传后缀为"+suffix.value+"的文件")
    return false;
  }
  return true;
};
const uploadSuccess = (context: { fileList: any[] }) => {
  file.value = context.fileList.map(
    (item: {
      name: any;
      response: { result: { url: any } };
      status: any;
    }) => {
      return {
        name: item?.name,
        status: item?.status,
        path: item?.response?.result?.url || '',
        url: item?.response?.result?.temporaryUrl || '',
      };
    },
  );
};
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="w-full"
      label-align="top"
    >
      <div class="grid w-full grid-cols-2 gap-1">
        <FormItem label="数据工具名称" name="serviceTitle">
          <Input v-model="formData.serviceTitle" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据工具归属" name="serviceOwner">
          <Input v-model="formData.serviceOwner" clearable placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据工具描述" name="serviceDescribe">
          <Textarea v-model="formData.serviceDescribe" placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据工具链接" name="serviceLink">
          <Textarea v-model="formData.serviceLink" placeholder="请输入内容" />
        </FormItem>
        <FormItem label="数据工具图片路径" name="imgPath">
          <Upload ref="img" v-model="file" :headers="{ authorization: `Bearer ${accessStore.accessToken}` }"
                  action="/rgdc-sys/file/upload" accept="image/*" theme="image" :showImageFileName="false"
                  :beforeUpload="beforeUpload" @success="uploadSuccess"/>
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
